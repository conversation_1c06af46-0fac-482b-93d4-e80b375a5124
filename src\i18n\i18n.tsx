import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from './english.json';
import ar from './arabic.json';
import be from './Belarusian.json';
import ca from './Catalan.json';
import hr from './Croatian.json';
import cs from './Czech.json';
import nl from './Dutch.json';
import fi from './Finnish.json';
import fr from './french.json';
import de from './german.json';
import he from './Hebrew.json';
import hu from './Hungarian.json';
import id from './Indonesian.json';
import it from './Italian.json';
import kk from './Kazakh.json';
import ko from './Korean.json';
import ms from './Malay.json';
import no from './Norwegian.json';
import fa from './Persian.json';
import ru from './Russian.json';
import sr from './Serbian.json';
import sk from './Slovak.json';
import es from './spanish.json';
import sv from './Swedish.json';
import tr from './Turkish.json';
import uk from './Ukrainian.json';
import uz from './Uzbek.json';
import zh_CN from './Chinese_simplified.json';
import zh_TW from './Chinese_traditional.json';
import hi from './Hindi.json';
import ur from './Urdu.json';
import bn from './Bengali.json';

i18n.use(initReactI18next).init({
  compatibilityJSON: 'v3',
  lng: 'en',
  fallbackLng: 'en',
  resources: {
    en: en,
    be: be,
    ar: ar,
    ca: ca,
    hr: hr,
    cs: cs,
    nl: nl,
    fi: fi,
    fr: fr,
    de: de,
    he: he,
    hu: hu,
    id: id,
    it: it,
    kk: kk,
    ko: ko,
    ms: ms,
    no: no,
    fa: fa,
    ru: ru,
    sr: sr,
    sk: sk,
    es: es,
    sv: sv,
    tr: tr,
    uk: uk,
    uz: uz,
    zh_CN: zh_CN,
    zh_TW: zh_TW,
    hi: hi,
    ur: ur,
    bn: bn,
  },
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
