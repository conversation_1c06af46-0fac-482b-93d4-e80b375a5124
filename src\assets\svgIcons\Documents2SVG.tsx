import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const Documents2SVG = ({ size = 16, color = '#232323', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 16 16" fill="none" {...props}>
    <Path d="M8.58594 11.457C8.58594 11.7806 8.32361 12.043 8 12.043H4.54297C4.21936 12.043 3.95703 11.7806 3.95703 11.457C3.95703 11.1334 4.21936 10.8711 4.54297 10.8711H8C8.32361 10.8711 8.58594 11.1334 8.58594 11.457ZM15.5 4.02002V10.8808C15.5 11.7805 15.1497 12.6262 14.5135 13.2624L13.2624 14.5135C12.6262 15.1497 11.7805 15.5 10.8808 15.5H4.02002C2.07438 15.5 0.5 13.9254 0.5 11.98V4.02002C0.5 2.07438 2.07456 0.5 4.02002 0.5H11.98C13.9256 0.5 15.5 2.07456 15.5 4.02002ZM14.2495 11.4668H12.6387C11.9925 11.4668 11.4668 11.9925 11.4668 12.6387V14.2495C11.829 14.1499 12.1609 13.9577 12.4337 13.6849L13.6849 12.4337C13.9577 12.1609 14.1499 11.829 14.2495 11.4668ZM14.3281 4.02002C14.3281 2.72214 13.2778 1.67188 11.98 1.67188H4.02002C2.72214 1.67188 1.67188 2.7222 1.67188 4.02002V11.98C1.67188 13.2779 2.7222 14.3281 4.02002 14.3281H10.2949V12.6387C10.2949 11.3463 11.3463 10.2949 12.6387 10.2949H14.3281V4.02002ZM11.457 7.41406H4.54297C4.21936 7.41406 3.95703 7.67639 3.95703 8C3.95703 8.32361 4.21936 8.58594 4.54297 8.58594H11.457C11.7806 8.58594 12.043 8.32361 12.043 8C12.043 7.67639 11.7806 7.41406 11.457 7.41406ZM11.457 3.95703H4.54297C4.21936 3.95703 3.95703 4.21936 3.95703 4.54297C3.95703 4.86658 4.21936 5.12891 4.54297 5.12891H11.457C11.7806 5.12891 12.043 4.86658 12.043 4.54297C12.043 4.21936 11.7806 3.95703 11.457 3.95703Z" fill={color}/>
  </Svg>
);

export default Documents2SVG; 