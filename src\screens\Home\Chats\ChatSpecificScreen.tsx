import { Route<PERSON>rop, useFocusEffect, useRoute } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  BackHandler,
  FlatList,
  Image,
  LayoutAnimation,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardStickyView } from 'react-native-keyboard-controller';
import Animated, { useAnimatedKeyboard, useAnimatedStyle } from 'react-native-reanimated';
import { SwipeListView } from 'react-native-swipe-list-view';
import { IMAGES } from '../../../assets/Images';
import ChatImageBackground from '../../../component/ChatImageBackground';
import CustomAlert from '../../../component/Common/CustomAlert';
import CommonView from '../../../component/CommonView';
import DeleteMessageModal from '../../../component/PersonalChat/DeleteMessageModal';
import PinModal from '../../../component/PersonalChat/PinModal';
import {
  ConversationSchema,
  IConversation,
} from '../../../device-storage/realm/schemas/ConversationSchema';
import {
  ConversationType,
  IMessage,
  MessageEventType,
  MessageStatus,
  MessageType,
  ScheduleUpdateType,
} from '../../../device-storage/realm/schemas/MessageSchema';
import { UserSchema } from '../../../device-storage/realm/schemas/UserSchema';
import {
  ModalType,
  useLiveStreamController,
} from '../../../hooks/channels/useLiveStreamController';
import { useFetchMessages } from '../../../hooks/chats/messages/useFetchMessages';
import useConversations from '../../../hooks/conversations/useConversations';
import { useMe } from '../../../hooks/util/useMe';
import { SCREENS } from '../../../navigation/screenNames';
import { ChatService } from '../../../service/ChatService';
import { getChatSpaceMembers, IGroupMember } from '../../../service/ChatSpacesService';
import usePresence from '../../../hooks/chats/usePresence';
import { colors } from '../../../theme/colors';
import { SCREEN_WIDTH } from '../../../theme/fonts';
import { ComposedMessage } from '../../../types/index.types';
import { unBlockUser } from '../../../utils/ApiService';
import { errorToast, navigateTo, showToast } from '../../../utils/commonFunction';
import { FontAwesome6Icons } from '../../../utils/vectorIcons';
import GoLiveInfoModal from '../Channels/components/GoliveInfoModal';
import NewLiveStreamModal from '../Channels/components/NewLiveStreamModal';
import StreamLiveCard from '../Channels/components/StreamLiveBottomCard';
import EditScheduleMsgModal from './ChatModals/EditScheduleMsgModal';
import JoinGroupCallModal from './ChatModals/JoinGroupCallModal';
import PinnedMessages from './ChatModals/PinnedMessages';
import ScheduleMessageModal from './ChatModals/ScheduleMessageModal';
import UnPinModal from './ChatModals/UnPinModal';
import ChatComposer from './Components/ChatComposer';
import ChatHeader from './Components/ChatHeader';
import ChatListItem from './Components/ChatListItem';
import FreshConversationUI from './Components/FreshConversationUI';
import useConversationInfo, {
  ConversationInfo,
} from '../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../types/chats.types';
import ChatScreenSkeleton from '../../../component/chats/ChatScreenSkeleton';
import Logger from '../../../lib/Logger';
// import { useWhatChanged } from '@simbathesailor/use-what-changed';

export type ChatScreenParams = {
  convId: string;
  initialConversationInfo?: ConversationInfo;
};

export interface IChatScreenProps {
  displayName: string;
  displayPic?: string;
  type: ConversationType;
  id: string;
  isActive?: boolean;
  isDeleted?: boolean;
  conversation: IConversation;
  followersCount?: number;
  chatSpaceId?: string;
  isFollowing?: boolean;
}

export interface GroupCallItemState {
  isVisible: boolean;
  selectedMessage: IMessage | null;
}

const DEFAULT_IMAGE = require('../../../assets/Image/ChatWallpaper.png');

export type ChatSpecificScreenParamsT = {
  userData: IChatScreenProps;
  isFollowing: boolean;
  isArchived?: boolean;
  isAlreadyMember?: boolean;
  data: ChatScreenParams;
};

export type ChatSpecificScreenParams = {
  ChatSpecificScreen: ChatSpecificScreenParamsT;
};

const ChatSpecificScreen = () => {
  console.log(' [renderlog] ChatSpecificScreen rendered');

  const route = useRoute<RouteProp<ChatSpecificScreenParams, 'ChatSpecificScreen'>>();
  const { userData } = route.params || {};
  // final params
  const { convId, initialConversationInfo } = route.params.data;
  const { conversationInfo } = useConversationInfo(convId, initialConversationInfo);

  const role =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus
      : undefined;

  const [groupCallItemState, setGroupCallItemState] = useState<GroupCallItemState>({
    isVisible: false,
    selectedMessage: null,
  });

  const { user: me, userPreferencesState } = useMe();
  const initialComposedMessage: ComposedMessage = {
    senderId: me?._id || '',
    receiverId: convId,
    isSilent: false,
    scheduledAt: undefined,
    // !! update this (make it dynamic)
    conversationType:
      userData.type === 'group'
        ? ConversationType.GROUP
        : userData.type === 'channel'
        ? ConversationType.CHANNEL
        : ConversationType.P2P,
    messageType: MessageType.TEXT,
    text: '',
  };
  const [composedMessage, setComposedMessage] = useState<ComposedMessage>(initialComposedMessage);

  const [groupMembers, setGroupMembers] = useState<IGroupMember[]>([]);
  const { messages, pinnedMessages, reactionsMap } = useFetchMessages(String(userData.id));

  const [scheduleTimerSheet, setScheduleTimerSheet] = useState(false);
  const [scheduleItemModal, setScheduleItemModal] = useState(false);
  const [selectedScheduledMessage, setSelectedScheduledMessage] = useState<IMessage | null>(null);
  const [scheduleDate, setScheduleDate] = useState(null);

  const [deleteBtmSheet, setDeleteBtmSheet] = useState(false);
  const [selectedMsgs, setSelectedMsgs] = useState<IMessage[]>([]);
  const [replyMsgData, setReplyMsgData] = useState<IMessage | null>();
  const [isPinMessage, setIsPinMessage] = useState<boolean>(false);
  const [editedMessage, setEditedMessage] = useState<IMessage | null>(null);
  const [isPinMessageVisible, setIsPinMessageVisible] = useState<boolean>(false);
  const [unPinModal, setUnpinModal] = useState<boolean>(false);
  const [selectedPinLocalId, setSelectedPinLocalId] = useState<string>('');
  const [isMsgHightlighted, setIsMsgHighlighted] = useState<boolean>(false);
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [deleteModalLoading, setDeleteModalLoading] = useState(false);
  const [userMemberDataState, setUserMemberDataState] = useState<UserSchema | null>(
    ChatService.getUserData(userData.id),
  );

  const swipeThreshold = 70; // The distance needed to trigger the action
  const activeSwipeRef = useRef<string | null>(null); // Track currently swiped row
  const swipeListViewRef = useRef<FlatList<IMessage> | null>(null);
  const textInputRef = useRef<TextInput>(null);

  const keyboard = useAnimatedKeyboard({
    isStatusBarTranslucentAndroid: true,
  });
  const stableKeyboardHeight = useAnimatedStyle(() => {
    const height = keyboard.height.value > 20 ? keyboard.height.value + 10 : 0;
    return { height };
  });

  // liveStream hook
  const {
    streamingModalState,
    streamDetails,
    setStreamDetails,
    openModal,
    resetStreamingState,
    joinStream,
  } = useLiveStreamController({
    type: userData.type,
    Conversation: userData.conversation,
  });

  // ======

  // // Debug state variables
  // useWhatChanged(
  //   [
  //     groupCallItemState,
  //     composedMessage,
  //     groupMembers,
  //     scheduleTimerSheet,
  //     scheduleItemModal,
  //     selectedScheduledMessage,
  //     scheduleDate,
  //     deleteBtmSheet,
  //     selectedMsgs,
  //     replyMsgData,
  //     isPinMessage,
  //     editedMessage,
  //     isPinMessageVisible,
  //     unPinModal,
  //     selectedPinLocalId,
  //     isMsgHightlighted,
  //     isCreating,
  //     deleteModalLoading,
  //     userMemberDataState,
  //   ],
  //   'groupCallItemState, composedMessage, groupMembers, scheduleTimerSheet, scheduleItemModal, selectedScheduledMessage, scheduleDate, deleteBtmSheet, selectedMsgs, replyMsgData, isPinMessage, editedMessage, isPinMessageVisible, unPinModal, selectedPinLocalId, isMsgHightlighted, isCreating, deleteModalLoading, userMemberDataState',
  //   'ChatSpecificScreen-state',
  // );

  // // Debug streaming state
  // useWhatChanged(
  //   [streamingModalState, streamDetails],
  //   'streamingModalState, streamDetails',
  //   'ChatSpecificScreen-streaming',
  // );

  // Rest of your component...

  // =======

  useEffect(() => {
    if (!conversationInfo?.conversationSettings?.disappearDuration) return;

    const interval = setInterval(() => {
      ChatService.disappearingMessages();
    }, 31 * 1000);

    return () => clearInterval(interval);
  }, [conversationInfo?.conversationSettings?.disappearDuration]);

  // const memberPermissions = currentConversation
  //   ? ChatSpacePermissions(liveConversation ?? userData.conversation)
  //   : defaultMemberPermissions;
  // const lastSeenAt = liveConversation?.lastSeenAt;

  // Fetch group members on mount
  useEffect(() => {
    if (conversationInfo && conversationInfo.type === ConversationType.GROUP) {
      handleGroupMembers();
    }
  }, []);

  // Handlers
  const handleGroupMembers = async () => {
    if (!conversationInfo) return;
    try {
      const memberData = await getChatSpaceMembers(conversationInfo.id);
      if (memberData) {
        setGroupMembers(memberData.members);
      }
    } catch (error) {
      setGroupMembers([]);
    }
  };

  const onReplyMessage = () => {
    setReplyMsgData(selectedMsgs[0]);
    setSelectedMsgs([]);
  };

  const toggleSelection = useCallback(
    (item: any) => {
      const canSelect =
        conversationInfo?.type === ConversationType.P2P ||
        (conversationInfo?.type === ConversationType.GROUP && role !== MembershipStatus.MEMBER) ||
        (conversationInfo?.type === ConversationType.CHANNEL && role === 'owner');

      if (!canSelect) {
        return;
      }

      setSelectedMsgs((prev) =>
        prev.includes(item) ? prev.filter((mid) => mid !== item) : [...prev, item],
      );
    },
    [selectedMsgs, conversationInfo?.type],
  );

  const onCardPress = useCallback((data: IMessage) => {
    if (data?.scheduledAt && data?.status == MessageStatus.SCHEDULED) {
      setScheduleItemModal(true);
      setSelectedScheduledMessage(data);
      return;
    }
    if (data.messageType === MessageType.EVENT && data.eventType === MessageEventType.GROUP_CALL) {
      setGroupCallItemState({
        isVisible: true,
        selectedMessage: data,
      });
    }
  }, []);

  const handleSwipeValueChange = useCallback(
    ({ key, value, direction }: { key: string; value: number; direction?: 'left' | 'right' }) => {
      // Only handle right swipes (positive values)
      if (direction === 'right' && value > 0) {
        if (value >= swipeThreshold && activeSwipeRef.current !== key) {
          // Swipe completed - trigger action
          activeSwipeRef.current = key;
          const msgData = messages.find((list) => list.localId === key);

          if (
            msgData?.messageType === MessageType.EVENT ||
            msgData?.status == MessageStatus.SCHEDULED
          ) {
            return;
          }

          requestAnimationFrame(() => {
            setReplyMsgData(msgData);
            LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          });
        } else if (value < swipeThreshold && activeSwipeRef.current === key) {
          // Swipe cancelled - reset
          activeSwipeRef.current = null;
        }
      }
    },
    [messages],
  );

  const onClickSchedule = () => {
    if (selectedScheduledMessage) {
      console.log(
        selectedScheduledMessage.globalId as string,
        composedMessage.scheduledAt as number,
      );
      ChatService.updateScheduleMessage(
        selectedScheduledMessage.globalId as string,
        ScheduleUpdateType.RESCHEDULE,
        composedMessage.scheduledAt as number,
      );
      setSelectedScheduledMessage(null);
      setComposedMessage((prev) => ({ ...prev, scheduledAt: undefined }));
    }
    setScheduleTimerSheet(false);
  };

  const handleSwipeListViewRef = (ref: any) => {
    if (ref && ref._listRef) {
      swipeListViewRef.current = ref._listRef;
    }
  };

  const scrollToItem = (localId: string) => {
    const index = messages.findIndex((item) => item.localId === localId);
    if (index === -1) {
      return;
    }

    if (swipeListViewRef.current) {
      setIsMsgHighlighted(true);
      swipeListViewRef.current?.scrollToIndex({
        index,
        animated: true,
        viewPosition: 0, // 0 = top, 0.5 = center, 1 = bottom
      });
    }
  };

  const onPinMessage = (value?: any) => {
    setIsPinMessage(false);

    let hours = 24;
    switch (value) {
      case '1_HOUR':
        hours = 1;
        break;
      case '24_HOURS':
        hours = 24;
        break;
      case '7_DAYS':
        hours = 168; // 7 * 24 hours
        break;
      case '30_DAYS':
        hours = 720; // 30 * 24 hours
        break;
    }

    selectedMsgs.forEach((msg) => {
      if (msg?.status !== MessageStatus.SCHEDULED) {
        ChatService.pinMessage(msg.localId, userData.id, hours);
      }
    });

    setSelectedMsgs([]);
  };

  const onUinPin = () => {
    setSelectedPinLocalId('');
    setUnpinModal(false);
    const msg = pinnedMessages.find((item: any) => item.localId === selectedPinLocalId);
    if (msg) {
      ChatService.unpinMessage(msg.localId, userData.id);
    }
  };

  const onEditMessage = useCallback((text: string, msgData: any) => {
    setComposedMessage((prev) => ({ ...prev, text: text, localId: msgData?.localId }));
    setEditedMessage(msgData);
    setSelectedMsgs([]);
    setTimeout(() => {
      textInputRef?.current?.focus();
    }, 200);
    if (replyMsgData) {
      setReplyMsgData(null);
    }
  }, []);

  /**
   * On conversationOpen marks all the unseen messages in a specific conversation,
   * then emits an event to server to notify other users.
   */

  const presenceSnapshot = usePresence(userData?.id);

  const chatWallpaper = userPreferencesState?.userPreferences?.chats?.chatWallpaper;

  // called when on screen is focussed.b/w navigations
  useFocusEffect(
    useCallback(() => {
      const userMemberData = ChatService.getUserData(userData.id);

      setUserMemberDataState(userMemberData);
    }, [userData.type, userMemberDataState?.isBlocked]),
  );

  useEffect(() => {
    const backAction = () => {
      if (editedMessage !== null) {
        setEditedMessage(null);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [editedMessage]);

  // Debug all major dependencies that could cause re-renders
  // useWhatChanged(
  //   [
  //     route.params,
  //     userData,
  //     conversationInfo,
  //     me,
  //     userPreferencesState,
  //     messages,
  //     pinnedMessages,
  //     reactionsMap,
  //     presenceSnapshot,
  //   ],
  //   'route.params, userData, conversationInfo, me, userPreferencesState, messages, pinnedMessages, reactionsMap, presenceSnapshot, liveConversationRealm',
  //   'ChatSpecificScreen-major-deps',
  // );

  if (!conversationInfo) return <ChatScreenSkeleton />;

  return (
    <CommonView
      customHeader={
        () => (
          <ChatHeader
            editedMessage={editedMessage}
            // memberPermissions={memberPermissions}
            replyMsgData={replyMsgData}
            selectedMsgs={selectedMsgs}
            setEditedMessage={setEditedMessage}
            presenceSnapshot={presenceSnapshot}
            userData={userData}
            conversationInfo={conversationInfo}
            userMemberDataState={userMemberDataState}
            onReplyMessage={onReplyMessage}
            setSelectedMsgs={setSelectedMsgs}
          />
        )
        // customHeader
      }
      isScrollable={false}
      containerStyle={{ paddingHorizontal: 0, paddingTop: 0, paddingBottom: 0 }}
    >
      <ChatImageBackground
        color={chatWallpaper?.color}
        url={chatWallpaper?.url}
        defaultImage={DEFAULT_IMAGE}
        opacity={chatWallpaper?.opacity ?? 1}
      >
        {pinnedMessages.length > 0 ? (
          <PinnedMessages
            isVisible={isPinMessageVisible}
            messages={pinnedMessages}
            onClose={() => setIsPinMessageVisible(false)}
            onClickMessage={(localId?: any) => {
              setSelectedPinLocalId(localId);
              setUnpinModal(true);
            }}
          />
        ) : null}

        {/* Fresh conversation UI */}
        {messages.length == 0 ? <FreshConversationUI /> : null}

        {/* Messages flatlist */}
        <SwipeListView
          data={messages}
          keyExtractor={(item) => item?.localId?.toString()}
          useFlatList={true}
          listViewRef={handleSwipeListViewRef}
          renderItem={({ item, index }) => {
            return (
              <ChatListItem
                data={item}
                reactions={reactionsMap[item.globalId!] || {}}
                onCardPress={() => onCardPress(item)}
                selectedMsgs={selectedMsgs}
                userData={userData}
                setSelectedMsgs={toggleSelection}
                scrollToItem={scrollToItem}
                isMsgHightlighted={
                  item.localId === selectedPinLocalId && selectedPinLocalId !== '' && !unPinModal
                }
                unHightlightFunc={() => setIsMsgHighlighted(false)}
                type={userData.type}
                onEditMessage={onEditMessage}
              />
            );
          }}
          renderHiddenItem={({ item }) => {
            return <View style={{ backgroundColor: '#ff0000' }} />;
          }}
          ListHeaderComponent={<Animated.View style={stableKeyboardHeight} />}
          inverted
          rightActionValue={70}
          disableLeftSwipe
          leftOpenValue={0}
          friction={8}
          tension={50}
          keyboardShouldPersistTaps={'handled'}
          onSwipeValueChange={(props) => {
            handleSwipeValueChange(props);
          }}
          showsVerticalScrollIndicator={false}
        />

        {userData.type === ConversationType.P2P && userMemberDataState?.isBlocked ? (
          <BlockedUserCard userData={userData} setUserMemberDataState={setUserMemberDataState} />
        ) : (
          <KeyboardStickyView>
            <View>
              <ChatComposer
                userData={userData}
                editedMessage={editedMessage}
                replyMsgData={replyMsgData}
                setEditedMessage={setEditedMessage}
                setReplyMsgData={setReplyMsgData}
                conversationInfo={conversationInfo}
                groupMembers={groupMembers}
              />
            </View>
          </KeyboardStickyView>
        )}
      </ChatImageBackground>

      {/* Pin modal */}
      <PinModal
        isVisible={isPinMessage}
        onCloseModal={() => setIsPinMessage(false)}
        onConfirmPin={onPinMessage}
      />

      {/* Unpin modal */}
      <UnPinModal
        isVisible={unPinModal}
        onUnPin={onUinPin}
        onGoToMessage={() => {
          setUnpinModal(false);
          scrollToItem(selectedPinLocalId);
        }}
        onClose={() => {
          setSelectedPinLocalId('');
          setUnpinModal(false);
        }}
        canUnpin={!(userData?.type === 'channel' && role === 'member')}
      />

      {/* Delete bottom sheet */}
      <DeleteMessageModal
        isVisible={deleteBtmSheet}
        onCloseModal={() => setDeleteBtmSheet(false)}
        onPressDeleteForMe={async () => {
          try {
            setDeleteModalLoading(true);
            const messageIds = selectedMsgs.map((msg) => msg.localId);
            const conversationId = messages.length ? messages[0].receiverId : '';
            ChatService.deleteMessages(messageIds, conversationId, false);
            setSelectedMsgs([]);
          } finally {
            setDeleteModalLoading(false);
            setDeleteBtmSheet(false);
          }
        }}
        onPressDeleteForBoth={async () => {
          try {
            setDeleteModalLoading(true);
            const messageIds = selectedMsgs.map((msg) => msg.localId);
            const conversationId = messages.length ? messages[0].receiverId : '';
            ChatService.deleteMessages(messageIds, conversationId, true);
            setSelectedMsgs([]);
          } finally {
            setDeleteModalLoading(false);
            setDeleteBtmSheet(false);
          }
        }}
        selectedMsgs={selectedMsgs}
        user={me}
        type={userData.type}
        role={role}
        loading={deleteModalLoading}
      />

      {/* schedule timer sheet */}
      <ScheduleMessageModal
        isVisible={scheduleTimerSheet}
        onClose={() => {
          setScheduleTimerSheet(false);
        }}
        setscheduleDate={(scheduledAt: any) => {
          setComposedMessage((prev) => ({ ...prev, scheduledAt }));
        }}
        scheduleDate={scheduleDate}
        onClickSchedule={onClickSchedule}
      />

      {/* schedule item sheet */}
      <EditScheduleMsgModal
        scheduleItemModal={scheduleItemModal}
        onCloseModal={() => {
          setScheduleItemModal(false);
          setSelectedScheduledMessage(null);
        }}
        onDeletePress={() => {
          ChatService.updateScheduleMessage(
            selectedScheduledMessage?.globalId as string,
            ScheduleUpdateType.DELETE,
            null,
            [selectedScheduledMessage?.localId as string],
          );
          setSelectedScheduledMessage(null);
          setScheduleItemModal(false);
        }}
        onEditPress={() => {
          setScheduleItemModal(false);
          onEditMessage(selectedScheduledMessage?.text as string, selectedScheduledMessage);
          setSelectedScheduledMessage(null);
        }}
        onReschedulePress={() => {
          setScheduleItemModal(false);
          setTimeout(() => {
            setScheduleTimerSheet(true);
          }, 250);
        }}
        onSendNowPress={() => {
          if (selectedScheduledMessage) {
            ChatService.sendScheduledMessageNow(selectedScheduledMessage?.globalId!);
          }
          ChatService.updateScheduleMessage(
            selectedScheduledMessage?.globalId as string,
            ScheduleUpdateType.SEND_NOW,
          );
          setScheduleItemModal(false);
        }}
      />

      {userData.type === ConversationType.CHANNEL && (
        <NewLiveStreamModal
          visible={
            streamingModalState.showModal &&
            streamingModalState.activeModal === ModalType.SelectLiveMode
          }
          onSchedule={() => {
            resetStreamingState();
            navigateTo(SCREENS.ScheduleLiveStreamScreen, {
              conversation: userData.conversation,
            });
          }}
          onGoLive={() => {
            openModal(ModalType.StreamSettings);
          }}
          onClose={() => {
            resetStreamingState();
          }}
        />
      )}
      {userData.type === ConversationType.CHANNEL && (
        <GoLiveInfoModal
          liveStreamData={streamDetails}
          isVisible={
            streamingModalState.showModal &&
            streamingModalState.activeModal === ModalType.StreamSettings
          }
          onCloseModal={() => {
            resetStreamingState();
          }}
          setStreamDetails={setStreamDetails}
          resetStreamingState={resetStreamingState}
        />
      )}
      {/* {conversationInfo.type === ConversationType.CHANNEL &&
        conversationInfo.membershipStatus === MembershipStatus.MEMBER &&
        currentConversation?.isLiveStreaming && (
          <StreamLiveCard
            onJoin={() => {
              joinStream(currentConversation);
            }}
          />
        )} */}
      {groupCallItemState.isVisible && (
        <JoinGroupCallModal
          isVisible={groupCallItemState.isVisible}
          onCloseModal={() => setGroupCallItemState({ isVisible: false, selectedMessage: null })}
          selectedMessage={groupCallItemState.selectedMessage}
        />
      )}
    </CommonView>
  );
};

export default ChatSpecificScreen;

type Props = {
  userData: IChatScreenProps;
  setUserMemberDataState: React.Dispatch<React.SetStateAction<any | null>>;
};

function BlockedUserCard({ userData, setUserMemberDataState }: Props) {
  const [showUnblockAlert, setShowUnblockAlert] = useState(false);
  const [loading, setLoading] = useState(false);
  const handleConfirmUnblockUser = async () => {
    try {
      setLoading(true);
      const res = await unBlockUser(userData.id);

      if (res?.body?.status === true) {
        ChatService.onUnBlocked(userData.id);
        ChatService.onUnBlockedUser(userData.id);
        setShowUnblockAlert(false);
        showToast(`${userData?.displayName} has been unblocked successfully`);
        const userMemberData = ChatService.getUserData(userData.id);
        setUserMemberDataState(userMemberData);
      } else {
        errorToast(res?.body?.message || 'Failed to unblock user. Please try again.');
      }
    } catch (error) {
      errorToast('Failed to unblock user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View
      style={[
        {
          alignItems: 'center',
          backgroundColor: colors.white,

          width: SCREEN_WIDTH,
          paddingHorizontal: 21,
          paddingVertical: 13,
        },
        { zIndex: 12, justifyContent: 'center' },
      ]}
    >
      <TouchableOpacity
        style={{
          paddingHorizontal: 20,
          paddingVertical: 10,
        }}
        onPress={() => setShowUnblockAlert(true)}
      >
        <Text style={{ color: colors.mainPurple, fontWeight: '600', fontSize: 16 }}>
          {`Unblock`}
        </Text>
      </TouchableOpacity>

      <CustomAlert
        visible={showUnblockAlert}
        onCancel={() => setShowUnblockAlert(false)}
        onConfirm={handleConfirmUnblockUser}
        title="Unblock User"
        message={`Are you sure you want to unblock ${userData?.displayName}?`}
        confirmText="Unblock"
        cancelText="Cancel"
        loading={loading}
      />
    </View>
  );
}
