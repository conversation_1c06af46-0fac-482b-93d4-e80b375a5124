import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet, Text, TouchableOpacity } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import AccountNameTab from './ProfileColorTabs/AccountNameTab';
import AccountProfileTab from './ProfileColorTabs/AccountProfileTab';

const NameProfileColoursScreen = () => {
  const [activeTab, setActiveTab] = useState<'name' | 'profile'>('name');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'name':
        return <AccountNameTab />;
      case 'profile':
        return <AccountProfileTab />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <HeaderBackWithTitle title="Name & Profile Colours" />
      <View style={styles.whiteContainer}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'name' && styles.activeTabButton]}
            onPress={() => setActiveTab('name')}
          >
            <Text
              style={[styles.tabButtonText, activeTab === 'name' && styles.activeTabButtonText]}
            >
              Name
            </Text>
            {activeTab === 'name' && <View style={styles.activeTabIndicator} />}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tabButton, activeTab === 'profile' && styles.activeTabButton]}
            onPress={() => setActiveTab('profile')}
          >
            <Text
              style={[styles.tabButtonText, activeTab === 'profile' && styles.activeTabButtonText]}
            >
              Profile
            </Text>
            {activeTab === 'profile' && <View style={styles.activeTabIndicator} />}
          </TouchableOpacity>
        </View>

        <View style={styles.tabContentContainer}>{renderTabContent()}</View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: hp(3),
    marginTop: hp(1.5),
    justifyContent: 'center',
  },
  tabButton: {
    paddingHorizontal: 20,
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
    marginHorizontal: 10,
  },
  activeTabButton: {
    borderBottomWidth: 0,
  },
  tabButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.gray_80,
  },
  activeTabButtonText: {
    color: colors.mainPurple,
    fontWeight: '600',
  },
  activeTabIndicator: {
    position: 'absolute',
    bottom: 0,
    width: 40,
    height: 3,
    backgroundColor: colors.mainPurple,
    borderRadius: 1.5,
  },
  tabContentContainer: {
    flex: 1,
  },
});

export default NameProfileColoursScreen;
