import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
  Keyboard,
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { mockComments } from './data';

import { useKeyboard } from '../../../../utils/useKeyboard';

import { colors } from '../../../../theme/colors';
import { FlatList } from 'react-native-gesture-handler';
import { IPaginatedChatController } from '../../../../hooks/calls/useLiveStreamchats';

// Types
export type ChatRank = 'viewer' | 'streamer';
export interface Comment {
  liveStreamId: string;
  senderId: string;
  message: string;
  displayName: string;
  displayImageUrl?: string;
  chatId: string;
  chatRank?: ChatRank;
  createdAt: string;
}

interface CommentItemProps {
  comment: Comment;
}

interface CommentsListProps {
  comments: Comment[];
  isLoading?: boolean;
  liveStreamChatController: IPaginatedChatController;
}

interface CommentInputProps {
  onSend?: (comment: string) => void;
}

// Comment Input Component
export const CommentInput: React.FC<CommentInputProps> = ({ onSend }) => {
  const [comment, setComment] = useState('');

  const submitComment = () => {
    if (!comment.trim()) return;

    onSend?.(comment.trim());
    setComment('');
    Keyboard.dismiss(); // optional: hide keyboard after send
  };

  return (
    <View
      style={{
        ...CommentInputStyles.commentInputContainer,
      }}
    >
      <TextInput
        style={CommentInputStyles.commentInput}
        placeholder="Type your comment.."
        placeholderTextColor="#999"
        value={comment}
        onChangeText={setComment}
        onSubmitEditing={submitComment}
        returnKeyType="send"
      />
    </View>
  );
};

// Comment Item Component
const CommentItem: React.FC<CommentItemProps> = ({ comment }) => {
  return (
    <View style={CommentsListStyles.commentItem}>
      <Image source={{ uri: comment.displayImageUrl }} style={CommentsListStyles.commentAvatar} />
      <View style={CommentsListStyles.commentContent}>
        <Text style={CommentsListStyles.commentUserName}>{comment.displayName}</Text>
        <Text style={CommentsListStyles.commentMessage}>{comment.message}</Text>
      </View>
    </View>
  );
};

// Comments List Component
export const CommentsList: React.FC<CommentsListProps> = ({
  comments,
  isLoading,
  liveStreamChatController,
}) => {
  const listRef = useRef<FlatList>(null);
  const renderItem = ({ item }: { item: any }) => <CommentItem comment={item} />;

  useEffect(() => {
    if (!listRef.current) return;
    listRef.current.scrollToEnd({ animated: true });
  }, [comments.length]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = event.nativeEvent.contentOffset.y;

    if (offsetY <= 0) {
      console.log('🟢 Scrolled to top - Load older messages');
      // Call your pagination loader here
      liveStreamChatController.loadOlderMessages();
    }
  };

  const renderListHeader = () => {
    if (!liveStreamChatController.loadingOlder) return null;
    return (
      <View style={{ paddingVertical: 8 }}>
        <ActivityIndicator size="small" color={colors.white} />
      </View>
    );
  };

  // On new comment
  return (
    <View style={CommentsListStyles.commentsContainer}>
      {liveStreamChatController.loadingIntialChats ? (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={colors.white} />
        </View>
      ) : (
        <FlatList
          ref={listRef}
          ListHeaderComponent={renderListHeader}
          data={liveStreamChatController.messages}
          keyExtractor={(item, index) => item.chatId + index.toString()}
          renderItem={renderItem}
          contentContainerStyle={{ ...CommentsListStyles.flatListContent }}
          showsVerticalScrollIndicator={false}
          onScrollToTop={() => {
            console.log('scrolled to top');
          }}
          onScroll={handleScroll}
          onEndReached={() => {
            console.log('reached end');
          }}
          onEndReachedThreshold={0.5}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
        />
      )}
    </View>
  );

  // return (
  //   <View style={CommentsListStyles.commentsContainer}>
  //     <ScrollView
  //       ref={scrollViewRef}
  //       showsVerticalScrollIndicator={false}
  //       contentContainerStyle={CommentsListStyles.scrollContent}
  //     >
  //       {isLoading ? (
  //         <View style={{}}>
  //           <ActivityIndicator size="large" color={colors.white} />
  //         </View>
  //       ) : (
  //         <View>
  //           {mockComments.map((comment) => (
  //             <CommentItem key={comment.chatId} comment={comment} />
  //           ))}
  //         </View>
  //       )}
  //     </ScrollView>
  //   </View>
  // );
};

export const CommentsListStyles = StyleSheet.create({
  commentsContainer: {
    flex: 1,

    flexDirection: 'row',
  },
  scrollContent: {
    paddingVertical: 8,

    // backgroundColor: 'blue',
  },
  flatListContent: {
    paddingVertical: 8,
  },
  commentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  commentContent: {
    flex: 1,
  },
  commentUserName: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  commentMessage: {
    color: colors.white,
    fontSize: 14,
    lineHeight: 18,
  },
});

export const CommentInputStyles = StyleSheet.create({
  commentInputContainer: {
    paddingBottom: 8,
    width: '100%',
  },
  commentInput: {
    backgroundColor: colors.overlayWhite_10,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: colors.white,
    fontSize: 14,
  },
});
