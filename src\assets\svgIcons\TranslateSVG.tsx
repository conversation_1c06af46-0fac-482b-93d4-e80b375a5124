import React from 'react';
import { Svg, Path } from 'react-native-svg';

interface TranslateSVGProps {
  size?: number;
  color?: string;
}

const TranslateSVG: React.FC<TranslateSVGProps> = ({ size = 18, color = '#232323' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 18 18" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.94977 1.30662e-07H9.05022C10.9738 -8.64983e-06 12.4846 -1.74016e-05 13.6642 0.158567C14.8729 0.321085 15.832 0.660925 16.5856 1.41446C17.3391 2.168 17.6789 3.12706 17.8414 4.33582C18 5.51541 18 7.02625 18 8.94977V9.05022C18 10.9738 18 12.4846 17.8414 13.6642C17.6789 14.8729 17.3391 15.832 16.5856 16.5856C15.832 17.3391 14.8729 17.6789 13.6642 17.8414C12.4846 18 10.9738 18 9.05022 18H8.94977C7.02625 18 5.51541 18 4.33582 17.8414C3.12706 17.6789 2.168 17.3391 1.41446 16.5856C0.660925 15.832 0.321085 14.8729 0.158567 13.6642C-1.74016e-05 12.4846 -8.64983e-06 10.9738 1.30662e-07 9.05022V8.94977C-8.64983e-06 7.02625 -1.74016e-05 5.51541 0.158567 4.33582C0.321085 3.12706 0.660925 2.168 1.41446 1.41446C2.168 0.660925 3.12706 0.321085 4.33582 0.158567C5.51541 -1.74016e-05 7.02625 -8.64983e-06 8.94977 1.30662e-07ZM4.51133 1.4639C3.4486 1.60678 2.81381 1.87773 2.34577 2.34577C1.87773 2.81381 1.60678 3.4486 1.4639 4.51133C1.31847 5.59299 1.31707 7.01529 1.31707 9C1.31707 10.9847 1.31847 12.407 1.4639 13.4887C1.60678 14.5514 1.87773 15.1862 2.34577 15.6542C2.81381 16.1223 3.4486 16.3933 4.51133 16.5361C5.59299 16.6815 7.01529 16.6829 9 16.6829C10.9847 16.6829 12.407 16.6815 13.4887 16.5361C14.5514 16.3933 15.1862 16.1223 15.6542 15.6542C16.1223 15.1862 16.3933 14.5514 16.5361 13.4887C16.6815 12.407 16.6829 10.9847 16.6829 9C16.6829 7.01529 16.6815 5.59299 16.5361 4.51133C16.3933 3.4486 16.1223 2.81381 15.6542 2.34577C15.1862 1.87773 14.5514 1.60678 13.4887 1.4639C12.407 1.31847 10.9847 1.31707 9 1.31707C7.01529 1.31707 5.59299 1.31847 4.51133 1.4639Z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9269 14.2679C14.9269 14.6316 14.6321 14.9264 14.2684 14.9264H7.24399C6.88029 14.9264 6.58545 14.6316 6.58545 14.2679C6.58545 13.9042 6.88029 13.6094 7.24399 13.6094H14.2684C14.6321 13.6094 14.9269 13.9042 14.9269 14.2679Z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.58545 10.7562C6.58545 10.3925 6.88029 10.0977 7.24399 10.0977H9.00008C9.36377 10.0977 9.65862 10.3925 9.65862 10.7562C9.65862 11.1199 9.36377 11.4147 9.00008 11.4147H7.24399C6.88029 11.4147 6.58545 11.1199 6.58545 10.7562ZM10.9757 10.7562C10.9757 10.3925 11.2705 10.0977 11.6342 10.0977H14.2684C14.6321 10.0977 14.9269 10.3925 14.9269 10.7562C14.9269 11.1199 14.6321 11.4147 14.2684 11.4147H11.6342C11.2705 11.4147 10.9757 11.1199 10.9757 10.7562Z"
        fill={color}
      />
    </Svg>
  );
};

export default TranslateSVG; 