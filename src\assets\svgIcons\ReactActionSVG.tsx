import * as React from "react"
import Svg, { Path } from "react-native-svg"

function RecentActionSVG({ size = 16, color = "#232323", ...props }) {
  const width = size
  const height = (size * 16) / 15 

  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 15 16"
      fill="none"
      {...props}
    >
      <Path
        d="M9.487 14.176H2.396A2.399 2.399 0 010 11.78V2.654A2.399 2.399 0 012.396.258h8.456a2.399 2.399 0 012.395 2.396v7.5a.423.423 0 11-.845 0v-7.5a1.552 1.552 0 00-1.55-1.55H2.396a1.552 1.552 0 00-1.55 1.55v9.127a1.552 1.552 0 001.55 1.55h7.091a.423.423 0 010 .845z"
        fill={color}
      />
      <Path
        d="M10.138 3.92h-7.03a.423.423 0 110-.846h7.03a.423.423 0 010 .846zM10.138 6.4h-7.03a.423.423 0 010-.845h7.03a.423.423 0 010 .845zM10.138 8.88h-7.03a.423.423 0 010-.845h7.03a.423.423 0 010 .846zM8.168 11.357h-5.06a.423.423 0 110-.845h5.06a.423.423 0 010 .845zM11.914 15.742a3.086 3.086 0 110-6.172 3.086 3.086 0 010 6.172zm0-5.326a2.24 2.24 0 100 4.481 2.24 2.24 0 000-4.481z"
        fill={color}
      />
      <Path
        d="M12.992 13.079h-1.077a.423.423 0 01-.423-.423V11.42a.423.423 0 11.846 0v.814h.654a.422.422 0 110 .846z"
        fill={color}
      />
    </Svg>
  )
}

export default RecentActionSVG
