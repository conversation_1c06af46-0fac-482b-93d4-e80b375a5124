import { useEffect, useState, useCallback } from 'react';
import { IUserPreferences } from '../device-storage/realm/schemas/UserPrefSchema';
import { UserPreferencesService } from '../service/UserService';
import { IUser } from '../types/index.types';

export type UserPreferencesState = {
  userPreferences: IUserPreferences | null;
  updatePreferences: <K extends keyof IUserPreferences>(
    section: K,
    updates: Partial<IUserPreferences[K]>,
  ) => void;
  setUserPreferences: React.Dispatch<React.SetStateAction<IUserPreferences | null>>;
};

export function useUserPreferences(user: IUser | null): UserPreferencesState {
  const [userPreferences, setUserPreferences] = useState<IUserPreferences | null>(null);
  const loadPreferences = useCallback(async () => {
    if (!user) return;
    const prefs = await UserPreferencesService.createUserPref();
    setUserPreferences(prefs);
  }, [user]);

  const updatePreferences = useCallback(
    async <K extends keyof IUserPreferences>(section: K, updates: Partial<IUserPreferences[K]>) => {
      if (!user) return;
      setUserPreferences((prev) => {
        if (!prev) return prev;
        const currentSection = prev[section];

        const updatedSection =
          currentSection && typeof currentSection === 'object' && !Array.isArray(currentSection)
            ? { ...currentSection, ...updates }
            : updates;

        const updated: IUserPreferences = {
          ...prev,
          [section]: updatedSection,
          updatedAt: Date.now(),
        };
        return updated;
      });
      await UserPreferencesService.updateSection(section, updates);
    },
    [user, loadPreferences],
  );

  useEffect(() => {
    if (user?._id) {
      loadPreferences();
    }
  }, [user?._id, loadPreferences]);

  return {
    userPreferences,
    updatePreferences,
    setUserPreferences,
  };
}
