import axios from 'axios';
import { clearAsync, getAsyncToken } from './asyncStorage';
import { errorToast } from './commonFunction';

import { API } from './apiConstant';

interface makeAPIRequestProps {
  method?: any;
  url?: any;
  data?: any;
  headers?: any;
  params?: any;
}

export const makeAPIRequest = ({ method, url, data, params, headers }: makeAPIRequestProps) =>
  new Promise((resolve, reject) => {
    const option = {
      method,
      baseURL: API.BASE_URL,
      url,
      data: data,
      headers: {
        Accept: 'application/json',
        ...headers,
        'Content-Type': 'application/json',
      },
      params: params,
    };
  });

export const setAuthorization = async (authToken: any) => {
  const token = await getAsyncToken();

  if (authToken == '') {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }
};

export const removeAuthorization = async () => {
  await clearAsync();
  delete axios.defaults.headers.common.Authorization;
};

export const uploadImage = async (data: any) => {
  const formData = new FormData();
  formData.append('file', {
    uri: data.uri,
    type: data.type || 'image/jpeg',
    name: data.fileName || 'photo.jpg',
  });

  try {
    const response = await fetch('https://server.chatbucket.chat/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });

    const json = await response.json();

    if (json?.data?.url) {
      return json.data.url;
    } else {
      const errorMessage = json?.message || 'Please try again';
      return null;
    }
  } catch (err) {
    console.log('Upload error:', err);
    return null;
  }
};

export const formDataApiCall = (data: any, url: any, onSuccess: any, onFailure: any) => {
  let formData = new FormData();
  if (data) {
    Object.keys(data).map((element) => {
      if (data[element] !== undefined) {
        formData.append(element, data[element]);
      }
    });
  }

  // console.log("\x1b[35m", data);
  return fetch(API.BASE_URL + url, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'multipart/form-data',
      Authorization: String(axios.defaults.headers.common['Authorization']),
    },
    body: formData,
  })
    .then((response) => {
      console.log('\x1b[35m', 'response--------', '\x1b[30m', response, data);
      return response.json().then((responseJson) => {
        if (responseJson.status == true) {
          onSuccess(responseJson);
        } else {
          if (onFailure) onFailure();
          if (responseJson?.message) {
            errorToast(responseJson?.message);
          } else errorToast('Please try again');
        }
      });
    })
    .catch((err) => {
      console.log('\x1b[35m', err);
      if (onFailure) onFailure();
      errorToast('Please try again');
    });
};

export const handleSuccessRes = (
  res: any,
  onSuccess: any,
  onFailure: any,
  dispatch: any,
  fun?: () => void,
) => {
  if (res?.status === 200 || res?.status === 201) {
    if (res?.data.status) {
      if (fun) fun();
      if (onSuccess) onSuccess(res?.data);
    } else {
      if (onFailure) onFailure(res?.data);
      errorToast(res?.data?.message);
    }
  }
};

export const handleErrorRes = (err: any, onFailure: any, dispatch: any, fun?: () => void) => {
  if (err?.response?.status == 401) {
    removeAuthorization();
    // navigationRef.reset({
    //   index: 0,
    //   routes: [{ name: SCREENS.SignupScreen }],
    // });
    errorToast('Please login again');
  } else {
    if (err?.response?.data?.errors) {
      errorToast(err?.response?.data?.message);
    } else if (err?.response?.data?.message) {
      errorToast(err?.response?.data?.message);
    } else if (err?.response?.data?.error) {
      errorToast(err?.response?.data?.error?.message);
    } else if (err?.message) {
      errorToast(err?.message);
    } else {
      errorToast('Something went wrong! Please try again');
    }
    if (fun) fun();
    if (onFailure) onFailure(err?.response);
  }
};
