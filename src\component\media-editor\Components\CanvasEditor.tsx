import React, { useState, useRef, useEffect, forwardRef, memo } from 'react';
import { View, StyleSheet, AppState, Platform } from 'react-native';
import {
  Canvas,
  Image as SkiaImage,
  useImage,
  ColorMatrix,
  useCanvasRef,
} from '@shopify/react-native-skia';
import { ScreenHeight, ScreenWidth } from '../utils/Constants/dimensionUtils';
import ElementRenderer from './CombineElement';
import VidTrimmer from './VTrim';
import FilteredVideoView, { FilteredVideoViewHandle } from './FilteredVideoView';
import TrackPlayer from 'react-native-track-player';
import RNFS from 'react-native-fs';
import { captureRef } from 'react-native-view-shot';
import PaintBrush, { PaintBrushRef } from './PaintBrush';

export interface CanvasEditorHandle {
  handleCapture: () => Promise<string | undefined>;
  getCurrentTime: () => number;
  seekTo: (time: any) => void;
  play: () => void;
  didReachEnd: () => boolean;
}

type MediaType = 'image' | 'video' | string;

interface CanvasEditorProps {
  selectedFilter: number[];
  images: {
    uri: string;
    stickers?: {
      uri: string;
      id: string;
      initialX?: number;
      initialY?: number;
      rotate?: number;
      scale?: number;
      zIndex?: number;
    }[];
    additionalImages?: {
      uri: string;
      id: string;
      initialX?: number;
      initialY?: number;
      rotate?: number;
      scale?: number;
      zIndex?: number;
    }[];
    texts?: {
      id: string | number;
      text: string;
      color: string;
      fontFamily?: string;
      assType?: number;
      initialX?: number;
      initialY?: number;
      rotate?: number;
      scale?: number;
    }[];
    matrix?: number[];
    appliedFilter: string;
    startTime: number;
    endTime: number;
    trimStartTime: number;
    trimEndTime: number;
    audio: string;
    audioFullUrl: string;
    tempUri?: string;
    isSoundOn?: boolean;
    drawingPaths?: any[];
  };
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number | string, type: string, value: boolean) => boolean;
  capture: (data: any) => void;
  type?: MediaType;
  backgroundImage: any;
  idkey: any;
  deleteItem: (id: number | string, type: string) => void;
  onTextPress: (id: string | number, text: string) => void;
  activeTextId: string | number | null;
  setResetMusicPlayer: (value: number) => number;
  isDrawing?: boolean;
  // Drawing-related props
  paintBrushRef?: React.RefObject<PaintBrushRef>;
  paintData?: any[];
  strokeWidth?: number;
  eraserSize?: number;
  paintClr?: string;
  activeStoryIdx?: number;
  onDrawStateChange?: (isDrawing: boolean) => void;
}

interface IPath {
  segments: string[];
  color?: string;
  isComplete?: boolean;
}

const ImageCava = memo(
  ({
    width,
    height,
    image,
    matrix,
  }: {
    width: number;
    height: number;
    image: any;
    matrix: number[];
  }) => {
    const img = useImage(image);
    return (
      <SkiaImage x={0} y={0} width={width} height={height} image={img} fit="contain">
        {matrix?.length > 0 && <ColorMatrix matrix={matrix} />}
      </SkiaImage>
    );
  },
  (prev, next) => prev.image === next.image && prev.matrix === next.matrix,
);

const CanvasEditor = forwardRef<CanvasEditorHandle, CanvasEditorProps>(
  (
    {
      itemDragging,
      isDeletable,
      images,
      capture,
      type,
      backgroundImage,
      idkey,
      deleteItem,
      onTextPress,
      activeTextId,
      setResetMusicPlayer,
      isDrawing,
      paintBrushRef,
      paintData,
      strokeWidth = 2,
      eraserSize = 10,
      paintClr = '#000',
      activeStoryIdx,
      onDrawStateChange,
    },
    ref,
  ) => {
    const appState = useRef(AppState.currentState);
    const canvasRef = useCanvasRef();
    const containerRef = useRef<View | null>(null);
    const [videoUri, setVideoUri] = useState(null);
    const [currentAppState, setCurrentAppState] = useState(false);
    const currentTimeRef = useRef(0);
    const previousTimeRef = useRef(0);
    const localRef = useRef(null);
    const videoRef = useRef<FilteredVideoViewHandle>(null);
    const [restartToggle, setRestartToggle] = useState(false);
    const videoDuration = images?.endTime;
    const didReachEndRef = useRef(false);

    // State for images and stickers to persist position updates
    const [localImages, setLocalImages] = useState(images?.additionalImages || []);
    const [localStickers, setLocalStickers] = useState(images?.stickers || []);
    const [localTexts, setLocalTexts] = useState(images?.texts || []);

    // Sync local state with props
    useEffect(() => {
      setLocalImages(images?.additionalImages || []);
      setLocalStickers(images?.stickers || []);
      setLocalTexts(images?.texts || []);
    }, [images?.additionalImages, images?.stickers, images?.texts]);

    React.useImperativeHandle(ref, () => ({
      handleCapture: async () => {
        try {
          // For images, capture the entire composed view (background + overlays)
          if (type === 'image' && containerRef.current) {
            // Allow UI to settle before capture
            await new Promise((res) => setTimeout(res, 30));
            const uri = await captureRef(containerRef.current, {
              format: 'png',
              quality: 1,
              result: 'tmpfile',
            });
            if (!uri) return undefined;
            // Normalize to file:// URI
            return uri.startsWith('file://') ? uri : `file://${uri}`;
          }

          // Fallback: capture only Skia canvas as PNG
          if (!canvasRef.current) return undefined;
          const skImage = canvasRef.current.makeImageSnapshot();
          if (!skImage) return undefined;
          // @ts-ignore
          const base64 = skImage.encodeToBase64?.() || '';
          if (!base64) return undefined;
          const fileName = `edited_${Date.now()}.png`;
          const filePath = `${RNFS.CachesDirectoryPath}/${fileName}`;
          await RNFS.writeFile(filePath, base64, 'base64');
          return `file://${filePath}`;
        } catch (e) {
          console.error('Canvas capture failed:', e);
          return undefined;
        }
      },
      getCurrentTime: () => currentTimeRef.current,
      seekTo: (time) => {
        if (Platform.OS === 'ios' && videoRef.current) {
          videoRef.current.seekTo(time);
        } else if (Platform.OS === 'android' && localRef.current) {
          localRef.current.seekTo(time);
        }
      },
      play: () => {
        if (Platform.OS === 'ios' && videoRef.current) {
          videoRef.current.play();
        } else if (Platform.OS === 'android' && localRef.current) {
          localRef.current.play();
        }
      },
      didReachEnd: () => didReachEndRef.current,
    }));

    useEffect(() => {
      setVideoUri(images?.uri);
    }, [images]);

    useEffect(() => {
      if (Platform.OS === 'ios' && videoRef.current) {
        videoRef.current.setIsSoundOn(images?.isSoundOn);
      }
    }, [images?.isSoundOn]);

    useEffect(() => {
      if (Platform.OS === 'ios' && videoRef.current) {
        videoRef.current.setFilter(images?.appliedFilter);
      }
    }, [images?.appliedFilter]);

    useEffect(() => {
      if (Platform.OS === 'ios' && videoRef.current) {
        videoRef.current.setIsSoundOn(true);
        videoRef.current.setAudioSource(images?.audio);
      }
    }, [videoUri, images?.audio]);

    useEffect(() => {
      if (Platform.OS === 'ios' && videoRef.current) {
        videoRef.current?.setPause(currentAppState);
      }
    }, [currentAppState]);

    useEffect(() => {
      const checkVideoEnd = () => {
        if (currentTimeRef.current >= videoDuration) {
          didReachEndRef.current = true;
          setResetMusicPlayer(true); // Trigger reset at video end
        } else {
          didReachEndRef.current = false;
          setResetMusicPlayer(false);
        }
      };
      const interval = setInterval(checkVideoEnd, 100);
      return () => clearInterval(interval);
    }, [videoDuration]);

    useEffect(() => {
      const subscription = AppState.addEventListener('change', (nextAppState) => {
        appState.current = nextAppState;
        if (nextAppState === 'background' || nextAppState === 'inactive') {
          setCurrentAppState(true);
        } else {
          setCurrentAppState(false);
        }
      });

      return () => {
        subscription.remove();
      };
    }, []);

    useEffect(() => {
      return () => {
        if (localRef.current) {
          localRef.current?.release?.();
        }
      };
    }, []);

    useEffect(() => {
      if (Platform.OS === 'ios' && localRef.current && videoUri !== null) {
        localRef.current?.setVideoPath(videoUri);
      } else if (Platform.OS === 'ios' && videoRef.current && videoUri !== null) {
        videoRef.current?.setSource(videoUri);
      }
    }, [videoUri]);

    useEffect(() => {
      let isMounted = true;
      const setupTrackPlayerListener = async () => {
        try {
          const unsubscribePlayback = TrackPlayer?.addEventListener('playback-state', (state) => {
            if (!isMounted) return;
            if (state?.state === 'playing') {
              handleRestartVideo();
            }
          });
          return () => {
            unsubscribePlayback.remove();
          };
        } catch (error) {
          console.error('Error setting up TrackPlayer listener:', error);
        }
      };
      setupTrackPlayerListener();
      return () => {
        isMounted = false;
      };
    }, []);

    const handleRestartVideo = () => {
      if (Platform.OS == 'android') {
        setRestartToggle((prev) => !prev);
        setTimeout(() => setRestartToggle(false), 0);
      } else if (Platform.OS === 'ios' && videoRef.current) {
        videoRef.current?.seekTo(0);
        videoRef.current?.play();
      }
    };

    return (
      <View style={[styles.canvasContainer]} ref={containerRef} collapsable={false}>
        <Canvas
          ref={canvasRef}
          key={backgroundImage || 'empty'}
          style={{
            width: ScreenWidth,
            height: ScreenHeight,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ImageCava
            width={ScreenWidth}
            height={ScreenHeight}
            image={backgroundImage}
            matrix={images.matrix || []}
          />
        </Canvas>
        {type === 'video' && (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 1,
            }}
          >
            <FilteredVideoView
              key={images?.tempUri || backgroundImage}
              ref={Platform.OS === 'ios' ? videoRef : localRef}
              style={{
                width: '100%',
                height: '100%',
                alignSelf: 'center',
              }}
              audioSource={images?.audio?.url}
              source={images?.tempUri || backgroundImage}
              filter={images.appliedFilter}
              isSoundOn={images?.isSoundOn}
              resizeMode="contain"
              onProgress={(e) => {
                const currentTime = e.nativeEvent.currentTime;
                currentTimeRef.current = currentTime;
                // console.log('currentTime ===', currentTime);

                // Trigger reset if currentTime decreases
                if (currentTime < previousTimeRef.current) {
                  // console.log('Resetting music player due to time decrease');
                  setResetMusicPlayer(true);
                }

                // Update previousTimeRef
                previousTimeRef.current = currentTime;
              }}
              // onProgress={(e) => {
              //   const currentTime = e.nativeEvent.currentTime;
              //   currentTimeRef.current = currentTime;
              //   console.log('currentTime ===', currentTime);
              //   if (currentTime < 0.1) {
              //     setResetMusicPlayer(true);
              //   }
              //   previousTimeRef.current = currentTime;
              // }}
              isPause={currentAppState}
              // restart={restartToggle}
            />
          </View>
        )}
        {type === 'video' && (
          <VidTrimmer
            videoUri={backgroundImage}
            setVideoUri={setVideoUri}
            vidStartTime={images?.startTime}
            vidEndtime={images?.endTime}
            trimVidStartTime={images?.trimStartTime}
            trimVidEndTime={images?.trimEndTime}
            backgroundImage={videoUri || backgroundImage}
            currentTime={currentTimeRef}
            audioTrimmedUrl={images?.audio}
            audioFullUrl={images?.audioFullUrl}
            isDrawing={isDrawing}
          />
        )}
        <ElementRenderer
          stickers={localStickers}
          images={localImages}
          texts={localTexts}
          itemDragging={itemDragging}
          isDeletable={isDeletable}
          deleteItem={deleteItem}
          onTextPress={onTextPress}
          activeTextId={activeTextId}
        />
        <PaintBrush
          ref={paintBrushRef}
          visible={isDrawing || false}
          paintData={paintData || images?.drawingPaths || []}
          strokeWidth={strokeWidth}
          eraserSize={eraserSize}
          paintClr={paintClr}
          activeStoryIdx={activeStoryIdx}
          onDrawStateChange={onDrawStateChange}
        />
      </View>
    );
  },
);

const styles = StyleSheet.create({
  canvasContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CanvasEditor;
