import * as React from "react"
import Svg, { Path , SvgProps } from "react-native-svg"

function HomeSvg(props : SvgProps) {
  return (
    <Svg
      width={20}
      height={23}
      viewBox="0 0 20 23"
      fill="none"
      {...props}
    >
      <Path
        d="M17.358 22.67H2.566A2.569 2.569 0 010 20.102V8.67c0-.755.34-1.472.943-1.963L8.34.594a2.512 2.512 0 013.245 0L18.98 6.67c.604.49.943 1.207.943 1.962v11.434a2.577 2.577 0 01-2.566 2.604zM9.962 1.537c-.226 0-.49.075-.68.226L1.888 7.877c-.227.189-.378.49-.378.793v11.434c0 .566.453 1.056 1.057 1.056h14.792c.566 0 1.057-.453 1.057-1.056V8.67c0-.302-.151-.604-.377-.793L10.64 1.764a1.13 1.13 0 00-.679-.226z"
        fill={props.color}
      />
      <Path
        d="M13.429 18.349H6.57c-.314 0-.571-.34-.571-.755s.257-.755.571-.755h6.858c.314 0 .571.34.571.755s-.257.755-.571.755z"
        fill={props.color}
      />
    </Svg>
  )
}

export default HomeSvg
