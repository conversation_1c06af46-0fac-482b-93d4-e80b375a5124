import React, { Component, useEffect, useRef, useState } from 'react';

import { RouteProp, useRoute } from '@react-navigation/native';

import { ViewerLiveStreamSession } from '../../../../api/Chatspace/chatspace.api';
import { zIndex } from '../../../../utils/Filters';
import RNVideo, { VideoRef, SelectedVideoTrackType } from 'react-native-video';

import useLivestreamSpace, {
  StreamStatus,
  ILivestreamSession,
  UserRole,
} from '../../../../hooks/channels/useLivestreamSpace';

import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TextStyle,
  ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import OverlayComponent from '../liveStreams/OverlayComponent';

import LiveStreamHeader from '../liveStreams/LiveStreamHeader';
import { mockComments } from './data';
import { DummyScreenStyles } from '../Screens/DummyScreen';
import { navigationRef } from '../../../../navigation/RootContainer';
import { useMe } from '../../../../hooks/util/useMe';

type ViewerStreamBackgroundProps = {
  liveStreamSpace: ILivestreamSession;
  llhlsUrl: string;
};

// Background Component
const ViewerStreamBackground: React.FC<ViewerStreamBackgroundProps> = ({
  liveStreamSpace,
  llhlsUrl,
}) => {
  const rnVideoRef = useRef<VideoRef>(null);

  function handleOnExitStream() {
    liveStreamSpace.leaveLiveStream();
    navigationRef.goBack();
  }

  function handleOnRetryStream() {
    liveStreamSpace.updateStreamDetails({ state: StreamStatus.PENDING });
  }

  useEffect(() => {
    if (liveStreamSpace.streamDetails.state === StreamStatus.ENDED) {
      if (rnVideoRef.current) {
        rnVideoRef.current.pause();

        console.log('paused video');
      }
    }
  }, [liveStreamSpace.streamDetails.state]);

  return (
    <View style={styles.container}>
      {liveStreamSpace.streamDetails.state === StreamStatus.PENDING && <StreamLoadingOverlay />}

      <View
        style={{
          zIndex: zIndex.level_3,
          width: '100%',
          height: '100%',
        }}
      >
        <RNVideo
          ref={rnVideoRef}
          style={{
            zIndex: zIndex.level_3,
            width: '100%',
            height: '100%',
          }}
          // muted
          resizeMode="cover"
          playInBackground
          playWhenInactive
          ignoreSilentSwitch="ignore"
          maxBitRate={10000}
          // paused={true}
          selectedVideoTrack={{
            type: SelectedVideoTrackType.RESOLUTION,
            value: 'highest',
          }}
          source={{ uri: llhlsUrl }}
          // source={{ uri: 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8' }}
          onError={(err) => {
            Alert.alert(`${err.error.code}`, `${err.error.errorString}`);
            liveStreamSpace.updateStreamDetails({ state: StreamStatus.FAILED });
          }}
          onLoad={(e) => {
            liveStreamSpace.updateStreamDetails({ state: StreamStatus.LIVE });
          }}
        />
      </View>

      {liveStreamSpace.streamDetails.state === StreamStatus.ENDED && (
        <StreamEndedOverlay onExit={handleOnExitStream} />
      )}

      {liveStreamSpace.streamDetails.state === StreamStatus.FAILED && (
        <StreamErrorOverlay onRetry={handleOnRetryStream} />
      )}
    </View>
  );
};

type ViewerStreamScreenProps = {
  screenprops: {
    streamInfo: ViewerLiveStreamSession;
  };
};

const ViewerStreamScreen = () => {
  const { params } = useRoute<RouteProp<ViewerStreamScreenProps, 'screenprops'>>();
  // const params = {
  //   streamInfo: {
  //     chatspaceId: '685d572d5130dbd43e393a99',
  //     _id: '685d572d5130dbd43e393a99',
  //     llhlsPlaybackUrl: 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8',
  //   },
  // };

  const liveStreamSpace = useLivestreamSpace({
    chatSpaceId: params.streamInfo.chatspaceId,
    liveStreamId: params.streamInfo._id,
    userRole: UserRole.VIEWER,
  });

  const { user } = useMe();

  const safeAreaInsets = useSafeAreaInsets();

  const { toggleOverlay, translateY, opacity, bottomHidden, bottomTranslateY, bottomOpacity } =
    useOverlayVisibility();

  const handleSendComment = (comment: string) => {
    if (!user) {
      return;
    }
    liveStreamSpace.sendMessageToStream({
      liveStreamId: params.streamInfo._id,
      senderId: user._id,
      message: comment,
      displayName: user.name,
      displayImageUrl: user.image,
      chatRank: 'viewer',
    });
  };

  return (
    <SafeAreaView
      style={{
        ...DummyScreenStyles.container,
        paddingTop: safeAreaInsets.top,
        paddingBottom: safeAreaInsets.bottom,
      }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <ViewerStreamBackground
        liveStreamSpace={liveStreamSpace}
        llhlsUrl={params.streamInfo.llhlsPlaybackUrl}
      />
      <Animated.View style={{ transform: [{ translateY }], opacity }}>
        <LiveStreamHeader
          title="Live Now"
          coins={500}
          viewers={liveStreamSpace.streamDetails.viewCount}
          onBackPress={() => {
            liveStreamSpace.leaveLiveStream();
            navigationRef.goBack();
          }}
          elapsedTime={liveStreamSpace.elapsedTime}
        />
      </Animated.View>
      <TouchableOpacity
        onPress={() => {
          toggleOverlay();
        }}
        style={{
          flex: 1,
        }}
      ></TouchableOpacity>

      {!bottomHidden && (
        <OverlayComponent
          bottomTranslateY={bottomTranslateY}
          bottomOpacity={bottomOpacity}
          mockComments={liveStreamSpace.streamChat}
          handleLike={liveStreamSpace.likeStream}
          handleShare={liveStreamSpace.shareStream}
          // handleStopLive={liveStreamSpace.stopStream}
          liveStreamSpace={liveStreamSpace}
          handleSendComment={handleSendComment}
          handleStopStream={() => {}}
        />
      )}
    </SafeAreaView>
  );
};

// define your styles
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    backgroundColor: 'blue',
  },
  messageBox: {
    backgroundColor: '#1e1e1e',
    paddingVertical: 20,
    paddingHorizontal: 30,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 5,
  },
  messageText: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
});

//make this component available to the app
export default ViewerStreamScreen;

// Shared overlay container style
const baseOverlay: ViewStyle = {
  ...StyleSheet.absoluteFillObject,
  zIndex: zIndex.level_4,
  justifyContent: 'center',
  alignItems: 'center',
  padding: 24,
  width: '100%',
};

// ----- 1. StreamErrorOverlay -----

type StreamErrorOverlayProps = {
  onRetry?: () => void;
};

export function StreamErrorOverlay({ onRetry }: StreamErrorOverlayProps) {
  return (
    <View style={[baseOverlay, { backgroundColor: '#ff3b30' }]}>
      <Text style={overlayStyles.titleWhite}>Stream has Failed </Text>
      <TouchableOpacity style={overlayStyles.buttonWhiteBg} onPress={onRetry}>
        <Text style={overlayStyles.buttonRedText}>Retry Stream</Text>
      </TouchableOpacity>
    </View>
  );
}

// ----- 2. StreamEndedOverlay -----

type StreamEndedOverlayProps = {
  onExit?: () => void;
};

export function StreamEndedOverlay({ onExit }: StreamEndedOverlayProps) {
  return (
    <View style={[baseOverlay, { backgroundColor: '#1e1e1e' }]}>
      <Text style={overlayStyles.titleWhite}>The Stream Has Ended</Text>
      <TouchableOpacity style={overlayStyles.buttonWhiteBg} onPress={onExit}>
        <Text style={overlayStyles.buttonDarkText}>Exit</Text>
      </TouchableOpacity>
    </View>
  );
}

// ----- 3. StreamLoadingOverlay -----

type StreamLoadingOverlayProps = {
  size?: number | 'small' | 'large';
  color?: string;
};

export function StreamLoadingOverlay({
  size = 'large',
  color = '#ffffff',
}: StreamLoadingOverlayProps) {
  return (
    <View style={[baseOverlay, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
}

// ----- Styles -----

const overlayStyles = StyleSheet.create({
  titleWhite: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  } as TextStyle,
  buttonWhiteBg: {
    backgroundColor: '#ffffff',
    paddingVertical: 10,
    paddingHorizontal: 24,
    borderRadius: 8,
  } as ViewStyle,
  buttonRedText: {
    color: '#ff3b30',
    fontSize: 16,
    fontWeight: '600',
  } as TextStyle,
  buttonDarkText: {
    color: '#1e1e1e',
    fontSize: 16,
    fontWeight: '600',
  } as TextStyle,
});

export function useOverlayVisibility({
  headerOffset = -100,
  bottomOffset = 200,
  duration = 300,
} = {}) {
  const [hide, setHide] = useState(false);

  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const [bottomHidden, setBottomHidden] = useState(false);
  const bottomTranslateY = useRef(new Animated.Value(0)).current;
  const bottomOpacity = useRef(new Animated.Value(1)).current;

  const hideHeader = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const showHeader = () => {
    translateY.setValue(-100);
    opacity.setValue(0);

    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideBottomView = () => {
    Animated.parallel([
      Animated.timing(bottomTranslateY, {
        toValue: 200, // push it down
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => setBottomHidden(true));
  };

  const showBottomView = () => {
    setBottomHidden(false);
    bottomTranslateY.setValue(200);
    bottomOpacity.setValue(0);
    Animated.parallel([
      Animated.timing(bottomTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  function toggleOverlay() {
    if (hide) {
      showHeader();
      showBottomView();
    } else {
      hideHeader();
      hideBottomView();
    }
    setHide((p) => !p);
  }

  return {
    toggleOverlay,
    bottomHidden,
    bottomTranslateY,
    bottomOpacity,
    translateY,
    opacity,
  };
}
