// ActivityContext.tsx
import React, { createContext, useContext, useState, useRef } from 'react';
import { PanResponder, View } from 'react-native';

type ActivityContextType = {
  lastActivity: Date | null;
  updateActivity: () => void;
};

const ActivityContext = createContext<ActivityContextType>({
  lastActivity: null,
  updateActivity: () => {},
});

export const ActivityProvider = ({ children }: { children: React.ReactNode }) => {
  const [lastActivity, setLastActivity] = useState<Date | null>(null);

  // Update activity timestamp
  const updateActivity = () => setLastActivity(new Date());

  // PanResponder to detect all touch interactions
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => {
        updateActivity();
        return true; // Allow touch events to propagate
      },
      onMoveShouldSetPanResponder: () => {
        updateActivity();
        return true;
      },
      onPanResponderGrant: updateActivity,
      onPanResponderMove: updateActivity,
      onPanResponderRelease: updateActivity,
    }),
  ).current;

  return (
    <ActivityContext.Provider value={{ lastActivity, updateActivity }}>
      <View style={{ flex: 1 }} {...panResponder.panHandlers}>
        {children}
      </View>
    </ActivityContext.Provider>
  );
};

export const useActivity = () => useContext(ActivityContext);
