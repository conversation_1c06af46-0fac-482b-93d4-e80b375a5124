import { useCallback, useEffect, useMemo, useState } from 'react';
import { ConversationType } from '../schemas/MessageSchema';
import { ChatService } from '../../../service/ChatService';
import { ChatPermissions, MembershipStatus } from '../../../types/chats.types';
import {
  getDefaultChatPermissions,
  isChatSpace as _isChatSpace,
  getChatSpaceType,
} from '../../../lib/chatLib';
import { useObject } from '../realm';
import { ChatSpaceSchema } from '../schemas/ChatSpaceSchema';
import { UserSchema } from '../schemas/UserSchema';
import { IConversationSettings } from '../schemas/ConversationSettingsSchema';
import Logger from '../../../lib/Logger';

// !!TODO:
// 1. Handle case where there is no user or chatspace in local db

// Base conversation info
type BaseConversationInfo = {
  displayPic?: string;
  displayName: string;
  bio: string;
  id: string;
  isLoading: boolean;
  error?: string;
  lastUpdated?: Date;
  isFromCache: boolean;
  conversationSettings: IConversationSettings | null;
};

// P2P specific fields
type P2PConversationInfo = BaseConversationInfo & {
  type: ConversationType.P2P;
  isBlocked: boolean;
  isDeviceContact: boolean;
};

type ChatSpaceConversationInfo = BaseConversationInfo & {
  type: ConversationType.GROUP | ConversationType.CHANNEL;
  membershipStatus: MembershipStatus;
  isPrivate: boolean;
  memberCount: number;
  permissions: Object;
  inviteCode: string;
};

export type ConversationInfo = P2PConversationInfo | ChatSpaceConversationInfo;
// Type guards
export const isP2PConversation = (info: ConversationInfo): info is P2PConversationInfo => {
  return info.type === ConversationType.P2P;
};

export const isChatSpaceConversation = (
  info: ConversationInfo,
): info is ChatSpaceConversationInfo => {
  return info.type === ConversationType.GROUP || info.type === ConversationType.CHANNEL;
};

const useConversationInfo = (
  conversationId: string,
  initialConversationInfo?: ConversationInfo,
): { conversationInfo: ConversationInfo | null } => {
  const isChatSpace = _isChatSpace(conversationId);
  const chatSpace = useObject(ChatSpaceSchema, conversationId);
  const user = useObject(UserSchema, conversationId);

  // states
  const [permissions, setPermissions] = useState<ChatPermissions | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check membership status (only for chat spaces)
  const checkMembershipStatus = useCallback(async (): Promise<void> => {
    if (!isChatSpace) {
      setIsLoading(false);
      return;
    }
  }, [conversationId]);

  const refreshMembership = useCallback(async () => {
    setIsLoading(true);
    await checkMembershipStatus();
  }, [checkMembershipStatus]);

  // Background membership check (non-blocking)
  useEffect(() => {
    // if (isChatSpace) {
    //   // Check if we need fresh membership data
    //   // const shouldCheck = !routeParams?.membershipStatus;
    //   // if (shouldCheck) {
    //   //   // Run in background without blocking UI
    //   //   requestAnimationFrame(() => {
    //   //     checkMembershipStatus();
    //   //   });
    //   // } else {
    //   //   // Use cached data from params
    //   //   setPermissions(getDefaultChatPermissions(routeParams.membershipStatus));
    //   //   setIsLoading(false);
    //   // }
    // } else {
    //   setIsLoading(false);
    // }
  }, [conversationId, initialConversationInfo, checkMembershipStatus]);

  const conversationInfo = useMemo((): ConversationInfo | null => {
    // Logger.logBg('calculating conversation info', 'red', 'white');

    // Start with route params for instant display
    const isFromCache = Boolean(initialConversationInfo && (!user || !chatSpace));

    if (isChatSpace) {
      // ChatSpace conversation
      if (!chatSpace && !initialConversationInfo) return null;

      const membershipStatus =
        chatSpace?.membershipStatus ||
        initialConversationInfo?.membershipStatus ||
        MembershipStatus.LOADING;

      const chatSpaceInfo: ChatSpaceConversationInfo = {
        id: conversationId,
        displayPic: chatSpace?.displayPic || initialConversationInfo?.displayPic,
        displayName: chatSpace?.name || initialConversationInfo?.displayName || 'Unknown',
        bio: chatSpace?.description || initialConversationInfo?.bio || '',
        type: chatSpace?.type || initialConversationInfo?.type || ConversationType.GROUP,
        isLoading,
        error: error || undefined,
        isFromCache: isFromCache,
        membershipStatus,
        isPrivate: chatSpace?.isPrivate || false,
        memberCount: chatSpace?.memberCount || initialConversationInfo.memberCount,
        inviteCode: chatSpace?.inviteCode || initialConversationInfo?.inviteCode!,
        permissions: getDefaultChatPermissions(membershipStatus, getChatSpaceType(conversationId)),
        conversationSettings: chatSpace ? chatSpace.conversationSettings : null,
      };

      return chatSpaceInfo;
    } else {
      // P2P conversation
      if (!user && !initialConversationInfo) return null;
      const p2pInfo: P2PConversationInfo = {
        displayPic: user?.profilePic || initialConversationInfo?.displayPic,
        displayName: user?.name || initialConversationInfo?.displayName || 'Unknown',
        bio: user?.bio || initialConversationInfo?.bio || '',
        id: conversationId,
        type: ConversationType.P2P,
        isLoading,
        error: error || undefined,
        isFromCache,
        isDeviceContact: user?.isDeviceContact || false,

        // P2P specific
        isBlocked: user?.isBlocked || false,
        conversationSettings: user ? user?.conversationSettings : null,
      };

      return p2pInfo;
    }
  }, [conversationId, permissions, isLoading, error, initialConversationInfo, chatSpace, user]);

  return {
    conversationInfo,
  };
};

export default useConversationInfo;
