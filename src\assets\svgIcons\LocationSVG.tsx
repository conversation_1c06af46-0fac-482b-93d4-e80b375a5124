import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const LocationSVG: React.FC<IconProps> = ({
    size = 18,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={(size * 15) / 18} // maintain aspect ratio
            height={size}
            viewBox="0 0 15 18"
            fill="none"
            {...restProps}
        >
            <Path
                d="M7.77.933a6.538 6.538 0 015.612 3.744l.007.015v.005a6.552 6.552 0 01-.637 6.703l-.143.189-4.249 5.4a1.178 1.178 0 01-1.761.1l-.09-.1-4.14-5.259C.748 9.673.472 6.932 1.641 4.566l.109-.21C2.907 2.197 5.086.893 7.534.925l.236.007zm-.33 1.239c-1.895 0-3.548.963-4.502 2.608l-.181.338c-.953 1.927-.726 4.17.592 5.844l4.08 5.182 4.212-5.325c1.205-1.533 1.484-3.466.778-5.249l-.154-.354c-.838-1.76-2.45-2.868-4.359-3.026l-.385-.018h-.08z"
                fill={color}
                stroke={color}
                strokeWidth={0.15}
            />
            <Path
                d="M7.435 4.285a3.057 3.057 0 013.052 3.053 3.057 3.057 0 01-3.052 3.053 3.057 3.057 0 01-3.053-3.053 3.057 3.057 0 013.053-3.053zm0 1.236a1.813 1.813 0 10.002 3.625 1.813 1.813 0 00-.002-3.625z"
                fill={color}
                stroke={color}
                strokeWidth={0.15}
            />
        </Svg>
    );
};

export default LocationSVG;

