import { Image, StyleSheet, FlatList, Text, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';

import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';

import LinearGradient from 'react-native-linear-gradient';
import PersonSVG from '../../assets/svgIcons/PersonSVG';

type Props = {};

const ScanContact = (props: Props) => {
  const languages = [
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'bn', name: 'Bengali', flag: '🇧🇩' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'jw', name: 'Javanese', flag: '🇮🇩' },
  ];

  const options = [
    { title: 'Translate voice', icon: 'globe-outline' },
    { title: 'Record call', icon: 'radio-button-on-outline' },
    { title: 'Share screen', icon: 'tv-outline' },
    { title: 'Change background', icon: 'image-outline' },
  ];

  return (
    <View style={{ flex: 1, position: 'relative' }}>
      <Image
        source={IMAGES.f_image}
        style={{ width: '100%', height: '100%', position: 'absolute' }}
      />

      {/* Gradient Overlay */}
      <LinearGradient
        colors={['rgba(0,0,0,0.2)', 'rgba(0,0,0,0.8)']}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
        }}
      />

      {/* Call Header */}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: 15,
          position: 'absolute',
          top: 0,
          width: '100%',
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {/* <Image
              source={{ uri: "https://source.unsplash.com/100x100/?person" }} // Replace with real user profile image
              style={{ width: 40, height: 40, borderRadius: 20, marginRight: 10 }}
            /> */}
          <Image
            source={IMAGES.whatsApp}
            style={{ width: 40, height: 40, borderRadius: 20, marginRight: 10 }}
          />
          <View>
            <Text style={{ color: '#fff', fontSize: 16, fontWeight: 'bold' }}>Gyllinton</Text>
            <Text style={{ color: '#bbb', fontSize: 14 }}>10:15</Text>
          </View>
        </View>
        <Image
          source={IMAGES.whatsApp}
          style={{ width: 25, height: 25, borderRadius: 20, marginRight: 10 }}
        />

        {/* <Ionicons name="ellipsis-vertical" size={24} color="white" /> */}
      </View>

      {/* Floating Video Thumbnail */}
      <View
        style={{
          position: 'absolute',
          bottom: 200,
          right: 20,
          backgroundColor: '#000',
          borderRadius: 50,
          overflow: 'hidden',
          borderWidth: 2,
          borderColor: '#fff',
        }}
      >
        {/* <Image
            source={{ uri: "https://source.unsplash.com/100x100/?man" }} // Replace with user's camera stream
            style={{ width: 70, height: 70 }}
          /> */}
        <Image source={IMAGES.whatsApp} style={{ width: 70, height: 70 }} />
      </View>

      {/* Options Bottom Panel */}
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          backgroundColor: 'rgba(255,255,255,0.1)',
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          padding: 20,
          backdropFilter: 'blur(10px)',
        }}
      >
        <Text style={{ color: '#fff', fontSize: 16, marginBottom: 10 }}>Options</Text>
        <FlatList
          data={options}
          keyExtractor={(item) => item.title}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
              }}
            >
              {/* <Ionicons name={item.icon} size={22} color="white" /> */}
              <PersonSVG size={30} />

              <Text style={{ color: '#fff', fontSize: 16, marginLeft: 10 }}>{item.title}</Text>
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );
};

export default ScanContact;

const styles = StyleSheet.create({
  searchView: {
    paddingHorizontal: hp(2),
  },
  titleText: {
    ...commonFontStyle(500, 16, colors.gray_80),
    paddingHorizontal: hp(2),
    marginTop: hp(2),
  },
  footerStyle: {
    flex: 1,
  },
});
