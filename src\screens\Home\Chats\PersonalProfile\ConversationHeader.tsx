import { View, Image, Text, StyleSheet } from 'react-native';
import { IMAGES } from '../../../../assets/Images';
import ButtonPurple from '../../../../component/ButtonPurple';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { colors } from '../../../../theme/colors';
import { MembershipStatus } from '../../../../types/chats.types';
import { ChatSocket } from '../../../../socket-client/ChatSocket';
import useSocket from '../../../../socket-client/useSocket';
import { ChatService } from '../../../../service/ChatService';
import { resetAndNavigateTo, showToast } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';

type ConversationHeaderProps = {
  conversationInfo: ConversationInfo | null;
};

const ConversationHeader: React.FC<ConversationHeaderProps> = ({ conversationInfo }) => {
  const { socket } = useSocket();
  const isChannel = conversationInfo?.type === ConversationType.CHANNEL;
  const isP2P = conversationInfo?.type === ConversationType.P2P;

  const isOwner =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus === MembershipStatus.OWNER
      : null;

  const isMember =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus === MembershipStatus.MEMBER
      : null;

  const description =
    conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.bio : conversationInfo?.bio;

  const showFollowButton = isChannel && !isOwner;

  const handleUnfollow = async () => {
    const chatSpaceId = conversationInfo?.id;
    ChatSocket.emitLeaveChatspace(socket, chatSpaceId as string, async (response) => {
      if (response?.error) return;
      ChatService.deleteConversation(chatSpaceId as string);
      socket?.emit('chatSpaces.deleted', { chatSpaceId });
      resetAndNavigateTo(SCREENS.HomeScreen, '', true);
      showToast(`You unfollowed "${conversationInfo?.displayName ?? 'this channel'}"`);
    });
  };

  return (
    <View style={styles.headerContainer}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatarBorder}>
            <Image
              source={
                conversationInfo?.displayPic
                  ? { uri: conversationInfo?.displayPic }
                  : IMAGES.profile_image
              }
              style={styles.avatar}
            />
          </View>
          {/* <View style={styles.onlineIndicator} /> */}
        </View>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>{conversationInfo?.displayName}</Text>
          <Text style={styles.userSubtitle} numberOfLines={1}>
            {isP2P ? conversationInfo?.displayName : description}

            {/* {conversationInfo?.type === ConversationType.P2P
            ? conversationInfo?.displayName
              ? conversationInfo?.displayName
              : conversationInfo?.displayName
            : ''} */}
          </Text>
        </View>
        {showFollowButton && (
          <ButtonPurple
            title={'Following'}
            onPress={() => {
              handleUnfollow();
            }}
            type="purple"
            titleColor={colors.white}
            extraStyle={styles.followButton}
          />
        )}

        {/* {newCondition && <Ionicons name={'return-up-forward-outline'} size={26} color="#232323" />} */}
      </View>
  );
};

export default ConversationHeader;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 8,
    paddingBottom: 18,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 10,
  },
  avatarBorder: {
    width: 68,
    height: 68,
    borderRadius: 34,
    borderWidth: 2,
    borderColor: colors.gray_cc,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: colors.black_23,
    fontSize: 18,
    fontWeight: '600',
  },
  userSubtitle: {
    color: colors.gray_80,
    fontSize: 14,
    fontWeight: '400',
  },
  followButton: {
    borderRadius: 15,
    height: 45,
  },
});
