import { BlurView } from '@react-native-community/blur';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
  Alert,
  TextInput,
  Image,
} from 'react-native';
import { CallScreenModalKey } from '../MainCallScreen';
import { useCallContext } from '../../../Context/CallProvider';
import { FlatList } from 'react-native-gesture-handler';
import useMultiSelectList from '../../../hooks/util/useMultiselect';
import LinearGradient from 'react-native-linear-gradient';
import { IMAGES } from '../../../assets/Images';
import { useContacts } from '../../../hooks/contacts/useContacts';
import ContactAvatarSVG from '../../../assets/svgIcons/ContactAvatarSVG';
import SearchSVG from '../../../assets/svgIcons/SearchSVG';
import { colors } from '../../../theme/colors';
import { useMe } from '../../../hooks/util/useMe';
import useUsers from '../../../device-storage/realm/hooks/useUsers';

type AddPeoplModalProps = {
  closeModal: (modal: CallScreenModalKey) => void;
  showModal: boolean;
};

const AddPeopleModal = ({ closeModal, showModal }: AddPeoplModalProps) => {
  const { registeredContacts } = useContacts();
  const { createFilteredContactsQuery } = useUsers();

  const { user } = useMe();

  const contactUsers = createFilteredContactsQuery()
    .onlyUnblocked()
    .exclude([user?.e164CompliantNumber ?? ''])
    .get();

  const { callDetails, inviteUsers } = useCallContext();

  const [search, setSearch] = useState('');
  // filter contacts that are not in the call
  const [filteredContacts, setFilteredContacts] = useState(contactUsers);

  const {
    selectedItems: selectedContacts,
    toggleSelection,
    isSelected,
    resetSelection,
  } = useMultiSelectList([], (item) => item.id, contactUsers);

  const filterContacts = useCallback(
    (text?: string) => {
      const searchText = text === undefined ? search : text;
      if (searchText != '') {
        const filtered = contactUsers.filter((contact) =>
          contact.name?.toLowerCase().includes(searchText.toLowerCase()),
        );
        setFilteredContacts(filtered);
      } else {
        setFilteredContacts(contactUsers);
      }
    },
    [search],
  );

  return (
    <Modal
      transparent={true}
      visible={showModal}
      onRequestClose={() => {
        closeModal('showAddPeople');
      }}
    >
      <Pressable
        style={styles.modalPressable}
        onPress={() => {
          closeModal('showAddPeople');
        }}
      >
        <LinearGradient colors={['#000000AA', '#000000AA']}>
          <View style={{ paddingHorizontal: 15 }}>
            <BlurView style={styles.blurContainer} blurType="dark" blurAmount={20} />
            <TouchableOpacity style={{ paddingVertical: 10 }} onPress={() => {}}>
              <View
                style={{
                  width: 50,
                  height: 5,
                  alignSelf: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: 50,
                }}
              ></View>
            </TouchableOpacity>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={[styles.title, { fontSize: 15, fontWeight: '500' }]}>
                Add people to call
              </Text>
            </View>
            <View
              style={{
                borderWidth: 0.5,
                borderBottomColor: '#aaa',
                marginVertical: 12,
              }}
            ></View>
            <Text style={[styles.title, {}]}>Select your friends</Text>
            <View style={styles.searchContainer}>
              <SearchSVG size={16} color={colors.white} />

              <TextInput
                style={styles.searchInput}
                placeholder="Search"
                placeholderTextColor="#c7c7c7"
                onChangeText={(text) => {
                  setSearch(text);
                  filterContacts(text);
                }}
                value={search}
              />
            </View>
            <FlatList
              data={filteredContacts}
              renderItem={({ item }) => (
                <TouchableOpacity
                  onPress={() => toggleSelection(item)}
                  style={{
                    // height: 100,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    // marginTop: index == 0 ? 0 : 20,
                    paddingVertical: 10,
                  }}
                >
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {/* <Image source={IMAGES.userImage} style={{ width: 40, height: 40 }} /> */}
                    <ContactAvatarSVG size={30} />

                    <Text style={{ color: '#fff', marginLeft: 10 }}>{item.name}</Text>
                  </View>

                  {isSelected(item) ? (
                    <Image
                      source={IMAGES.selected_white}
                      style={{ width: 22, height: 22 }}
                      resizeMode="contain"
                    />
                  ) : (
                    <Image
                      source={IMAGES.unselected_white}
                      style={{ width: 22, height: 22 }}
                      resizeMode="contain"
                    />
                  )}
                </TouchableOpacity>
              )}
              style={{ height: 400, marginVertical: 15, paddingBottom: 100 }}
              showsVerticalScrollIndicator={false}
            />
            <View style={styles.footer}>
              <TouchableOpacity
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  // paddingHorizontal: 50,
                  paddingVertical: 14,
                  borderRadius: 10,
                  marginBottom: 12,
                  width: '90%',
                  //   left: 10,
                }}
                onPress={() => {
                  if (selectedContacts.length === 0) {
                    Alert.alert('select atleast 1 contact');
                    return;
                  }
                  inviteUsers(selectedContacts);
                  closeModal('showAddPeople');
                  resetSelection();
                }}
              >
                <Text
                  style={{
                    color: '#FFF',
                    alignSelf: 'center',
                    fontWeight: '500',
                  }}
                >
                  Add to call
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalPressable: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    height: '60%',
    width: '100%',
    borderTopRightRadius: 25,
    borderTopLeftRadius: 25,
    overflow: 'hidden',
    alignSelf: 'center',
    paddingVertical: 10,
    backgroundColor: 'transparent',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 15,
  },
  flatListContent: {
    paddingBottom: 80,
  },
  contactRow: {
    height: 25,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  contactName: {
    color: '#fff',
  },
  addButton: {
    height: 24,
    width: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  footer: {
    // position: 'absolute',
    // bottom: 10,
    alignItems: 'center',
  },
  callButton: {
    backgroundColor: '#00aced',
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 20,
  },
  callButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    // marginLeft: 15,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginTop: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    color: '#fff',
    fontSize: 14,
  },
});

export default AddPeopleModal;
