import React, { useEffect, useState, useRef } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useCallContext } from '../../../Context/CallProvider';

import { CallButtons } from './CallButtons';

import { CallDetails } from '../../../types/calls.types';
import UserAvatar from '../../../component/Common/UserAvatar';

type IncomingCallParams = {
  setCallMembersModal: React.Dispatch<React.SetStateAction<boolean>>;
  setOptionsModal: React.Dispatch<React.SetStateAction<boolean>>;
};
function IncomingCall({ setCallMembersModal, setOptionsModal }: IncomingCallParams) {
  let membership = 'standard';

  return (
    <LinearGradient
      locations={[0, 0.35, 1]}
      colors={['#e09e61', '#5b2e15', '#000000']}
      style={{ flex: 1, justifyContent: 'center' }}
    >
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        <View style={newStyles.profileContainer}>
          <View
            style={[
              newStyles.profileWrapper,
              { borderColor: membership == 'gold' ? '#F9C32E' : '#EF924F' },
            ]}
          >
            <RenderImages />
          </View>
        </View>
      </View>
      <CallButtons setCallMembersModal={setCallMembersModal} setOptionsModal={setOptionsModal} />
    </LinearGradient>
  );
}

export default IncomingCall;

const newStyles = StyleSheet.create({
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },

  textContainer: {
    marginTop: 60,
    alignSelf: 'center',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    color: '#bbb',
    fontSize: 14,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  extraText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  goldBadge: {
    position: 'absolute',
    top: -6,
    right: 6,
    borderRadius: 10,
    padding: 3,
  },
  profileContainer: {
    alignItems: 'center',
    position: 'absolute',
    bottom: '50%',
    width: '100%',
  },
  profileWrapper: {
    width: '100%',
    height: '100%',
  },

  singleCallContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarContainer: {
    position: 'absolute',
    zIndex: 1,
    // backgroundColor: 'red',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  singleAvatar: {
    width: 120,
    height: 120,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
});

const RenderImages = () => {
  const { callDetails } = useCallContext();

  if (callDetails.origin && callDetails.origin.type === 'groupConversation') {
    return (
      <View style={newStyles.singleCallContainer}>
        <Image
          source={{
            uri:
              callDetails.origin.conversation.displayPic ||
              'https://avatar.iran.liara.run/public/boy?username=Ash',
          }}
          style={newStyles.singleAvatar}
        />
        <View style={{ alignItems: 'center', marginTop: 10 }}>
          <Text style={newStyles.title}>{callDetails.origin.conversation.displayName}</Text>
          <Text style={newStyles.subtitle}>
            {callDetails.initiatorDetails?.name} started {callDetails.type} call in group{' '}
            {callDetails.origin.conversation.displayName}
          </Text>
        </View>
      </View>
    );
  }

  // For a 1:1 call, show only one participant
  if (!callDetails?.isGroupCall) {
    return (
      <View style={newStyles.singleCallContainer}>
        <View style={newStyles.singleAvatar}>
          <UserAvatar url={callDetails.initiatorDetails?.image} />
          {/* <Image
            source={{
              uri: callDetails.initiatorDetails?.image,
            }}
            style={newStyles.singleAvatar}
          /> */}
        </View>
        <View style={{ alignItems: 'center', marginTop: 10 }}>
          <Text style={newStyles.title}>{callDetails.initiatorDetails?.name || 'Unknown'}</Text>
          <Text style={newStyles.subtitle}>Incoming {callDetails?.type} call</Text>
        </View>
      </View>
    );
  }
  // For group calls, show up to 3 participants and a "+X Others" indicator if needed
  const visibleParticipants = callDetails.recipentList.slice(0, 4);

  return (
    <View style={newStyles.centerContent}>
      <View
        style={{
          justifyContent: 'center',
        }}
      >
        <View style={{ alignSelf: 'center' }}>
          {visibleParticipants.map((profile, index) => {
            if (index <= 2) {
              const showOverlay = callDetails.recipentList.length > 3 && index === 2;
              return (
                <View key={profile._id} style={[{ left: index * 40, position: 'absolute' }]}>
                  <Image
                    source={{ uri: profile.image }}
                    style={{
                      width: 80,
                      height: 80,
                      borderRadius: 100,
                      borderWidth: 2,
                      borderColor: '#fff',
                    }}
                  />
                  {showOverlay && (
                    <View style={newStyles.overlay}>
                      <Text style={[newStyles.extraText, { fontSize: 15 }]}>
                        +{callDetails.recipentList.length - 3}
                      </Text>
                      <Text style={newStyles.extraText}>Others</Text>
                    </View>
                  )}
                </View>
              );
            }
            return null;
          })}
          <View style={{ marginTop: 85, alignSelf: 'center' }}>
            <Text style={newStyles.title}>Group Call ({callDetails.recipentList.length})</Text>
            <Text style={newStyles.subtitle}>Incoming {callDetails.type} call</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export function buildIncomingCallTitle(callDetails: CallDetails) {
  if (callDetails.origin && callDetails.origin.type === 'groupConversation') {
    return callDetails.origin.conversation.displayName;
  }

  return callDetails.initiatorDetails?.name || 'Unknown';
}
