import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';
import { ConversationType } from './MessageSchema';
import { MembershipStatus } from '../../../types/chats.types';
import { ConversationSettingsSchema } from './ConversationSettingsSchema';

export type RemoteChatSpace = {
  _id: string;
  chatSpaceId: string;
  name: string;
  displayPic?: string;
  description: string;
  type: ConversationType.CHANNEL | ConversationType.GROUP;
  isPrivate: boolean;
  inviteLink?: string;
  inviteCode?: string;
  memberCount: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};

export interface IChatSpace {
  id: string;
  name: string;
  type: ConversationType.CHANNEL | ConversationType.GROUP;
  description?: string;
  displayPic?: string;
  isPrivate: boolean;
  createdBy: string; // ObjectId as string in Realm
  membershipStatus: MembershipStatus;
  inviteCode?: string;
  memberCount: number;
  createdAt: number;
  updatedAt: number;
  conversationSettings: ConversationSettingsSchema;
}

export class ChatSpaceSchema extends Realm.Object<ChatSpaceSchema> implements IChatSpace {
  id!: string;
  name!: string;
  type!: ConversationType.CHANNEL | ConversationType.GROUP;
  description?: string;
  displayPic?: string;
  isPrivate!: boolean;
  createdBy!: string; // Store MongoDB ObjectId as string
  membershipStatus!: MembershipStatus;
  inviteCode?: string;
  memberCount!: number;
  createdAt!: number;
  updatedAt!: number;
  conversationSettings!: ConversationSettingsSchema;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.chat_space,
    primaryKey: 'id',
    properties: {
      id: 'string',
      name: 'string',
      type: { type: 'string', indexed: true }, // For filtering groups vs channels
      description: { type: 'string', default: '' },
      displayPic: { type: 'string', optional: true },
      isPrivate: { type: 'bool', default: false },
      createdBy: 'string', // MongoDB ObjectId as string
      membershipStatus: { type: 'string' },
      inviteCode: { type: 'string', optional: true },
      memberCount: { type: 'int', default: 0 },
      createdAt: { type: 'int' },
      updatedAt: { type: 'int' },
      conversationSettings: `${realmSchemaNames.conversation_settings}`,
    },
  };
}
