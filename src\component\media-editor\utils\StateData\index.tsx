import { Dispatch, SetStateAction } from 'react';
import { Alert } from 'react-native';

interface Image {
  id: number;
  stickers: Sticker[];
  texts?: TextInput[];
  additionalImages?: ImageItem[];
  shapes?: Shape[];
  path?: Path[];
}

interface Sticker {
  id: number;
  initialX?: number;
  initialY?: number;
  scale?: number;
  rotate?: number;
}

interface TextInput {
  id: number;
  initialX?: number;
  initialY?: number;
  scale?: number;
  rotate?: number;
}

interface ImageItem {
  id: number;
  initialX?: number;
  initialY?: number;
  scale?: number;
  rotate?: number;
}

interface Shape {
  id: number;
  shape?: string;
}

interface Path {
  id: number | string;
  segments: string[];
  color?: string;
}

export const updatePosition = (
  setImages: Dispatch<SetStateAction<Image[]>>,
  activeStoryIdx: number,
  memorizedPosition: any,
) => {
  // Alert.alert(JSON.stringify(memorizedPosition));
  setImages((prevImages) =>
    prevImages.map((image) => {
      if (image.id !== activeStoryIdx) return image;

      const updateItem = (item: any) => {
        if (item.id !== memorizedPosition.id) return item;

        let updatedItem = {
          ...item,
          ...(memorizedPosition.x !== undefined || memorizedPosition.y !== undefined
            ? { initialX: memorizedPosition.x, initialY: memorizedPosition.y }
            : {}),
          ...(memorizedPosition.scale !== undefined ? { scale: memorizedPosition.scale } : {}),
          ...(memorizedPosition.rotation !== undefined
            ? { rotate: memorizedPosition.rotation }
            : {}),
          ...(memorizedPosition.shape ? { shape: memorizedPosition.shape } : {}),
          ...(memorizedPosition.zIndex ? { zIndex: memorizedPosition.zIndex } : {}),
        };

        if (memorizedPosition.path !== undefined) {
          updatedItem.path = memorizedPosition.path.length > 0 ? memorizedPosition.path : undefined;
        }
        return updatedItem;
      };

      // Handle paths update
      if (memorizedPosition.type === 'path' && memorizedPosition.paths) {
        return {
          ...image,
          stickers: image.stickers.map(updateItem),
          texts: image.texts ? image.texts.map(updateItem) : [],
          additionalImages: image.additionalImages ? image.additionalImages.map(updateItem) : [],
          shapes: image.shapes ? image.shapes.map(updateItem) : [],
          path: memorizedPosition.paths.length > 0 ? memorizedPosition.paths : undefined,
        };
      }

      // Handle additionalImages update
      let updatedAdditionalImages = image.additionalImages
        ? image.additionalImages.map(updateItem)
        : [];
      if (memorizedPosition.type === 'additionalImages') {
        const existingImage = updatedAdditionalImages.find(
          (img) => img.id === memorizedPosition.id,
        );
        if (!existingImage) {
          updatedAdditionalImages.push({
            id: memorizedPosition.id,
            uri: memorizedPosition.uri,
            initialX: memorizedPosition.initialX,
            initialY: memorizedPosition.initialY,
            scale: memorizedPosition.scale,
            rotate: memorizedPosition.rotate,
            zIndex: memorizedPosition.zIndex,
          });
        }
      }

      return {
        ...image,
        stickers: image.stickers.map(updateItem),
        texts: image.texts ? image.texts.map(updateItem) : [],
        additionalImages: updatedAdditionalImages,
        shapes: image.shapes ? image.shapes.map(updateItem) : [],
        path: image.path ? [...image.path] : [],
      };
    }),
  );
};
