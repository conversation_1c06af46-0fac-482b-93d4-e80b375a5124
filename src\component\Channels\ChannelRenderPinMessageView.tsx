import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useAppDispatch, useAppSelector} from '../../redux/hooks';
import {colors} from '../../theme/colors';
import {commonFontStyle, hp, SCREEN_WIDTH, wp} from '../../theme/fonts';
import {IMAGES} from '../../assets/Images';
import ModalWrapper from '../ModalWrapper';
import {Emit_Event, sendData} from '../../Socket/socket';
import {useTranslation} from 'react-i18next';
import {onGetChannelChat} from '../../service/ChannelService';
import CustomImage from '../CustomImage';
import {ChatService} from '../../service/ChatService';

type Props = {
  activeChat: any;
  channelChatList: any[];
};

const ChannelRenderPinMessageView = ({activeChat, channelChatList}: Props) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();
  const [showRemovePinModal, setShowRemovePinModal] = useState(false);
  const [pinnedMessage, setPinnedMessage] = useState<any>(null);

  const onPressUnpin = () => {
    setShowRemovePinModal(false);
    if (pinnedMessage?.localId) {
      // Use new server-side unpin functionality
      ChatService.unpinMessage(pinnedMessage.localId, activeChat?.channelId);
    }
  };

  useEffect(() => {
    if (channelChatList?.length > 0) {
      const newList = channelChatList
        .filter(i => i.isPinned === true)
        .reverse();

      if (newList?.length > 0) {
        setPinnedMessage(newList[0]);
      } else {
        setPinnedMessage(null);
      }
    }
  }, [channelChatList]);

  return Boolean(pinnedMessage) ? (
    <View style={styles.pinMessageView}>
      <TouchableOpacity
        onLongPress={() => setShowRemovePinModal(true)}
        style={styles.pinView}>
        <Image source={IMAGES.pinMessage} style={styles.pinIcon} />
        {pinnedMessage?.messageType == 'TEXT' && (
          <Text numberOfLines={1} style={styles.messageText}>
            {pinnedMessage.message}
          </Text>
        )}
        {pinnedMessage?.messageType == 'IMAGE' && (
          <Image
            source={{uri: pinnedMessage.message}}
            style={styles.messageImage}
            resizeMode="contain"
          />
        )}
        {pinnedMessage?.messageType == 'VIDEO' && (
          <Text numberOfLines={1} style={styles.messageText}>
            {pinnedMessage?.messageType}
          </Text>
        )}
        {pinnedMessage?.messageType == 'GIF' &&
          (Platform.OS === 'ios' ? (
            <Image
              source={{uri: pinnedMessage?.message}}
              style={styles.gifStyle}
              resizeMode="contain"
            />
          ) : (
            <CustomImage
              source={{uri: pinnedMessage?.message}}
              size={hp(3)}
              tintColor={'a'}
            />
          ))}
        {pinnedMessage?.messageType == 'MUSIC' && (
          <Text numberOfLines={1} style={styles.messageText}>
            {pinnedMessage?.message.split('/').pop()}
          </Text>
        )}
        {pinnedMessage?.messageType == 'LOCATION' && (
          <Text numberOfLines={1} style={styles.messageText}>
            {pinnedMessage?.message}
          </Text>
        )}
        {pinnedMessage?.messageType == 'DOCUMENT' && (
          <Text numberOfLines={1} style={styles.messageText}>
            {pinnedMessage?.message.split('/').pop()}
          </Text>
        )}
      </TouchableOpacity>
      {showRemovePinModal && (
        <ModalWrapper
          isVisible={showRemovePinModal}
          onCloseModal={() => setShowRemovePinModal(false)}>
          <View>
            <Text style={styles.titletext}>{t('Options')}</Text>
            <TouchableOpacity
              onPress={() => {
                onPressUnpin();
              }}
              style={styles.row}>
              <Image source={IMAGES.pinMessage} style={styles.pinIcon} />
              <Text style={styles.title}>{t('Unpin')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setShowRemovePinModal(false);
              }}
              style={styles.row}>
              <Image source={IMAGES.pinMessage} style={styles.pinIcon} />
              <Text style={styles.title}>{t('Go to message')}</Text>
            </TouchableOpacity>
          </View>
        </ModalWrapper>
      )}
    </View>
  ) : (
    <></>
  );
};

export default ChannelRenderPinMessageView;

const styles = StyleSheet.create({
  pinMessageView: {
    backgroundColor: colors.white,
    position: 'absolute',
    width: SCREEN_WIDTH,
    zIndex: 1,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
  },
  pinIcon: {
    tintColor: colors.black_23,
    transform: [{rotate: '315deg'}],
    height: 18,
    width: 18,
  },
  pinView: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: hp(2),
    gap: 10,
  },
  messageText: {
    ...commonFontStyle(400, 14, colors.black_23),
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingLeft: hp(3),
    paddingVertical: 10,
  },
  titletext: {
    ...commonFontStyle(600, 16, colors.black_23),
    paddingLeft: hp(3),
    marginBottom: 10,
  },
  title: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  messageImage: {
    height: 50,
    width: 50,
    borderRadius: 10,
  },
  gifStyle: {
    height: hp(4),
    width: wp(10),
    borderRadius: 10,
  },
});
