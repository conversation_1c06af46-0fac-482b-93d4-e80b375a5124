import * as React from "react";
import Svg, { Mask, Path, G } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const CurrentLocationSVG: React.FC<SvgComponentProps> = ({
    size = 20,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 20 20"
            fill="none"
            {...props}
        >
            <Mask
                id="a"
                style={{ maskType: "luminance" }}
                maskUnits="userSpaceOnUse"
                x={-1}
                y={0}
                width={21}
                height={20}
            >
                <Path d="M-.001 0h19.666v19.667H0V.001z" fill="#fff" />
            </Mask>
            <G
                mask="url(#a)"
                stroke={color}
                strokeWidth={1.15233}
                strokeMiterlimit={10}
                strokeLinejoin="round"
            >
                <Path d="M17.768 9.834a7.935 7.935 0 11-15.87 0 7.935 7.935 0 0115.87 0z" />
                <Path
                    d="M9.832.576V3.22M.574 9.833H3.22M9.832 19.09v-2.644M19.088 9.833h-2.645M12.123 9.833a2.292 2.292 0 11-4.585 0 2.292 2.292 0 014.585 0z"
                    strokeLinecap="round"
                />
            </G>
        </Svg>
    );
};

export default CurrentLocationSVG;


