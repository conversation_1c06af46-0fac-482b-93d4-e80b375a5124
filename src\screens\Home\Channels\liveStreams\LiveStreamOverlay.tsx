import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import HeartOutlineSvg from '../../../../assets/svgIcons/HeartOutlineSvg';
import ShareOutlineSvg from '../../../../assets/svgIcons/ShareOutlineSvg';
import StickerOutlineSvg from '../../../../assets/svgIcons/StickerOutlineSvg';
import { useKeyboard } from '../../../../utils/useKeyboard';
import EyeOutlineSvg from '../../../../assets/svgIcons/EyeOutlineSvg';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { colors } from '../../../../theme/colors';

import RNVideo from 'react-native-video';

// Overlay Component
const LiveStreamOverlay: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { keyboardHeight, keyboardVisible } = useKeyboard();

  return (
    <View
      style={{
        ...LiveStreamOverlayStyles.overlay,
        bottom: keyboardVisible ? keyboardHeight + 25 : 0,
      }}
    >
      {children}
    </View>
  );
};

export default LiveStreamOverlay;

export const LiveStreamOverlayStyles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    height: '100%',

    width: '100%',
    bottom: 0,
    display: 'flex',
    alignItems: 'flex-end',
    gap: 10,
  },
});
