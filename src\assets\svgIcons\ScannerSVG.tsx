import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

interface GridSVGProps {
  size?: number;
  color?: string;
}

const ScannerSVG: React.FC<GridSVGProps> = ({
  size = 25,
  color = '#fff',
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 25 25"
      fill="none"
      {...props}
    >
      <Path
        d="M4.883 25H2.93A2.933 2.933 0 010 22.07v-1.953a.977.977 0 111.953 0v1.953c0 .539.438.977.977.977h1.953a.977.977 0 110 1.953zM22.07 25h-1.953a.977.977 0 110-1.953h1.953a.978.978 0 00.977-.977v-1.953a.977.977 0 111.953 0v1.953A2.933 2.933 0 0122.07 25zM.977 5.86A.977.977 0 010 4.882V2.93A2.933 2.933 0 012.93 0h1.953a.977.977 0 110 1.953H2.93a.978.978 0 00-.977.977v1.953c0 .54-.437.976-.976.976zM24.023 5.86a.977.977 0 01-.976-.977V2.93a.978.978 0 00-.977-.977h-1.953a.977.977 0 110-1.953h1.953A2.933 2.933 0 0125 2.93v1.953c0 .54-.437.976-.977.976zM8.594 21.29H6.64a2.933 2.933 0 01-2.93-2.93v-1.954a2.933 2.933 0 012.93-2.93h1.953a2.933 2.933 0 012.93 2.93v1.953a2.933 2.933 0 01-2.93 2.93zM6.64 15.43a.978.978 0 00-.977.976v1.953c0 .539.438.977.977.977h1.953a.978.978 0 00.976-.977v-1.953a.978.978 0 00-.976-.976H6.64zM8.594 11.523H6.64a2.933 2.933 0 01-2.93-2.93V6.642a2.933 2.933 0 012.93-2.93h1.953a2.933 2.933 0 012.93 2.93v1.953a2.933 2.933 0 01-2.93 2.93zM6.64 5.664a.978.978 0 00-.977.977v1.953c0 .538.438.976.977.976h1.953a.978.978 0 00.976-.976V6.64a.978.978 0 00-.976-.977H6.64zM18.36 11.523h-1.954a2.933 2.933 0 01-2.93-2.93V6.642a2.933 2.933 0 012.93-2.93h1.953a2.933 2.933 0 012.93 2.93v1.953a2.933 2.933 0 01-2.93 2.93zm-1.954-5.859a.978.978 0 00-.976.977v1.953c0 .538.438.976.976.976h1.953a.978.978 0 00.977-.976V6.64a.978.978 0 00-.977-.977h-1.953zM18.36 21.29h-1.954a2.933 2.933 0 01-2.93-2.93v-1.954a2.933 2.933 0 012.93-2.93h1.953a2.933 2.933 0 012.93 2.93v1.953a2.933 2.933 0 01-2.93 2.93zm-1.954-5.86a.978.978 0 00-.976.976v1.953c0 .539.438.977.976.977h1.953a.978.978 0 00.977-.977v-1.953a.978.978 0 00-.977-.976h-1.953z"
        fill={color}
      />
    </Svg>
  );
};

export default ScannerSVG;
