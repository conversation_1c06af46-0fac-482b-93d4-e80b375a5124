export const languages = [
  { id: 'Afrikaans', symbol: '🇿🇦', code: 'afr', s2stSupported: false },
  { id: 'Amharic', symbol: '🇪🇹', code: 'amh', s2stSupported: false },
  { id: 'Arabic (Standard)', symbol: '🇸🇦', code: 'arb', s2stSupported: true },
  { id: 'Arabic (Moroccan)', symbol: '🇲🇦', code: 'ary', s2stSupported: false },
  { id: 'Arabic (Egyptian)', symbol: '🇪🇬', code: 'arz', s2stSupported: false },
  { id: 'Assamese', symbol: '🇮🇳', code: 'asm', s2stSupported: false },
  { id: 'Azerbaijani', symbol: '🇦🇿', code: 'azj', s2stSupported: false },
  { id: 'Belarusian', symbol: '🇧🇾', code: 'bel', s2stSupported: false },
  { id: 'Bengali', symbol: '🇧🇩', code: 'ben', s2stSupported: true },
  { id: 'Bosnian', symbol: '🇧🇦', code: 'bos', s2stSupported: false },
  { id: 'Bulgarian', symbol: '🇧🇬', code: 'bul', s2stSupported: false },
  { id: 'Burmese', symbol: '🇲🇲', code: 'mya', s2stSupported: false },
  { id: 'Catalan', symbol: '🇪🇸', code: 'cat', s2stSupported: true },
  { id: 'Cebuano', symbol: '🇵🇭', code: 'ceb', s2stSupported: false },
  { id: 'Czech', symbol: '🇨🇿', code: 'ces', s2stSupported: true },
  { id: 'Kurdish (Sorani)', symbol: '🇮🇶', code: 'ckb', s2stSupported: false },
  { id: 'Mandarin (Simplified)', symbol: '🇨🇳', code: 'cmn', s2stSupported: true },
  { id: 'Mandarin (Traditional)', symbol: '🇹🇼', code: 'cmn_Hant', s2stSupported: true },
  { id: 'Croatian', symbol: '🇭🇷', code: 'hrv', s2stSupported: false },
  { id: 'Chichewa', symbol: '🇲🇼', code: 'nya', s2stSupported: false },
  { id: 'Cantonese', symbol: '🇭🇰', code: 'yue', s2stSupported: false },
  { id: 'Danish', symbol: '🇩🇰', code: 'dan', s2stSupported: true },
  { id: 'Dutch', symbol: '🇳🇱', code: 'nld', s2stSupported: true },
  { id: 'English', symbol: '🇬🇧', code: 'eng', s2stSupported: true },
  { id: 'Estonian', symbol: '🇪🇪', code: 'est', s2stSupported: true },
  { id: 'Finnish', symbol: '🇫🇮', code: 'fin', s2stSupported: true },
  { id: 'French', symbol: '🇫🇷', code: 'fra', s2stSupported: true },
  { id: 'Galician', symbol: '🇪🇸', code: 'glg', s2stSupported: false },
  { id: 'Georgian', symbol: '🇬🇪', code: 'kat', s2stSupported: false },
  { id: 'German', symbol: '🇩🇪', code: 'deu', s2stSupported: true },
  { id: 'Greek', symbol: '🇬🇷', code: 'ell', s2stSupported: false },
  { id: 'Gujarati', symbol: '🇮🇳', code: 'guj', s2stSupported: false },
  { id: 'Hebrew', symbol: '🇮🇱', code: 'heb', s2stSupported: false },
  { id: 'Hindi', symbol: '🇮🇳', code: 'hin', s2stSupported: true },
  { id: 'Hungarian', symbol: '🇭🇺', code: 'hun', s2stSupported: false },
  { id: 'Icelandic', symbol: '🇮🇸', code: 'isl', s2stSupported: false },
  { id: 'Igbo', symbol: '🇳🇬', code: 'ibo', s2stSupported: false },
  { id: 'Indonesian', symbol: '🇮🇩', code: 'ind', s2stSupported: true },
  { id: 'Irish', symbol: '🇮🇪', code: 'gle', s2stSupported: false },
  { id: 'Italian', symbol: '🇮🇹', code: 'ita', s2stSupported: true },
  { id: 'Japanese', symbol: '🇯🇵', code: 'jpn', s2stSupported: true },
  { id: 'Javanese', symbol: '🇮🇩', code: 'jav', s2stSupported: false },
  { id: 'Kannada', symbol: '🇮🇳', code: 'kan', s2stSupported: false },
  { id: 'Kazakh', symbol: '🇰🇿', code: 'kaz', s2stSupported: false },
  { id: 'Khmer', symbol: '🇰🇭', code: 'khm', s2stSupported: false },
  { id: 'Korean', symbol: '🇰🇷', code: 'kor', s2stSupported: true },
  { id: 'Kyrgyz', symbol: '🇰🇬', code: 'kir', s2stSupported: false },
  { id: 'Lao', symbol: '🇱🇦', code: 'lao', s2stSupported: false },
  { id: 'Luo', symbol: '🇰🇪', code: 'luo', s2stSupported: false },
  { id: 'Latvian', symbol: '🇱🇻', code: 'lvs', s2stSupported: false },
  { id: 'Luganda', symbol: '🇺🇬', code: 'lug', s2stSupported: false },
  { id: 'Lithuanian', symbol: '🇱🇹', code: 'lit', s2stSupported: false },
  { id: 'Macedonian', symbol: '🇲🇰', code: 'mkd', s2stSupported: false },
  { id: 'Malayalam', symbol: '🇮🇳', code: 'mal', s2stSupported: false },
  { id: 'Maltese', symbol: '🇲🇹', code: 'mlt', s2stSupported: true },
  { id: 'Marathi', symbol: '🇮🇳', code: 'mar', s2stSupported: false },
  { id: 'Manipuri', symbol: '🇮🇳', code: 'mni', s2stSupported: false },
  { id: 'Maithili', symbol: '🇮🇳', code: 'mai', s2stSupported: false },
  { id: 'Malay', symbol: '🇲🇾', code: 'zsm', s2stSupported: false },
  { id: 'Nepali', symbol: '🇳🇵', code: 'npi', s2stSupported: false },
  { id: 'Norwegian Bokmål', symbol: '🇳🇴', code: 'nob', s2stSupported: false },
  { id: 'Norwegian Nynorsk', symbol: '🇳🇴', code: 'nno', s2stSupported: false },
  { id: 'Nigerian Fulfulde', symbol: '🇳🇬', code: 'fuv', s2stSupported: false },
  { id: 'Odiya (Oriya)', symbol: '🇮🇳', code: 'ory', s2stSupported: false },
  { id: 'Pashto', symbol: '🇦🇫', code: 'pbt', s2stSupported: false },
  { id: 'Persian (Farsi)', symbol: '🇮🇷', code: 'pes', s2stSupported: true },
  { id: 'Polish', symbol: '🇵🇱', code: 'pol', s2stSupported: true },
  { id: 'Portuguese', symbol: '🇵🇹', code: 'por', s2stSupported: true },
  { id: 'Punjabi', symbol: '🇮🇳', code: 'pan', s2stSupported: false },
  { id: 'Romanian', symbol: '🇷🇴', code: 'ron', s2stSupported: true },
  { id: 'Russian', symbol: '🇷🇺', code: 'rus', s2stSupported: true },
  { id: 'Sindhi', symbol: '🇵🇰', code: 'snd', s2stSupported: false },
  { id: 'Slovak', symbol: '🇸🇰', code: 'slk', s2stSupported: true },
  { id: 'Slovenian', symbol: '🇸🇮', code: 'slv', s2stSupported: false },
  { id: 'Somali', symbol: '🇸🇴', code: 'som', s2stSupported: false },
  { id: 'Shona', symbol: '🇿🇼', code: 'sna', s2stSupported: false },
  { id: 'Spanish', symbol: '🇪🇸', code: 'spa', s2stSupported: true },
  { id: 'Swahili', symbol: '🇰🇪', code: 'swh', s2stSupported: true },
  { id: 'Swedish', symbol: '🇸🇪', code: 'swe', s2stSupported: true },
  { id: 'Serbian', symbol: '🇷🇸', code: 'srp', s2stSupported: false },
  { id: 'Tamil', symbol: '🇮🇳', code: 'tam', s2stSupported: false },
  { id: 'Telugu', symbol: '🇮🇳', code: 'tel', s2stSupported: true },
  { id: 'Thai', symbol: '🇹🇭', code: 'tha', s2stSupported: true },
  { id: 'Tajik', symbol: '🇹🇯', code: 'tgk', s2stSupported: false },
  { id: 'Tagalog', symbol: '🇵🇭', code: 'tgl', s2stSupported: true },
  { id: 'Turkish', symbol: '🇹🇷', code: 'tur', s2stSupported: true },
  { id: 'Ukrainian', symbol: '🇺🇦', code: 'ukr', s2stSupported: true },
  { id: 'Urdu', symbol: '🇵🇰', code: 'urd', s2stSupported: true },
  { id: 'Uzbek', symbol: '🇺🇿', code: 'uzn', s2stSupported: true },
  { id: 'Vietnamese', symbol: '🇻🇳', code: 'vie', s2stSupported: true },
  { id: 'Welsh', symbol: '', code: 'cym', s2stSupported: true },
  { id: 'West Central Oromo', symbol: '🇪🇹', code: 'gaz', s2stSupported: false },
  { id: 'Yoruba', symbol: '🇳🇬', code: 'yor', s2stSupported: false },
  { id: 'Zulu', symbol: '🇿🇦', code: 'zul', s2stSupported: false },
];
