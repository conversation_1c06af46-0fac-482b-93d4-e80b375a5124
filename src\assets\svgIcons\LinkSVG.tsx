import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

interface IconProps {
  size?: number;
  color?: string;
}

const LinkSVG: React.FC<IconProps> = ({ size = 16, color = '#232323', ...props }) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <Path
        d="M12.394 7.432a.804.804 0 101.136 1.136l1.24-1.24a4.313 4.313 0 00-6.099-6.1L7.431 2.47a.803.803 0 101.136 1.136l1.24-1.24a2.706 2.706 0 113.827 3.826l-1.24 1.24zM3.605 8.568A.803.803 0 102.47 7.432l-1.24 1.24a4.313 4.313 0 006.098 6.1l1.241-1.241a.803.803 0 00-1.136-1.136l-1.24 1.24a2.706 2.706 0 01-3.827-3.827l1.24-1.24z"
        fill={color}
      />
      <Path
        d="M4.95 11.053a.804.804 0 001.136 0l4.966-4.967A.802.802 0 109.916 4.95L4.95 9.912a.804.804 0 000 1.14z"
        fill={color}
      />
    </Svg>
  );
};

export default LinkSVG;
