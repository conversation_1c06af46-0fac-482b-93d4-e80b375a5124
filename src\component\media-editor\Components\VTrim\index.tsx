import React, { useState, useEffect, FC, useRef, memo } from "react";
import { View, StyleSheet, Alert, NativeModules, Platform } from "react-native";
import Trimmer from "./trim";
import RNFS from "react-native-fs";
import { useTimeData } from "../../utils/Context/TimeContext";
const { AudioTrimmer, AudioTrimModule } = NativeModules;

const PREVIEW_HEIGHT = 200;

interface VidTrimmerProps {
    id?: number;
    videoUri?: any;
    setVideoUri?: (uri: any) => void;
    forAudio?: boolean;
    audioUri?: any;
    setAudioUri?: (uri: any) => void;
    setADuration?: (duration: number) => void;
    audioStartTime?: number;
    vidStartTime?: number;
    vidEndtime?: number;
    trimVidStartTime?: number;
    trimVidEndTime?: number;
    backgroundImage?: any;
    currentTime?: number;
    finalDuration?: number;
    vaDuration?: number;
    audioFullUrl?: string;
    isDrawing?:boolean;
}

const VidTrimmer: FC<VidTrimmerProps> = ({
    id,
    videoUri = null,
    setVideoUri,
    forAudio = false,
    audioUri = null,
    setAudioUri,
    setADuration,
    audioStartTime = 0,
    backgroundImage,
    vidStartTime,
    vidEndtime,
    trimVidStartTime,
    trimVidEndTime,
    currentTime,
    finalDuration = trimVidEndTime || 15,
    audioFullUrl,
    isDrawing,
}) => {
    const [startTime, setStartTime] = useState(forAudio ? audioStartTime : 0);
    const [endTime, setEndTime] = useState(forAudio ? audioStartTime + 15 : 30);
    const [videoDuration, setVideoDuration] = useState(30);
    // const [isLoading, setIsLoading] = useState(false);
    const [trimmedVideo, setTrimmedVideo] = useState<string | null>(null);
    const {
        setTrimEndTime,
        setEndTime: setEndTimeContext,
        setTempUri,
    } = useTimeData();

    useEffect(() => {
        getDuration();
    }, [vidEndtime == 0]);

    useEffect(() => {
        // console.log('trimming called');
        if (forAudio || trimVidStartTime || trimVidEndTime) {
                trimAudio();
        }
    }, [audioStartTime, trimVidStartTime, trimVidEndTime]);

    const getDuration = async () => {
        try {
            if (endTime - startTime < 0.1) {
                Alert.alert('Trim duration is too short.');
                throw new Error("Trim duration is too short.");
            }
            // setIsLoading(true);
            const uri = !forAudio ? videoUri : audioUri;
            if (!uri) {
                throw new Error("URI is null or undefined");
            }
            const duration = Platform.OS == 'ios' ? await AudioTrimmer.getAudioDuration(uri) : await AudioTrimModule.getAudioDuration(uri);
            // console.log('Audio Duration:', duration);
            if (!forAudio) {
                setTrimEndTime(duration);
                setEndTimeContext(duration);
                setEndTime(duration);
                setVideoDuration(duration);
            } else {
                setADuration?.(duration);
            }
        } catch (error) {
            console.error(`Error getting ${forAudio ? 'Audio' : 'Video'} duration:`, error);
        }
    };

    const trimVideo = async () => {
        setTrimmedVideo(videoUri);
        setVideoUri?.(videoUri);
        setTempUri?.(videoUri);
    };

    const trimAudio = async () => {
        try {
            if (startTime < 0) {
                throw new Error("Invalid start time.");
            }
            if (!audioFullUrl && !audioUri) {
                throw new Error("Audio path is missing");
            }

            // console.log('audioStartTime ===', audioStartTime)
            // console.log('finalDuration + audioStartTime', finalDuration + audioStartTime)
            // console.log('finalDuration AND audioStartTime', finalDuration +  ' and ' + audioStartTime)

            const trimmedFilePath = Platform.OS == 'ios'
                ? await AudioTrimmer.trimAudio(audioFullUrl || audioUri, trimVidStartTime || audioStartTime, finalDuration + audioStartTime)
                : await AudioTrimModule.trimAudio(audioFullUrl || audioUri, trimVidStartTime || audioStartTime, finalDuration + audioStartTime);
            if (await RNFS.exists(trimmedFilePath)) {
                setAudioUri?.(trimmedFilePath);
            } else {
                throw new Error("Trimmed audio file not created");
            }
        } catch (error) {
            console.error('Error trimming Audio:', error);
        }
    };

    return (
        <View style={[styles.container, {zIndex: isDrawing ? 0 : 200}]}>
            {!forAudio &&
                <Trimmer
                    id={id}
                    trimVideo={trimVideo}
                    videoDuration={videoDuration}
                    backgroundImage={backgroundImage}
                    startTime={vidStartTime}
                    endTime={vidEndtime}
                    tStartTime={trimVidStartTime}
                    tEndTime={trimVidEndTime}
                    currentTime={currentTime}
                />
            }
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        bottom: '16.3%',
        width: '100%',
        justifyContent: 'flex-end',
        position: 'absolute',
    },
    videoPlayer: {
        width: '100%',
        height: PREVIEW_HEIGHT,
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.7)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        color: '#fff',
        fontSize: 16,
    },
    overlay: {
        width: '100%',
        height: '100%',
        position: 'absolute',
    },
});

export default memo(VidTrimmer);