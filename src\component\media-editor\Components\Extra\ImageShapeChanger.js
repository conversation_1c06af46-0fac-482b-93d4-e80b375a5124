import React, { useState, useEffect } from "react";
import { View, Pressable, StyleSheet, Image as RNImage } from "react-native";
import { Images } from "../../Assets/Images";
import { Canvas, Path, Image as SkiaImage, useImage, Mask, Rect, Circle, RoundedRect } from "@shopify/react-native-skia";

const shapes = ["rectangle", "circle", "rounded","CustomRadius", "star", "hexagon", "triangle", "heart"];

const ImageShapeChanger = () => {
  const [shapeIndex, setShapeIndex] = useState(0);
  const [imageUri, setImageUri] = useState(null);
  const image = useImage(imageUri); // Load image for Skia

  useEffect(() => {
    const resolveImage = () => {
      const resolvedImage = RNImage.resolveAssetSource(Images.Mainimg);
      setImageUri(resolvedImage.uri);
    };
    resolveImage();
  }, []);

  const nextShape = () => {
    setShapeIndex((prev) => (prev + 1) % shapes.length);
  };

  const getClipPath = () => {
    switch (shapes[shapeIndex]) {
      case "circle":
        return { borderRadius: 75 };
      case "rounded":
        return { borderRadius: 20 };
      case "CustomRadius":
        return { borderTopRightRadius:30, borderBottomLeftRadius:30, };
      case "star":
        return "M 75,0 L 92,40 L 145,57 L 105,90 L 120,145 L 70,120 L 25,145 L 40,90 L 0,57 L 48,40 Z";
        // return "M 75,5 L 91,57 L 145,57 L 100,90 L 115,145 L 75,115 L 35,145 L 50,90 L 5,57 L 59,57 Z";
      case "hexagon":
        return "M 75 0 L 150 43 L 150 107 L 75 150 L 0 107 L 0 43 Z"; 
      case "triangle":
        return "M 75,1 L 148,149 L 1,149 Z";
      case "heart":
        return "M 75,130 C -10,40 75,-10 160,40 C 200,80 75,160 75,130 Z";
      default:
        return null;
    }
  };

  const getShapeStyle = () => {
    switch (shapes[shapeIndex]) {
      case "circle":
        return { borderRadius: 75 };
      case "rounded":
        return { borderRadius: 20 };
      case "CustomRadius":
        return { borderTopRightRadius:30, borderBottomLeftRadius:30, };
      default:
        return { borderRadius: 0 };
    }
  };

  return (
    <View style={styles.container}>
      <Pressable onPress={nextShape} style={[styles.imageContainer, getShapeStyle()]}>
        {["star", "hexagon", "triangle", "heart"].includes(shapes[shapeIndex]) && image ? (
          <Canvas style={styles.canvas}>
            <Mask mask={<Path path={getClipPath()} color="white" />}>
              <SkiaImage image={image} fit="cover" x={0} y={0} width={150} height={150} />
            </Mask>
          </Canvas>
        ) : (
          <RNImage source={Images.Mainimg} style={styles.image} />
        )}
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  imageContainer: {
    
    width: 150,
    height: 150,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  canvas: {
    width: 150,
    height: 150,
  },
});

export default ImageShapeChanger;
