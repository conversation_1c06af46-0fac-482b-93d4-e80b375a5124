export const socketEvents = {
  // Fetch chat history for a single user
  FETCH_CHAT_HISTORY: 'CHAT_LIST',

  // Ping server to retrieve chats for a single user
  PING_CHAT_HISTORY: 'CHATS_LIST',

  // Used to listen own messages
  RECEIVE_OWN_MESSAGE: 'RECEIVE_OWN_MESSAGE',

  // Used to listen other users message
  RECEIVE_MESSAGE: 'RECEIVE_CHAT_MESSAGE',

  // Delete message
  DELETE_MESSAGE: 'DELETE_MESSAGE',

  // Send edited message
  SEND_EDITED_MESSAGE: 'SEND_EDIT_MESSAGE',

  // Receive own edited message
  RECEIVE_OWN_EDITED_MESSAGE: 'RECEIVE_OWN_EDITED_MESSAGE',

  // Pings server to get all conversations
  PING_CONVERSATIONS: 'CHATS_HISTORY_LIST',

  // Gets all the conversations
  FETCH_CONVERSATIONS: 'RECEIVE_CHAT_HISTORY',

  // used to listen when deleted a message
  DELETE_OWN_MESSAGE: 'DELETE_OWN_MESSAGE',

  // used to delete the conversation
  DELETE_CHAT_MESSAGES: 'DELETE_CHAT_MESSAGES',

  // used to listen when deleted a conversation
  DELETE_CHAT_MESSAGES_ACK: 'DELETE_CHAT_MESSAGES_ACK',

  PING_CONVS: 'PING_CONVERSATIONS',
  FETCH_CONVS: 'FETCH_CONVERSATIONS',

  // This is to listen when a new message is sent
  NEW_MESSAGE: 'message:new',
  // This is to acknowledge client that the message has been sent to server
  ACK_MESSAGE: 'message:ack',

  SEND_MESSAGE: 'message:send',
  COMPANION_INFO: 'storage:companion',
  CHATSPACE_DELETED: 'chatSpace:deleted',
};

export const authSocketEvents = {
  AUTHENTICATED: 'AUTHENTICATED',
  RECIEVE_QR: 'RECIEVE_QR',
  AUTH_CONNECTION: 'AUTH_CONNECTION',
};

export const callSocketEvents = {
  getCallDetails: 'calls:getCallDetails',
  TERMINATE_CALL: 'calls:terminate',
  START_CALL: 'calls:initiate',
  INCOMING_CALL: 'calls:incoming',
  ANSWER_CALL: 'calls:answer',
  MUTE_CALL: 'MUTE_CALL',
  REJECT_CALL: 'REJECT_CALL',
  END_CALL: 'calls:end',
  END_OUTGOING_CALL: 'calls:end_outgoing',

  INVITE_USERS: 'calls:invite_users',
  CALL_REJECTED: 'calls:rejected',
  CANCEL_CALL: 'CANCEL_CALL',
  RECONNECT: 'calls:reconnect',
  NEW_USER_JOINED: 'NEW_USER_JOINED',
  CALL_ANSWERED: 'CALL_ANSWERED',
  CALL_IGNORED: 'CALL_IGNORED',
  ROOM_CLOSED: 'ROOM_CLOSED',
  CALL_ENDED: 'CALL_ENDED',
  USER_LEFT_CALL: 'USER_LEFT_CALL',
  NOTIFY_OWNER: 'NOTIFY_OWNER',
  ADD_PEOPLE_TO_CALL: 'ADD_PEOPLE_TO_CALL',
  ACCEPT_SWITCH_TO_VIDEO: 'ACCEPT_SWITCH_TO_VIDEO',
  MUTE_AUDIO: 'MUTE_AUDIO',
  MUTE_VIDEO: 'MUTE_VIDEO',
  TOGGLE_AUDIO: 'TOGGLE_AUDIO',
  TOGGLE_VIDEO: 'TOGGLE_VIDEO',
  LEAVE_CALL: 'calls:leave',

  CHECK_ACTIVE_SCREEN_SHARER: 'CHECK_ACTIVE_SCREEN_SHARER',
  STOP_CURRENT_SCREEN_SHARE: 'STOP_CURRENT_SCREEN_SHARE',
  CALL_ADMIN_SCREENSHARE_RESPONSE: 'CALL_ADMIN_SCREENSHARE_RESPONSE',
  DENY_SCREEN_SHARE_SWITCH: 'DENY_SCREEN_SHARE_SWITCH',
  SCREEN_SHARE_STATUS: 'SCREEN_SHARE_STATUS',
  ADMIN_RESPONSE: 'ADMIN_RESPONSE',
  SCREEN_SHARE_STOPPED: 'SCREEN_SHARE_REJECTED',
  SHARE_SCREEN: 'calls:share_screen',
  STOP_SCREENSHARING: 'calls:stop_screen_sharing',
  REQUEST_SCREEN_SHARE: 'calls:request_screen_share',
  REMOVE_SCREEN_SHARE_REQUEST: 'calls:remove_screen_share_request',

  CALL_STATUS: 'calls:status',
  SWITCH_TO_VIDEO: 'SWITCH_TO_VIDEO',
  SWITCH_TO_VIDEO_ACCEPTED: 'SWITCH_TO_VIDEO_ACCEPTED',
  SWITCH_TO_VIDEO_REJECTED: 'SWITCH_TO_VIDEO_REJECTED',
  REMOVE_PARTICIPANT_FROM_CALL: 'REMOVE_PARTICIPANT_FROM_CALL',
  YOU_ARE_REMOVED_FROM_CALL: 'YOU_ARE_REMOVED_FROM_CALL',
  PARTICIPANT_REMOVED_FROM_CALL: 'PARTICIPANT_REMOVED_FROM_CALL',
  USER_IS_ON_OTHER_CALL: 'USER_IS_ON_OTHER_CALL',
  NEW_INCOMING_CALL: 'NEW_INCOMING_CALL',
  CALL_IS_ANSWERED: 'CALL_ANSWERED',
  KEEP_CALL_ALIVE: 'KEEP_CALL_ALIVE',

  INITIATE_TRANSLATION: 'INITIATE_TRANSLATION',
  TERMINATE_TRANSLATION: 'TERMINATE_TRANSLATION',
  CALL_ATTEMPT: 'call_attempt',
};

export const mediaSocketEvents = {
  GET_RTP_CAPABILITES: 'GET_RTP_CAPABILITES',
  CREATE_PRODUCER_TRANSPORT: 'CREATE_PRODUCER_TRANSPORT',
  CONNECT_PRODUCER_TRANSPORT: 'CONNECT_PRODUCER_TRANSPORT',
  PRODUCE: 'PRODUCE',
  CREATE_CONSUMER_TRANSPORT: 'CREATE_CONSUMER_TRANSPORT',
  CONNECT_CONSUMER_TRANSPORT: 'CONNECT_CONSUMER_TRANSPORT',
  CONSUME: 'CONSUME',
  UNPAUSE: 'UNPAUSE',
  GET_PRODUCERS: 'GET_PRODUCERS',
  NEW_PRODUCER: 'NEW_PRODUCER',

  PRODUCE_SCREEN_SHARING: 'PRODUCE_SCREEN_SHARING',
  CONSUME_DATA_PRODCUCER: 'CONSUME_DATA_PRODCUCER',
  GET_TRANSPORT_INFO: 'GET_TRANSPORT_INFO',
};
