import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'; // or use any icon library
import HeartSVG from '../../../../assets/svgIcons/HeartSVG';
import { colors } from '../../../../theme/colors';

type StreamLiveCardProps = {
  onJoin: () => void;
};

const StreamLiveCard: React.FC<StreamLiveCardProps> = ({ onJoin }) => {
  return (
    <View
      style={{
        padding: 20,
        backgroundColor: 'white',
      }}
    >
      <View style={styles.card}>
        <View style={styles.leftSection}>
          <HeartSVG color="black" />
          <Text style={styles.text}>Live streaming now..</Text>
        </View>
        <TouchableOpacity onPress={onJoin} style={styles.joinButton}>
          <Text style={styles.joinText}>Join</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default StreamLiveCard;

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    backgroundColor: '#f6f6f6',
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  icon: {
    marginRight: 8,
  },
  text: {
    fontSize: 16,
    color: '#000',
    fontWeight: '500',
  },
  joinButton: {
    backgroundColor: '#EEE6FF', // very light purple
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  joinText: {
    color: colors._7155C3_purple, // main purple
    fontWeight: '600',
    fontSize: 15,
  },
});
