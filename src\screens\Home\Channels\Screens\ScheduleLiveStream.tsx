import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';

import DateTimeWheel from '../components/DateTimeWheel';
import { useLiveStreamController } from '../../../../hooks/channels/useLiveStreamController';
import { ScrollView, Text, TextInput } from 'react-native-gesture-handler';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { navigationRef } from '../../../../navigation/RootContainer';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import StreamToggleOption from '../components/StreamToggle';
import { initializeStream, StreamType } from '../../../../api/Chatspace/chatspace.api';
import GalleryImgSvg from '../../../../assets/svgIcons/GalleryImgSvg';
import DollarCircle from '../../../../assets/svgIcons/DollarCircle';
import ImageCropPicker from 'react-native-image-crop-picker';
import {
  ConversationType,
  MessageType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import { RouteProp, useRoute } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import { zIndex } from '../../../../utils/Filters';
import { uploadFiles } from '../../../../utils/ApiService';
import { ChatService } from '../../../../service/ChatService';
import { ComposedMessage } from '../../../../types/index.types';
import { useMe } from '../../../../hooks/util/useMe';

export type ScreenProps = {
  streamDetailsData: {
    conversation: IConversation;
  };
};

const ScheduleLiveStreamScreen = () => {
  const { params } = useRoute<RouteProp<ScreenProps, 'streamDetailsData'>>();

  const { user } = useMe();
  const selectedDateRef = useRef<Date>(null);
  const [loading, setLoading] = useState(false);
  const {
    streamingModalState,
    streamDetails: liveStreamData,
    setStreamDetails,
    openModal,
    resetStreamingState,
    joinStream,
  } = useLiveStreamController({
    type: ConversationType.CHANNEL,
    Conversation: params.conversation,
  });

  const initialComposedMessage: ComposedMessage = {
    senderId: '',
    receiverId: params.conversation.chatSpaceId || '',
    isSilent: false,
    scheduledAt: undefined,
    conversationType: ConversationType.CHANNEL,
    messageType: MessageType.TEXT,
    text: '',
  };

  function handlePickImage() {
    ImageCropPicker.openPicker({
      cropping: true,
    }).then((image) => {
      console.log(image);
      setStreamDetails((prev) => {
        return {
          ...prev,
          thumbnailImage: image,
        };
      });
    });
  }

  async function handleStartLive(scheduledTime: Date) {
    try {
      if (!user) {
        return;
      }

      setLoading(true);
      console.log('liveStreamData', liveStreamData);
      let thumbnailUrl = '';
      if (liveStreamData.thumbnailImage && liveStreamData.thumbnailImage.path) {
        const data: any = await uploadFiles(liveStreamData.thumbnailImage);
        thumbnailUrl = data?.url || '';
      }

      const resp = await initializeStream({
        ...liveStreamData,
        thumbnail: thumbnailUrl,
        isScheduled: true,
        scheduledTime: scheduledTime,
      });

      const payload: ComposedMessage = {
        ...initialComposedMessage,
        senderId: user._id,
        scheduledAt: scheduledTime.getTime(),
        // @ts-ignore
        text: `Livestream scheduled for ${scheduledTime.toLocaleString()}`,
      };

      ChatService.sendMessage(payload);
      Toast.show({
        type: 'success',
        text1: 'Livestream scheduled successfully',
      });
      navigationRef.goBack();

      resetStreamingState();
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  }

  async function handleScheduleLiveStream() {
    const MinTimeGap = 1000 * 60 * 15; // 15 minutes
    const MaxTimeGap = 1000 * 60 * 60 * 24 * 7; // 7 days
    const selectedDate = selectedDateRef.current;
    if (!selectedDate) {
      Toast.show({
        type: 'error',
        text1: 'Please select a date and time',
      });
      return;
    }
    const currentTime = new Date();
    const timeGap = selectedDate.getTime() - currentTime.getTime();
    if (timeGap < MinTimeGap) {
      Toast.show({
        type: 'error',
        text1: 'Please select a future date and time. Minimum time gap is 15 minutes. ',
      });
      return;
    }
    if (timeGap > MaxTimeGap) {
      Toast.show({
        type: 'error',
        text1: 'Please select a date within 7 days',
      });
      return;
    }
    handleStartLive(selectedDate);
  }

  return (
    <View
      style={{
        ...styles.container,
        backgroundColor: colors.mainPurple,
      }}
    >
      <StatusBar backgroundColor="#7c3aed" barStyle="light-content" />
      <View style={styles.headerMainView}>
        <View style={styles.header}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
            <TouchableOpacity onPress={() => navigationRef.goBack()}>
              <BackArrowSVG size={25} />
            </TouchableOpacity>
            <Text style={{ fontSize: 18, fontWeight: '500', color: colors.white }}>
              Schedule Livestream
            </Text>
          </View>
        </View>
      </View>
      <View
        style={{
          flex: 1,
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 30,
        }}
      >
        <View>
          <DateTimeWheel
            date={new Date()}
            onChange={(date) => {
              selectedDateRef.current = date;
            }}
          />
        </View>
        <ScrollView>
          {/* Target Amount */}
          <TouchableOpacity style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Enter target amount"
              keyboardType="numeric"
              placeholderTextColor="black"
              value={liveStreamData.targetAmount?.toString()}
              onChangeText={(text) => {
                setStreamDetails((prev) => {
                  return {
                    ...prev,
                    targetAmount: Number(text),
                  };
                });
              }}
            />
            <DollarCircle />
          </TouchableOpacity>

          {/* Cause */}
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Enter the cause for"
              placeholderTextColor="black"
              value={liveStreamData.streamCause?.toString()}
              onChangeText={(text) => {
                setStreamDetails((prev) => {
                  return {
                    ...prev,
                    streamCause: text,
                  };
                });
              }}
            />
          </View>

          {/* Upload Thumbnail */}
          {/* implement image upload  */}
          <TouchableOpacity style={styles.inputContainer} onPress={handlePickImage}>
            <TextInput
              style={styles.input}
              placeholder="Upload thumbnail"
              placeholderTextColor="black"
              editable={false}
            />
            <TouchableOpacity style={styles.iconButton}>
              <GalleryImgSvg />
            </TouchableOpacity>
          </TouchableOpacity>
          <View style={styles.toggleContainer}>
            <StreamToggleOption
              label="Allow comments"
              value={liveStreamData.allowChat}
              onToggle={(val) => setStreamDetails((prev) => ({ ...prev, allowChat: val }))}
            />
            <StreamToggleOption
              label="Allow gifts"
              value={liveStreamData.allowGifts}
              onToggle={(val) => setStreamDetails((prev) => ({ ...prev, allowGifts: val }))}
            />
            <StreamToggleOption
              label="Private Stream"
              value={liveStreamData.streamType === StreamType.private ? true : false}
              onToggle={(val) => {
                const messageType: StreamType = val ? StreamType.private : StreamType.public;
                setStreamDetails((prev) => ({ ...prev, streamType: messageType }));
              }}
            />
          </View>
        </ScrollView>
        <View
          style={{
            width: '100%',
            // zIndex: zIndex.level_10,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              console.log('hello');
              handleScheduleLiveStream();
            }}
            style={{
              backgroundColor: colors.mainPurple,
              paddingHorizontal: 20,
              paddingVertical: 20,
              borderRadius: 20,
              width: '100%',
              alignItems: 'center',
            }}
          >
            <Text style={{ color: colors.white, fontSize: 16, fontWeight: '700' }}>Schedule</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default ScheduleLiveStreamScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  iconButton: {
    padding: 6,
    backgroundColor: '#EDEDED',
    borderRadius: 10,
    marginLeft: 8,
  },
  headerMainView: {
    paddingHorizontal: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1.5),
    height: hp(13),
    backgroundColor: colors.mainPurple,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 30,
  },
  toggleContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  input: {
    flex: 1,
    padding: 12,
    borderRadius: 12,

    fontSize: 16,
    color: 'black',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.gray_cc,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 12,
  },
});
