import {
  Alert,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ModalWrapper from '../ModalWrapper';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import RenderRadioButton from '../RenderRadioButton';
import ButtonPurple from '../ButtonPurple';
import Input from '../Input';
import Contacts from 'react-native-contacts';

interface IProps {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
  data?: any;
}

const SaveContactModal = ({ isVisible, setIsVisible, data }: IProps) => {
  const { t } = useTranslation();

  const [_isVisible, _setIsVisible] = useState(isVisible);
  const [isSelected, setIsSelected] = useState('0');

  const [name, setName] = useState('');
  const [number, setNumber] = useState('');

  useEffect(() => {
    _setIsVisible(isVisible);
  }, [isVisible]);

  useEffect(() => {
    setName(data?.opponentDetails?.name);
    setNumber(data?.opponentDetails?.phoneNumber);
  }, []);

  const onClose = () => {
    _setIsVisible(false);
    setIsVisible(!_isVisible);
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_CONTACTS,
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true; // iOS permissions handled via Info.plist
  };

  const addNewContact = async (name: string, phoneNumber: string) => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Denied', 'Cannot add contacts without permission.');
        return;
      }

      const newContact = {
        givenName: name,
        phoneNumbers: [{ label: 'mobile', number: phoneNumber }],
      };

      console.log('newContact', newContact);
      Contacts.addContact(newContact)
        .then(() => {
          Alert.alert('Success', 'Contact added successfully!');
          onClose();
        })
        .catch((error) => {
          Alert.alert('Error', 'Failed to add contact.');
        });
    } catch (error) {
      console.error('Error adding contact:', error);
    }
  };

  const updateExistingContact = async () => {
    const contactName = name;
    const newNumber = number;

    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Cannot modify contacts without permission.');
      return;
    }

    // Search for the contact by name
    Contacts.getContactsMatchingString(contactName)
      .then((contacts) => {
        console.log('contacts', contacts);
        if (contacts.length === 0) {
          Alert.alert('Not Found', 'No contact found with the specified name.');
          return;
        }

        // Assuming the first match is the correct contact
        const contact = contacts[0];

        // Check if the number already exists
        const exists = contact.phoneNumbers.some((phone) => phone.number === newNumber);
        if (exists) {
          Alert.alert('Duplicate', 'This number already exists for the contact.');
          return;
        }

        // Add the new number
        contact.phoneNumbers.push({ label: 'mobile', number: newNumber });

        // Update the contact
        Contacts.updateContact(contact)
          .then(() => {
            Alert.alert('Success', `Number added to ${contactName}.`);
            onClose();
          })
          .catch(() => {
            Alert.alert('Error', 'Failed to update contact.');
          });
      })
      .catch(() => {
        Alert.alert('Error', 'Failed to search contacts.');
      });
  };

  const RenderRow = ({ title, value, onPress }: any) => {
    return (
      <TouchableOpacity onPress={onPress} style={{ ...styles.row, gap: 10 }}>
        <RenderRadioButton type={'light'} value={value} />
        <Text style={styles.renderTitle}>{title}</Text>
      </TouchableOpacity>
    );
  };
  return (
    <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
      <View style={styles.container}>
        <Text style={styles.title}>{t('Save contact')}</Text>
        <View style={styles.line} />

        <View style={styles.row}>
          <RenderRow
            title={t('Save contact')}
            value={isSelected == '0'}
            onPress={() => setIsSelected('0')}
          />
          <RenderRow
            title={t('Add to existing')}
            value={isSelected == '1'}
            onPress={() => setIsSelected('1')}
          />
        </View>
        <View style={{ gap: hp(2), paddingBottom: 20 }}>
          <Input title={t('Name')} value={name} onChangeText={setName} />
          <Input title={t('Mobile number')} value={number} onChangeText={setNumber} />
        </View>

        <ButtonPurple
          title={t('Save')}
          onPress={() => {
            if (isSelected == '0') {
              addNewContact(name, number);
            } else {
              updateExistingContact();
            }
          }}
        />
      </View>
    </ModalWrapper>
  );
};

export default SaveContactModal;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: hp(2),
  },
  title: {
    ...commonFontStyle(600, 18, colors.black_23),
  },
  line: {
    height: 1,
    backgroundColor: colors._DADADA_gray,
    marginVertical: hp(2),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingHorizontal: hp(3),
    paddingVertical: hp(2),
    gap: 20,
  },
  renderTitle: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
});
