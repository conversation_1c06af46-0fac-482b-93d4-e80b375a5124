import React, { useState, useMemo, useEffect } from 'react';
import {
  FlatList,
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  NativeSyntheticEvent,
  NativeTouchEvent,
  useWindowDimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';

import CommonView from '../../../component/CommonView';
import SearchInput from '../../../component/SearchInput';
import { hp, commonFontStyle, wp } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import { IMAGES } from '../../../assets/Images';
import { addChatsSpaceMember, assignMemberRole } from '../../../api/Chatspace/chatspace.api';
import { ChatService } from '../../../service/ChatService';
import { ChannelType, ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import MemberActionsModal, { ModalOption } from './reUsableComponents/MemberActionModal';
import { useMe } from '../../../hooks/util/useMe';
import { IGroupMember, IGroupMembersData } from '../../../service/ChatSpacesService';
import useConversationInfo from '../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../types/chats.types';

type GroupMembersScreenParams = {
  GroupMembersScreen: {
    membersData: IGroupMembersData;
    id: string;
  };
};

const GroupMembersScreen = () => {
  const route = useRoute<RouteProp<GroupMembersScreenParams, 'GroupMembersScreen'>>();
  const { t } = useTranslation();
  const { height: screenHeight } = useWindowDimensions();
  const { user: myself } = useMe();

  const { membersData, id } = route.params || {};
  const navigation = useNavigation();

  const chatSpaceId = id;

  const { conversationInfo } = useConversationInfo(chatSpaceId) || {};

  const [members, setMembers] = useState<IGroupMember[]>([]);
  const [search, setSearch] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMember, setSelectedMember] = useState<IGroupMember | null>(null);
  const [menuPosition, setMenuPosition] = useState<{
    top?: number;
    bottom?: number;
    right: number;
  }>({ right: 0 });

  useEffect(() => {
    if (membersData?.members) {
      setMembers(membersData.members);
    }
  }, [membersData]);

  const formattedMembers: IGroupMember[] = useMemo(() => {
    return members;
  }, [members]);

  const filteredMembers = useMemo(() => {
    if (!search) {
      return formattedMembers;
    }
    const lowercasedSearch = search.toLowerCase();
    return formattedMembers.filter((member) =>
      member.user.name?.toLowerCase().includes(lowercasedSearch),
    );
  }, [formattedMembers, search]);

  const handleOpenModal = (item: IGroupMember, event: NativeSyntheticEvent<NativeTouchEvent>) => {
    const currentUserRole = conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.membershipStatus : null;

    const selectedMemberRole = item.role;

    if (currentUserRole === MembershipStatus.MEMBER) return;

    if (item.userId === myself?._id) return;

    if (selectedMemberRole === 'owner') return;

    if (currentUserRole === MembershipStatus.ADMIN && selectedMemberRole === 'admin') return;

    const { pageY } = event.nativeEvent;
    setSelectedMember(item);
    const MODAL_ESTIMATED_HEIGHT = 250;
    if (pageY > screenHeight - MODAL_ESTIMATED_HEIGHT) {
      setMenuPosition({ bottom: screenHeight - pageY, right: wp(5) });
    } else {
      setMenuPosition({ top: pageY, right: wp(5) });
    }
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
    setSelectedMember(null);
  };

  const handleMemberAction = async (action: string, member: IGroupMember) => {
    if (!member?.userId || !chatSpaceId) return;

    try {
      switch (action) {
        case 'remove':
          const res = await addChatsSpaceMember(chatSpaceId, [], [member.userId]);
          if (res?.systemMessage) {
            ChatService.onIncomingMessage(res.systemMessage);
            navigation.goBack();
          }
          setMembers((prev) => prev.filter((m) => m.userId !== member.userId));
          break;

        case 'promote':
          const promoteRes = await assignMemberRole(
            chatSpaceId,
            [member.userId],
            MembershipStatus.ADMIN,
          );
          if (promoteRes) {
            setMembers((prev) =>
              prev.map((m) => (m.userId === member.userId ? { ...m, role: MembershipStatus.ADMIN } : m)),
            );
            navigation.goBack();
          }
          break;

        case 'demote':
          const demoteRes = await assignMemberRole(
            chatSpaceId,
            [member.userId],
            MembershipStatus.MEMBER,
          );
          if (demoteRes) {
            setMembers((prev) =>
              prev.map((m) =>
                m.userId === member.userId ? { ...m, role: MembershipStatus.MEMBER } : m,
              ),
            );
            navigation.goBack();
          }
          break;

        case 'permissions':
          navigateTo(SCREENS.GroupPermissions, { chatSpaceId, selectedMember: member });
          break;
      }
    } catch (error) {
      console.error(`Error handling ${action}:`, error);
    }
    handleCloseModal();
  };

  const modalOptions: ModalOption[] = useMemo(() => {
    if (!selectedMember || selectedMember.role === 'owner') {
      return [];
    }

    const options: ModalOption[] = [];

    if (selectedMember.role === MembershipStatus.MEMBER) {
      options.push(
        // {
        //   label: 'Manage Permissions',
        //   icon: 'settings',
        //   onPress: () => handleMemberAction('permissions', selectedMember),
        // },
        {
          label: 'Grant Admin Rights',
          icon: 'shield',
          onPress: () => handleMemberAction('promote', selectedMember),
        },
      );
    } else if (selectedMember.role === MembershipStatus.ADMIN) {
      options.push({
        label: 'Revoke Admin Rights',
        icon: 'user',
        onPress: () => handleMemberAction('demote', selectedMember),
      });
    }

    options.push({
      label: 'Remove from group',
      icon: 'trash-2',
      onPress: () => handleMemberAction('remove', selectedMember),
      isDestructive: true,
    });

    return options;
  }, [selectedMember]);

  return (
    <CommonView
      headerTitle={t('Group Members')}
      contentContainerStyle={{ paddingBottom: hp(2) }}
      containerStyle={{ paddingHorizontal: hp(1) }}
    >
      <View style={styles.searchView}>
        <SearchInput value={search} onChangeText={setSearch} placeholder="Search group members" />
      </View>

      <FlatList
        data={filteredMembers}
        keyExtractor={(item) => item.userId}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={(event) => handleOpenModal(item, event)}
            style={styles.memberItemContainer}
          >
            <Image
              source={item?.user?.image ? { uri: item?.user?.image } : IMAGES.profile_image}
              style={styles.memberImage}
            />
            <View style={styles.memberInfoContainer}>
              <Text style={styles.memberName}>{item?.user?.name}</Text>
              <Text style={styles.memberUsername}>@{item?.user?.username}</Text>
            </View>
            {item.role !== 'member' && (
              <View style={styles.roleBadge}>
                <Text style={styles.roleText}>{item.role === 'owner' ? 'Owner' : 'Admin'}</Text>
              </View>
            )}
          </TouchableOpacity>
        )}
      />
      <MemberActionsModal
        isVisible={modalVisible}
        onClose={handleCloseModal}
        options={modalOptions}
        position={menuPosition}
      />
    </CommonView>
  );
};

export default GroupMembersScreen;

const styles = StyleSheet.create({
  searchView: {
    paddingBottom: hp(2),
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(10),
  },
  emptyText: {
    ...commonFontStyle(400, 16, colors.gray_80),
  },
  memberItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(1.5),
    paddingVertical: hp(1.5),
  },
  memberImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: hp(2),
    backgroundColor: colors.gray_80,
  },
  memberInfoContainer: {
    flex: 1,
  },
  memberName: {
    ...commonFontStyle(600, 16, colors.black),
  },
  memberUsername: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: hp(0.5),
  },
  roleBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 14,
    backgroundColor: 'rgba(128, 128, 128, 0.08)',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray_80,
    lineHeight: 15,
    textAlign: 'center',
  },
});
