import React from "react"
import Svg, { Circle, Path, SvgProps } from "react-native-svg"

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}


function PlusSVG({ size = 27, color = "#232323", ...restProps }: IconProps) {

    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 27 27"
            fill="none"
            {...restProps}
        >
            <Circle
                cx={13.0954}
                cy={13.0954}
                r={12.3454}
                stroke={color}
                strokeWidth={1.5}
            />
            <Path
                d="M13.096 7.404c.43 0 .78.35.78.78v4.131h4.132a.78.78 0 010 1.56h-4.132v4.132a.78.78 0 01-1.56 0v-4.132H8.185a.78.78 0 010-1.56h4.131V8.184c0-.43.35-.78.78-.78z"
                fill={color}
                stroke={color}
                strokeWidth={0.467747}
            />
        </Svg>
    )
}

export default PlusSVG
