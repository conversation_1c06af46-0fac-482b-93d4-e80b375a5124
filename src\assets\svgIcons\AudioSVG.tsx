import * as React from "react";
import Svg, { G, <PERSON> } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const AudioSVG: React.FC<SvgComponentProps & React.ComponentProps<typeof Svg>> = ({
    size = 24,
    color = "gray",
    ...props
}) => {


    return (
        <Svg
            width={size}
            height={size * (23 / 20)} // maintain original aspect ratio
            viewBox="0 0 20 23"
            fill="none"
            {...props}
        >
            <G opacity={1} fill={color}>
                <Path d="M7.563 6.396a.813.813 0 00-.812-.812H3.824a6.23 6.23 0 0112.353 0h-4.551a.812.812 0 100 1.625h4.604v1.625h-4.605a.812.812 0 100 1.625h4.552a6.23 6.23 0 01-12.353 0H6.751a.813.813 0 000-1.625H3.77V7.209h2.98a.813.813 0 00.812-.813z" />
                <Path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M1.334 7.75c.449 0 .812.364.812.813v1.083a7.854 7.854 0 0015.709 0V8.563a.813.813 0 011.625 0v1.083c0 4.962-3.812 9.033-8.667 9.445v2.472a.812.812 0 11-1.625 0V19.09C4.333 18.679.521 14.608.521 9.646V8.563c0-.449.364-.813.813-.813z"
                />
            </G>
        </Svg>
    );
};

export default AudioSVG;


