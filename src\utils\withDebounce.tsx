import React, { useRef, ComponentType } from 'react';

export interface DebounceProps {
  onPress?: (...args: any[]) => void;
}

export const withDebounce = <P extends DebounceProps>(
  WrappedComponent: ComponentType<P>,
  delay = 500,
) => {
  const DebouncedComponent: React.FC<P> = (props) => {
    const lastPress = useRef(0);

    const handlePress = (...args: any[]) => {
      const now = Date.now();
      if (now - lastPress.current < delay) {
        return; // Ignore if pressed too soon
      }
      lastPress.current = now;
      props.onPress?.(...args);
    };

    return <WrappedComponent {...props} onPress={handlePress} />;
  };

  return DebouncedComponent;
};
