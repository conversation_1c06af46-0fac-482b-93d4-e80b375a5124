// import { reject } from 'lodash';
import api from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Api from './api';

import { Client } from '../lib/Client';
import { use } from 'react';

export const LoginApi = async (data) => {
  try {
    let res = await api.post('v1/auth/login', data);
    if (res?.body?.status) {
      return Promise.resolve(res.body);
    } else {
      return Promise.resolve(res.body);
    }
  } catch (error) {
    console.log(' error  = = = > ', error);
    Promise.reject(error);
  }
};

export const VerifyMisedCall = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .post('v2/auth/verify_otp', data)
        .then((response) => {
          if (response?.body?.status) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

/**
 * Uploads files to the server.
 *
 * @param {import('react-native-image-crop-picker').ImageOrVideo} file - The form data containing the files to be uploaded.
 * @returns {Promise<Object>} A promise that resolves with the server's response.
 * @throws Will reject the promise if the upload fails.
 */
export const uploadFiles = async (file) => {
  try {
    let formData = new FormData();
    formData.append('file', {
      uri: file.path,
      name: file.filename,
      type: file.mime,
    });
    let res = await api.post('upload', formData, true);
    return res.body?.data;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const uploadFile = async (formData) => {
  try {
    let res = await api.post('upload', formData, true);
    return res.body?.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

export const VerifyWhatsappOTP = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .post('v1/auth/verify_whatsapp', data)
        .then((response) => {
          if (response?.body?.status) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

export const VerifyUserName = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .post('v1/auth/verify_username', data)
        .then((response) => {
          if (response?.body?.status) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

export const setUpProfile = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .post('v1/auth/setup_profile', data)
        .then((response) => {
          if (response?.body?.status) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

export const UploadContactsApi = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .post('v1/users/contacts', data)
        .then((response) => {
          if (response?.body?.status) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

export const GetProfileAPI = (data) => {
  try {
    return new Promise((resolve, reject) => {
      api
        .get('v1/users/get_profile', data)
        .then((response) => {
          if (response?.body) {
            resolve(response.body);
          } else {
            resolve(response.body);
          }
        })
        .catch(reject);
    });
  } catch (error) {
    reject(error);
  }
};

export const getMe = async (refetch = false) => {
  try {
    const cachedUser = await Client.User.get();
    if (cachedUser && !refetch) return cachedUser;
    const res = await Api.get('v1/users/get_profile');
    const profile = res?.body?.data?.user;
    if (profile) {
      await Client.User.set(profile);
      return profile;
    }
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

export const blockUser = async (targetUserId) => {
  const res = await Api.post('v1/chatspaces/block', { targetUserId });
  return res;
};

export const unBlockUser = async (targetUserId) => {
  const res = await Api.post('v1/chatspaces/unblock', { targetUserId });
  return res;
};

export const reportUser = async (targetUserId, reason) => {
  try {
    const payload = {
      targetType: 'user',
      targetId: targetUserId,
      reason: reason,
    };

    const res = await Api.post('v1/reports', payload);
    console.log(res)

    if (res?.body.status === true) {
      return res.body;
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error reporting user:', error);
    return null;
  }
};

export const getUser = async (userId) => {
  try {
    const res = await Api.get(`v1/users/${userId}`);

    const profile = res?.body?.data;
    return profile ? profile : null;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

export const getExploreChannels = async (search) => {
  try {
    const res = await Api.get(
      `v1/chatspaces/exploreChannels${search ? `?search=${encodeURIComponent(search)}` : ''}`
    );

    const exploreChannels = res?.body?.data;
    return exploreChannels ? exploreChannels : null;
  } catch (error) {
    console.error('Error fetching explore channels:', error);
    throw error;
  }
};


export const getFollowersinChannels = async (chatSpaceId) => {
  try {
    const res = await Api.get(`v1/chatspaces/allFollowers/${chatSpaceId}`);

    const followersChannels = res?.body?.data;
    return followersChannels ? followersChannels : null;
  } catch (error) {
    console.error('Error fetching followers channels:', error);
    throw error;
  }
};



export const getChatspaces = async () => {
  try {
    const res = await Api.get(`v1/chatspaces/chatSpaces`);
    return res?.body?.data;
  } catch (error) {
    console.error('Error fetching all channels', error);
    return {
      groups: [],
      groupCount: 0,
      channels: [],
      channelCount: 0,
    };
  }
};

export const getMyPrivilegesForAll = async (chatSpaceIds) => {
  try {
    const params = new URLSearchParams();
    chatSpaceIds.forEach(id => params.append('chatSpaceIds', id));

    const res = await Api.get(`v1/chatspaces/privileges/all?${params.toString()}`);

    return res?.body?.data;
  } catch (error) {
    console.error('Error fetching privileges for all chat spaces', error);
  }
};


export const getPrivileges = async (chatSpaceId, userId) => {
  try {
    const res = await Api.get(`v1/chatspaces/${chatSpaceId}/privileges?userId=${userId}`);
    return res?.body?.data;
  } catch (error) {
    console.error('Error fetching my privileges', error);
  }
};

export const getTranslationText = async (text, src_lang, tgt_lang) => {
  try {
    const res = await fetch(`http://10.10.0.174:8000/translate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
        src_lang: src_lang,
        tgt_lang: tgt_lang,
      }),
    });

    if (res.ok) {
      const data = await res.json();
      return data.translation;
    } else {
      throw new Error('Failed to fetch translation');
    }
  } catch (error) {
    console.error('Error fetching translation:', error);
    throw error;
  }
};

export const callHistoryAPI = async (params) => {
  let { page, perPage, search = '', callStatus = [], callType = [] } = params;
  const searchQuery = search && `&search=${search}`;
  const callStatusQuery = callStatus.map((item) => `&callStatus=${item}`).join('') ?? '';
  const callTypeQuery = callType.map((item) => `&callType=${item}`).join('') ?? '';

  let queryParam = `?page=${page}&perPage=${perPage}${
    searchQuery + callStatusQuery + callTypeQuery
  }`;

  try {
    const response = await api.get(`v1/calls/call_history${queryParam}`);
    if (response?.body && response?.body?.status) {
      return response.body;
    }
  } catch (error) {
    throw error;
  }
};

export const sendUserFeedbackApi = async (data) => {
  try {
    let res = await api.post('v1/users/userFeedbacks', data);
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const logout = async (data) => {
  await AsyncStorage.removeItem('cb_user_me');
  await AsyncStorage.removeItem('@token');
  try {
    const response = await api.post('v1/auth/logout', data);
    return response?.body;
  } catch (error) {
    throw error;
  }
};
