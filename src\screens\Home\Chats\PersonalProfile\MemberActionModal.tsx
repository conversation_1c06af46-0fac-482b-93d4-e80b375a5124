import {
  Modal,
  TouchableWithoutFeedback,
  View,
  FlatList,
  TouchableOpacity,
  Text,
  StyleSheet,
} from 'react-native';
import { colors } from '../../../../theme/colors';
import { IGroupMember } from '../../../../service/ChatSpacesService';
import { ModalOption } from '../../Groups/reUsableComponents/MemberActionModal';

type MemberActionModalProps = {
  visible: boolean;
  selectedMember: IGroupMember | null;
  onClose: () => void;
  menuOptions: ModalOption[];
  onAction: (action: string, member: IGroupMember) => void;
};

const MemberActionModal: React.FC<MemberActionModalProps> = ({
  visible,
  selectedMember,
  onClose,
  menuOptions,
  onAction,
}) => {

  return (
    <>
      <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View>
                <FlatList
                  data={menuOptions}
                  keyExtractor={(item) => item.label}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.actionMenuItem}
                      onPress={() => {
                        if (selectedMember) {
                          onAction(item.label, selectedMember);
                          onClose();
                        }
                      }}
                    >
                      <Text style={styles.actionMenuText}>{item.label}</Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </>
  );
};

export default MemberActionModal;

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    backgroundColor: 'transparent',
  },
  actionMenu: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  actionMenuItem: {
    paddingVertical: 10,
  },
  actionMenuText: {
    fontSize: 16,
    color: colors.black,
  },
});
