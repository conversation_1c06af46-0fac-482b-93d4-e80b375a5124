import Realm from 'realm';
import { realmSchemaNames } from '../../device-storage/realm/schemas/schemaNames';
import { ContactSchema, IContact } from '../../device-storage/realm/schemas/ContactSchema';
import { useQuery, useRealm } from '../../device-storage/realm/realm';
import { IUser, UserSchema } from '../../device-storage/realm/schemas/UserSchema';

type QueryBuilder = {
  search: (query: string, fields?: string[]) => QueryBuilder;
  exclude: (numbers: (string | number)[]) => QueryBuilder;
  searchIds: (userIds: (string | number)[]) => QueryBuilder;
  get: () => IUser[];
};
export const useContacts = () => {
  const realm = useRealm();

  const registeredContacts = useQuery<UserSchema>(realmSchemaNames.user)
    .filtered('isDeviceContact == true')
    .sorted('contactName');
  const unregisteredContacts = useQuery<IContact>(realmSchemaNames.contact)
    .filtered('isRegistered == false')
    .sorted('name');
  const allContacts = useQuery<IContact>(realmSchemaNames.contact).sorted('name');

  const useContactByUserId = (userId?: string) => {
    const contact = useQuery<IContact>(realmSchemaNames.contact).filtered(
      'userId == $0',
      userId ?? '',
    )[0];
    return contact;
  };

  const useContactByPhoneNumber = (phoneNumber?: string) => {
    if (!phoneNumber) return undefined;

    const contact = useQuery<IContact>(realmSchemaNames.contact).filtered(
      'phoneNumber == $0',
      phoneNumber,
    )[0];

    return contact;
  };

  // Pure function - no write
  const pureSaveOrUpdate = (contact: Partial<IContact> & { phoneNumber: string }): void => {
    const existing = realm.objectForPrimaryKey<IContact>(
      realmSchemaNames.contact,
      contact.phoneNumber,
    );
    const contactData: IContact = existing
      ? { ...existing, ...contact }
      : {
          phoneNumber: contact.phoneNumber,
          name: contact.name ?? '',
          // username: contact.username ?? '',
          // image: contact.image ?? '',
          // bio: contact.bio ?? '',
          isRegistered: contact.isRegistered ?? false,
          // userId: contact.userId ?? '',
          isInDevice: contact.isInDevice ?? false,
        };
    realm.create(realmSchemaNames.contact, contactData, Realm.UpdateMode.Modified);
  };

  const saveOrUpdate = async (
    contact: Partial<IContact> & { phoneNumber: string },
  ): Promise<void> => {
    try {
      realm.write(() => {
        pureSaveOrUpdate(contact);
      });
    } catch (error) {
      console.error('Error saving contact:', error);
      throw error;
    }
  };

  const saveOrUpdateMany = async (
    contacts: (Partial<IContact> & { phoneNumber: string })[],
  ): Promise<void> => {
    try {
      realm.write(() => {
        contacts.forEach((contact) => {
          pureSaveOrUpdate(contact);
        });
      });
    } catch (error) {
      console.error('Error saving multiple contacts:', error);
      throw error;
    }
  };

  const deleteContact = async (phoneNumber: string): Promise<void> => {
    try {
      realm.write(() => {
        const contact = realm.objectForPrimaryKey<IContact>(realmSchemaNames.contact, phoneNumber);
        if (contact) {
          realm.delete(contact);
        }
      });
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  };

  const createFilteredContactsQuery = (): QueryBuilder => {
    let results = registeredContacts;

    return {
      search(query: string, fields: string[] = ['contactName']) {
        if (query.trim()) {
          const conditions = fields.map((f) => `${f} CONTAINS[c] $0`).join(' OR ');
          results = results.filtered(conditions, query.trim());
        }
        return this;
      },

      searchIds(userIds: (string | number)[]) {
        if (userIds.length > 0) {
          results = results.filtered('userId IN $0', userIds);
        }
        return this;
      },

      exclude(numbers: (string | number)[]) {
        if (numbers.length > 0) {
          results = results.filtered('NOT phoneNumber IN $0', numbers);
        }
        return this;
      },

      get() {
        return Array.from(results);
      },
    };
  };

  const deleteAllContacts = async (): Promise<void> => {
    try {
      realm.write(() => {
        const allContacts = realm.objects<IContact>(realmSchemaNames.contact);
        realm.delete(allContacts);
      });
    } catch (error) {
      console.error('Error deleting all contacts:', error);
      throw error;
    }
  };

  const getContactByUserId = async (userId: string): Promise<IContact | undefined> => {
    const contact = realm
      .objects<IContact>(realmSchemaNames.contact)
      .filtered('userId == $0', userId)[0];

    return contact;
  };

  return {
    saveOrUpdate,
    saveOrUpdateMany,
    pureSaveOrUpdate, // expose internally if needed elsewhere
    registeredContacts,
    unregisteredContacts,
    deleteContact,
    deleteAllContacts,
    getContactByUserId,
    allContacts,
    useContactByUserId,
    useContactByPhoneNumber,
    createFilteredContactsQuery,
  };
};
