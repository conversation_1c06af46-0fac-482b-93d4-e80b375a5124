import React, { useState, useCallback } from 'react';
import { StyleSheet } from 'react-native';
import { Canvas, Text as SkiaText, useFont } from '@shopify/react-native-skia';
import { GestureHandlerRootView, PanGestureHandler } from 'react-native-gesture-handler';

interface TextElement {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
}

interface CanvasWithTextProps {
  texts: TextElement[];
}

const CanvasWithText: React.FC<CanvasWithTextProps> = ({ texts }) => {
  const [updatedTexts, setUpdatedTexts] = useState<TextElement[]>(texts);
  const font = useFont(require('../Assets/Fonts/BadScript-Regular.ttf'), 16); // Use your desired font

  // Update the text position when dragged
  const updateTextPosition = (id: string, dx: number, dy: number) => {
    setUpdatedTexts((prevTexts) =>
      prevTexts.map((text) => {
        if (text.id === id) {
          return { ...text, x: text.x + dx, y: text.y + dy };
        }
        return text;
      }),
    );
  };

  // Gesture handling for moving text
  const handlePanGesture = useCallback((e, textId: string) => {
    const { translationX, translationY } = e.nativeEvent;
    updateTextPosition(textId, translationX, translationY);
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Canvas style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        {updatedTexts.map(({ id, text, x, y, fontSize, color }) => (
          <PanGestureHandler
            key={id}
            onGestureEvent={(e) => handlePanGesture(e, id)} // Pass the id to the handler
          >
            <SkiaText x={x} y={y} fontSize={fontSize} color={color} font={font} text={text} />
          </PanGestureHandler>
        ))}
      </Canvas>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
});

export default CanvasWithText;
