import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const CloseFilledSVG: React.FC<SvgComponentProps> = ({
    size = 24,
    color = "black",
    ...props
}) => {


    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 1024 1024"
            fill="none"
            {...props}
        >
            <Path
                d="M512 64a448 448 0 110 896 448 448 0 010-896zm0 393.664L407.936 353.6a38.4 38.4 0 10-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1054.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1054.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 10-54.336-54.336L512 457.664z"
                fill={color}
            />
        </Svg>
    );
};

export default CloseFilledSVG;


