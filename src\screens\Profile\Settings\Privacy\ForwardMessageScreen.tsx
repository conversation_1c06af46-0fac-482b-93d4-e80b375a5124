import React from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';

const ForwardMessageScreen = () => (
  <SafeAreaView style={styles.container}>
    <HeaderBackWithTitle title="Forward message" />
    <View style={styles.whiteContainer} />
  </SafeAreaView>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
});

export default ForwardMessageScreen;
