import * as React from "react";
import Svg, { Circle, Path, SvgProps } from "react-native-svg";
import { colors } from "../../theme/colors";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
  backgroundColor?: string;
  showBackground?: boolean;
}

const SendSVG: React.FC<IconProps> = ({
  size = 50,
  backgroundColor = colors.mainPurple,
  color = colors.white,
  showBackground = true,
}) => {

  // If no background, use a smaller icon size and viewBox
  const actualSize = showBackground ? size : size * 0.5;
  const viewBox = showBackground ? "0 0 50 50" : "0 0 24 24";
  const pathProps = showBackground
    ? {}
    : { transform: "translate(-13, -13) scale(0.95)" };

  return (
    <Svg width={actualSize} height={actualSize} viewBox={viewBox} fill="none">
      {showBackground && <Circle cx={25} cy={25} r={25} fill={backgroundColor} />}
      <Path
        d="M17.009 16L17 23l6.213 2L17 27l.009 7L36 25l-18.991-9z"
        fill={color}
        {...pathProps}
      />
    </Svg>
  );
};

export default SendSVG;
