import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import CommonView from '../../../../component/CommonView';
import { useMe } from '../../../../hooks/util/useMe';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import useConversations from '../../../../hooks/conversations/useConversations';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import useConversationInfo from '../../../../device-storage/realm/hooks/useConversationInfo';
import ConversationSettings from '../PersonalProfile/ConversationSettings';
import ProfileActionButtons from '../PersonalProfile/ProfileActionButtons';
import ProfileRightHeader from '../PersonalProfile/ProfileRightHeader';
import UserActions from '../PersonalProfile/UserActions';
import UserCommonGroups from '../PersonalProfile/UserCommonGroups';
import ConversationSharedFiles from '../PersonalProfile/ConversationSharedFiles';
import GroupMembers from '../PersonalProfile/GroupMembers';
import ConversationHeader from '../PersonalProfile/ConversationHeader';
import DeleteChatSpace from '../PersonalProfile/DeleteChatSpace';

export type PersonalProfileScreenScreenT = {
  convId: string;
};

type PersonalProfileScreenParams = {
  PersonalProfileScreen: PersonalProfileScreenScreenT;
};

const PersonalProfileScreen = () => {
  const { t } = useTranslation();
  const route = useRoute<RouteProp<PersonalProfileScreenParams, 'PersonalProfileScreen'>>();
  const { user: myself } = useMe();
  const { convId } = route.params;

  const conversationId = convId;

  const { conversationInfo } = useConversationInfo(conversationId as string);

  const currentUserRole =
    conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.membershipStatus : null;

  const { getConversattionById } = useConversations();
  const currentConversation: IConversation = getConversattionById(conversationId as string);

  const isP2P = conversationInfo?.type === ConversationType.P2P;
  const isGroup = conversationInfo?.type === ConversationType.GROUP;
  const isChannel = conversationInfo?.type === ConversationType.CHANNEL;

  const getHeaderTitle = () => {
    switch (conversationInfo?.type) {
      case ConversationType.P2P:
        return t('Profile Details');
      case ConversationType.GROUP:
        return t('Group Details');
      case ConversationType.CHANNEL:
        return t('Channel Details');
      default:
        return '';
    }
  };

  return (
    <CommonView
      headerTitle={getHeaderTitle()}
      renderRight={() => (
        <ProfileRightHeader
          conversationInfo={conversationInfo}
          currentConversation={currentConversation}
        />
      )}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Header - Profile Image, Name, Description */}
        <ConversationHeader conversationInfo={conversationInfo} />

        {/* Message, Audio Call Buttons */}
        {<ProfileActionButtons conversationInfo={conversationInfo} myId={myself?._id as string} />}

        {/* Shared Files */}
        <ConversationSharedFiles conversationId={conversationId} />

        {/* Notifications(Mute and Unmute), Disappearing Messages, Translate Messages */}
        <ConversationSettings conversationInfo={conversationInfo} />

        {/* Group Members */}
        {isGroup && (
          <GroupMembers
            conversationInfo={conversationInfo}
            myId={myself?._id}
            currentConversation={currentConversation}
          />
        )}

        {/* Common Groups */}
        {isP2P && <UserCommonGroups conversationInfo={conversationInfo} myId={myself?._id} />}

        {/* Delete Chatspace - only for Group and Channel and only for Owner */}
        {currentUserRole === 'owner' && !isP2P && (
          <DeleteChatSpace conversationInfo={conversationInfo} />
        )}

        {/* Block, Unblock and Report User - only for P2P */}
        {isP2P && <UserActions conversationInfo={conversationInfo} />}
      </ScrollView>
    </CommonView>
  );
};

export default PersonalProfileScreen;

const styles = StyleSheet.create({
  scrollContainer: {
    paddingBottom: 100,
  },
  section: {
    marginBottom: 10,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
});
