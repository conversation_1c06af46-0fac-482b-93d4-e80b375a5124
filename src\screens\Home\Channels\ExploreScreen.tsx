import React, { useEffect, useMemo, useState } from 'react';
import {
  FlatList,
  View,
  Text,
  StyleSheet,
  ToastAndroid,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import ExploreChannelCard from '../All/ExploreChannelCard';
import useConversations from '../../../hooks/conversations/useConversations';
import { getExploreChannels } from '../../../utils/ApiService';
import { ChannelType, ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { ChatService } from '../../../service/ChatService';
import { IExploreChannelCardItem } from '../../../device-storage/realm/schemas/ConversationSchema';
import { navigateTo, showToast } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import useSocket from '../../../socket-client/useSocket';
import { ChatSocket } from '../../../socket-client/ChatSocket';
import CommonView from '../../../component/CommonView';
import SearchInput from '../../../component/SearchInput';
import { hp } from '../../../theme/fonts';
import { useDebounce } from 'use-debounce';
import { colors } from '../../../theme/colors';
import { MembershipStatus } from '../../../types/chats.types';
import { getDefaultChatPermissions } from '../../../lib/chatLib';
import { ChatScreenParams } from '../Chats/ChatSpecificScreen';
import { ConversationInfo } from '../../../device-storage/realm/hooks/useConversationInfo';

const ExploreScreen = () => {
  const { socket } = useSocket();
  const { unArchivedConversations } = useConversations();

  const followedChannels = useMemo(
    () => unArchivedConversations.filter((c) => c.type === ConversationType.CHANNEL),
    [unArchivedConversations],
  );

  // Raw explore channels from API
  const [rawExploreChannels, setRawExploreChannels] = useState<IExploreChannelCardItem[]>([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [debouncedSearch] = useDebounce(search, 500);

  useEffect(() => {
    const fetchExplore = async () => {
      try {
        setLoading(true);
        const data = await getExploreChannels(debouncedSearch);
        if (data) {
          setRawExploreChannels(data.data);
        } else {
          setRawExploreChannels([]);
        }
      } catch (error) {
        console.error('Failed to fetch explore channels:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchExplore();
  }, [debouncedSearch]);

  const exploreChannels = useMemo(() => {
    return rawExploreChannels.filter(
      (channel) => !followedChannels.some((fc) => fc.chatSpaceId === channel.chatSpaceId),
    );
  }, [rawExploreChannels, followedChannels]);

  const handleFollowChannel = (channel: IExploreChannelCardItem) => {
    const chatSpaceId = channel.chatSpaceId;
    if (!chatSpaceId) return;

    ChatSocket.joinChannel(socket, chatSpaceId, async (response) => {
      if (response?.error) {
        console.error('Follow failed due to socket error:', response.error);
        return;
      }
      try {
        // ChatService.createChatSpace({
        //   _id: channel._id,
        //   name: channel.name ?? (channel.displayName as string),
        //   type: ConversationType.CHANNEL,
        //   description: channel.description ?? '',
        //   displayPic: channel.displayPic,
        //   isPrivate: false,
        //   inviteLink: null,
        //   createdBy: '',
        //   chatSpaceId: channel.chatSpaceId as string,
        //   createdAt: new Date().toISOString(),
        //   updatedAt: new Date().toISOString(),
        //   role: ChannelType.MEMBER,
        //   inviteCode: channel.inviteCode ?? '',
        //   isLiveStreaming: channel.isLiveStreaming ?? false,
        //   memberCount: channel.memberCount ?? 0,
        //   lastMessageTimestamp: Date.now(),
        //});
        ChatService.onIncomingMessage(response);
        ChatService.updateMemberCount(chatSpaceId, 1, 0);
        setRawExploreChannels((prev) => prev.filter((c) => c.chatSpaceId !== chatSpaceId));
        showToast(`You are now following ${channel.name}`);
      } catch (error) {
        console.error('Error while creating chatspace:', error);
      }
    });
  };

  const handleUnfollowChannel = (channel: IExploreChannelCardItem) => {
    const chatSpaceId = channel.chatSpaceId;
    if (!chatSpaceId) return;

    ChatSocket.emitLeaveChatspace(socket, chatSpaceId, async (response) => {
      if (response?.error) {
        console.error('Unfollow failed due to socket error:', response.error);
        return;
      }
      try {
        await ChatService.deleteConversation(chatSpaceId);
        socket?.emit('chatspace_deleted', { chatSpaceId });

        setRawExploreChannels((prev) => {
          const exists = prev.some((c) => c.chatSpaceId === chatSpaceId);
          return exists ? prev : [channel, ...prev];
        });
        showToast(`You unfollowed ${channel.name}`);
      } catch (error) {
        console.error('Error unfollowing channel:', error);
        showToast('Failed to unfollow channel');
      }
    });
  };

  const handleExplorePress = (item: any) => {
    const convInfo: ConversationInfo = {
      id: item?.chatSpaceId as string,
      displayName: item?.name as string,
      displayPic: item.displayPic,
      isPrivate: item?.isPrivate as boolean,
      bio: item?.description as string,
      memberCount: item?.memberCount as number,
      type: item?.type as ConversationType,
      isLoading: false,
      isFromCache: true,
      conversationSettings: null,
      membershipStatus: MembershipStatus.DISCOVERING,
      permissions: getDefaultChatPermissions(
        MembershipStatus.DISCOVERING,
        item?.type as ConversationType,
      ),
      inviteCode: item?.inviteCode || '',
    };

    const paramsData: ChatScreenParams = {
      convId: item?.chatSpaceId as string,
      initialConversationInfo: convInfo,
    };
    navigateTo(SCREENS.ChatSpecificScreen, {
      userData: {
        chatSpaceId: item.chatSpaceId,
        id: item._id!,
        displayName: item.displayName ?? item.name,
        displayPic: item.displayPic ?? '',
        type: ConversationType.CHANNEL,
        conversation: {
          role: 'member',
          description: item.description || '',
          memberCount: item.memberCount || 0,
        },
      },
      isExploreScreen: true,
      data: paramsData,
    });
  };

  return (
    <CommonView headerTitle="Explore">
      <View style={styles.container}>
        <View style={styles.searchView}>
          <SearchInput
            value={search}
            onChangeText={(text) => {
              setSearch(text);
            }}
          />
        </View>
        {loading ? (
          <ActivityIndicator size="large" color={colors.mainPurple} style={{ marginTop: 20 }} />
        ) : (
          <FlatList
            data={exploreChannels}
            keyExtractor={(item) => item.chatSpaceId as string}
            renderItem={({ item }) => {
              const isFollowing = followedChannels.some(
                (fc) => fc.chatSpaceId === item.chatSpaceId,
              );
              return (
                <ExploreChannelCard
                  item={item}
                  isFollowing={isFollowing}
                  onFollowPress={() =>
                    isFollowing ? handleUnfollowChannel(item) : handleFollowChannel(item)
                  }
                  onPress={() => handleExplorePress(item)}
                />
              );
            }}
            ListEmptyComponent={<Text style={styles.emptyText}>No explore channels found.</Text>}
            contentContainerStyle={{ paddingBottom: 60 }}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </CommonView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    color: '#999',
  },
  searchView: {
    paddingHorizontal: hp(2),
    paddingBottom: hp(2),
  },
});

export default ExploreScreen;
