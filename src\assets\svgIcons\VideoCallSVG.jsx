import React from "react"
import Svg, { Path } from "react-native-svg"

function VideoCallSVG({ size = 24, color = "#fff" }) {


    return (
        <Svg
            width={size}
            height={(size * 20) / 24} // maintains aspect ratio
            viewBox="0 0 24 20"
            fill="none"
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.238.52h.106c1.736 0 3.11 0 4.199.13 1.117.131 2.036.408 2.804 ************.5.453.713.713.63.768.908 1.688 1.04 2.804.03.251.052.518.07.8.851-.424 1.573-.773 2.174-.97.706-.23 1.455-.324 2.145.103s.942 1.138 1.051 1.873c.105.708.105 1.642.105 2.763v.45c0 1.121 0 2.055-.105 2.763-.109.735-.36 1.446-1.05 1.873-.691.426-1.44.333-2.146.102-.601-.196-1.323-.545-2.174-.97-.018.283-.04.55-.07.8-.132 1.117-.41 2.037-1.04 2.805-.213.26-.452.5-.713.713-.768.63-1.687.907-2.804 1.04-1.088.128-2.463.128-4.199.128h-.106c-1.736 0-3.11 0-4.199-.128-1.117-.133-2.036-.41-2.804-1.04-.26-.214-.5-.453-.713-.713-.63-.768-.908-1.688-1.04-2.805-.129-1.087-.128-2.463-.128-4.198v-1.19c0-1.736 0-3.11.128-4.199.132-1.116.41-2.036 1.04-2.804.213-.26.452-.5.713-.713.768-.63 1.687-.908 2.804-1.04C6.127.521 7.502.521 9.238.521zm7.366 10.022V9.458c0-1.8-.002-3.08-.118-4.06-.114-.964-.328-1.534-.682-1.965a3.524 3.524 0 00-.488-.488c-.43-.353-1-.568-1.964-.682-.981-.116-2.26-.117-4.061-.117-1.8 0-3.08.001-4.061.117-.964.114-1.534.329-1.964.682a3.52 3.52 0 00-.488.488c-.354.43-.568 1-.682 1.964-.116.982-.117 2.26-.117 4.061v1.084c0 1.8 0 3.08.117 4.06.114.964.328 1.534.682 1.965.146.178.31.342.488.488.43.353 1 .568 1.964.682.981.116 2.26.117 4.061.117 1.8 0 3.08-.001 4.061-.117.963-.114 1.534-.329 1.964-.682.178-.146.342-.31.488-.488.354-.43.568-1 .682-1.964.116-.982.117-2.26.117-4.061zm1.625 1.664l.264.132c1.078.54 1.805.9 2.357 1.08.54.178.704.11.785.06.08-.05.214-.165.298-.728.085-.575.087-1.386.087-2.592v-.316c0-1.206-.002-2.017-.087-2.592-.084-.563-.218-.679-.298-.729-.08-.05-.244-.117-.785.06-.553.18-1.28.542-2.357 1.08l-.264.133V12.206z"
                fill={color}
            />
        </Svg>
    )
}

export default VideoCallSVG
