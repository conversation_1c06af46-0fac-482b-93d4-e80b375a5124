import { emoji } from '../assets/Images';

export const default_reactions = ['😄', '🤩', '🙏', '💯'];

export const languageList = [
  {
    __v: 0,
    _id: '671bce70c93362492f54b0aa',
    key: 'en',
    logo: 'https://flagcdn.com/w320/gb.png',
    name: 'English',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b0c1',
    key: 'ar',
    logo: 'https://flagcdn.com/w320/sa.png',
    name: 'Arabic',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b0ce',
    key: 'be',
    logo: 'https://flagcdn.com/w320/by.png',
    name: 'Belarusian',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b0d9',
    key: 'ca',
    logo: 'https://flagcdn.com/w320/es.png',
    name: 'Catalan',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b0ec',
    key: 'hr',
    logo: 'https://flagcdn.com/w320/hr.png',
    name: 'Croatian',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b0f8',
    key: 'cs',
    logo: 'https://flagcdn.com/w320/cz.png',
    name: 'Czech',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b102',
    key: 'nl',
    logo: 'https://flagcdn.com/w320/nl.png',
    name: 'Dutch',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b111',
    key: 'fi',
    logo: 'https://flagcdn.com/w320/fi.png',
    name: 'Finnish',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b125',
    key: 'fr',
    logo: 'https://flagcdn.com/w320/fr.png',
    name: 'French',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b132',
    key: 'de',
    logo: 'https://flagcdn.com/w320/de.png',
    name: 'German',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b13e',
    key: 'he',
    logo: 'https://flagcdn.com/w320/il.png',
    name: 'Hebrew',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b14d',
    key: 'hu',
    logo: 'https://flagcdn.com/w320/hu.png',
    name: 'Hungarian',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b158',
    key: 'id',
    logo: 'https://flagcdn.com/w320/id.png',
    name: 'Indonesian',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b163',
    key: 'it',
    logo: 'https://flagcdn.com/w320/it.png',
    name: 'Italian',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b16f',
    key: 'kk',
    logo: 'https://flagcdn.com/w320/kz.png',
    name: 'Kazakh',
  },
  {
    __v: 0,
    _id: '671bce70c93362492f54b17a',
    key: 'ko',
    logo: 'https://flagcdn.com/w320/kr.png',
    name: 'Korean',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b188',
    key: 'ms',
    logo: 'https://flagcdn.com/w320/my.png',
    name: 'Malay',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b19c',
    key: 'no',
    logo: 'https://flagcdn.com/w320/no.png',
    name: 'Norwegian',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b1df',
    key: 'fa',
    logo: 'https://flagcdn.com/w320/ir.png',
    name: 'Persian',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b1f6',
    key: 'ru',
    logo: 'https://flagcdn.com/w320/ru.png',
    name: 'Russian',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b20b',
    key: 'sr',
    logo: 'https://flagcdn.com/w320/rs.png',
    name: 'Serbian',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b219',
    key: 'sk',
    logo: 'https://flagcdn.com/w320/sk.png',
    name: 'Slovak',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b227',
    key: 'es',
    logo: 'https://flagcdn.com/w320/es.png',
    name: 'Spanish',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b233',
    key: 'sv',
    logo: 'https://flagcdn.com/w320/se.png',
    name: 'Swedish',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b247',
    key: 'tr',
    logo: 'https://flagcdn.com/w320/tr.png',
    name: 'Turkish',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b254',
    key: 'uk',
    logo: 'https://flagcdn.com/w320/ua.png',
    name: 'Ukrainian',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b260',
    key: 'uz',
    logo: 'https://flagcdn.com/w320/uz.png',
    name: 'Uzbek',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b277',
    key: 'zh',
    logo: 'https://flagcdn.com/w320/cn.png',
    name: 'Mandarin Chinese',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b282',
    key: 'hi',
    logo: 'https://flagcdn.com/w320/in.png',
    name: 'Hindi',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b298',
    key: 'ur',
    logo: 'https://flagcdn.com/w320/pk.png',
    name: 'Urdu',
  },
  {
    __v: 0,
    _id: '671bce71c93362492f54b2a2',
    key: 'bn',
    logo: 'https://flagcdn.com/w320/bd.png',
    name: 'Bengali',
  },
];

export const userSettingDefault = {
  chatSettings: {
    opacity: '1',
    background: 'https://chatbucketcdn.lon1.digitaloceanspaces.com/1729618774185-922222782.png',
    saveToGallery: false,
    muted: false,
    pinned: false,
  },
  privacySettings: {
    lastSeeen: {
      lastSeenType: 'EVERYONE',
    },
    messageRemoveTimer: 'OFF',
    readReceipts: false,
    appLock: false,
    chatLock: false,
  },
  languageSettings: {
    text: false,
    voice: false,
    audioVideo: false,
    language: 'EN',
  },
  notificationSettings: {
    isMute: false,
  },
};

export const emojiGifList = [
  {
    id: 1,
    emoji: emoji.e1,
  },
  {
    id: 2,
    emoji: emoji.e2,
  },
];

export const convertChatData = (input: any) => {
  return {
    _id: {
      conversationWith: input?._id, // Replace with actual value if dynamic
    },
    locationSetting: input.locationSetting,
    opponentDetails: {
      _id: input?._id, // Replace with actual value if dynamic
      bio: input?.bio ?? '',
      dateOfBirth: '',
      email: input.email,
      gender: input.gender, // Replace or modify based on logic
      image: input?.image,
      isArchive: false,
      isMuted: false,
      isPinned: false,
      name: input?.locationSetting?.differentName ? input.locationSetting?.name : input.name,
      online: input?.online ?? false,
      phoneCode: input?.phoneCode ?? '',
      phoneNumber: input?.phoneNumber ?? '',
      username: input?.username ?? '',
    },
  };
};

export const musicList = [
  {
    id: 1,
    title: 'Music 1',
    music: 'https://chatbucketcdn.lon1.digitaloceanspaces.com/1736487259564-84139678.mp3',
  },
];

export const maxMembersInCall = 10;
