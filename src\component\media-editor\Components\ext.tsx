// import React, { useEffect } from "react";
// import { View } from "react-native";
// import { Canvas, Rect, Path, Skia } from "@shopify/react-native-skia";
// import { useSharedValue, withTiming, useDerivedValue } from "react-native-reanimated";

// const AnimatedProgressBar = () => {
//   const progress = useSharedValue(0);
//   const waveX = useSharedValue(0); // Wave movement

//   useEffect(() => {
//     progress.value = withTiming(300, { duration: 3000 });
//     waveX.value = withTiming(300, { duration: 3000 });
//   }, []);

//   // Derived value to update wave path
//   const wavePath = useDerivedValue(() => {
//     const path = Skia.Path.Make();
//     path.moveTo(waveX.value - 300, 10);
//     path.cubicTo(waveX.value - 250, 0, waveX.value - 200, 20, waveX.value - 150, 10);
//     path.cubicTo(waveX.value - 100, 0, waveX.value - 50, 20, waveX.value, 10);
//     path.lineTo(waveX.value, 20);
//     path.lineTo(waveX.value - 300, 20);
//     path.close();
//     return path;
//   });

//   return (
//     <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
//       <Canvas style={{ width: 300, height: 20 }}>
//         {/* Background Bar */}
//         <Rect x={0} y={0} width={300} height={20} color="lightgray" rx={10} ry={10} />
//         {/* Animated Filling Bar */}
//         <Rect x={0} y={0} width={progress} height={20} color="yellow" rx={10} ry={10} />
//         {/* Moving Wave Overlay */}
//         <Path path={wavePath} color="rgba(255, 255, 255, 0.5)" style="fill" />
//       </Canvas>
//     </View>
//   );
// };

// export default AnimatedProgressBar;
// import React, { useEffect } from "react";
// import { View, Dimensions } from "react-native";
// import { Canvas, RoundedRect, Group, Rect } from "@shopify/react-native-skia";
// import { useSharedValue, useDerivedValue, withTiming, withRepeat } from "react-native-reanimated";

// const { width } = Dimensions.get("window");
// const barWidth = width * 0.8;
// const height = 60;
// const waveData = [5, 12, 18, 25, 30, 20, 15, 10, 7, 3, 5, 12, 18, 25, 30, 20, 15, 10, 7, 3];
// const barRadius = 3;

// const StaticWaveform = () => {
//   const fillProgress = useSharedValue(0);

//   useEffect(() => {
//     // Animate the fill effect in a loop
//     fillProgress.value = withRepeat(withTiming(barWidth, { duration: 3000 }), -1, false);
//   }, []);

//   // Derived value for dynamic width of the filling color
//   const animatedWidth = useDerivedValue(() => {
//     return fillProgress.value;
//   });

//   return (
//     <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
//       <Canvas style={{ width: barWidth, height }}>
//         {/* Animated Background Fill */}
//         <Rect x={0} y={0} width={animatedWidth} height={height} color="rgb(255, 50, 10)" />

//         {/* Waveform Bars */}
//         <Group blendMode="multiply">
//           {waveData.map((amp, index) => {
//             const x = (index / waveData.length) * barWidth + 5;
//             const barHeight = amp;
//             const y = (height - barHeight) / 2;

//             return (
//               <RoundedRect key={index} x={x} y={y} width={6} height={barHeight} r={barRadius} color="white" />
//             );
//           })}
//         </Group>
//       </Canvas>
//     </View>
//   );
// };

// export default StaticWaveform;

import React, { useEffect } from "react";
import { View, Dimensions } from "react-native";
import { Canvas, RoundedRect, Group } from "@shopify/react-native-skia";
import { useSharedValue, useDerivedValue, withRepeat, withTiming } from "react-native-reanimated";

const { width } = Dimensions.get("window");
const barWidth = width * 0.8;
const height = 60;
const waveData = [5, 12, 18, 25, 30, 20, 15, 10, 7, 3, 5, 12, 18, 25, 30, 20, 15, 10, 7, 3];
const barRadius = 3;

const AnimatedWaveform = () => {
  const fillProgress = useSharedValue(0);

  useEffect(() => {
    // Animate the fill effect from left to right continuously
    fillProgress.value = withRepeat(withTiming(1, { duration: 2500 }), -1, true);
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Canvas style={{ width: barWidth, height }}>
        {/* Waveform Bars with Individual Animation */}
        <Group>
          {waveData.map((amp, index) => {
            const x = (index / waveData.length) * barWidth + 5;
            const barHeight = amp;
            const y = (height - barHeight) / 2;

            // Each bar should animate its fill color smoothly
            const animatedColor = useDerivedValue(() => {
              return fillProgress.value > index / waveData.length
                ? "rgb(255, 50, 10)" // Filled color
                : "white"; // Default color
            });

            return (
              <RoundedRect
                key={index}
                x={x}
                y={y}
                width={6}
                height={barHeight}
                r={barRadius}
                color={animatedColor}
              />
            );
          })}
        </Group>
      </Canvas>
    </View>
  );
};

export default AnimatedWaveform;


