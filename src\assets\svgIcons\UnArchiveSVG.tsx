import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const UnArchiveSVG: React.FC<IconProps> = ({
    size = 16,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={(size * 14) / 16} // maintain original 16:14 aspect ratio
            viewBox="0 0 16 14"
            fill="none"
            {...restProps}
        >
            <Path
                d="M13.272 2.145H7.935L6.658.227A.51.51 0 006.23 0h-4.18A2.053 2.053 0 000 2.05v9.792a2.053 2.053 0 002.05 2.05h11.222a2.053 2.053 0 002.051-2.05V4.196a2.053 2.053 0 00-2.05-2.05zm1.03 9.697a1.03 1.03 0 01-1.03 1.03H2.051a1.03 1.03 0 01-1.03-1.03V2.051a1.03 1.03 0 011.03-1.03h3.907L7.235 2.94a.51.51 0 00.426.228h5.611a1.03 1.03 0 011.03 1.029v7.646z"
                fill={color}
            />
            <Path
                d="M9.446 8.006L8.172 6.733v3.788a.51.51 0 11-1.021 0V6.733L5.877 8.006a.51.51 0 01-.722-.722L7.3 5.14a.51.51 0 01.723 0l2.145 2.145a.51.51 0 01-.722.722z"
                fill={color}
            />
        </Svg>
    );
};

export default UnArchiveSVG;

