// useAudioRecorder.ts
import { useState, useRef, useEffect } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';
import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSet,
  AudioSourceAndroidType,
  AVEncoderAudioQualityIOSType,
  AVModeIOSOption,
  AVEncodingOption,
  OutputFormatAndroidType,
  AVLinearPCMBitDepthKeyIOSType,
} from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import { uploadFile } from '../../utils/ApiService';

export type UseAudioRecorderReturn = {
  isRecording: boolean;
  isPaused: boolean;
  recordTime: number;
  audioPath: string;
  isFileUploading: boolean;
  startRecording: () => Promise<void>;
  pauseRecording: () => Promise<void>;
  resumeRecording: () => Promise<void>;
  stopRecording: () => Promise<string>;
  uploadAudio: (audioPath: string) => Promise<any>;
  refreshRecording: () => void;
  playRecording: (audioPath: string) => Promise<void>;
  pausePlayback: () => Promise<void>;
  stopPlayback: () => Promise<void>;
  resumePlayback: () => Promise<void>;
  isPlaying: boolean;
  isPlaybackPaused: boolean;
};

const useAudioRecorder = (): UseAudioRecorderReturn => {
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordTime, setRecordTime] = useState(0);
  const [audioPath, setAudioPath] = useState<string>('');
  const [isFileUploading, setIsFileUploading] = useState(false);

  const [isPlaying, setIsPlaying] = useState(false);
  const [isPlaybackPaused, setIsPlaybackPaused] = useState(false);

  const playRecording = async (audioPath: string) => {
    if (!audioPath) return;
    try {
      await audioRecorderPlayer.startPlayer(audioPath);
      audioRecorderPlayer.setVolume(1.0);
      setIsPlaying(true);
      setIsPlaybackPaused(false);
      // 🔁 Listen for end of playback
      audioRecorderPlayer.addPlayBackListener((e) => {
        if (e.currentPosition === e.duration) {
          // Audio finished
          audioRecorderPlayer.stopPlayer();
          audioRecorderPlayer.removePlayBackListener();
          setIsPlaying(false);
          setIsPlaybackPaused(false);
        }
      });
    } catch (error) {
      console.error('Error playing audio:', error);
    }
  };

  const pausePlayback = async () => {
    try {
      await audioRecorderPlayer.pausePlayer();
      setIsPlaybackPaused(true);
    } catch (error) {
      console.error('Error pausing playback:', error);
    }
  };

  const resumePlayback = async () => {
    try {
      await audioRecorderPlayer.resumePlayer();
      setIsPlaybackPaused(false);
    } catch (error) {
      console.error('Error resuming playback:', error);
    }
  };

  const stopPlayback = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      setIsPlaying(false);
      setIsPlaybackPaused(false);
    } catch (error) {
      console.error('Error stopping playback:', error);
    }
  };

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO);
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true;
  };

  const startRecording = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) {
      console.warn('Permission denied');
      return;
    }

    const audioSet: AudioSet = {
      // ✅ Android Config
      AudioSourceAndroid: AudioSourceAndroidType.MIC,
      OutputFormatAndroid: OutputFormatAndroidType.DEFAULT, // PCM format supported here
      AudioEncoderAndroid: AudioEncoderAndroidType.DEFAULT, // Default uses PCM when no compression

      // ✅ iOS Config for WAV (Linear PCM)
      AVFormatIDKeyIOS: AVEncodingOption.lpcm,
      AVModeIOS: AVModeIOSOption.measurement,
      AVNumberOfChannelsKeyIOS: 2,
      AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
      AVLinearPCMBitDepthKeyIOS: AVLinearPCMBitDepthKeyIOSType.bit16,
      AVLinearPCMIsBigEndianKeyIOS: false,
      AVLinearPCMIsFloatKeyIOS: false,
    };
    const path = Platform.select({
      ios: 'audio.wav',
      android: `${RNFS.DocumentDirectoryPath}/audio.wav`,
    });
    await audioRecorderPlayer.startRecorder(path, audioSet || '');

    setIsRecording(true);
    setIsPaused(false);
    // setAudioPath(audioRecorderPlayer.getRealPathFromURI(audioPath) || '');
    setRecordTime(0);

    intervalRef.current = setInterval(() => {
      setRecordTime((prev) => prev + 1);
    }, 1000);
  };

  const pauseRecording = async () => {
    try {
      await audioRecorderPlayer.pauseRecorder();
      if (intervalRef.current) clearInterval(intervalRef.current);
      setIsPaused(true);
    } catch (error) {
      console.error('Pause failed:', error);
    }
  };

  const resumeRecording = async () => {
    try {
      await audioRecorderPlayer.resumeRecorder();
      setIsPaused(false);
      intervalRef.current = setInterval(() => {
        setRecordTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Resume failed:', error);
    }
  };

  const stopRecording = async () => {
    const resultPath = await audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();
    if (intervalRef.current) clearInterval(intervalRef.current);

    setIsRecording(false);
    setIsPaused(false);
    setAudioPath(resultPath);

    // // 🔽 Save to device
    // const fileName = `recording_${Date.now()}.wav`;

    // const destPath = Platform.select({
    //   android: `${RNFS.DownloadDirectoryPath}/${fileName}`,
    //   ios: `${RNFS.DocumentDirectoryPath}/${fileName}`,
    // });

    // if (destPath) {
    //   try {
    //     await RNFS.copyFile(resultPath, destPath);
    //     console.log('Saved to device at:', destPath);
    //   } catch (error) {
    //     console.error('Failed to save file:', error);
    //   }
    // }

    return resultPath;
  };

  const uploadAudio = async (audioPath: string) => {
    if (!audioPath) return;
    try {
      setIsFileUploading(true);
      const formData = new FormData();
      formData.append('file', {
        uri: audioPath,
        type: 'audio/wav',
        name: 'recording.wav',
      });
      const res = await uploadFile(formData);
      return res;
    } catch (err) {
      throw err;
    } finally {
      setIsFileUploading(false);
    }
  };

  const refreshRecording = () => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();

    setIsRecording(false);
    setIsPaused(false);
    setRecordTime(0);
    setAudioPath('');
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
    };
  }, []);

  return {
    isRecording,
    isPaused,
    recordTime,
    audioPath,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    uploadAudio,
    refreshRecording,
    isFileUploading,
    playRecording,
    pausePlayback,
    stopPlayback,
    isPlaying,
    isPlaybackPaused,
    resumePlayback,
  };
};

export default useAudioRecorder;
