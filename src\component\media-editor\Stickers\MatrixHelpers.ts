import type { Sk<PERSON><PERSON><PERSON>, Vector } from '@shopify/react-native-skia';
import { Skia, MatrixIndex } from '@shopify/react-native-skia';

export const scale = (matrix: SkMatrix, s: number, origin: Vector) => {
  'worklet';
  const source = Skia.Matrix(matrix.get());
  source.translate(origin.x, origin.y);
  source.scale(s, s);
  source.translate(-origin.x, -origin.y);
  return source;
};

export const rotateZ = (matrix: SkMatrix, theta: number, origin: Vector) => {
  'worklet';
  const source = Skia.Matrix(matrix.get());
  source.translate(origin.x, origin.y);
  source.rotate(theta);
  source.translate(-origin.x, -origin.y);
  return source;
};

export const translate = (matrix: SkMatrix, x: number, y: number) => {
  'worklet';
  const m = Skia.Matrix();
  m.translate(x, y);
  m.concat(matrix);
  return m;
};

export const toM4 = (m3: SkMatrix) => {
  'worklet';
  const m = m3.get();
  const tx = m[MatrixIndex?.TransX];
  const ty = m[MatrixIndex?.TransY];
  const sx = m[MatrixIndex?.ScaleX];
  const sy = m[MatrixIndex?.ScaleY];
  const skewX = m[MatrixIndex?.SkewX];
  const skewY = m[MatrixIndex?.SkewY];
  const persp0 = m[MatrixIndex?.Persp0];
  const persp1 = m[MatrixIndex?.Persp1];
  const persp2 = m[MatrixIndex?.Persp2];
  return [sx, skewY, persp0, 0, skewX, sy, persp1, 0, 0, 0, 1, 0, tx, ty, persp2, 1];
};
