import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';
import { ConversationSettingsSchema } from './ConversationSettingsSchema';

export class UserSchema extends Realm.Object<UserSchema> implements IUser {
  // Server related
  id!: string;
  username!: string;
  profilePic?: string;
  bio!: string;
  name!: string;

  // Device related
  contactName?: string;
  phoneNumber?: string;
  isDeviceContact!: boolean;

  // Misc
  isBlocked!: boolean;
  textColor?: string;

  conversationSettings!: ConversationSettingsSchema;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.user,
    primaryKey: 'id',
    properties: {
      // Server related
      id: 'string',
      username: 'string',
      profilePic: { type: 'string', optional: true },
      bio: 'string',
      name: 'string',

      // Device related
      contactName: { type: 'string', optional: true },
      phoneNumber: { type: 'string', optional: true },
      isDeviceContact: { type: 'bool' },

      // Misc
      isBlocked: { type: 'bool', default: false },
      textColor: { type: 'string', optional: true },

      conversationSettings: `${realmSchemaNames.conversation_settings}`,
    },
  };
}

export interface IUser {
  // Server related
  id: string;
  username: string;
  profilePic?: string;
  bio: string;
  name: string;

  // Device related
  contactName?: string;
  phoneNumber?: string;
  isDeviceContact: boolean;

  // Misc
  isBlocked?: boolean;
  textColor?: string;

  conversationSettings: ConversationSettingsSchema;
}
