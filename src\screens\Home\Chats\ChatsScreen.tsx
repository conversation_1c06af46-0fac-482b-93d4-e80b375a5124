import { StyleSheet, Text, View } from 'react-native';
import React, { useMemo, useRef, useState } from 'react';
import { SwipeListView, SwipeRow } from 'react-native-swipe-list-view';
import RenderHiddenItem from '../components/RenderHiddenItem';

import useConversations from '../../../hooks/conversations/useConversations';
import { colors } from '../../../theme/colors';
import ChatItemCard from '../All/ChatItemCard';
import { ChatScreenParams, IChatScreenProps } from './ChatSpecificScreen';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';

interface ChatHistory {
  // chatHistory: any;
  // onPressDeleteChat: any;
  // onPressIcon: any;
  searchText: string;
}

const ChatsScreen: React.FC<ChatHistory> = ({ searchText }) => {
  const { unArchivedConversations } = useConversations();
  const myChats = unArchivedConversations?.filter((conv) => conv.type === 'p2p');
  const swipeListRef = useRef<any>(undefined);
  const [selectedChats, setSelectedChats] = useState<any[]>([]);
  const [isSelectionEnabled, setIsSelectionEnabled] = useState(false);

  const filteredData = useMemo(() => {
    if (!searchText?.trim()) return myChats;
    const lower = searchText.toLowerCase();
    return myChats.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower) ||
        conv.phoneNumber?.toLowerCase().includes(lower),
    );
  }, [myChats, searchText]);

  const onSelect = (chats: any) => {
    const isSelected = selectedChats.some((c) => c.id === chats.id);

    if (isSelectionEnabled) {
      setSelectedChats((prev) => {
        if (isSelected) {
          const updated = prev.filter((c) => c.id !== chats.id);
          if (updated.length === 0) {
            setIsSelectionEnabled(false);
          }
          return updated;
        } else {
          return [...prev, chats];
        }
      });
    } else {
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }

      const userData: IChatScreenProps = {
        displayName: chats.displayName,
        displayPic: chats.displayPic,
        type: chats.type,
        id: chats.id,
        isActive: chats.isActive,
        conversation: myChats.find((conv: any) => conv.id === chats.id),
      };
      const paramsData: ChatScreenParams = {
        convId: userData.id,
      };
      navigateTo(SCREENS.ChatSpecificScreen, {
        userData,
        data: paramsData,
      });
    }
  };

  const renderItem = ({ item }: any) => (
    <ChatItemCard
      type="channel"
      item={item}
      selectedUser={selectedChats}
      isSelectionEnabled={isSelectionEnabled}
      onSelect={onSelect}
      // onLongSelect={onLongSelect}
    />
  );

  return (
    <View style={styles.container}>
      <SwipeListView
        ref={swipeListRef}
        data={filteredData}
        useFlatList
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        // renderHiddenItem={renderHiddenItem}
        rightOpenValue={-120}
        disableRightSwipe
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default ChatsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});
