import Api from '../../utils/api';

export interface Color {
  _id: string;
  name: string;
  hex: string;
  rgb: string;
  isActive: boolean;
}

export interface Country {
  _id: string;
  name: string;
  code: string;
  flag: string;
  phoneCode: string;
  isActive: boolean;
}

export interface ILanguage {
  _id: string;
  name: string;
  code: string;
  flag: string;
  countryName: string;
  isActive: boolean;
}

interface CreateColorPayload {
  name: string;
  hex: string;
  rgb: string;
  isActive?: boolean;
}

interface UpdateColorPayload {
  name?: string;
  hex?: string;
  rgb?: string;
  isActive?: boolean;
}

interface CreateCountryPayload {
  name: string;
  code: string;
  flag: string;
  phoneCode: string;
  isActive?: boolean;
}

interface UpdateCountryPayload {
  name?: string;
  code?: string;
  flag?: string;
  phoneCode?: string;
  isActive?: boolean;
}

interface UpdateLanguagePayload {
  name?: string;
  code?: string;
  flag?: string;
  countryName?: string;
  isActive?: boolean;
}

const staticRoutes = {
  getAllLanguages: 'v1/static/languages',
  getActiveLanguages: 'v1/static/languages/active',
  updateLanguage: 'v1/static/languages/:id',
  getAllColors: 'v1/static/colors',
  getActiveColors: 'v1/static/colors/active',
  createColor: 'v1/static/colors',
  updateColor: 'v1/static/colors/:id',
  getAllCountries: 'v1/static/countries',
  getActiveCountries: 'v1/static/countries/active',
  createCountry: 'v1/static/countries',
  updateCountry: 'v1/static/countries/:id',
};

export const getActiveLanguages = async (): Promise<ILanguage[]> => {
  try {
    const response = await Api.get(staticRoutes.getActiveLanguages);
    if (response?.body?.status) {
      return response.body.data || [];
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error fetching active languages:', error);
    return Promise.reject(error);
  }
};

export const updateLanguage = async (
  id: string,
  payload: UpdateLanguagePayload,
): Promise<ILanguage> => {
  try {
    const endpoint = staticRoutes.updateLanguage.replace(':id', id);
    const response = await Api.put(endpoint, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating language:', error);
    return Promise.reject(error);
  }
};

export const getActiveColors = async (): Promise<Color[]> => {
  try {
    const response = await Api.get(staticRoutes.getActiveColors);
    if (response?.body?.status) {
      return response.body.data || [];
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error fetching active colors:', error);
    return Promise.reject(error);
  }
};

export const getActiveCountries = async (): Promise<Country[]> => {
  try {
    const response = await Api.get(staticRoutes.getActiveCountries);
    if (response?.body?.status) {
      return response.body.data || [];
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error fetching active countries:', error);
    return Promise.reject(error);
  }
};
