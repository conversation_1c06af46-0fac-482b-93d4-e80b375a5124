import { View, TouchableOpacity, StyleSheet } from 'react-native';
import <PERSON>SVG from '../../../../assets/svgIcons/EditSVG';
import PollsSVG from '../../../../assets/svgIcons/PollsSVG';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { SCREENS } from '../../../../navigation/screenNames';
import { colors } from '../../../../theme/colors';
import { navigateTo } from '../../../../utils/commonFunction';
import { FontAwesomeIcons } from '../../../../utils/vectorIcons';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { useFavoriteContacts } from '../../../Profile/Settings/FavouriteScreen';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';

type ProfileRightHeaderProps = {
  conversationInfo: ConversationInfo | null;
  currentConversation: IConversation;
};

const ProfileRightHeader: React.FC<ProfileRightHeaderProps> = ({ conversationInfo, currentConversation }) => {
  const { favoriteContact, isContactFavorited } = useFavoriteContacts();

  const isChannel = conversationInfo?.type === ConversationType.CHANNEL;
  const isGroup = conversationInfo?.type === ConversationType.GROUP;
  const isP2P = conversationInfo?.type === ConversationType.P2P;

  const currentUserRole = !isP2P ? conversationInfo?.membershipStatus : null;

  const conversationId = conversationInfo?.id || '';

  const isDeviceContact = isP2P ? conversationInfo?.isDeviceContact : false

  return (
    <View style={styles.headerRightContainer}>
      {isP2P && isDeviceContact && (
        <TouchableOpacity onPress={() => favoriteContact({ id: conversationId })}>
          {isContactFavorited({ id: conversationId }) ? (
            <FontAwesomeIcons name="star" size={20} color={'white'} />
          ) : (
            <FontAwesomeIcons name="star-o" size={20} color={'white'} />
          )}
        </TouchableOpacity>
      )}

      {isGroup && currentConversation && (
        <>
          {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
            <TouchableOpacity
              onPress={() => navigateTo(SCREENS.GroupAnalyticsScreen, { userData: conversationId })}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <PollsSVG color={colors.white} size={21} />
            </TouchableOpacity>
          )}
          {currentUserRole === 'owner' ||
          currentUserRole === 'admin' ||
          currentUserRole === 'member' ? (
            <TouchableOpacity
              onPress={() => navigateTo(SCREENS.EditGroupInfo, { userDetails: conversationId })}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <EditSVG size={21} color={colors.white} />
            </TouchableOpacity>
          ) : null}
          {/* Pending not in mvp */}
          {/* <ThreeDotsSVG
            size={19}
            onPress={() =>
              navigateTo(SCREENS.GroupSettingsScreen, {
                userData: adminsAndOwner,
                userDetails: user,
                data: memberIds,
              })
            }
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          /> */}
        </>
      )}
      {isChannel && currentConversation && currentUserRole === 'owner' && (
        <TouchableOpacity
          onPress={() =>
            navigateTo(SCREENS.CreateChannelScreen, {
              isEdit: true,
              userDetails: conversationId,
            })
          }
          // hitSlop={{ top: 10, bottom: 10, left: 10, right: 20 }}
        >
          <EditSVG size={21} color={colors.white} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ProfileRightHeader;

const styles = StyleSheet.create({
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
});
