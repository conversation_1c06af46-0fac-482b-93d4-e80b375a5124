// NetInfoProvider.tsx
import React, { createContext, useContext, useEffect, useState, useMemo } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';

type NetInfoContextType = {
  isConnected: boolean | null;
  details: NetInfoState | null;
  isInternetReachable: boolean | null;
};

const NetInfoContext = createContext<NetInfoContextType | undefined>(undefined);

export const NetInfoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [netInfo, setNetInfo] = useState<NetInfoState | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setNetInfo(state);
    });
    return () => unsubscribe();
  }, []);

  const value = useMemo(
    () => ({
      isConnected: netInfo?.isConnected ?? null,
      details: netInfo,
      isInternetReachable: netInfo?.isInternetReachable ?? null,
    }),
    [netInfo],
  );

  return <NetInfoContext.Provider value={value}>{children}</NetInfoContext.Provider>;
};

export const useNetInfoCtx = () => {
  const context = useContext(NetInfoContext);
  if (!context) {
    throw new Error('useNetInfo must be used inside a NetInfoProvider');
  }
  return context;
};
