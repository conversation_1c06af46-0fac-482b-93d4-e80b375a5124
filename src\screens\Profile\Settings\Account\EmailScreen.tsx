import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { CInput } from '../../../../common/CInput';
import { useNavigation, useRoute } from '@react-navigation/native';
import { CButton } from '../../../../common';
import ContactUsSVG from '../../../../assets/svgIcons/ContactUsSVG';
import { SCREENS } from '../../../../navigation/screenNames';

const EmailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const initialEmail = route.params?.email || '';
  const [email, setEmail] = useState(initialEmail);
  const title = email ? 'Change Email' : 'Add Email';

  const handleVerify = () => {
    // Placeholder for backend integration
    navigation.navigate(SCREENS.VerifyEmailScreen, { email });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <HeaderBackWithTitle title={title} />
      <View style={styles.whiteContainer}>
        <KeyboardAwareScrollView
          contentContainerStyle={{ paddingBottom: 40, flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          enableOnAndroid={true}
          extraScrollHeight={40}
        >
          <Text style={styles.label}>Enter your email</Text>
          <View style={styles.inputRow}>
            <ContactUsSVG color={colors.gray_80} style={styles.emailIcon} />
            <CInput
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              cStyle={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          <CButton onPress={handleVerify} cStyle={styles.button}>
            <Text style={styles.buttonText}>Verify</Text>
          </CButton>
        </KeyboardAwareScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  label: {
    color: '#232323',
    fontSize: 16,
    marginBottom: 8,
    marginLeft: 5,
    marginTop: 15,
    fontWeight: '400',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F7F7F7',
    borderRadius: 12,
    marginBottom: 10,
    paddingHorizontal: 8,
  },
  emailIcon: {
    marginRight: 8,
    marginLeft: 10,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    borderRadius: 12,
    fontSize: 15,
    paddingHorizontal: 0,
  },
  button: {
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    width: '109%',
    alignSelf: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EmailScreen;
