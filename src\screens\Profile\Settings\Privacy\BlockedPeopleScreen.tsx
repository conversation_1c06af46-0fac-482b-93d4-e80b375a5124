import React, { useState, useEffect } from 'react';
import { View, SafeAreaView, StyleSheet, FlatList, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { hp, wp } from '../../../../theme/fonts';
import { ChatService } from '../../../../service/ChatService';
import BlockedUserItem from '../../components/BlockedUserItem';
import { unBlockUser } from '../../../../utils/ApiService';
import CustomAlert from '../../../../component/Common/CustomAlert';
import { successToast, errorToast } from '../../../../utils/commonFunction';

export type BlockedUserData = {
  id: string;
  displayName: string;
  displayPic?: string;
};

const BlockedPeopleScreen = () => {
  const navigation = useNavigation();
  const [blockedUsers, setBlockedUsers] = useState<BlockedUserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUnblockAlert, setShowUnblockAlert] = useState(false);
  const [selectedUser, setSelectedUser] = useState<BlockedUserData | null>(null);

  useEffect(() => {
    loadBlockedUsers();
  }, []);

  const loadBlockedUsers = () => {
    try {
      const blocked = ChatService.getBlockedEntities();
      const mapped = (blocked?.data || []).map((u: any) => ({
        id: u?.id,
        displayName: u?.contactName ?? u?.name ?? u?.username ?? 'Unknown User',
        displayPic: u?.profilePic,
      }));
      setBlockedUsers(mapped);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleUnblockUser = (user: BlockedUserData) => {
    setSelectedUser(user);
    setShowUnblockAlert(true);
  };

  const handleConfirmUnblockUser = async () => {
    if (!selectedUser) return;

    try {
      const res = await unBlockUser(selectedUser.id);
      ChatService.onUnBlocked(selectedUser.id);
      ChatService.onUnBlockedUser(selectedUser.id);
      loadBlockedUsers();
      setShowUnblockAlert(false);
      setSelectedUser(null);
      successToast(`${selectedUser.displayName} has been unblocked successfully`);
    } catch (error) {
      errorToast('Failed to unblock user. Please try again.');
    }
  };

  const renderBlockedUser = ({ item }: { item: BlockedUserData }) => {
    return <BlockedUserItem data={item} onPressUnblock={handleUnblockUser} />;
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>No Blocked Users</Text>
      <Text style={styles.emptySubtitle}>You haven't blocked any users yet.</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Blocked people" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : (
          <FlatList
            data={blockedUsers}
            renderItem={renderBlockedUser}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      {/* Unblock User Alert */}
      <CustomAlert
        visible={showUnblockAlert}
        onCancel={() => {
          setShowUnblockAlert(false);
          setSelectedUser(null);
        }}
        onConfirm={handleConfirmUnblockUser}
        title="Unblock User"
        message={`Are you sure you want to unblock ${selectedUser?.displayName}?`}
        confirmText="Unblock"
        cancelText="Cancel"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
  listContainer: {
    paddingHorizontal: wp(4),
    paddingVertical: hp(2),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.black_23,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingVertical: hp(40),
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
    marginBottom: hp(1),
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors._757575_gray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default BlockedPeopleScreen;
