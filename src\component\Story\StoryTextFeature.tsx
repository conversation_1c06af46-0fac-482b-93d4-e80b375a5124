import React, { useRef, useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  PanResponder,
  Animated,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import { colors } from '../../theme/colors';
import { useTranslation } from 'react-i18next';
import ViewShot, { captureRef } from 'react-native-view-shot';

const StoryTextFeature = () => {
  const { t } = useTranslation();
  const [text, setText] = useState(''); // Default placeholder text
  const [fontSize, setFontSize] = useState(20); // Default font size
  const [textColor, setTextColor] = useState('#ffffff'); // Default text color (white)
  const [fontStyle, setFontStyle] = useState('normal'); // Default font style
  const [textAlign, setTextAlign] = useState<any>('center'); // Default text alignment
  const [isEditing, setIsEditing] = useState(true); // Toggles between editing and non-editing mode

  const pan: any = useRef(new Animated.ValueXY()).current; // Drag and drop position
  const [posXY, setPosXY] = useState({
    x: 0,
    y: 0,
  });
  const viewRef = useRef(null);
  // // PanResponder to allow dragging of the text
  // const panResponder = useRef(
  //   PanResponder.create({
  //     onMoveShouldSetPanResponder: () => true,
  //     onPanResponderMove: Animated.event([null, { dx: pan.x, dy: pan.y }], {
  //       useNativeDriver: false,
  //     }),
  //     onPanResponderRelease: () => {
  //       Animated.spring(pan, {
  //         toValue: { x: pan.x._value, y: pan.y._value },
  //         useNativeDriver: false,
  //       }).start();
  //     },
  //   })
  // ).current;

  // PanResponder for dragging overlay
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    // onPanResponderMove: Animated.event([null, { dx: pan.x, dy: pan.y }], {
    //   useNativeDriver: false,
    // }),
    onPanResponderMove: (event, gesture) => {
      setPosXY({ ...posXY, x: posXY.x + gesture.dx, y: posXY.y + gesture.dy }); // Update X position
    },
  });

  // Toggle editing mode
  const toggleEditing = () => setIsEditing(!isEditing);

  return (
    <View style={styles.container}>
      <ViewShot
        ref={viewRef}
        options={{
          fileName: 'file-name',
          format: 'jpg',
          quality: 0.9,
          result: 'base64',
        }}
        style={styles.container}
      >
        {/* Text Editing View */}
        <Animated.View
          style={[
            styles.textWrapper,
            { transform: [{ translateX: posXY.x }, { translateY: posXY.y }] },
          ]}
          {...panResponder.panHandlers}
        >
          {isEditing ? (
            <TextInput
              style={{
                ...styles.textInput,
                fontSize: fontSize,
                color: textColor,
                textAlign: textAlign,
              }}
              value={text}
              onChangeText={setText}
              placeholder={t('Tap to start typing..')}
              placeholderTextColor="white"
              multiline
              // autoFocus
              enterKeyHint="done"
            />
          ) : (
            <TouchableOpacity onPress={toggleEditing}>
              <Text
                style={{
                  ...styles.textDisplay,
                  fontSize: fontSize,
                  color: textColor,
                  textAlign: textAlign,
                }}
              >
                {text}
              </Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      </ViewShot>
      {/* Toolbar for font styles, color, and size */}
      {/* {isEditing && (
        <View style={styles.toolbar}>
          <ScrollView horizontal>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={() =>
                setFontStyle(fontStyle === "normal" ? "italic" : "normal")
              }
            >
              <Text style={styles.toolbarText}>{t("Italic")}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={() => setFontSize(fontSize + 5)}
            >
              <Text style={styles.toolbarText}>A+</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={() => setFontSize(fontSize > 15 ? fontSize - 5 : 15)}
            >
              <Text style={styles.toolbarText}>A-</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={() =>
                setTextColor(textColor === "#ffffff" ? "#ff0000" : "#ffffff")
              }
            >
              <Text
                style={[
                  styles.toolbarText,
                  { color: textColor === "#ffffff" ? "red" : "white" },
                ]}
              >
                {t("Color")}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.toolbarButton}
              onPress={() =>
                setTextAlign(
                  textAlign === "center"
                    ? "left"
                    : textAlign === "left"
                    ? "right"
                    : "center"
                )
              }
            >
              <Text style={styles.toolbarText}>{t("Align")}</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      )} */}

      {/* Done button to toggle between view and edit */}
      {/* <TouchableOpacity style={styles.doneButton} onPress={toggleEditing}>
        <Text style={styles.doneButtonText}>
          {isEditing ? t("Done") : t("Edit Text")}
        </Text>
      </TouchableOpacity> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors._7A5DCB_purple, // Background to mimic Instagram's story
    justifyContent: 'center',
    alignItems: 'center',
  },
  textWrapper: {
    position: 'absolute',
    padding: 10,
    borderRadius: 5,
    backgroundColor: 'transparent', // Transparent background
  },
  textInput: {
    fontSize: 30,
    color: '#fff',
    textAlign: 'center',
  },
  textDisplay: {
    fontSize: 20,
    color: '#fff',
  },
  toolbar: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    flexDirection: 'row',
    paddingVertical: 10,
    justifyContent: 'center',
  },
  toolbarButton: {
    marginHorizontal: 10,
  },
  toolbarText: {
    color: '#fff',
    fontSize: 18,
  },
  doneButton: {
    position: 'absolute',
    bottom: 50,
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  doneButtonText: {
    fontSize: 16,
    color: 'black',
  },
});

export default StoryTextFeature;
