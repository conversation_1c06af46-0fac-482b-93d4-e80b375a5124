import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import ModalWrapper from '../ModalWrapper';
import { colors } from '../../theme/colors';
import { commonFontStyle, hp } from '../../theme/fonts';
import ButtonPurple from '../ButtonPurple';

import { useTranslation } from 'react-i18next';
import { ChannelType, ConversationType, IMessage } from '../../device-storage/realm/schemas/MessageSchema';
import { MembershipStatus } from '../../types/chats.types';

type Props = {
  isVisible: boolean;
  onCloseModal?: () => void;
  onPressDeleteForMe?: () => void;
  onPressDeleteForBoth?: () => void;
  selectedMsgs?: IMessage[] | null;
  user: any;
  type?: ConversationType;
  role?: MembershipStatus;
  loading?: boolean;
};

const DeleteMessageModal = ({
  isVisible,
  onCloseModal = () => {},
  onPressDeleteForBoth,
  onPressDeleteForMe,
  selectedMsgs = [],
  user,
  type = ConversationType.P2P,
  role = MembershipStatus.MEMBER,
  loading = false,
}: Props) => {
  const { t } = useTranslation();
  // const canDeleteForEveryone =
  //   [ConversationType.P2P, ConversationType.GROUP, ConversationType.CHANNEL].includes(type) &&
  //   selectedMsgs.length > 0 &&
  //   (selectedMsgs.every((msg) => [user._id].includes(msg.senderId)) && type === ConversationType.P2P);

  const canDeleteForEveryone = (() => {
    if (selectedMsgs?.length === 0) return false;

    switch (type) {
      case ConversationType.P2P:
        return selectedMsgs?.every((msg) => msg.senderId === user._id);

      case ConversationType.GROUP:
        if (role === MembershipStatus.OWNER) {
          return true;
        }
        if (role === MembershipStatus.ADMIN) {
          return selectedMsgs?.every((msg: any) => msg?.senderRole !== MembershipStatus.OWNER);
        }
        return selectedMsgs?.every((msg) => msg.senderId === user._id);

      case ConversationType.CHANNEL:
        return (
          role === MembershipStatus.OWNER && selectedMsgs?.every((msg) => msg.senderId === user._id)
        );

      default:
        return false;
    }
  })();

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
      <View>
        <Text style={styles.title}>{t('Delete message')}</Text>
        <View style={styles.rowView}>
          {type === ConversationType.P2P && (
            <ButtonPurple
              extraStyle={{ flex: 1, backgroundColor: colors.gray_f3 }}
              titleColor={colors.black_23}
              title={t('Delete for me')}
              onPress={onPressDeleteForMe}
              textStyle={{ fontSize: 14 }}
              isLoading={loading}
              disabled={loading}
            />
          )}
          {canDeleteForEveryone && (
            <ButtonPurple
              extraStyle={{ flex: 1, backgroundColor: colors._F11010_red }}
              title={t('Delete for everyone')}
              onPress={onPressDeleteForBoth}
              textStyle={{ fontSize: 14 }}
              isLoading={loading}
              disabled={loading}
            />
          )}
        </View>
      </View>
    </ModalWrapper>
  );
};

export default DeleteMessageModal;

const styles = StyleSheet.create({
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  rowView: {
    flexDirection: 'row',
    gap: hp(2),
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
});
