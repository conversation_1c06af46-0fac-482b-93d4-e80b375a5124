import { Text, View } from 'react-native';
import Toast from 'react-native-toast-message';
import React, { useEffect, useState } from 'react';
import { colors } from './src/theme/colors';
import RootContainer from './src/navigation/RootContainer';
import useFCMSetup from './src/firebase/useFCMSetup';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import RootProviders from './src/Context/RootProviders';
import { toastConfig } from './src/lib/toastConfig';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AppStateListener } from './src/lib/AppStateListener';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import NetInfo, { NetInfoStateType } from '@react-native-community/netinfo';

type Props = {};

const App = (props: Props) => {
  useFCMSetup();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <KeyboardProvider>
        <SafeAreaProvider>
          <RootProviders>
            <View style={{ flex: 1, backgroundColor: colors._7A5DCB_purple }}>
              <RootContainer />
              <AppStateListener />
              <Toast config={toastConfig} position="bottom" topOffset={0} />
            </View>
          </RootProviders>
        </SafeAreaProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
};

export default App;
