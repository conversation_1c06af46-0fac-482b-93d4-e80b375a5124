import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SVGProps {
  size?: number;
  color?: string;
}

const EditImageSVG: React.FC<SVGProps> = ({
  size = 16,
  color = "#6A4DBB",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <Path
        d="M.464 13.611l-.016.016a4.95 4.95 0 01-.408-1.6c.056.584.208 1.112.424 1.584zM5.6 6.703a1.904 1.904 0 100-3.808 1.904 1.904 0 000 3.808z"
        fill={color}
      />
      <Path
        d="M11.352 0H4.648C1.736 0 0 1.736 0 4.648v6.704c0 .872.152 1.632.448 2.272.688 1.52 2.16 2.376 4.2 2.376h6.704C14.264 16 16 14.264 16 11.352V4.648C16 1.736 14.264 0 11.352 0zm3.344 8.4c-.624-.536-1.632-.536-2.256 0l-3.328 2.856c-.624.536-1.632.536-2.256 0l-.272-.224c-.568-.496-1.472-.544-2.112-.112L1.48 12.928a4.266 4.266 0 01-.28-1.576V4.648C1.2 2.392 2.392 1.2 4.648 1.2h6.704c2.256 0 3.448 1.192 3.448 3.448v3.84l-.104-.088z"
        fill={color}
      />
    </Svg>
  );
};

export default EditImageSVG;
