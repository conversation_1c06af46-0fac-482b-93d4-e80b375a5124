//import liraries
import React, { Component } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import ModalWrapper from '../../../../component/ModalWrapper';

interface LiveStreamModalProps {
  visible: boolean;
  onSchedule: () => void;
  onGoLive: () => void;
  onClose: () => void;
}

const NewLiveStreamModal: React.FC<LiveStreamModalProps> = ({
  visible,
  onSchedule,
  onGoLive,
  onClose,
}) => {
  return (
    <ModalWrapper isVisible={visible} transparent animationType="slide" onCloseModal={onClose}>
      <View style={{}}>
        <Text style={styles.title}>Live stream</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.scheduleButton} onPress={onSchedule}>
            <Text style={styles.scheduleText}>Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.goLiveButton} onPress={onGoLive}>
            <Text style={styles.goLiveText}>Go live</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ModalWrapper>
  );
};

const styles = StyleSheet.create({
  dragIndicator: {
    width: 40,
    height: 5,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 24,
    color: 'black',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 16,

    justifyContent: 'center',
    width: '100%',
  },
  scheduleButton: {
    backgroundColor: '#F2F2F2',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 16,
    width: '45%',
    alignItems: 'center',
  },
  scheduleText: {
    color: '#222',
    fontSize: 18,
  },
  goLiveButton: {
    backgroundColor: '#6A41D9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 16,
    width: '45%',
    alignItems: 'center',
  },
  goLiveText: {
    color: 'white',
    fontSize: 18,
  },
});

export default NewLiveStreamModal;
