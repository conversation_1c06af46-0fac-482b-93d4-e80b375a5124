import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import Accordion, { AccordionItem } from '../../../../component/Accordion';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import InfoSVG from '../../../../assets/svgIcons/InfoSVG';
import ProfileSVG from '../../../../assets/svgIcons/ProfileSVG';
import SecuritySettingSVG from '../../../../assets/svgIcons/securitySettingSVG';

const generalItems: AccordionItem[] = [
  {
    title: 'What is ChatBucket?',
    content:
      'ChatBucket is a next-generation chatting application that lets you connect, chat, and share securely with friends and groups.',
  },
  {
    title: 'How do I create an account?',
    content:
      'Download the app, tap Sign Up, and follow the instructions to create your ChatBucket account.',
  },
  {
    title: 'How do I start a new chat?',
    content:
      'Tap the New Chat button on the home screen, select a contact, and start messaging instantly.',
  },
  {
    title: 'How do I invite friends to ChatBucket?',
    content:
      'Go to the Invite Friends section in the menu and share your invite link via SMS, email, or social media.',
  },
];

const accountItems: AccordionItem[] = [
  {
    title: 'How do I reset my password?',
    content: 'Go to Account Settings > Security > Reset Password and follow the instructions.',
  },
  {
    title: 'How do I change my profile picture or username?',
    content: 'Open your profile, tap Edit, and update your picture or username as desired.',
  },
  {
    title: 'How do I delete my account?',
    content:
      'Go to Account Settings > Delete Account and follow the prompts to permanently remove your account.',
  },
];

const securityItems: AccordionItem[] = [
  {
    title: 'Is my data secure on ChatBucket?',
    content:
      'Yes, your messages and data are encrypted and stored securely. We prioritize your privacy and security.',
  },
  {
    title: 'How do I block or report a user?',
    content:
      'Open the chat with the user, tap on their profile, and select Block or Report from the options.',
  },
  {
    title: 'How do I contact support?',
    content: 'Go to the Help section in the app and tap Contact Us to reach our support team.',
  },
];

const FAQScreen = () => {
  return (
    <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <SafeAreaView />
      <HeaderBackWithTitle title="FAQ" />
      <View style={styles.whiteContainer}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} showsVerticalScrollIndicator={false}>
          <View style={styles.headerRow}>
            <InfoSVG style={styles.headerIcon} />
            <Text style={styles.headerText}>General</Text>
          </View>
          <Accordion items={generalItems} />
          <View style={styles.headerRow}>
            <ProfileSVG style={styles.headerIcon} />
            <Text style={styles.headerText}>Account</Text>
          </View>
          <Accordion items={accountItems} />
          <View style={styles.headerRow}>
            <SecuritySettingSVG style={styles.headerIcon} />
            <Text style={styles.headerText}>Security</Text>
          </View>
          <Accordion items={securityItems} />
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 4,
  },
  headerIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
    resizeMode: 'contain',
  },
  headerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.black_23,
  },
});

export default FAQScreen;
