// components/Common/Gestrurewrapper.tsx
import React from 'react';
import GestureHandler from '../GesturehandlerComponent/GestureHandler';

interface GestureWrapperProps {
  id: number | string;
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number | string, type: string, bool: boolean) => boolean;
  deleteItem: (id: number | string, type: string) => void;
  xPos: number;
  yPos: number;
  rotate: number;
  scale: number;
  onSelectSticker: () => void;
  index: number;
  zIndex: number;
  topItemzIdx: any;
  type: string;
  children: React.ReactNode;
}

const GestureWrapper: React.FC<GestureWrapperProps> = ({
  id,
  itemDragging,
  isDeletable,
  deleteItem,
  xPos,
  yPos,
  rotate,
  scale,
  onSelectSticker,
  index,
  zIndex,
  topItemzIdx,
  type,
  children,
}) => {
  return (
    <GestureHandler
      id={id}
      type={type}
      index={index}
      itemDragging={itemDragging}
      isDeletable={isDeletable}
      deleteItem={deleteItem}
      xPos={xPos}
      yPos={yPos}
      rotate={rotate}
      scale={scale}
      zIndex={zIndex}
      topItemzIdx={topItemzIdx}
      onSelectSticker={onSelectSticker}
    >
      {children}
    </GestureHandler>
  );
};

export default React.memo(GestureWrapper);
