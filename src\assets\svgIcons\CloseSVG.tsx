import * as React from "react";
import Svg, { Path } from "react-native-svg";
import { colors } from "../../theme/colors";



interface SvgComponentProps {
    size?: number;
    color?: string;
}


const CloseSVG: React.FC<SvgComponentProps> = ({
    size = 13,
    color = colors.gray_80,
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 13 13"
            fill="none"
            {...props}
        >
            <Path
                opacity={0.5}
                d="M7.691 6.5l5.062-5.062A.841.841 0 1011.562.247L6.5 5.309 1.438.247a.841.841 0 10-1.19 1.19L5.308 6.5.247 11.562a.841.841 0 101.191 1.191L6.5 7.691l5.062 5.062a.84.84 0 001.19 0 .84.84 0 000-1.19L7.692 6.5z"
                fill={color}
            />
        </Svg>
    );
};



export default CloseSVG;


