import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  View,
  Text,
  Switch,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import CommonView from '../../../component/CommonView';
import { colors } from '../../../theme/colors';
import Api from '../../../utils/api';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import SingleTickSVG from '../../../assets/svgIcons/SingleTickSVG';
import ModalWrapper from '../../../component/ModalWrapper';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import { IGroupMember } from '../../../service/ChatSpacesService';
import { ChatService } from '../../../service/ChatService';
import { getPrivileges } from '../../../utils/ApiService';

type GroupPermissionsParams = {
  GroupPermissions: {
    chatSpaceId: string;
    selectedMember: any;
  };
};

export type MemberPermissions = {
  canSendMessage: boolean;
  canSendMedia: {
    photos: boolean;
    videos: boolean;
    files: boolean;
    music: boolean;
    voiceMessages: boolean;
    videoMessages: boolean;
    stickersGifs: boolean;
    polls: boolean;
    embedLinks: boolean;
  };
  canAddUsers: boolean;
  canPinMessage: boolean;
  canChangeChatInfo: boolean;
  canManageLiveStreams: boolean;
  canRemainAnonymous: boolean;
  canAddNewAdmins: boolean;
};

// Moved to a constant to be used as a default state
export const defaultMemberPermissions: MemberPermissions = {
  canSendMessage: true,
  canSendMedia: {
    photos: true,
    videos: true,
    files: true,
    music: true,
    voiceMessages: true,
    videoMessages: true,
    stickersGifs: true,
    polls: true,
    embedLinks: true,
  },
  canAddUsers: true,
  canPinMessage: false,
  canChangeChatInfo: false,
  canManageLiveStreams: true,
  canRemainAnonymous: true,
  canAddNewAdmins: true,
};

const GroupPermissions = () => {
  const route = useRoute<RouteProp<GroupPermissionsParams, 'GroupPermissions'>>();
  const chatSpaceId = route.params.chatSpaceId;
  const userIds = [route.params.selectedMember.userId];
  const userId = route.params.selectedMember.userId;
  const selectedMember = route.params.selectedMember;
  const [isUpdating, setIsUpdating] = useState(false);
  const [loading, setLoading] = useState(true);
  

  // Initialize with a default value to avoid 'undefined' errors
  const [memberPermissions, setMemberPermissions] =
    useState<MemberPermissions>(defaultMemberPermissions);

  const navigation = useNavigation();

  useEffect(() => {
    const fetchMemberPrivileges = async () => {
      setLoading(true);
      try {
        const privilegesResponse = await getPrivileges(chatSpaceId, userId);
        const fetchedPrivileges = privilegesResponse.privileges;
        setMemberPermissions((prev) => ({
          ...prev,
          canSendMessage: fetchedPrivileges.canSendMessage ?? prev.canSendMessage,
          canSendMedia: {
            photos: fetchedPrivileges.canSendMedia?.photos ?? prev.canSendMedia.photos,
            videos: fetchedPrivileges.canSendMedia?.videos ?? prev.canSendMedia.videos,
            files: fetchedPrivileges.canSendMedia?.files ?? prev.canSendMedia.files,
            music: fetchedPrivileges.canSendMedia?.music ?? prev.canSendMedia.music,
            voiceMessages:
              fetchedPrivileges.canSendMedia?.voiceMessages ?? prev.canSendMedia.voiceMessages,
            videoMessages:
              fetchedPrivileges.canSendMedia?.videoMessages ?? prev.canSendMedia.videoMessages,
            stickersGifs:
              fetchedPrivileges.canSendMedia?.stickersGifs ?? prev.canSendMedia.stickersGifs,
            polls: fetchedPrivileges.canSendMedia?.polls ?? prev.canSendMedia.polls,
            embedLinks: fetchedPrivileges.canSendMedia?.embedLinks ?? prev.canSendMedia.embedLinks,
          },
          canAddUsers: fetchedPrivileges.canAddUsers ?? prev.canAddUsers,
          canPinMessage: fetchedPrivileges.canPinMessage ?? prev.canPinMessage,
          canChangeChatInfo: fetchedPrivileges.canChangeChatInfo ?? prev.canChangeChatInfo,
          // Assuming these are also part of the API response, if not, they can be removed
          canManageLiveStreams: fetchedPrivileges.canManageLiveStreams ?? prev.canManageLiveStreams,
          canRemainAnonymous: fetchedPrivileges.canRemainAnonymous ?? prev.canRemainAnonymous,
          canAddNewAdmins: fetchedPrivileges.canAddNewAdmins ?? prev.canAddNewAdmins,
        }));
      } catch (error) {
        console.error('Error fetching privileges:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchMemberPrivileges();
  }, [chatSpaceId, userId]);

  const [openModal, setOpenModal] = useState(false);

  const handleSave = async () => {
    setIsUpdating(true);
    try {
      const endpoint = `v1/chatspaces/${chatSpaceId}/memberPrivilege`;
      const payload = { userIds, ...memberPermissions };
      await Api.patch(endpoint, payload);
      navigation.goBack();
    } catch (err) {
      console.error(err);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const renderToggle = (label: string, value: boolean, onChange: (value: boolean) => void) => (
    <View style={styles.toggleRow}>
      <Text style={styles.label}>{label}</Text>
      <Switch
        value={value}
        onValueChange={onChange}
        trackColor={{ false: '#E9E9EA', true: colors.mainPurple }}
        thumbColor={colors.white}
      />
    </View>
  );

  if (loading) {
    return (
      <CommonView headerTitle="Group analytics">
        <ActivityIndicator size="large" color={colors.back_opacity_10} />
      </CommonView>
    );
  }

  return (
    <CommonView
      headerTitle="Manage Permissions"
      renderRight={() =>
        isUpdating ? (
          <ActivityIndicator color={colors.white} size="small" />
        ) : (
          <TouchableOpacity
            onPress={handleSave}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <SingleTickSVG color={colors.white} size={17}/>
          </TouchableOpacity>
        )
      }
    >
      <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.avatarSection}>
          <View style={styles.avatarTextContainer}>
            <Image source={{ uri: selectedMember.user.image }} style={styles.avatarImage} />
            <View style={{ flexDirection: 'column' }}>
              <Text style={styles.userName}>{selectedMember.user.name}</Text>
              <Text style={styles.userHandle}>@{selectedMember?.user.username}</Text>
            </View>
          </View>
          <View style={styles.roleBadge}>
            <Text style={styles.roleText}>{selectedMember.role}</Text>
          </View>
        </View>
        <Text style={styles.sectionTitle}>This user can do</Text>
        {renderToggle('Send messages', memberPermissions.canSendMessage, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canSendMessage: val })),
        )}
        {renderToggle(
          'Send media',
          Object.values(memberPermissions.canSendMedia).every(Boolean),
          (val) =>
            setMemberPermissions((prev) => ({
              ...prev,
              canSendMedia: {
                ...prev.canSendMedia,
                photos: val,
                videos: val,
                files: val,
                music: val,
                voiceMessages: val,
                videoMessages: val,
                stickersGifs: val,
                polls: val,
                embedLinks: val,
              },
            })),
        )}
        {renderToggle('Pin messages', memberPermissions.canPinMessage, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canPinMessage: val })),
        )}
        {renderToggle('Change chat info', memberPermissions.canChangeChatInfo, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canChangeChatInfo: val })),
        )}
        {renderToggle('Add users', memberPermissions.canAddUsers, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canAddUsers: val })),
        )}
        {renderToggle('Manage live streams', memberPermissions.canManageLiveStreams, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canManageLiveStreams: val })),
        )}
        {renderToggle('Remain anonymous', memberPermissions.canRemainAnonymous, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canRemainAnonymous: val })),
        )}
        {renderToggle('Add new admins', memberPermissions.canAddNewAdmins, (val) =>
          setMemberPermissions((prev) => ({ ...prev, canAddNewAdmins: val })),
        )}
        <TouchableOpacity onPress={handleOpenModal}>
          <Text style={styles.kickText}>🚫 Kick and Ban from Group</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* <ModalWrapper isVisible={openModal} onCloseModal={() => setOpenModal(false)}>
      </ModalWrapper> */}
    </CommonView>
  );
};
const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  avatarSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 16,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
  },
  userHandle: {
    fontSize: 14,
    color: colors.gray_80,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.gray_80,
    marginVertical: 12,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    flex: 1,
    color: colors.black_23,
  },
  kickText: {
    marginTop: 24,
    color: colors.thick_red,
    fontWeight: '400',
    fontSize: 16,
    textAlign: 'center',
  },
  avatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    backgroundColor: '#ccc', // fallback background
  },
  avatarTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleBadge: {
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 14,
    backgroundColor: 'rgba(91, 135, 153, 0.08)',
  },
  roleText: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.gray_80,
    lineHeight: 15,
    textAlign: 'center',
  },
});
export default GroupPermissions;
