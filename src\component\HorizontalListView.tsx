import { FlatList, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import React, { useState } from 'react';
import { commonFontStyle, hp } from '../theme/fonts';
import { colors } from '../theme/colors';
import { useTranslation } from 'react-i18next';

interface Props {
  data: any;
  defaultSelectedItem: string;
  selectedItem: (item: any) => void;
  contentContainerStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  bottomLine?: boolean;
  styleViewTwo?: boolean;
  [key: string]: any;
}

const HorizontalListView = ({
  data,
  defaultSelectedItem,
  selectedItem,
  contentContainerStyle,
  containerStyle,
  bottomLine = true,
  styleViewTwo = false,
  ...props
}: Props) => {
  const [tab, setTab] = useState(defaultSelectedItem);
  const { t } = useTranslation();

  const renderItem = ({ item, index }: any) => {
    return (
      <TouchableOpacity
        key={index}
        onPress={() => {
          selectedItem(item);
          setTab(item);
        }}
        style={[
          styleViewTwo ? styles.tabViewInnerTwo : styles.tabViewInner,
          {
            borderColor: styleViewTwo && tab == item ? colors.mainPurple : colors._DADADA_gray,
            backgroundColor:
              styleViewTwo && tab == item ? colors.opacity_main_purple_15 : colors.white,
          },
        ]}
      >
        <Text
          style={
            tab == item
              ? { ...commonFontStyle(600, 15, colors.mainPurple) }
              : { ...commonFontStyle(500, 15, colors.gray_80) }
          }
        >
          {t(item)}
        </Text>
        {bottomLine && (
          <View
            style={[
              styles.lineBottom,
              {
                backgroundColor: tab == item ? colors.mainPurple : 'transparent',
              },
            ]}
          />
        )}
      </TouchableOpacity>
    );
  };
  return (
    <View style={styles.tabView}>
      <FlatList
        data={data}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          ...styles.contentContainerStyle,
          ...contentContainerStyle,
        }}
        renderItem={renderItem}
        horizontal
        {...props}
      />
    </View>
  );
};

export default HorizontalListView;

const styles = StyleSheet.create({
  tabView: {},
  tabViewInner: {
    paddingHorizontal: hp(2),
    paddingTop: hp(2),
  },
  tabViewInnerTwo: {
    borderWidth: 1,
    gap: 10,
    paddingHorizontal: hp(2),
    height: hp(4),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  lineBottom: {
    height: 3,
    width: 20,
    borderRadius: 50,
    alignSelf: 'center',
    marginBottom: hp(2),
    marginTop: 4,
  },
  contentContainerStyle: {},
});
