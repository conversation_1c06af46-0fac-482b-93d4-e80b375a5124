import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface UserAvatarSVGProps {
    size?: number;
    color?: string;
}

const ReplyAvatarSVG: React.FC<UserAvatarSVGProps> = ({
    size = 12,
    color = "#232323",
    ...props
}) => {
    const aspectRatio = 13 / 12;
    const width = size;
    const height = size * aspectRatio;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 12 13"
            fill="none"
            style={{ width, height }}
            {...props}
        >
            <Path
                d="M6 6.842A3.421 3.421 0 106 0a3.421 3.421 0 000 6.842zM6 .977a2.443 2.443 0 110 4.887A2.443 2.443 0 016 .977zm5.865 9.442a4.503 4.503 0 00-4.307-3.258H4.45A4.51 4.51 0 00.143 10.42H.136a1.792 1.792 0 001.72 2.287h8.276a1.792 1.792 0 001.733-2.287zm-1.068.984a.816.816 0 01-.652.326H1.863a.815.815 0 01-.782-1.036A3.519 3.519 0 014.45 8.152h3.102A3.512 3.512 0 0110.92 10.7a.808.808 0 01-.13.703h.007z"
                fill={color}
            />
        </Svg>
    );
};

export default ReplyAvatarSVG;