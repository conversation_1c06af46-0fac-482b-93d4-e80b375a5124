import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function EyeOutlineSvg(props) {
  return (
    <Svg
      width={17}
      height={13}
      viewBox="0 0 17 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M16.349 6.203s-3.364 5.203-7.675 5.203C4.364 11.406 1 6.203 1 6.203S4.364 1 8.674 1c4.31 0 7.675 5.203 7.675 5.203z"
        stroke="#fff"
        strokeWidth={1.30075}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M8.674 8.33a2.127 2.127 0 100-4.254 2.127 2.127 0 000 4.254z"
        stroke="#fff"
        strokeWidth={1.30225}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

export default EyeOutlineSvg;
