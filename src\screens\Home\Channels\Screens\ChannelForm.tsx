import React, { useState } from 'react';
import { View, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { useForm, Controller, Resolver } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import ImageCropPicker from 'react-native-image-crop-picker';
import CommonView from '../../../../component/CommonView';
import EditImageSVG from '../../../../assets/svgIcons/EditImageSVG';
import Input from '../../../../component/Input';
import ButtonPurple from '../../../../component/ButtonPurple';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import { IMAGES } from '../../../../assets/Images';
import { VALIDATION_RULES } from '../../../../utils/commonFunction';

export type ChannelFormValues = {
  name: string;
  description: string;
  image?:
    | string
    | null
    | {
        uri: string;
        type?: string;
        fileName?: string;
      };
};

// Schema for validation
const channelSchema = Yup.object().shape({
  name: Yup.string()
    .required('Channel name is required')
    .min(
      VALIDATION_RULES.MIN_CHANNEL_NAME,
      `Minimum ${VALIDATION_RULES.MIN_CHANNEL_NAME} characters`,
    )
    .max(
      VALIDATION_RULES.MAX_CHANNEL_NAME,
      `Maximum ${VALIDATION_RULES.MAX_CHANNEL_NAME} characters`,
    ),
  description: Yup.string()
    .required('Description is required')
    .min(
      VALIDATION_RULES.MIN_CHANNEL_DESCRIPTION,
      `Minimum ${VALIDATION_RULES.MIN_CHANNEL_DESCRIPTION} characters`,
    )
    .max(
      VALIDATION_RULES.MAX_CHANNEL_DESCRIPTION,
      `Maximum ${VALIDATION_RULES.MAX_CHANNEL_DESCRIPTION} characters`,
    ),
  image: Yup.string().nullable(),
});

type Props = {
  isEditing?: boolean;
  initialValues?: ChannelFormValues;
  onSubmit: (values: ChannelFormValues) => void;
};

export default function ChannelForm({ isEditing = false, initialValues, onSubmit }: Props) {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ChannelFormValues>({
    resolver: yupResolver(channelSchema) as Resolver<ChannelFormValues>,
    defaultValues: {
      name: initialValues?.name || '',
      description: initialValues?.description || '',
      image: initialValues?.image,
    },
  });

  const [localImage, setLocalImage] = useState<string | null>(initialValues?.image ?? null);

  const pickImage = async () => {
    try {
      const image = await ImageCropPicker.openPicker({ cropping: true });
      const uri = image.path ?? image.sourceURL;
      if (uri) {
        setLocalImage(uri);
        setValue('image', uri);
      }
    } catch (error) {
      console.log('Image selection cancelled', error);
    }
  };

  return (
    <CommonView
      headerTitle={isEditing ? 'Edit Channel' : 'Create Channel'}
      isScrollable
      containerStyle={{ paddingHorizontal: 0 }}
      contentContainerStyle={{ paddingHorizontal: 20 }}
    >
      <View style={styles.container}>
        {/* Profile Image Section */}
        <View style={styles.header}>
          <View style={styles.circle}>
            {localImage ? (
              <Image source={{ uri: localImage }} style={styles.image} />
            ) : (
              <Image source={IMAGES.profile_image} style={styles.image} />
            )}
            <TouchableOpacity style={styles.editIcon} onPress={pickImage}>
              <EditImageSVG size={20} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Channel Name */}
        <Controller
          control={control}
          name="name"
          render={({ field: { onChange, value } }) => (
            <Input
              placeHolder="Channel name"
              value={value}
              onChangeText={onChange}
              error={errors.name?.message}
            />
          )}
        />

        {/* Channel Description */}
        <Controller
          control={control}
          name="description"
          render={({ field: { onChange, value } }) => (
            <Input
              placeHolder="Channel description"
              value={value}
              onChangeText={onChange}
              error={errors.description?.message}
              multiline
            />
          )}
        />

        {/* Submit Button */}
        <View style={styles.buttonContainer}>
          <ButtonPurple
            title={isEditing ? 'Update Channel' : 'Create Channel'}
            onPress={handleSubmit(onSubmit)}
            isLoading={isSubmitting}
            disabled={isSubmitting}
          />
        </View>
      </View>
    </CommonView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignContent: 'center',
    paddingVertical: 50,
  },
  header: { alignItems: 'center', marginBottom: hp(2) },
  circle: {
    width: hp(10),
    height: hp(10),
    borderRadius: hp(5),
    backgroundColor: colors.gray_80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  image: { width: '100%', height: '100%', borderRadius: hp(5) },
  editIcon: {
    position: 'absolute',
    bottom: -15,
    backgroundColor: colors.white,
    padding: 5,
    borderRadius: 15,
  },
  buttonContainer: {
    paddingVertical: 30,
  },
});
