import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import { AppStyles } from '../../../theme/appStyles';
import RenderUserIcon from '../../../component/RenderUserIcon';
import { IMAGES } from '../../../assets/Images';
import BlockSVG from '../../../assets/svgIcons/BlockSVG';

type BlockedUserData = {
  id: string;
  displayName: string;
  displayPic?: string;
  lastMessage?: {
    text: string;
  };
};

type Props = {
  data: BlockedUserData;
  onPress?: (user: BlockedUserData) => void;
  onPressUnblock?: (user: BlockedUserData) => void;
};

const BlockedUserItem = ({ data, onPress, onPressUnblock }: Props) => {
  const handleUnblockPress = () => {
    onPressUnblock?.(data);
  };

  return (
    <TouchableOpacity style={AppStyles.flex} onPress={() => onPress?.(data)}>
      <View style={styles.rowView}>
        <View>
          <RenderUserIcon url={data?.displayPic} image={IMAGES.profile_image} size={50} />
        </View>
        <View style={AppStyles.flex}>
          <Text numberOfLines={1} style={styles.title}>
            {data?.displayName || 'Unknown User'}
          </Text>
        </View>
        <TouchableOpacity onPress={handleUnblockPress} style={styles.unblockButton}>
          <BlockSVG size={24} color={colors._F61B1B_red} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default BlockedUserItem;

const styles = StyleSheet.create({
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(1),
    paddingVertical: hp(1),
    gap: 15,
  },
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  lastMessage: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: 3,
  },
  unblockButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
