import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet, Text, Platform, ScrollView } from 'react-native';
import HeaderBackWithTitle from '../../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { CButton } from '../../../../../common';
import PinInputField from '../../../../../component/Common/PinInputField';
import { SCREENS } from '../../../../../navigation/screenNames';

const ChangeSecurityPinScreen = () => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  const [currentPin, setCurrentPin] = useState<string[]>(['', '', '', '']);
  const [newPin, setNewPin] = useState<string[]>(['', '', '', '']);
  const [confirmPin, setConfirmPin] = useState<string[]>(['', '', '', '']);
  const [error, setError] = useState<string>('');

  const savedPin = '1234';

  const handleCurrentPinChange = (pin: string[]) => {
    setCurrentPin(pin);
    setError('');
  };

  const handleNewPinChange = (pin: string[]) => {
    setNewPin(pin);
    setError('');
  };

  const handleConfirmPinChange = (pin: string[]) => {
    setConfirmPin(pin);
    setError('');
  };

  const handleSubmit = () => {
    if (currentPin.join('') !== savedPin) {
      setError('Current PIN is incorrect');
      return;
    }

    if (newPin.some((digit) => digit === '')) {
      setError('Please enter a new 4-digit PIN');
      return;
    }

    if (newPin.join('') === currentPin.join('')) {
      setError('New PIN cannot be the same as current PIN');
      return;
    }

    if (confirmPin.some((digit) => digit === '')) {
      setError('Please confirm your new PIN');
      return;
    }

    if (newPin.join('') !== confirmPin.join('')) {
      setError('New PINs do not match');
      return;
    }
    navigation.goBack();
  };

  const isPinComplete = () => {
    return (
      currentPin.every((digit) => digit !== '') &&
      newPin.every((digit) => digit !== '') &&
      confirmPin.every((digit) => digit !== '')
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Change PIN" onBack={() => navigation.goBack()} />
      <ScrollView
        style={styles.whiteContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.pinSection}>
          <Text style={styles.pinLabel}>Current PIN</Text>
          <PinInputField
            length={4}
            value={currentPin}
            onChange={handleCurrentPinChange}
            secureTextEntry={true}
            autoFocus={false}
            containerStyle={styles.pinInputContainer}
            inputStyle={styles.pinInput}
            showCursor={true}
            showFocusedStyle={false}
          />
        </View>

        <View style={styles.pinSection}>
          <Text style={styles.pinLabel}>New PIN</Text>
          <PinInputField
            length={4}
            value={newPin}
            onChange={handleNewPinChange}
            secureTextEntry={true}
            autoFocus={false}
            containerStyle={styles.pinInputContainer}
            inputStyle={styles.pinInput}
            showCursor={true}
            showFocusedStyle={false}
          />
        </View>

        <View style={styles.pinSection}>
          <Text style={styles.pinLabel}>Confirm New PIN</Text>
          <PinInputField
            length={4}
            value={confirmPin}
            onChange={handleConfirmPinChange}
            secureTextEntry={true}
            autoFocus={false}
            containerStyle={styles.pinInputContainer}
            inputStyle={styles.pinInput}
            showCursor={true}
            showFocusedStyle={false}
          />
        </View>

        {error ? <Text style={styles.errorText}>{error}</Text> : null}

        <CButton
          onPress={handleSubmit}
          cStyle={[styles.button, !isPinComplete() && styles.buttonDisabled]}
          clickable={!isPinComplete()}
        >
          <Text style={styles.buttonText}>Submit</Text>
        </CButton>

        <View style={styles.keyboardSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  keyboardView: {
    flex: 1,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingTop: hp(2),
    paddingHorizontal: hp(2),
    marginTop: 8,
  },
  scrollContent: {
    paddingTop: hp(4),
    flexGrow: 1,
  },
  pinSection: {
    marginBottom: hp(3),
  },
  pinLabel: {
    ...commonFontStyle(600, 16, colors.black_23),
    marginBottom: hp(2),
    textAlign: 'center',
  },
  pinInputContainer: {
    justifyContent: 'center',
  },
  pinInput: {
    width: 60,
    height: 60,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: colors.gray_f3,
    backgroundColor: colors.white,
    fontSize: 20,
    color: colors.black_23,
    marginHorizontal: 15,
  },
  errorText: {
    ...commonFontStyle(400, 14, colors.red_ff4444),
    textAlign: 'center',
    marginBottom: hp(2),
  },
  button: {
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    width: '100%',
  },
  buttonDisabled: {
    backgroundColor: colors._DADADA_gray,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  keyboardSpacer: {
    height: Platform.OS === 'ios' ? hp(10) : hp(5),
  },
});

export default ChangeSecurityPinScreen;
