// EventMessage.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import {
  ConversationType,
  IMessage,
  MessageEventType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { buildUserData as buildSenderData, ChatService } from '../../../../service/ChatService';
import { colors } from '../../../../theme/colors';
import { commonFontStyle } from '../../../../theme/fonts';
import useUser from '../../../../device-storage/realm/hooks/useUser';
import { getRealm, useRealm } from '../../../../device-storage/realm/realm';

interface EventMessageProps {
  msgData: IMessage;
  currentUserId?: string;
}

const EventMessage = ({ msgData, currentUserId }: EventMessageProps) => {
  const [eventText, setEventText] = useState<string>('');

  const senderData = useUser(msgData?.senderId);

  useEffect(() => {
    const generateEventText = async () => {
      if (!msgData.eventType || !msgData.targetUserIds) return;
      const { eventType, targetUserIds, senderId } = msgData;
      let action = '';
      let targets: string[] = [];

      // Determine the action verb based on the event type
      switch (eventType) {
        case MessageEventType.MEMBER_ADDED:
          action = 'added';
          break;
        case MessageEventType.MEMBER_REMOVED:
          action = 'removed';
          break;
        case MessageEventType.USER_JOINED:
          action = 'joined';
          break;
        case MessageEventType.USER_LEFT:
          action = 'left';
          break;
        case MessageEventType.CHAT_SPACE_CREATED:
          action = 'created';
          break;
        case MessageEventType.CHAT_SPACE_DELETED:
          action = 'deleted';
          break;
        case MessageEventType.CHAT_SPACE_INFO_UPDATED:
          action = 'updated';
          break;
        case MessageEventType.MEMBER_PRIVILEGES_UPDATED:
          action = 'updated privileges of';
          break;
        case MessageEventType.ADMIN_PRIVILEGES_UPDATED:
          action = 'updated privileges of';
          break;
        case MessageEventType.OWNERSHIP_TRANSFERRED:
          action = 'transferred ownership';
          break;
        case MessageEventType.GROUP_ADDITION_REQUEST:
          action = 'requested to join';
          break;
        case MessageEventType.GROUP_ADDITION_REQUEST_ACCEPTED:
          action = 'accepted your request to join';
          break;
        case MessageEventType.ROLE_UPDATED:
          action = 'promoted';
          break;
        case MessageEventType.CHAT_REACTION_ADDED:
          action = 'reacted with';
          break;
        case MessageEventType.CHAT_REACTION_REMOVED:
          action = 'removed reaction';
          break;

        // case MessageEventType.BLOCKED:
        //   action = 'blocked';
        //   break;
        // case MessageEventType.UNBLOCKED:
        //   action = 'unblocked';
        //   break;
      }

      if (eventType === MessageEventType.CHAT_SPACE_CREATED) {
        const isYouInTargets = targetUserIds.includes(currentUserId || '');
        const senderName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';

        if (isYouInTargets) {
          setEventText(`${senderName} ${action} the ${msgData.conversationType} and added you`);
        } else {
          setEventText(`${senderName} ${action} the ${msgData.conversationType}`);
        }
        return;
      }

      if (eventType === MessageEventType.ROLE_UPDATED) {
        const payload = JSON.parse(msgData.eventPayload || '{}');
        const { updatedRole, existingRole } = payload;

        const senderName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';

        const targetId = targetUserIds?.[0];
        let targetName = 'Someone';

        if (targetId) {
          if (targetId === currentUserId) {
            targetName = 'you';
          } else {
            try {
              const user = await buildSenderData(targetId);
              targetName = user?.name || 'Someone';
            } catch {
              targetName = 'Someone';
            }
          }
        }

        let actionText = '';

        if (existingRole === 'member' && updatedRole === 'admin') {
          actionText = `promoted ${targetName} to admin`;
        } else if (existingRole === 'admin' && updatedRole === 'member') {
          actionText = `demoted ${targetName} to member`;
        } else {
          actionText = `changed ${targetName}'s role to ${updatedRole}`;
        }

        setEventText(`${senderName} ${actionText}`);
        return;
      }

      if (eventType === MessageEventType.CHAT_SPACE_INFO_UPDATED) {
        const senderName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';
        setEventText(`${senderName} ${action} ${msgData.conversationType} info`);
        return;
      }

      if (eventType === MessageEventType.USER_JOINED) {
        const joinerName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';
        const actionText =
          msgData.conversationType === ConversationType.CHANNEL
            ? 'started following'
            : `joined the ${msgData.conversationType}`;
        setEventText(`${joinerName} ${actionText}`);
        return;
      }

      // Handle specific event messages for a user leaving
      if (eventType === MessageEventType.USER_LEFT) {
        const leaverName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';
        setEventText(`${leaverName} left from ${msgData.conversationType}`);
        return;
      }

      // Resolve user names for the target IDs
      for (const userId of targetUserIds) {
        if (userId === currentUserId) {
          targets.push('you');
        } else {
          try {
            const user = await buildSenderData(userId);
            targets.push(user?.name || 'Someone');
          } catch (error) {
            console.error('Failed to get user for event message:', error);
            targets.push('Someone');
          }
        }
      }

      const senderName = senderId === currentUserId ? 'You' : senderData?.name || 'Someone';
      if (targets.length > 0) {
        // Construct the final event text string
        const targetText = targets.join(', ');
        setEventText(`${senderName} ${action} ${targetText}`);
      } else if (action === 'left') {
        setEventText(`${senderName} ${action}`);
      }
    };

    generateEventText();
  }, [msgData, senderData, currentUserId]);

  if (!eventText) {
    return null; // Don't render anything until the text is generated
  }

  return (
    <View style={styles.eventWrapper}>
      <View style={styles.eventContainer}>
        <Text style={styles.eventText}>{eventText}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  eventWrapper: {
    alignSelf: 'center',
    width: '100%',
    alignItems: 'center',
    marginVertical: 8,
  },
  eventContainer: {
    backgroundColor: colors.gray_f3,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  eventText: {
    ...commonFontStyle(400, 12, colors._757575_gray),
    textAlign: 'center',
  },
});

export default EventMessage;
