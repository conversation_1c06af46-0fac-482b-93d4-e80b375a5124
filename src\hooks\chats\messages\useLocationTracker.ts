// useLocationTracker.js
import { useEffect, useRef } from 'react';
import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';
import io from 'socket.io-client';
import useSocket from '../../../socket-client/useSocket';

interface IProps {
  userId: string;
}

const useLocationTracker = ({ userId }: IProps) => {
  const socketRef = useRef(null);
  const watchIdRef = useRef<string>('');
  const { socket } = useSocket();

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true; // iOS handles permissions differently
  };

  const startTracking = async () => {
    // const granted = await requestPermissions();
    // if (!granted) return;

    socketRef.current = socket;

    watchIdRef.current = Geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        socketRef.current.emit('delete', {
          userId,
          location: { latitude, longitude },
          timestamp: new Date().toISOString(),
        });
      },
      (error) => console.error(error),
      {
        enableHighAccuracy: true,
        distanceFilter: 10,
        interval: 5000,
        fastestInterval: 2000,
      },
    );
  };

  console.log('watchIdRef', watchIdRef.current);

  const stopTracking = () => {
    if (watchIdRef.current) {
      Geolocation.clearWatch(watchIdRef.current);
      if (socketRef.current) {
        socketRef.current.emit('stopTracking', { userId });
        socketRef.current.disconnect();
      }
    }
  };

  useEffect(() => {
    return () => {
      stopTracking();
    };
  }, []);

  return { startTracking, stopTracking };
};

export default useLocationTracker;
