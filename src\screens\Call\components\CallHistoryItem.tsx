import { CallHistoryItemNormalized } from '../../../device-storage/realm/schemas/CallSchema';
import { <PERSON>ert, StyleSheet, Text, ToastAndroid, TouchableOpacity, View } from 'react-native';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import { FeatherIcons } from '../../../utils/vectorIcons';
import { CallHistoryAvatar } from './CallHistoryAvatar';
import { safeJsonParse } from '../../../common/utils';
import { CallUser } from '../../../api/calls/calls.api';
import { CallHistoryT } from '../CallScreen';
import { useMe } from '../../../hooks/util/useMe';
import { IUser } from '../../../device-storage/realm/schemas/UserSchema';
import { IUser as ISelfUser } from '../../../types/index.types';
import { ConversationSchema } from '../../../device-storage/realm/schemas/ConversationSchema';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import useUsers from '../../../device-storage/realm/hooks/useUsers';
import { ChatService } from '../../../service/ChatService';
import { StartCallParams } from '../../../types/calls.types';
import { maxMembersInCall } from '../../../utils/constants';
import { useCallContext } from '../../../Context/CallProvider';

export type CallVariant = 'groupCall' | 'p2pCall' | 'adhocCall';

const CallHistoryItem = ({ item }: { item: CallHistoryT }) => {
  const { user } = useMe();
  const { isUserBlocked } = useUsers();
  const { startCall } = useCallContext();

  const initiatorDetails: CallUser | null = safeJsonParse(item.initiatorJson);
  const participantDetails: CallUser[] | null = safeJsonParse(item.participantsJson);
  const invitedUserIdsDetails: CallUser[] | null = safeJsonParse(item.invitedUserIdsJson);
  let CallVariant: CallVariant = 'p2pCall';
  if (item.originType === 'groupConversation') {
    CallVariant = 'groupCall';
  } else if (invitedUserIdsDetails && invitedUserIdsDetails?.length > 1) {
    CallVariant = 'adhocCall';
  }
  const callDetails: CallHistoryItemNormalized = {
    ...item,
    initiatorDetails,
    participantDetails,
    invitedUserIdsDetails,
    callVariant: CallVariant,
  };
  if (item.syncPending === true) {
    return null;
  }

  function handlePress() {
    if (CallVariant === 'groupCall') {
      const groupConversation: { conversation: ConversationSchema } | null = safeJsonParse(
        item.originJson,
      );
      navigateTo(SCREENS.GropuCallInfoScreen, {
        callDetails: callDetails,
        groupConversation: groupConversation?.conversation,
      });
    } else if (CallVariant === 'adhocCall') {
      navigateTo(SCREENS.GropuCallInfoScreen, {
        callDetails: callDetails,
      });
    }
  }

  function handleCall() {
    const allUserIds = [
      ...(invitedUserIdsDetails || []).map((item) => {
        return item._id;
      }),
    ];
    if (initiatorDetails?._id) {
      allUserIds.push(initiatorDetails._id);
    }
    const uniqueUserIds = [...new Set(allUserIds)].filter((item) => item !== user?._id);
    // check whether we can call now or not?
    if (CallVariant === 'groupCall') {
      // handle group call
      const groupConversation: { conversation: ConversationSchema } | null = safeJsonParse(
        item.originJson,
      );
      const groupChatSpace = ChatService.getLiveChatSpaceById(
        groupConversation?.conversation.id || '',
      );
      if (!groupChatSpace) {
        ToastAndroid.show(
          'Group deleted or youre are no longer a member of this group.',
          ToastAndroid.SHORT,
        );
        return;
      }
      if (groupChatSpace.memberCount > maxMembersInCall) {
        ToastAndroid.show(
          `Group has more than ${maxMembersInCall} members.Cannot make a call`,
          ToastAndroid.SHORT,
        );
        return;
      }
      const payload: StartCallParams = {
        callType: callDetails.callType,
        origin: {
          type: 'groupConversation',
          conversation: {
            chatSpaceId: groupConversation?.conversation.id || '',
            displayName: groupConversation?.conversation.displayName || '',
            displayPic: groupConversation?.conversation.displayPic || '',
          },
        },
        recipients: [],
      };

      startCall(payload);
    } else if (CallVariant === 'adhocCall') {
      // handle adhoc call

      const { blockedUserIds, notBlockedUserIds } = uniqueUserIds.reduce(
        (acc, item) => {
          if (isUserBlocked(item)) {
            acc.blockedUserIds.push(item);
          } else {
            acc.notBlockedUserIds.push(item);
          }
          return acc;
        },
        {
          blockedUserIds: [],
          notBlockedUserIds: [],
        } as { blockedUserIds: string[]; notBlockedUserIds: string[] },
      );
      if (notBlockedUserIds.length === 0) {
        ToastAndroid.show('All users are blocked.Cannot make a call', ToastAndroid.SHORT);
      }
      if (blockedUserIds.length > 0) {
        Alert.alert(
          'Blocked users',
          'Some participants are blocked. Do you want to call with the remaining users?',
          [
            {
              text: 'No',
              style: 'cancel',
              onPress: () => {
                console.log('User cancelled call when blocked users found ');
              },
            },
            {
              text: 'Yes',
              style: 'default',
              onPress: () => {
                console.log('>>>>>>>>>>>>>... start call called ', notBlockedUserIds);
                // start call with not blocked users.
              },
            },
          ],
        );
      }
      const recipients: Partial<IUser>[] = notBlockedUserIds
        .map((item) => {
          const user = participantDetails?.find((pt) => {
            return pt._id === item;
          });
          if (user) {
            return {
              _id: user?._id,
              username: user?.username,
              name: user?.name,
              image: user?.image,
            };
          }
          return null;
        })
        .filter((item) => item !== null);

      const payload: StartCallParams = {
        callType: callDetails.callType,
        origin: {
          type: 'callHistory',
        },
        recipients: recipients,
      };

      // startCall(payload);
    } else {
      if (uniqueUserIds.length === 0) {
        ToastAndroid.show('Cannot place this Call from here', ToastAndroid.SHORT);
        return;
      }
      if (uniqueUserIds.length === 1) {
        const isBlockedUser = isUserBlocked(uniqueUserIds[0]);
        if (isBlockedUser) {
          ToastAndroid.show('This user is blocked.Cannot place Call', ToastAndroid.SHORT);
          return;
        } else {
          // call start call
          const participant = participantDetails?.find((pt) => {
            return pt._id === uniqueUserIds[0];
          });
          if (user) {
            const recipiennt: Partial<IUser> = {
              id: participant?._id,
              username: participant?.username,
              name: participant?.name,
              profilePic: participant?.image,
            };
            const payload: StartCallParams = {
              callType: callDetails.callType,
              origin: {
                type: 'callHistory',
              },
              recipients: [recipiennt],
            };
            startCall(payload);
            // console.log(payload, '>>>>>>>>>>>>>>>>>. ');
          }
        }
      }
    }
  }

  const title = parseTitle(callDetails, user);
  const callStatus = parseStatus(callDetails);

  return (
    <View style={styles.itemContainer}>
      <View
        style={{
          paddingRight: 10,
        }}
      >
        <CallHistoryAvatar CallVariant={CallVariant} item={callDetails} />
      </View>

      <TouchableOpacity style={{ ...styles.textContainer }} onPress={handlePress}>
        <Text style={styles.name}>{title}</Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <RenderIcon callStatus={callStatus} />

          <Text style={styles.details}>{calculateDateInfo(callDetails)}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        disabled={true}
        style={{ padding: 10, marginLeft: 15 }}
        onPress={handleCall}
      >
        {callDetails.callType === 'audio' ? (
          <FeatherIcons name="phone" size={20} color={colors.mainPurple} />
        ) : (
          <FeatherIcons name="video" size={20} color={colors.mainPurple} />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default CallHistoryItem;

const styles = StyleSheet.create({
  container: {
    gap: 20,
    backgroundColor: colors.white,
  },
  valueText: {
    ...commonFontStyle(600, 18, colors.black_23),
  },
  header: {
    ...commonFontStyle(600, 18, colors.black_23),
    fontWeight: 700,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.black,
  },
  details: {
    ...commonFontStyle(400, 12, colors.gray_80),
    marginLeft: 5,
  },

  newCallStyle: {
    position: 'absolute',
    bottom: 120,
    right: 16,
    borderRadius: 100,
    elevation: 18,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  newCallStyle1: {
    width: 60,
    height: 60,
  },
});

function parseTitle(item: CallHistoryItemNormalized, user: ISelfUser | null) {
  const isInitiator = item.initiator === user?._id.toString();
  const invitedUsers = item.invitedUserIdsDetails ?? [];
  const isGroupCall = invitedUsers.length > 1;
  let title = '';

  if (item.originType === 'groupConversation') {
    const groupConversation: { conversation: ConversationSchema } | null = safeJsonParse(
      item.originJson,
    );
    if (groupConversation) {
      title = groupConversation.conversation.displayName;
    } else {
      title = 'Unknown group';
    }

    return title;
  }

  if (isGroupCall) {
    if (isInitiator) {
      if (invitedUsers.length === 2) {
        title = `${invitedUsers[0].name} and ${invitedUsers[1].name}`;
      } else if (invitedUsers.length === 3) {
        title = `${invitedUsers[0].name}, ${invitedUsers[1].name} and ${1} other`;
      } else {
        title = `${invitedUsers[0].name} , ${invitedUsers[1].name} and ${
          invitedUsers.length - 2
        } others`;
      }
    } else {
      const invitedUsersWithoutMe = invitedUsers.filter(
        (item) => item._id !== user?._id.toString(),
      );
      if (invitedUsersWithoutMe.length === 1) {
        title = `${item.initiatorDetails?.name} and ${invitedUsersWithoutMe[0].name}`;
      } else if (invitedUsersWithoutMe.length === 2) {
        title = `${item.initiatorDetails?.name}, ${invitedUsersWithoutMe[0].name} and ${1} other`;
      } else {
        title = `${item.initiatorDetails?.name} , ${invitedUsersWithoutMe[0].name} and ${
          invitedUsersWithoutMe.length - 1
        } others`;
      }
    }
  } else {
    if (isInitiator) {
      title = invitedUsers[0]?.name ?? invitedUsers[0]?.username;
    } else {
      title =
        item.initiatorContact?.contactName ||
        (item.initiatorContact?.name?.trim() ?? item.initiatorDetails?.name?.trim() ?? 'unknown');
    }
  }

  return title;
}

function parseStatus(item: CallHistoryItemNormalized) {
  if (item.callStatus === 'outgoing') {
    return 'Outgoing';
  } else if (
    item.callStatus === 'missed' ||
    item.callStatus === 'rejected' ||
    item.callStatus === 'ignored'
  ) {
    return 'Missed';
  } else {
    return 'Incoming';
  }
}

export function calculateDateInfo(item: CallHistoryItemNormalized) {
  const dateObj = new Date(item.createdAt);

  if (!isNaN(dateObj.getTime())) {
    const options: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    };

    const time = dateObj.toLocaleString('en-US', options);
    const month = dateObj.toLocaleString('en-US', { month: 'short' });
    const day = dateObj.getDate();

    return `${time} on ${month} ${day}`;
  }

  return 'Invalid date';
}

function RenderIcon({ callStatus }: { callStatus: string }) {
  return (
    <>
      {callStatus === 'Incoming' && (
        <FeatherIcons name="arrow-down-left" size={14} color={'green'} />
      )}
      {callStatus === 'Missed' && <FeatherIcons name="arrow-down-left" size={14} color={'red'} />}
      {callStatus === 'Outgoing' && (
        <FeatherIcons name="arrow-up-right" size={14} color={'green'} />
      )}
    </>
  );
}

/*

for p2p call. 
if he opens the conversation. get the user details and store it in redis if not exists.


for ad_hoc calls.
if 

*/
