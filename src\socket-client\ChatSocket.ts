import { Socket } from 'socket.io-client';
import {
  ConversationType,
  IContactMessage,
  ILocationMessage,
  IMessage,
  MessageStatus,
  MessageType,
  Reaction,
} from '../device-storage/realm/schemas/MessageSchema';
import { notifEvents } from './notificationTypes';
import {
  ServerMessage,
  PinMessageDto,
  PinMessageResponse,
  UnpinMessageDto,
  UnpinMessageResponse,
} from '../types/socketPayload.type';
import { ChatService } from '../service/ChatService';
import { errorToast } from '../utils/commonFunction';
import { getRealm } from '../device-storage/realm/realm';
import { UpdateMode } from 'realm';

export type NewMessagePayload = {
  senderLocalId: string;
  receiverId: string;
  conversationType?: ConversationType;
  messageType: MessageType;
  text?: string;
  mediaUrl?: string;
  location?: ILocationMessage;
  contact?: IContactMessage;
  replyToMessageId?: string;
  [key: string]: unknown;
};
const emitNewMessage = (socket: Socket, payload: NewMessagePayload) => {
  const finalPayload = { ...payload };

  if (Array.isArray(finalPayload.mentionedIds) && finalPayload.mentionedIds.length === 0) {
    delete finalPayload.mentionedIds;
  }

  socket.emit(notifEvents.messages.send, finalPayload, (sentMessage: ServerMessage) => {
    try {
      if (sentMessage.error && sentMessage.error.trim()) {
        // this will avoid updating the message status if sent to a blocked user.
        // handle error here if possible
        //todo:  save error in the messageItem. add another field
        ChatService.ackSentMessage({ ...sentMessage, status: MessageStatus.FAILED });
        return;
      }
      ChatService.ackSentMessage(sentMessage);
    } catch (error) {
      console.log(error);
    }
  });
};

const emitReceivedMessages = (socket: Socket, messages: string[], messageLocalIds: string[]) => {
  const payload = {
    messagesReceived: messages,
    messageLocalIds,
  };
  socket.emit(notifEvents.messages.received, payload);
};

const emitPendingMessages = (socket: Socket, messages: IMessage[]) => {
  const messagesToSend: NewMessagePayload[] = messages.map((msg) => ({
    ...msg,
    senderLocalId: msg.localId,
  }));
  messagesToSend.forEach((message) => {
    emitNewMessage(socket, message);
  });
};

const emitMessagesSeenByMe = (
  socket: Socket,
  globalMessageIds: string[],
  messageLocalIds: string[],
) => {
  console.log('emitMessagesSeenByMe', globalMessageIds, 'correspondig local_ids', messageLocalIds);

  const payload = {
    messagesSeen: globalMessageIds,
    messageLocalIds,
  };
  socket.emit(notifEvents.messages.seen, payload);
};

const sendScheduleMessagges = (socket: Socket, globalId: string) => {
  const payload = {
    globalId: globalId,
  };
  socket.emit(notifEvents.messages.queued, payload);
};

const rescheduledMessage = (socket: Socket, globalId: string, scheduledAt: number) => {
  const payload = {
    globalId: globalId,
    scheduledAt: scheduledAt,
  };
  socket.emit(notifEvents.messages, payload);
};

const updateScheduleMessage = (socket: Socket, payload: any) => {
  socket.emit(notifEvents.messages.updateScheduled, payload, (data: any) => {
    console.log('update schedule event', data);
  });
};

const sendLiveLocationStopped = (socket: Socket, payload: any) => {
  console.log('payload-------', payload);
  socket.emit(notifEvents.messages.liveLocationStopped, payload, (data: any) => {
    console.log('send live location event', data);
  });
};

const emitDeleteMessages = (
  socket: Socket,
  messsageGlobalIds: string[],
  conversationId: string,
  forEveryone: boolean,
) => {
  const payload = {
    messagesDeleted: messsageGlobalIds,
    forEveryone: forEveryone,
    conversationId: conversationId,
  };
  socket.emit(notifEvents.messages.delete, payload, (response: any) => {
    if (response?.status) {
      ChatService.handleMessagesDeletedEvent(response?.data);
    }
    if (!response?.status) {
      errorToast('Message could not be deleted for everyone.');
    }
  });
};

const emitPinMessage = (socket: Socket, payload: PinMessageDto) => {
  socket.emit(notifEvents.messages.pin, payload, (response: PinMessageResponse) => {
    try {
      ChatService.handlePinResponse(response);
    } catch (error) {
      if (!response.status) {
        errorToast(response.message || 'Failed to pin message');
      }
    }
  });
};

const emitUnpinMessage = (socket: Socket, payload: UnpinMessageDto) => {
  socket.emit(notifEvents.messages.unpin, payload, (response: UnpinMessageResponse) => {
    console.log('[ChatSocket] Unpin response received:', response);
    try {
      if (response.status) {
        ChatService.handleUnpinResponse(response);
      } else {
        errorToast(response.message || 'Failed to unpin message');
      }
    } catch (error) {
      errorToast('Failed to unpin message');
    }
  });
};

const joinChannel = (socket: Socket, chatSpaceId: string, callback?: (response: any) => void) => {
  if (!chatSpaceId) {
    console.error('[EMIT BLOCKED] chatSpaces:join — chatSpaceId is missing!');
    return;
  }

  // console.log('[SOCKET EMIT] chatSpaces:join', { chatSpaceId });
  socket.emit(notifEvents.chatSpaces.join, { chatSpaceId }, (response: any) => {
    if (response?.error) {
      console.error('Failed to join channel:', response.error);
    } else {
      console.log('Successfully joined channel:', chatSpaceId);
    }
    if (callback) callback(response);
  });
};

const emitLeaveChatspace = (
  socket: Socket,
  chatSpaceId: string,
  callback?: (response: any) => void,
) => {
  if (!chatSpaceId) {
    console.error('[EMIT BLOCKED] chatSpaces:leave — chatSpaceId is missing!');
    return;
  }
  // console.log('[SOCKET EMIT] chatSpaces:leave', { chatSpaceId });
  socket.emit(notifEvents.chatSpaces.leave, { chatSpaceId }, (response: any) => {
    if (response?.error) {
      console.error('Failed to leave chatspace:', response.error);
    } else {
      // console.log('Successfully left channel:', chatSpaceId);
    }
    if (callback) callback(response);
  });
};

const emitAddReaction = (
  socket: Socket,
  messageId: string,
  newEmoji: string,
  userId: string,
  callback?: (response: any) => void,
) => {
  const realm = getRealm();

  const existing = realm
    .objects<Reaction>('Reaction')
    .filtered('messageId == $0 && userId == $1', messageId, userId)[0];

  // console.log('emiteaddreaction props================', messageId, newEmoji, userId);
  let eventName = notifEvents.messages.reaction.add;
  // console.log('eventName', eventName);

  if (existing && existing.emoji !== newEmoji) {
    // console.log('if block in emitaddreaction-------------', existing);
    eventName = notifEvents.messages.reaction.update;
  }

  const reactionId = `${messageId}_${userId}_${newEmoji}`;
  socket.emit(eventName, { messageId, reaction: newEmoji }, (response: any) => {
    if (response?.error) {
      console.error(
        `Failed to ${eventName.includes('update') ? 'update' : 'add'} reaction:`,
        response.error,
      );
    } else {
      // console.log('Writing reaction to Realm (emitAddReaction success):', reactionId, newEmoji);
      realm.write(() => {
        try {
          realm.create(
            'Reaction',
            {
              _id: reactionId,
              messageId,
              userId,
              emoji: newEmoji,
              createdAt: Date.now(),
            },
            UpdateMode.Modified,
          );
          // console.log('------------ reaction realmbd', reactionId, newEmoji);
        } catch (error) {
          // console.log('----- reaction error ', error);
        }
      });
    }

    if (callback) callback(response);
  });
};

const emitRemoveReaction = (
  socket: Socket,
  messageId: string,
  emoji: string,
  userId: string, // need userId to target exactly this user's reaction
  callback?: (response: any) => void,
) => {
  const reactionId = `${messageId}_${userId}_${emoji}`;

  socket.emit(
    notifEvents.messages.reaction.remove,
    { messageId, reaction: emoji },
    (response: any) => {
      if (response?.error) {
        console.error('Failed to remove reaction:', response.error);
      } else {
        const realm = getRealm();
        realm.write(() => {
          const existing = realm.objects('Reaction').filtered('_id == $0', reactionId);
          if (existing.length) {
            realm.delete(existing);
          }
        });
        // console.log(`Successfully removed reaction "${emoji}" from message ${messageId}`);
      }

      if (callback) callback(response);
    },
  );
};

const emitTyping = (socket: Socket, conversationId: string) => {
  socket.emit(notifEvents.users.typing, { conversationId });
};

const getLastSeen = (
  socket: Socket,
  userId: string,
): Promise<{ userId: string; lastSeen?: number; isUserOnline?: boolean } | null> => {
  return new Promise((resolve) => {
    socket.emit(notifEvents.users.lastSeen, { userId }, (response: any) => {
      try {
        const obj = response?.data ?? response?.body ?? response?.result ?? response ?? null;
        if (!obj) return resolve(null);
        const base = obj?.data ?? obj; // handle nesting
        const result = {
          userId,
          lastSeen:
            typeof base.lastSeen === 'number'
              ? base.lastSeen
              : typeof base.lastSeen === 'string'
              ? Date.parse(base.lastSeen)
              : undefined,
          isUserOnline: typeof base.isUserOnline === 'boolean' ? base.isUserOnline : undefined,
        };
        resolve(result);
      } catch (e) {
        resolve(null);
      }
    });
  });
};

export const ChatSocket = {
  emitNewMessage,
  emitReceivedMessages,
  emitMessagesSeenByMe,
  emitPendingMessages,
  emitDeleteMessages,
  sendLiveLocationStopped,
  emitPinMessage,
  emitUnpinMessage,
  sendScheduleMessagges,
  rescheduledMessage,
  updateScheduleMessage,
  joinChannel,
  emitLeaveChatspace,
  emitAddReaction,
  emitRemoveReaction,
  emitTyping,
  getLastSeen,
};
