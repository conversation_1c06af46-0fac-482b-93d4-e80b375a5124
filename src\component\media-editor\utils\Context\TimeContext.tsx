import React, { createContext, useContext, useState } from "react";

interface DataContextType {
  trimStartTime: number;
  setTrimStartTime: React.Dispatch<React.SetStateAction<number>>;
  trimEndTime: number;
  setTrimEndTime: React.Dispatch<React.SetStateAction<number>>;
  startTime: number;
  setStartTime: React.Dispatch<React.SetStateAction<number>>;
  endTime: number;
  setEndTime: React.Dispatch<React.SetStateAction<number>>;
  tempUri: string | null;
  setTempUri: React.Dispatch<React.SetStateAction<string | null>>;
  // resetTimeData: () => void; // Add reset function
}

const TimeContext = createContext<DataContextType | undefined>(undefined);

export const TimeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [trimStartTime, setTrimStartTime] = useState<number>(0);
  const [trimEndTime, setTrimEndTime] = useState<number>(0);
  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(0);
  const [tempUri, setTempUri] = useState<string | null>(null);

  // Add reset function to clear all state
  // const resetTimeData = () => {
  //   setTrimStartTime(0);
  //   setTrimEndTime(0);
  //   setStartTime(0);
  //   setEndTime(0);
  //   setTempUri(null);
  // };

  return (
    <TimeContext.Provider
      value={{
        trimStartTime,
        setTrimStartTime,
        trimEndTime,
        setTrimEndTime,
        startTime,
        setStartTime,
        endTime,
        setEndTime,
        tempUri,
        setTempUri,
        // resetTimeData,
      }}
    >
      {children}
    </TimeContext.Provider>
  );
};

export const useTimeData = (): DataContextType => {
  const context = useContext(TimeContext);
  if (!context) {
    throw new Error("useData must be used within a TimeProvider");
  }
  return context;
};