import { Image, StyleSheet } from 'react-native';
import { CallHistoryItemNormalized } from '../../../device-storage/realm/schemas/CallSchema';
import { View } from 'react-native';
import { IMAGES } from '../../../assets/Images';
import { useMe } from '../../../hooks/util/useMe';
import { useState } from 'react';
import UserAvatar from '../../../component/Common/UserAvatar';
import { CallVariant } from './CallHistoryItem';
import { ConversationSchema } from '../../../device-storage/realm/schemas/ConversationSchema';
import { safeJsonParse } from '../../../common/utils';

export const CallHistoryAvatar = ({
  item,
  CallVariant,
}: {
  item: CallHistoryItemNormalized;
  CallVariant: CallVariant;
}) => {
  // this only fror p2p call.yet to implement group call avatar
  return <SingleCallHistoryAvatar item={item} CallVariant={CallVariant} />;
};

const SingleCallHistoryAvatar = ({
  item,
  CallVariant,
}: {
  item: CallHistoryItemNormalized;
  CallVariant: CallVariant;
}) => {
  const { user } = useMe();
  const url = parseImageUrl(item, CallVariant, user?._id.toString() || '');

  return (
    <View
      style={{
        height: 50,
        width: 50,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
      }}
    >
      <UserAvatar url={url} />
    </View>
  );
};

const parseImageUrl = (
  item: CallHistoryItemNormalized,
  CallVariant: CallVariant,
  selfUserId: string,
) => {
  if (CallVariant === 'groupCall') {
    const groupConversation: { conversation: ConversationSchema } | null = safeJsonParse(
      item.originJson,
    );
    return groupConversation?.conversation.displayPic;
  }

  const isInitiator = item.initiator === selfUserId;
  const url = isInitiator ? item.invitedUserIdsDetails?.[0].image : item.initiatorDetails?.image;
  return url;
};
