import React, { forwardRef, useImperativeHandle } from 'react';
import { requireNativeComponent, ViewStyle, NativeModules, findNodeHandle } from 'react-native';

const NativeFilteredVideoView = requireNativeComponent('FilteredVideoView');

type Props = {
  style?: ViewStyle;
  source: any;
  filter?: string;
  audioSource?: string;
  isSoundOn?: boolean;
  resizeMode?: string;
  isPaused?: boolean;
  onProgress?: (e: any) => void;
};

export interface FilteredVideoViewHandle {
  setSource: (uri: string) => void;
  setIsSoundOn: (isSoundOn: boolean) => void;
  setFilter: (filter: string) => void;
  setPause: (isPause: boolean) => void;
  setAudioSource: (path: string) => void;
}
const { FilteredVideoViewManager, ThumbnailModule } = NativeModules;

export const generateVideoThumbnail = async (filePath: string): Promise<string> => {
  if (!ThumbnailModule || !ThumbnailModule.generateVideoThumbnail) {
    throw new Error('ThumbnailModule is not properly linked.');
  }

  return await ThumbnailModule.generateVideoThumbnail(filePath);
};

const FilteredVideoView = forwardRef<FilteredVideoViewHandle, Props>((props, ref) => {
  const nativeRef = React.useRef<any>(null);

  // Expose setSource to the parent via ref
  const reactTag = findNodeHandle(nativeRef.current);
  useImperativeHandle(ref, () => ({
    setSource: (uri: string) => {
      if (reactTag) {
        console.log('Calling setSource with:', uri);
        FilteredVideoViewManager.setSource(uri);
      }
    },
    setIsSoundOn: (isSoundOn: boolean) => {
      if (reactTag) {
        console.log('Calling setIsSoundOn with:', isSoundOn);
        FilteredVideoViewManager.setIsSoundOn(isSoundOn);
      }
    },
    setFilter: (filter: string) => {
      if (reactTag) {
        console.log('Calling setFilter with:', filter);
        FilteredVideoViewManager.setFilter(filter);
      }
    },
    setPause: (isPause: boolean) => {
      if (reactTag) {
        console.log('Calling setPause with:', isPause);
        FilteredVideoViewManager.setPause(isPause);
      }
    },
    setAudioSource: (path: string) => {
      if (reactTag) {
        console.log('Calling setAudioSource with:', path);
        FilteredVideoViewManager.setAudioSource(path);
      }
    },
  }));

  return <NativeFilteredVideoView ref={nativeRef} {...props} />;
});

export default FilteredVideoView;
