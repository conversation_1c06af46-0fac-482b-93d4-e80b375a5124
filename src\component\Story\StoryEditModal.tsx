import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { commonFontStyle } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import ModalWrapper from '../ModalWrapper';
import { IMAGES } from '../../assets/Images';
import { useTranslation } from 'react-i18next';

interface IProps {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
  onPress: (value: any) => void;
  imageSource?: any;
}
const _tab = [
  {
    id: 1,
    title: 'Delete',
    image: IMAGES.trash,
  },
  {
    id: 2,
    title: 'Edit',
    image: IMAGES.editSend,
  },
  {
    id: 3,
    title: 'Reschedule',
    image: IMAGES.time,
  },
  {
    id: 4,
    title: 'Post now',
    image: IMAGES.post_icon,
  },
];

const StoryEditModal = ({ isVisible, setIsVisible, onPress, imageSource }: IProps) => {
  const [_isVisible, _setIsVisible] = useState(isVisible);

  const { t } = useTranslation();
  useEffect(() => {
    _setIsVisible(isVisible);
  }, [isVisible]);

  const onClose = () => {
    _setIsVisible(false);
    setIsVisible(false);
  };

  const renderOptionList = ({ item, index }: { item: any; index: number }) => {
    return (
      <TouchableOpacity key={index} style={styles.renderListView} onPress={() => onPress(item)}>
        <Image
          source={item.image}
          style={styles.imageIcon}
          resizeMode="contain"
          tintColor={item.id === 1 ? colors._EC0B0B_red : colors.black_23}
        />

        <Text
          style={{
            ...styles.title,
            color: item.id === 1 ? colors._EC0B0B_red : colors.black_23,
          }}
        >
          {t(item.title)}
        </Text>
      </TouchableOpacity>
    );
  };
  return (
    <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
      <View style={styles.container}>
        {_tab.map((item, index) => {
          return renderOptionList({ item, index });
        })}
      </View>
    </ModalWrapper>
  );
};

export default StoryEditModal;

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
  },
  renderListView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 10,
  },
  imageIcon: {
    height: 20,
    width: 20,
  },

  title: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
});
