import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Platform,
  StyleSheet,
  Text,
  ToastAndroid,
  TouchableOpacity,
  View,
} from 'react-native';
import { SwipeListView } from 'react-native-swipe-list-view';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import { SCREENS } from '../../../navigation/screenNames';
import { navigateTo, resetAndNavigateTo, showToast } from '../../../utils/commonFunction';
import useConversations from '../../../hooks/conversations/useConversations';
import CreateChannelButton from '../components/CreateChannelButton';
import ButtonPurple from '../../../component/ButtonPurple';
import ChatItemCard from '../All/ChatItemCard';
import RenderHiddenItem from '../components/RenderHiddenItem';
import { ChatService } from '../../../service/ChatService';
import {
  ConversationSchema,
  IConversation,
  IExploreChannelCardItem,
  mapConversationToExploreChannelItem,
} from '../../../device-storage/realm/schemas/ConversationSchema';
import { getExploreChannels } from '../../../utils/ApiService';
import ExploreChannelCard from '../All/ExploreChannelCard';
import RightArrowSVG from '../../../assets/svgIcons/RightArrowSVG';
import useSocket from '../../../socket-client/useSocket';
import { ChatSocket } from '../../../socket-client/ChatSocket';
import { ChannelType, ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { socketEvents } from '../../../socket-client/socketEvents';
import { notifEvents } from '../../../socket-client/notificationTypes';
import { deleteChatSpace } from '../../../api/Chatspace/chatspace.api';
import DeleteConfirmModal from '../../../DeleteModal';
import { useFocusEffect } from '@react-navigation/native';
import { formToJSON } from 'axios';
import { MembershipStatus } from '../../../types/chats.types';
import { ChatScreenParams, ChatSpecificScreenParamsT } from '../Chats/ChatSpecificScreen';
import { ConversationInfo } from '../../../device-storage/realm/hooks/useConversationInfo';
import { getDefaultChatPermissions, isConversationMuted } from '../../../lib/chatLib';

const ChannelsScreen = ({ searchText }: { searchText: string }) => {
  const { socket } = useSocket();
  useEffect(() => {
    const handleChannelDeleted = async ({ chatSpaceId }: { chatSpaceId: string }) => {
      ChatService.markChannelAsDeleted(chatSpaceId);
      showToast('A channel you followed was deleted');
    };

    socket?.on(notifEvents.chatSpaces.deleted, handleChannelDeleted);

    return () => {
      socket?.off(notifEvents.chatSpaces.deleted, handleChannelDeleted);
    };
  }, [socket]);

  const { unArchivedConversations } = useConversations();
  const { myUnarchivedChannels, unarchivedFollowingChannels, allUnarchivedChannels } =
    useConversations();

  // Channels from Realm
  const channels = useMemo(
    () => unArchivedConversations.filter((c) => c.type === 'channel'),
    [unArchivedConversations],
  );
  // const channels = allUnarchivedChannels;

  // "My Channels" - user is owner
  // Direct from Realm queries
  const myChannels = myUnarchivedChannels;
  const followingChannels = unarchivedFollowingChannels;
  // console.log('=====>channels', channels.toJSON());
  // console.log('=====>mychannels', myChannels.toJSON());
  // console.log('======>followingChannels', followingChannels.toJSON());

  const [loading, setLoading] = useState(true);

  const [rawExploreChannels, setRawExploreChannels] = useState<IExploreChannelCardItem[]>([]);
  const showEmptyState = myChannels?.length === 0;
  const swipeListRef = useRef<any>(undefined);
  const [selectedChannels, setSelectedChannels] = useState<any[]>([]);
  const [isSelectionEnabled, setIsSelectionEnabled] = useState(false);
  const [isDeleteModalVisible, setDeleteModalVisible] = useState(false);
  const [channelToDelete, setChannelToDelete] = useState<any>(null);
  const exploreChannels = useMemo(() => {
    const filtered = rawExploreChannels.filter(
      (channel) => !channels.some((c) => c.id === channel.chatSpaceId),
    );
    return filtered;
  }, [rawExploreChannels, channels]);

  const filteredMyChannels = useMemo(() => {
    if (!searchText?.trim()) return myChannels;
    const lower = searchText.toLowerCase();
    return myChannels.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower),
    );
  }, [myChannels, searchText]);

  const filteredFollowingChannels = useMemo(() => {
    if (!searchText?.trim()) return followingChannels;
    const lower = searchText.toLowerCase();
    return followingChannels.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower),
    );
  }, [followingChannels, searchText]);

  const filteredExploreChannels = useMemo(() => {
    if (!searchText?.trim()) return exploreChannels;
    const lower = searchText.toLowerCase();
    return exploreChannels.filter(
      (channel) =>
        channel.name?.toLowerCase().includes(lower) ||
        channel.description?.toLowerCase().includes(lower),
    );
  }, [exploreChannels, searchText]);

  const mapExploreChannelToUserData = (item: IExploreChannelCardItem) => ({
    chatSpaceId: item.chatSpaceId,
    id: item._id,
    displayName: item.name!,
    displayPic: item.displayPic ?? '',
    type: ConversationType.CHANNEL,
    conversation: {
      role: 'member',
      description: item.description ?? '',
      memberCount: item.memberCount ?? 0,
    },
  });
  const handleFollowChannel = (channel: IExploreChannelCardItem) => {
    console.log('===>channel', JSON.stringify(channel, null, 2));

    const chatSpaceId = channel.chatSpaceId;
    if (!chatSpaceId) return;

    ChatSocket.joinChannel(socket, chatSpaceId, async (response) => {
      console.log('SocketjoinChannelresponse:', response);
      if (response?.error) {
        console.error('Follow failed due to socket error:', response.error);
        return;
      }
      try {
        ChatService.onIncomingMessage(response);
        const chatScreenRouteParams: ChatSpecificScreenParamsT = {
          userData: {
            displayName: channel.name as string,
            displayPic: channel.displayPic,
            type: ConversationType.CHANNEL,
            chatSpaceId: channel.chatSpaceId,
            conversation: {} as IConversation,
            id: channel._id,
            // description:channel.description,
          },
          isFollowing: true,
          data: {
            convId: channel.chatSpaceId as string,
          },
        };

        // Navigate
        // resetAndNavigateTo(SCREENS.ChatSpecificScreen, chatScreenRouteParams, true);
        ChatService.updateMemberCount(chatSpaceId, 1, 0);
        setRawExploreChannels((prev) => prev.filter((c) => c.chatSpaceId !== chatSpaceId));
        showToast(`You are now following ${channel.name}`);
      } catch (error) {
        console.error('Error creating chat space:', error);
      }
    });
  };

  const handleUnfollowChannel = (channel: IExploreChannelCardItem) => {
    const chatSpaceId = channel._id || channel.chatSpaceId;
    if (!chatSpaceId) return;

    ChatSocket.emitLeaveChatspace(socket, chatSpaceId, async (response) => {
      if (response?.error) {
        console.error('Unfollow failed due to socket error:', response.error);
        return;
      }
      try {
        await ChatService.deleteConversation(chatSpaceId);
        socket?.emit(socketEvents.CHATSPACE_DELETED, { chatSpaceId });
        setRawExploreChannels((prev) => {
          const exists = prev.some((c) => c.chatSpaceId === chatSpaceId);
          if (exists) {
            return prev;
          }
          return [channel, ...prev];
        });

        showToast(`You unfollowed ${channel.name || channel.displayName}`);
      } catch (error) {
        console.error('Error unfollowing channel:', error);
        showToast('Failed to unfollow channel');
      }
    });
  };

  const fetchExplore = async () => {
    try {
      const response = await getExploreChannels();
      if (response?.data) {
        setRawExploreChannels(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch explore channels:', error);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchExplore();
    }, []),
  );

  const closeRow = (rowMap: any, rowKey: any) => {
    if (rowMap[rowKey]) {
      rowMap[rowKey].closeRow();
    }
  };

  const onPressCardMenuItems = (rowMap: any, item: ConversationSchema, type: string) => {
    closeRow(rowMap, item.id);
    if (type === 'ARCHIVE') {
      ChatService.toggleConversationArchive(item.id);
    } else if (type === 'MUTE') {
      const isMuted = isConversationMuted(item.conversationSettings.muteUntil);
      ChatService.toggleConversationMute(item?.id, isMuted ? undefined : 'always');
    } else if (type === 'CLEAR') {
      ChatService.clearChat(item.id);
    } else {
      ChatService.toggleConversationPin(item.id);
    }
  };

  const onSelect = (channel: any) => {
    const isSelected = selectedChannels.some((c) => c.id === channel.id);

    if (isSelectionEnabled) {
      setSelectedChannels((prev) => {
        if (isSelected) {
          const updated = prev.filter((c) => c.id !== channel.id);
          if (updated.length === 0) {
            setIsSelectionEnabled(false);
          }
          return updated;
        } else {
          return [...prev, channel];
        }
      });
    } else {
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }

      const findChannel = channels.find((conv: any) => conv.id === channel.id);

      const copyConversation = findChannel ? JSON.parse(JSON.stringify(findChannel)) : undefined;
      const isFollowing = followingChannels.some((c) => c.id === channel.chatSpaceId);
      const userData = {
        displayName: channel.displayName,
        displayPic: channel.displayPic,
        type: channel.type,
        id: channel.id,
        isActive: channel.isActive,
        conversation: copyConversation,
      };
      const chatSpaceId: ChatScreenParams = { convId: userData.id };

      navigateTo(SCREENS.ChatSpecificScreen, {
        userData: { ...userData, isFollowing },
        data: chatSpaceId,
      });
    }
  };

  const onLongSelect = (channel: any) => {
    // if (selectedChannels.length > 0) {
    //   onSelect(channel);
    // } else {
    //   setIsSelectionEnabled(true);
    //   setSelectedChannels([channel]);
    //   // setTimeout(() => onSelect(channel), 0);
    // }
  };

  const renderItem = ({ item }: any) => (
    <ChatItemCard
      type="channel"
      item={item}
      selectedUser={selectedChannels}
      isSelectionEnabled={isSelectionEnabled}
      onSelect={onSelect}
      onLongSelect={onLongSelect}
    />
  );
  const confirmDeleteChannel = () => {
    if (!channelToDelete) return;
    const chatSpaceId = channelToDelete.id;
    setDeleteModalVisible(false);
    deleteChatSpace(chatSpaceId)
      .then(async () => {
        await ChatService.deleteConversation(chatSpaceId);
        socket?.emit(notifEvents.chatSpaces.deleted, { chatSpaceId });
        showToast('Channel deleted successfully');
        setChannelToDelete(null);
      })
      .catch((error) => {
        console.error('Failed to delete channel:', error);
        showToast('Failed to delete channel');
      });
  };

  const onDeleteChannelRequest = (rowMap: any, channel: any) => {
    closeRow(rowMap, channel.id);
    setChannelToDelete(channel);
    setDeleteModalVisible(true);
  };

  const renderHiddenItem = ({ item }: any, rowMap: any) => (
    <RenderHiddenItem
      item={item}
      type="Channel"
      onPressDeleteChat={() => onDeleteChannelRequest(rowMap, item)}
      onPressIcon={(itemType) => onPressCardMenuItems(rowMap, item, itemType)}
    />
  );

  // console.log(
  //   filteredFollowingChannels.map((item) => item.toJSON()),
  //   '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>..              ',
  // );

  const renderFollowingSection = () => {
    if (followingChannels.length === 0) return null;
    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Following</Text>
        <FlatList
          data={filteredFollowingChannels}
          scrollEnabled={false}
          keyExtractor={(item) => item.id as string}
          renderItem={({ item }) => {
            const exploreItem = mapConversationToExploreChannelItem(item);
            return (
              <ExploreChannelCard
                item={exploreItem}
                isFollowing={true}
                onFollowPress={() => handleUnfollowChannel(exploreItem)}
                onPress={() => {
                  if (item.isDeleted) return;
                  const userData = mapExploreChannelToUserData(exploreItem);
                  const paramsData: ChatScreenParams = {
                    convId: item?.id as string,
                  };
                  navigateTo(SCREENS.ChatSpecificScreen, {
                    userData: { ...userData, isFollowing: true },
                    data: paramsData,
                  });
                }}
              />
            );
          }}
          contentContainerStyle={styles.listContentContainer}
        />
      </View>
    );
  };

  const renderMyChannelsSection = () => {
    if (showEmptyState) {
      return (
        <View style={styles.headerView}>
          <Text style={styles.chanelText}>You haven't created any channels yet!</Text>
          <ButtonPurple
            title="Create Channel"
            extraStyle={{ borderRadius: 30, paddingHorizontal: 40 }}
            onPress={() => navigateTo(SCREENS.CreateChannelScreen, { isEdit: false })}
          />
        </View>
      );
    }

    return (
      <View style={styles.myChannelContainer}>
        <View style={styles.channelRow}>
          <Text style={styles.sectionTitle}>My Channels</Text>
        </View>
        <SwipeListView
          ref={swipeListRef}
          data={filteredMyChannels}
          useFlatList
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          renderHiddenItem={renderHiddenItem}
          rightOpenValue={-120}
          disableRightSwipe
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };

  const handleExploreChannel = (item: IExploreChannelCardItem) => {
    const isFollowing = followingChannels.some((c) => c.id === item.chatSpaceId);
    const userData = mapExploreChannelToUserData(item);

    const convInfo: ConversationInfo = {
      id: item?.chatSpaceId as string,
      displayName: item?.name as string,
      displayPic: item.displayPic,
      isPrivate: item?.isPrivate as boolean,
      bio: item?.description as string,
      memberCount: item?.memberCount as number,
      type: item?.type as ConversationType,
      isLoading: false,
      isFromCache: true,
      conversationSettings: null,
      membershipStatus: MembershipStatus.DISCOVERING,
      permissions: getDefaultChatPermissions(
        MembershipStatus.DISCOVERING,
        item?.type as ConversationType,
      ),
      inviteCode: item?.inviteCode || '',
    };

    const paramsData: ChatScreenParams = {
      convId: item?.chatSpaceId as string,
      initialConversationInfo: convInfo,
    };

    navigateTo(SCREENS.ChatSpecificScreen, {
      userData: { ...userData, isFollowing },
      data: paramsData,
    });
  };

  const renderExploreSection = () => (
    <View style={styles.exploreSection}>
      <View style={styles.exploreRow}>
        <Text style={styles.sectionTitle}>Explore Channels</Text>
        <TouchableOpacity
          style={styles.exploreActionRow}
          onPress={() => navigateTo(SCREENS.ExploreScreen)}
        >
          <Text style={styles.exploreActionText}>Explore</Text>
          <RightArrowSVG />
        </TouchableOpacity>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={colors.mainPurple} style={{ marginTop: 20 }} />
      ) : (
        <FlatList
          data={filteredExploreChannels}
          keyExtractor={(item) => item._id as string}
          renderItem={({ item }) => {
            const isFollowing = followingChannels.some((c) => c.id === item.chatSpaceId);
            return (
              <ExploreChannelCard
                item={item}
                isFollowing={isFollowing}
                onFollowPress={() =>
                  isFollowing ? handleUnfollowChannel(item) : handleFollowChannel(item)
                }
                onPress={() => handleExploreChannel(item)}
              />
            );
          }}
          ListEmptyComponent={<Text style={styles.emptyText}>No explore channels found.</Text>}
          contentContainerStyle={styles.listContentContainer}
        />
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={[]}
        renderItem={() => null}
        ListHeaderComponent={
          <>
            {renderMyChannelsSection()}
            {renderFollowingSection()}
          </>
        }
        ListFooterComponent={renderExploreSection()}
        contentContainerStyle={styles.mainListContainer}
        showsVerticalScrollIndicator={false}
      />
      <CreateChannelButton onPress={() => navigateTo(SCREENS.CreateChannelScreen)} />
      <DeleteConfirmModal
        isVisible={isDeleteModalVisible}
        onConfirm={confirmDeleteChannel}
        onCancel={() => setDeleteModalVisible(false)}
        message="Are you sure you want to delete this channel?"
      />
    </View>
  );
};

export default ChannelsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  mainListContainer: {
    paddingTop: 10,
    paddingBottom: 80,
  },
  myChannelContainer: {
    marginBottom: 10,
  },
  sectionContainer: {
    marginTop: 10,
    paddingHorizontal: 20,
  },
  exploreSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    color: colors.gray_80,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  chanelText: {
    ...commonFontStyle(400, 15, colors.gray_80),
    marginBottom: 10,
  },
  headerView: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  exploreRow: {
    paddingTop: 16,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  channelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },

  exploreActionRow: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },

  exploreActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.mainPurple,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 3,
  },
  listContentContainer: {
    paddingBottom: 30,
  },
  emptyText: {
    paddingTop: 10,
    color: colors.gray_80,
  },
});
