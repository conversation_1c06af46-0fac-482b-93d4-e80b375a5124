// import React, { createContext, useContext, useRef, useState } from "react";
// import { Device, types } from "mediasoup-client";
// import { socket } from "../Socket/socket";
// import {
//     ScreenCapturePickerView,
//     RTCPeerConnection,
//     RTCIceCandidate,
//     RTCSessionDescription,
//     RTCView,
//     MediaStream,
//     MediaStreamTrack,
//     mediaDevices,
//     registerGlobals
// } from 'react-native-webrtc';

// // make this a type
// export type remoteVideoT = {
//   id: string;
//   audioTrack?: MediaStreamTrack;
//   videoTrack?: MediaStreamTrack;
//   producerId: string;
//   clientId: string;
//   audioConsumer: types.Consumer | null;
//   videoConsumer: types.Consumer | null;
// };

// interface MediasoupContextType {
//   initializeDevice: (
//     roomId: string,
//     rtpCapabilities: types.RtpCapabilities
//   ) => Promise<Device>;
//   createProducerTransport: (
//     roomId: string,
//     callType: string,
//     device: Device
//   ) => Promise<void>;
//   createConsumerTransport: (
//     device: Device,
//     roomId: string,
//     clientId: string
//   ) => Promise<types.Transport>;
//   localRef: React.MutableRefObject<HTMLVideoElement | null>;
//   localStream : React.MutableRefObject<MediaStream | null>;
//   consumers: remoteVideoT[];
//   consume: (
//     producerId: string,
//     roomId: string,
//     clientId: string,
//     device: Device
//   ) => Promise<void>;

//   getDevice(): Device | null;
// }

// const MediasoupContext = createContext<MediasoupContextType | undefined>(
//   undefined
// );

// export const MediasoupProvider: React.FC<{ children: React.ReactNode }> = ({
//   children,
// }) => {
//   const deviceRef = useRef<Device | null>(null);
//   const consumerTransportRef = useRef<types.Transport | null>(null);
//   const producerTransportRef = useRef<types.Transport | null>(null);
//   // console.log( " socket in context  = =  = = > ", socket)
//   const [producers, setProducers] = useState<{
//     audioProducer?: types.Producer | null;
//     videoProducer: types.Producer | null;
//   } | null>(null);
//   const localRef = useRef<HTMLVideoElement | null>(null);
//   const localStream = useRef<MediaStream | null>(null);
// //   const { socket } = useSocket();
//   const [consumers, setConsumers] = useState<remoteVideoT[]>([]);

//   const initializeDevice = async (
//     roomId: string,
//     rtpCapabilities: types.RtpCapabilities
//   ) => {
//     try {
//       const newDevice = new Device();
//       await newDevice.load({
//         routerRtpCapabilities: rtpCapabilities,
//       });
//       deviceRef.current = newDevice;
//       return Promise.resolve(newDevice);
//     } catch (err) {
//       return Promise.reject(err);
//     }
//   };

//   function getDevice() {
//     return deviceRef.current;
//   }

//   const createProducerTransport = async (
//     roomId: string,
//     callType: string,
//     device: Device
//   ) => {
//     console.log( " device   = = = = = =  ", roomId , callType  , device)
//     if (device) {
//       socket?.emit(
//         "createProducerTransport",
//         { roomId, callType },
//         async (transportOptions: any) => {
//             console.log( " transportOptions ", transportOptions)
//           try {
//             const producerTransport = device.createSendTransport({
//               id: transportOptions.id,
//               iceParameters: transportOptions.iceParameters,
//               iceCandidates: transportOptions.iceCandidates,
//               dtlsParameters: transportOptions.dtlsParameters,
//             });
//             // setProducerTransport(producerTransport);
//             producerTransportRef.current = producerTransport;
//             producerTransport.on(
//               "connect",
//               async ({ dtlsParameters }, callback, errback) => {
//                 console.log(" dtlsParameters  = = = = = => ", dtlsParameters , roomId)
//                 try {
//                   socket.emit(
//                     "connectProducerTransport",
//                     { dtlsParameters, roomId },
//                     (data: any) => {
//                     console.log( " connectProducerTransport resp  == ", data)
//                       if (data === "success") {
//                         callback();
//                       } else if (data == "error") {
//                         errback(
//                           new Error("Failed to connect to the transport")
//                         );
//                       }
//                     }
//                   );
//                   // Tell the transport that parameters were transmitted.
//                 } catch (error: any) {
//                   // Tell the transport that something was wrong.
//                   errback(error);
//                 }
//               }
//             );

//             producerTransport.on(
//               "produce",
//               ({ kind, rtpParameters, appData }, callback, errback) => {
//                 try {
//                   socket.emit(
//                     "produce",
//                     {
//                       id: producerTransport.id,
//                       kind: kind,
//                       rtpParameters: rtpParameters,
//                       appData: appData,
//                       roomId: roomId,
//                     },
//                     (data: any) => {
//                       callback({ id: data.id });
//                     }
//                   );
//                 } catch (err: any) {
//                   errback(err);
//                 }
//               }
//             );

//             producerTransport.on("connectionstatechange", (state) => {
//               console.log(state, ">>>>>>>>>>>.");
//             });
//             await startProducing(producerTransport);
//           } catch (err) {
//             console.log(err);
//             throw new Error("Failed to create producer transport");
//           }
//         }
//       );
//     }
//   };

//   async function startProducing(producerTransport: types.Transport) {
//     console.log(">>>>>>>>>>> 1");
//     // console.log(navigator);
//     // console.log(navigator.mediaDevices);
//     const stream = await mediaDevices.getUserMedia({ audio: true, video: true })

//     localStream.current = stream;

//     console.log(">>>>>>>>>>> 2");
//     const vidTrack = stream.getVideoTracks()[0];
//     const audioTrack = stream.getAudioTracks()[0];
//     const videoProducer = await producerTransport.produce({
//       track: vidTrack,
//     });
//     const audioProducer = await producerTransport.produce({
//       track: audioTrack,
//     });
//     console.log(">>>>>>>>>>> 3");
//     setProducers({
//       audioProducer,
//       videoProducer,
//     });
//   }

//   const createConsumerTransport = async (
//     device: Device,
//     roomId: string,
//     clientId: string
//   ): Promise<types.Transport> => {
//     return new Promise(async (resolve, reject) => {
//       console.log("came here");
//       socket?.emit(
//         "createConsumerTransport",
//         { roomId, clientId },
//         async (transportOptions: any) => {
//           if (device) {
//             const transport = device.createRecvTransport({
//               id: transportOptions.id,
//               iceParameters: transportOptions.iceParameters,
//               iceCandidates: transportOptions.iceCandidates,
//               dtlsParameters: transportOptions.dtlsParameters,
//             });
//             // setConsumerTransport(transport);
//             consumerTransportRef.current = transport;
//             transport.on(
//               "connect",
//               async ({ dtlsParameters }, callback, errback) => {
//                 try {
//                   socket.emit(
//                     "connectConsumerTransport",
//                     { dtlsParameters },
//                     (data: any) => {
//                       if (data === "success") {
//                         callback();
//                       } else if (data == "error") {
//                         errback(
//                           new Error("Failed to connect to the transport")
//                         );
//                       }
//                     }
//                   );
//                 } catch (err: any) {
//                   console.log(err);
//                   errback(err);
//                 }
//               }
//             );
//             transport.on("connectionstatechange", (state) => {
//               console.log(state, ">>>>>>>>>>>. consumer ");
//             });
//             resolve(transport);
//           }
//         }
//       );
//     });
//   };

//   const consume = async (
//     producerId: string,
//     roomId: string,
//     clientId: string,
//     device: Device
//   ) => {
//     const consumerTransport = consumerTransportRef.current;
//     if (consumerTransport) {
//       emitConsume(
//         consumerTransport,
//         { producerId: producerId, roomId, clientId },
//         device
//       );
//     } else {
//       console.log("1");
//       if (device) {
//         console.log("2");
//         const consumerTransport = await createConsumerTransport(
//           device,
//           roomId,
//           clientId
//         );

//         console.log(consumerTransport, ">>>>>>>>>consumser transport");
//         emitConsume(
//           consumerTransport,
//           { producerId: producerId, roomId, clientId },
//           device
//         );
//       }
//     }
//   };

//   function emitConsume(
//     consumerTransport: types.Transport,
//     data: any,
//     device: Device | null
//   ) {
//     const { producerId, roomId, clientId } = data;
//     socket?.emit(
//       "consume",
//       {
//         rtpCapabilities: device?.rtpCapabilities,
//         producerId: producerId,
//         roomId,
//         clientId,
//       },
//       async (data: any) => {
//         if (data.error) {
//           console.error("Error:", data);
//           return;
//         }

//         console.log(data);

//         const consumer = await consumerTransport.consume(data);
//         const { clientId, producerId } = data;
//         const { track, kind } = consumer;

//         setConsumers((prevConsumers) => {
//           // Try to find an existing consumer for the clientId
//           const existingIndex = prevConsumers.findIndex(
//             (c) => c.clientId === clientId
//           );

//           // If we already have a consumer, update it with the new info
//           if (existingIndex !== -1) {
//             const updatedConsumer = { ...prevConsumers[existingIndex] };

//             if (kind === "audio") {
//               updatedConsumer.audioConsumer = consumer;
//               updatedConsumer.audioTrack = track;
//             } else if (kind === "video") {
//               updatedConsumer.videoConsumer = consumer;
//               updatedConsumer.videoTrack = track;
//             }

//             // Replace the old consumer with the updated one
//             return [
//               ...prevConsumers.slice(0, existingIndex),
//               updatedConsumer,
//               ...prevConsumers.slice(existingIndex + 1),
//             ];
//           } else {
//             // If not found, create a new consumer object
//             const newConsumer = {
//               clientId,
//               id: clientId,
//               producerId,
//               audioConsumer: kind === "audio" ? consumer : null,
//               videoConsumer: kind === "video" ? consumer : null,
//               audioTrack: kind === "audio" ? track : undefined,
//               videoTrack: kind === "video" ? track : undefined,
//             };

//             return [...prevConsumers, newConsumer];
//           }
//         });

//         // Listen for track events
//         track.addEventListener("ended", () => console.log("Track has ended"));
//         track.onmute = () => console.log("Track has muted");
//         track.onunmute = () => console.log("Track has unmuted");

//         //   consumer
//         //     .getStats()
//         //     .then((statsReport) => {
//         //       statsReport.forEach((report) => {
//         //         // Check for inbound RTP stats (for video or audio)
//         //         if (report.type === "inbound-rtp" && report.kind === "video") {
//         //           console.log(
//         //             "Video Packets Received:",
//         //             report.packetsReceived
//         //           );
//         //           console.log("Video Bytes Received:", report.bytesReceived);
//         //           // Optionally log other useful stats:
//         //           console.log("Video Packets Lost:", report.packetsLost);
//         //           console.log("Video Jitter:", report.jitter);
//         //         }
//         //         if (report.type === "inbound-rtp" && report.kind === "audio") {
//         //           console.log(
//         //             "Audio Packets Received:",
//         //             report.packetsReceived
//         //           );
//         //           console.log("Audio Bytes Received:", report.bytesReceived);
//         //           console.log("Audio Packets Lost:", report.packetsLost);
//         //           console.log("Audio Jitter:", report.jitter);
//         //         }
//         //       });
//         //     })
//         //     .catch((err) => {
//         //       console.error("Error getting consumer stats:", err);
//         //     });
//         // }, 2000);

//         // const stream = new MediaStream([track]);
//         // if (remoteRef.current) {
//         //   console.log(stream);
//         //   remoteRef.current.srcObject = stream;
//         socket.emit("unpause", { id: data.id }, () => {
//           console.log("unpaused");
//         });
//         // }
//       }
//     );
//   }

//   return (
//     <MediasoupContext.Provider
//       value={{
//         initializeDevice,
//         createProducerTransport,
//         localRef,
//         createConsumerTransport,
//         consume,
//         localStream,
//         consumers,
//         getDevice,
//       }}
//     >
//       {children}
//     </MediasoupContext.Provider>
//   );
// };

// export const useMediasoup = () => {
//   const context = useContext(MediasoupContext);
//   if (!context) {
//     throw new Error("useMediasoup must be used within a MediasoupProvider");
//   }
//   return context;
// };

// /*
// remoteVideo = {
//  audioStream ;
//  videoStream;
//  producerId;
//  clientId;
//  audioCosumerId;
//  videoCosumerId;
// }

// localVideo = {
// audioStream;
// videoStream;
// producerId;

// }

// */
