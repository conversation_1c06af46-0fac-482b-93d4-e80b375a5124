import { useEffect, useRef, useState } from 'react';
import { useQuery, useRealm } from '../../../device-storage/realm/realm';
import {
  IMessage,
  MessageSchema,
  Reaction,
} from '../../../device-storage/realm/schemas/MessageSchema';
import Realm from 'realm';
import { ChatService } from '../../../service/ChatService';

export const useFetchMessages = (receiverId: string) => {
  const realm = useRealm();
  const allMessages = useQuery(MessageSchema);
  const _messages = allMessages
    .filtered('conversationId == $0 AND isCleared == false', receiverId)
    .sorted('createdAt', true);
  // const [messages, setMessages] = useState<IMessage[]>([]);
  const [pinnedMessages, setPinnedMessages] = useState<IMessage[]>([]);
  const [reactionsMap, setReactionsMap] = useState<
    Record<string, Record<string, { count: number; users: string[] }>>
  >({});

  const timersRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    // ----- Messages -----
    const query = realm
      .objects(MessageSchema)
      .filtered('conversationId == $0 AND isCleared == false', receiverId)
      .sorted('createdAt', true);

    const pinnedQuery = realm
      .objects(MessageSchema)
      .filtered(
        'conversationId == $0 AND pinnedUntil > $1 AND isCleared == false',
        receiverId,
        Date.now(),
      )
      .sorted('createdAt', true);

    const toPlain = (collection: Realm.Results<MessageSchema>): IMessage[] => {
      return collection.slice(0, 100).map((msg) => ({
        ...(msg.toJSON() as unknown as IMessage),
      }));
    };

    const clearExpiredPinned = () => {
      const now = Date.now();

      const expired = realm
        .objects(MessageSchema)
        .filtered('pinnedUntil <= $0 AND pinnedBy != nil', now);

      if (expired.length > 0) {
        realm.write(() => {
          expired.forEach((msg) => {
            msg.pinnedBy = undefined;
            msg.pinnedUntil = undefined;
            msg.isPinned = false;
            msg.pinnedAt = undefined;
            console.log('Cleared expired pinned:', msg.globalId);
          });
        });
      }
    };

    // ---- schedule the next expiration timer ----
    const scheduleNextUnpin = (pinned: IMessage[]) => {
      if (timersRef.current) clearTimeout(timersRef.current);

      // find the nearest expiring pinned message
      const soonest = pinned
        .filter((msg) => msg.pinnedUntil && msg.pinnedUntil > Date.now())
        .sort((a, b) => a.pinnedUntil! - b.pinnedUntil!)[0];
      if (soonest) {
        const delay = soonest.pinnedUntil! - Date.now();
        timersRef.current = setTimeout(() => {
          realm.write(() => {
            const expired = realm
              .objects(MessageSchema)
              .filtered('conversationId == $0 AND pinnedUntil <= $1', receiverId, Date.now());
            expired.forEach((msg) => {
              msg.isPinned = false;
              msg.pinnedUntil = undefined;
              msg.pinnedBy = undefined;
              msg.pinnedAt = undefined;
              console.log('Auto cleared:', msg.globalId);
            });
          });
        }, delay);
      }
    };

    // Initial load
    // setMessages(toPlain(query));
    const initialPinned = toPlain(pinnedQuery).slice(0, 5);
    setPinnedMessages(initialPinned);
    scheduleNextUnpin(initialPinned);

    query.addListener((collection) => {
      // setMessages(toPlain(collection as Realm.Results<MessageSchema>));
      ChatService.onConversationOpen(receiverId);
    });

    // pinned query
    clearExpiredPinned();
    pinnedQuery.addListener((collection) => {
      const updated = toPlain(collection as Realm.Results<MessageSchema>).slice(0, 5);
      setPinnedMessages(updated);
      scheduleNextUnpin(updated);
    });

    // ----- Reactions -----
    const reactionQuery = realm.objects<Reaction>('Reaction');

    const buildReactionsMap = () => {
      const map: Record<string, Record<string, { count: number; users: string[] }>> = {};

      // Build a quick lookup of all message ids in this conversation
      const messageIdSet = new Set(query.map((m) => m.globalId));

      reactionQuery.forEach((r) => {
        if (!messageIdSet.has(r.messageId)) return; // skip other chats

        if (!map[r.messageId]) map[r.messageId] = {};
        if (!map[r.messageId][r.emoji]) {
          map[r.messageId][r.emoji] = { count: 1, users: [r.userId] };
        } else {
          map[r.messageId][r.emoji].count += 1;
          map[r.messageId][r.emoji].users.push(r.userId);
        }
      });

      setReactionsMap(map);
    };

    // Initial map
    buildReactionsMap();

    // Listener
    reactionQuery.addListener((collection) => {
      console.log('[Debug] Reaction listener fired. Count:', collection.length);
      console.log(
        '[Debug] Current Reactions:',
        collection.map((r) => r.toJSON()),
      );
      buildReactionsMap();
    });

    return () => {
      query.removeAllListeners();
      pinnedQuery.removeAllListeners();
      reactionQuery.removeAllListeners();
      if (timersRef.current) {
        clearTimeout(timersRef.current);
        timersRef.current = null;
      }
    };
  }, [realm, receiverId]);

  return {
    messages: _messages,
    pinnedMessages,
    reactionsMap,
  };
};
