import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet, Text, Platform, ScrollView } from 'react-native';
import HeaderBackWithTitle from '../../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { CButton } from '../../../../../common';
import { SCREENS } from '../../../../../navigation/screenNames';
import PinInputField from '../../../../../component/Common/PinInputField';

const SetSecurityPinScreen = () => {
  const navigation = useNavigation();
  const [pin, setPin] = useState<string[]>(['', '', '', '']);
  const [confirmPin, setConfirmPin] = useState<string[]>(['', '', '', '']);
  const [error, setError] = useState<string>('');

  const handlePinChange = (newPin: string[]) => {
    setPin(newPin);
    setError('');
  };

  const handleConfirmPinChange = (newPin: string[]) => {
    setConfirmPin(newPin);
    setError('');
  };

  const handleSubmit = () => {
    if (pin.some((digit) => digit === '')) {
      setError('Please enter a 4-digit PIN');
      return;
    }

    if (confirmPin.some((digit) => digit === '')) {
      setError('Please confirm your PIN');
      return;
    }

    if (pin.join('') !== confirmPin.join('')) {
      setError('PINs do not match');
      return;
    }

    navigation.goBack();
  };

  const isPinComplete = () => {
    return pin.every((digit) => digit !== '') && confirmPin.every((digit) => digit !== '');
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Set PIN" onBack={() => navigation.goBack()} />
      <ScrollView
        style={styles.whiteContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.pinSection}>
          <Text style={styles.pinLabel}>Create PIN</Text>
          <PinInputField
            length={4}
            value={pin}
            onChange={handlePinChange}
            secureTextEntry={true}
            autoFocus={false}
            containerStyle={styles.pinInputContainer}
            inputStyle={styles.pinInput}
            showCursor={true}
            showFocusedStyle={false}
          />
        </View>

        <View style={styles.pinSection}>
          <Text style={styles.pinLabel}>Confirm PIN</Text>
          <PinInputField
            length={4}
            value={confirmPin}
            onChange={handleConfirmPinChange}
            secureTextEntry={true}
            autoFocus={false}
            containerStyle={styles.pinInputContainer}
            inputStyle={styles.pinInput}
            showCursor={true}
            showFocusedStyle={false}
          />
        </View>

        {error ? <Text style={styles.errorText}>{error}</Text> : null}

        <CButton
          onPress={handleSubmit}
          cStyle={[styles.button, !isPinComplete() && styles.buttonDisabled]}
          clickable={!isPinComplete()}
        >
          <Text style={styles.buttonText}>Submit</Text>
        </CButton>

        <View style={styles.keyboardSpacer} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingTop: hp(2),
    paddingHorizontal: hp(2),
    marginTop: 8,
  },
  scrollContent: {
    paddingTop: hp(4),
    flexGrow: 1,
  },
  pinSection: {
    marginBottom: hp(3),
  },
  pinLabel: {
    ...commonFontStyle(600, 16, colors.black_23),
    marginBottom: hp(2),
    textAlign: 'center',
  },
  pinInputContainer: {
    justifyContent: 'center',
  },
  pinInput: {
    width: 60,
    height: 60,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: colors.gray_f3,
    backgroundColor: colors.white,
    fontSize: 20,
    color: colors.black_23,
    marginHorizontal: 15,
  },
  errorText: {
    ...commonFontStyle(400, 14, colors.red_ff4444),
    textAlign: 'center',
    marginBottom: hp(2),
  },
  button: {
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    width: '100%',
  },
  buttonDisabled: {
    backgroundColor: colors._DADADA_gray,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  keyboardSpacer: {
    height: Platform.OS === 'ios' ? hp(10) : hp(5),
  },
});

export default SetSecurityPinScreen;
