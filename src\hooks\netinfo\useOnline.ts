import { useEffect, useState } from 'react';
import NetInfo from '@react-native-community/netinfo';

interface UseNetworkEventsOptions {
  onOnline?: () => void;
  onOffline?: () => void;
}

export const useNetworkEvents = ({ onOnline, onOffline }: UseNetworkEventsOptions = {}) => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      const newStatus = state.isConnected ?? false;
      setIsOnline((prev) => {
        if (prev !== null && prev !== newStatus) {
          if (newStatus && onOnline) onOnline();
          if (!newStatus && onOffline) onOffline();
        }
        return newStatus;
      });
    });

    NetInfo.fetch().then((state) => {
      const initialStatus = state.isConnected ?? false;
      setIsOnline(initialStatus);
    });

    return () => unsubscribe();
  }, []);

  return isOnline;
};
