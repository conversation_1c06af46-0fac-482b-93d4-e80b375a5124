import { Socket } from 'socket.io-client';
import { Realm } from '@realm/react';
import { ChatSpace } from '../types/socketPayload.type';
import { ConversationRepo } from '../device-storage/realm/repositories/ConversationRepo';

import { ConversationType } from '../device-storage/realm/schemas/MessageSchema';
import { IConversation } from '../device-storage/realm/schemas/ConversationSchema';

import { safeRealmWrite } from '../device-storage/realm/lib';

export type RealmChatSpace = Realm.Object<IConversation, never> & IConversation;

let socket: Socket | null = null;
let realm: Realm | null = null;

const validateResources = () => {
  if (!socket) {
    console.log('Socket not initialized or connected');
  }
  if (!realm) throw new Error('Realm not initialized');
};
export const setChatSpaceRealm = (r: any) => {
  realm = r;
};

export const setChatSpaceSocket = (socket: Socket) => {
  socket = socket;
};

const createChatSpace = (conversation: ChatSpace) => {
  validateResources();
  const _realm = realm!;

  const conversationData: IConversation = {
    id: conversation.chatSpaceId,
    displayName: conversation.name,
    description: conversation.description,
    displayPic: conversation.displayPic,
    unreadCount: 0,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    type: conversation.type,
    createdBy: conversation.createdBy,
    role: conversation.role,
    chatSpaceId: conversation.chatSpaceId,
    isLiveStreaming: conversation.type === ConversationType.CHANNEL ? false : undefined,
  };
  safeRealmWrite(_realm, () => {
    const createdConversation = ConversationRepo.createChatSpaceConversation(
      _realm,
      conversationData,
    );
  });

  return conversationData;
};

const updateChatSpace = (data: Partial<ChatSpace>): RealmChatSpace => {
  validateResources();
  const _realm = realm!;

  if (!data.chatSpaceId) {
    throw new Error('chatSpaceId is required to update a chat space');
  }

  const existingConversation = _realm
    .objects<IConversation>('Conversation')
    .filtered('chatSpaceId == $0', data.chatSpaceId)[0];

  if (!existingConversation) {
    throw new Error(`Conversation with chatSpaceId ${data.chatSpaceId} not found`);
  }

  safeRealmWrite(_realm, () => {
    if (data.name !== undefined) existingConversation.displayName = data.name;
    if (data.description !== undefined) existingConversation.description = data.description;
    if (data.displayPic !== undefined) existingConversation.displayPic = data.displayPic;
    if (data.type !== undefined) existingConversation.type = data.type;
    if (data.createdBy !== undefined) existingConversation.createdBy = data.createdBy;
    if (data.isLiveStreaming !== undefined)
      existingConversation.isLiveStreaming = data.isLiveStreaming;
    existingConversation.updatedAt = Date.now();
  });

  return existingConversation;
};

export const ChatSpaceService = {
  createChatSpace,
  updateChatSpace,
};
