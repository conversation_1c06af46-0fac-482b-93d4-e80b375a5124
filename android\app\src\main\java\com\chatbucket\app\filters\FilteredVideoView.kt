package com.chatbucket.filters

import android.content.Context
import android.opengl.GLSurfaceView
import com.facebook.react.bridge.ReactContext
import com.facebook.react.uimanager.events.RCTEventEmitter

class FilteredVideoView(context: Context) : GLSurfaceView(context) {
    private val renderer: FilterRenderer = FilterRenderer(context, this)
    private var currentFilter: String = "default"
    private var resizeMode: String = "contain"

    init {
        setEGLContextClientVersion(2)
        setRenderer(renderer)
        renderMode = RENDERMODE_CONTINUOUSLY
    }

    fun setVideoPath(path: String) {
        renderer.setVideoPath(path)
        renderer.setFilter(currentFilter)
        renderer.setResizeMode(resizeMode)
        requestRender()
    }

    fun setFilter(filter: String) {
        currentFilter = filter
        renderer.setFilter(filter)
        requestRender()
    }

    fun setSoundOn(isSoundOn: Boolean) {
        renderer.setSoundOn(isSoundOn)
    }

    fun setResizeMode(mode: String) {
        resizeMode = mode
        renderer.setResizeMode(mode)
        requestRender()
    }

    fun setAudioPath(uri: String?) {
        renderer.setAudioPath(uri)
    }

    fun seekTo(positionMs: Int) {
        renderer?.seekTo(positionMs)
    }

    fun restart() {
        renderer?.seekTo(0)
    }

    fun release() {
        renderer.release()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        release()
    }

    public override fun onPause() {
        super.onPause()
        renderer.onPause()
    }

    public override fun onResume() {
        super.onResume()
        renderer.onResume()
    }

    fun emitProgress(time: Float) {
        val event = com.facebook.react.bridge.Arguments.createMap().apply {
            putDouble("currentTime", time.toDouble())
        }
        val reactContext = context as ReactContext
        reactContext.getJSModule(RCTEventEmitter::class.java).receiveEvent(id, "onProgress", event)
    }
}