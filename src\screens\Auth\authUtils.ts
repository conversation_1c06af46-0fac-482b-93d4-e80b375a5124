import { Client } from '../../lib/Client';
import { navigationRef } from '../../navigation/RootContainer';
import { SCREENS } from '../../navigation/screenNames';
import { getSocket } from '../../socket-client/socket';
import { IUser } from '../../types/index.types';
import { setStreamToken } from '../../utils/asyncStorage';
import { errorToast } from '../../utils/commonFunction';
import { initializeChatspaces } from '../../utils/initializeApp';
import { getMe } from '../../utils/userApiService';

// handles response from otp verification api
export const handleVerificationResponse = async (res: any, updateUser: (user: IUser) => void) => {
  if (res?.status && res?.status_code == 200) {
    setStreamToken(res?.data?.streamToken);
    // new user
    if (res?.data?.statusCode === 201) {
      navigationRef.reset({
        index: 1,
        routes: [
          { name: SCREENS.SignupScreen },
          { name: SCREENS.SetUsernameScreen, params: { interimToken: res?.data?.accessToken } },
        ],
      });
    }
    // existing user
    else {
      await Client.AuthToken.set(res?.data?.accessToken);
      const user = await getMe();
      await initializeChatspaces();
      if (!user) {
        errorToast('Failed to fetch user data');
        return;
      }
      updateUser(user);
      // const socket = getSocket();
      // socket.connect();
      navigationRef.reset({
        index: 0,
        routes: [{ name: SCREENS.HomeScreen }],
      });
    }
  } else if (res?.statusCode === -6) {
    console.warn('Verification pending or another issue occurred.');
  } else {
    errorToast(res?.message || 'Login failed. Please try again.');
  }
};
