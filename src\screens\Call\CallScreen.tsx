import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  StyleSheet,
  Text,
  ToastAndroid,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { navigateTo } from '../../utils/commonFunction';
import CommonView from '../../component/CommonView';
import { colors } from '../../theme/colors';
import { commonFontStyle } from '../../theme/fonts';

import RenderUserIcon from '../../component/RenderUserIcon';
import { SCREENS } from '../../navigation/screenNames';
import { useCallContext } from '../../Context/CallProvider';

import RenderUserIconGroup from '../../component/RenderUserIconGroup';

import { CallType } from '../../types/calls.types';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getRealm } from '../../device-storage/realm/realm';
import { CallHistoryStatus, CallSchema } from '../../device-storage/realm/schemas/CallSchema';

import { updateCallHistoryOnEnd } from '../../calls/calls.lib';
import CallHistoryItem from './components/CallHistoryItem';
import useUsers from '../../device-storage/realm/hooks/useUsers';
import { UserSchema } from '../../device-storage/realm/schemas/UserSchema';
import { EntypoIcons } from '../../utils/vectorIcons';
import ThreeDotsSVG from '../../assets/svgIcons/ThreeDotsSVG';
import CustomAlert from '../../component/Common/CustomAlert';
import { Menu, MenuOptions, MenuOption, MenuTrigger, renderers } from 'react-native-popup-menu';
import CallService from '../../service/CallService';
const { SlideInMenu, Popover, ContextMenu, NotAnimatedContextMenu } = renderers;
type UserSummary = {
  _id: string;
  name: string;
  username: string;
  image: string;
  gender: 'male' | 'female' | 'other' | string;
  phoneNumber: string;
};

const alert = Alert.alert;

export type CallHistoryItem = {
  _id: string;
  callStatus: 'outgoing' | 'missed' | 'received';
  callType: CallType;
  scheduledAt: Date;
  createdAt: Date;
  participants: string[];
  invitedUserIds: string[];
  duration: number;
  initiator: UserSummary;
  invitedUsers: UserSummary[];
  participantUsers: UserSummary[];
};

export type CallHistoryT = CallSchema & {
  initiatorContact?: UserSchema;
};

const CallScreen = () => {
  const { t } = useTranslation();
  const realm = getRealm();

  const { contactUsers } = useUsers();
  const [showCallOptionsModal, setShowCallOptionsModal] = useState(false);

  const getContactByUserId = (userId: string) => {
    return contactUsers.find((user) => user.id === userId);
  };

  const [callHistory, setCallHistory] = useState<CallHistoryT[]>([]);
  function delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  type retryCallPayload = {
    callId: string;
    callStatus: CallHistoryStatus;
  };
  let initailDelay = 5000;
  async function syncCalls(notSyncedCalls: retryCallPayload[]) {
    let unSyncedCalls = [...notSyncedCalls];

    while (unSyncedCalls.length > 0) {
      const batchSize = 3;
      const currentUnSynced: retryCallPayload[] = [];

      for (let i = 0; i < unSyncedCalls.length; i += batchSize) {
        const batch = unSyncedCalls.slice(i, i + batchSize);
        const prmResponse = await Promise.all(
          batch.map((call) => updateCallHistoryOnEnd(call.callId, call.callStatus)),
        );

        currentUnSynced.push(
          ...prmResponse
            .filter((item) => item !== undefined)
            .filter((item) => item.status === false),
        );

        if (i + batchSize < unSyncedCalls.length) {
          await delay(500);
        }
      }

      if (currentUnSynced.length > 0) {
        await delay(initailDelay); // wait before retrying
        initailDelay += 5000;
        unSyncedCalls = currentUnSynced;
      } else {
        break;
      }
    }
  }

  useEffect(() => {
    const calls = realm.objects(CallSchema).sorted('createdAt', true);

    const updateList = async () => {
      try {
        const notSyncedCalls: CallSchema[] = [];
        const updatedList: CallHistoryT[] = [];
        for (const item of calls) {
          if (item.syncPending) {
            notSyncedCalls.push(item);
          }

          // todo: do not query always. store it once in the call schema
          const contact = getContactByUserId(item.initiator);
          updatedList.push({
            ...item,
            initiatorContact: contact,
          } as unknown as CallHistoryT);
        }

        setCallHistory(updatedList);
        if (notSyncedCalls.length > 0) {
          syncCalls(
            notSyncedCalls.map((call) => ({ callId: call.callId, callStatus: call.callStatus })),
          );
        }
      } catch (err) {
        console.error(err, 'paga ragilina fire');
      }
    };
    // Initial load
    updateList();

    // Realm change listener
    const listener = () => {
      updateList();
    };

    calls.addListener(listener);
    return () => calls.removeListener(listener);
  }, [realm]);

  return (
    <CommonView headerTitle={t('Calls')} containerStyle={styles.container}>
      {/* <CurrentCallDisplay /> */}

      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <Text style={styles.header}>
          History {callHistory.length == 0 ? '' : `(${callHistory.length})`}
        </Text>

        <CallScreenOptionsModal
          visible={showCallOptionsModal}
          onClose={() => setShowCallOptionsModal(false)}
          onOptionPress={() => {}}
        />
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={callHistory}
        renderItem={({ item }) => <CallHistoryItem item={item} />}
        keyExtractor={(item, index) => item?.callId}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={5}
        removeClippedSubviews={true}
        ListEmptyComponent={() => {
          return (
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Text style={{ color: colors.black_23, fontSize: 18, fontWeight: '500' }}>
                No calls history found.
              </Text>
            </View>
          );
        }}
        style={{ marginBottom: 50 }}
        onEndReachedThreshold={0.5}
        contentContainerStyle={{ ...(callHistory.length == 0 && { flexGrow: 1 }) }}
      />
      <TouchableOpacity
        style={styles.newCallStyle}
        activeOpacity={0.6}
        onPress={() => {
          navigateTo(SCREENS.NewCallScreen);
        }}
      >
        <Ionicons name={'add-outline'} size={30} color={colors.mainPurple} />
      </TouchableOpacity>
    </CommonView>
  );
};

export default CallScreen;

const styles = StyleSheet.create({
  container: {
    gap: 20,
    backgroundColor: colors.white,
  },
  valueText: {
    ...commonFontStyle(600, 18, colors.black_23),
  },
  header: {
    ...commonFontStyle(600, 18, colors.black_23),
    fontWeight: 700,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    ...commonFontStyle(600, 16, colors.black),
  },
  details: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginLeft: 5,
  },

  newCallStyle: {
    position: 'absolute',
    bottom: 120,
    right: 16,
    borderRadius: 100,
    elevation: 8,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  newCallStyle1: {
    width: 60,
    height: 60,
  },
  menu: {
    position: 'absolute',
    top: 30,
    right: 33,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});

const CurrentCallDisplay = () => {
  const { callDetails } = useCallContext();

  if (callDetails.state !== 'ongoing') return null;

  const participants = callDetails.participants;

  const getNameString = () => {
    const names = participants.slice(0, 3).map((p) => p.client.name);
    if (names.length === 3 && participants.length === 3) {
      return `${names[0]}, ${names[1]} and ${names[2]}.`;
    }
    if (names.length === 3 && participants.length > 3) {
      return `${names[0]}, ${names[1]} and`;
    }
    return names.join(', ');
  };

  const CurrentCallItem = () => {
    const multipleParticipants = participants.length > 1;

    return (
      <TouchableOpacity
        style={[styles.itemContainer, { paddingVertical: 10 }]}
        onPress={() => navigateTo(SCREENS.MainCallScreen)}
      >
        {multipleParticipants ? (
          <RenderUserIconGroup
            mainSize={50}
            subSize={40}
            containerStyle={{ marginRight: 12 }}
            mainUrl={participants[0]?.client?.image}
            subUrl={participants[1]?.client?.image}
          />
        ) : (
          <RenderUserIcon
            size={50}
            containerStyle={{ marginRight: 12 }}
            url={participants[0]?.client?.image}
          />
        )}

        <View style={{ flex: 1 }}>
          <Text
            style={{
              ...commonFontStyle(600, 16, colors.black),
              fontWeight: '700',
            }}
          >
            {getNameString()}
          </Text>

          {participants.length > 3 && (
            <Text style={{ color: colors.mainPurple, fontWeight: '700' }}>
              {participants.length - 2} Others
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Text style={styles.header}>Audio call ongoing</Text>
      <CurrentCallItem />
    </>
  );
};

type CallScreenOptionsModalProps = {
  visible: boolean;
  onClose: () => void;
  onOptionPress: (option: string) => void;
};
const CallScreenOptionsModal = ({ onOptionPress }: CallScreenOptionsModalProps) => {
  const [visible, setVisible] = useState(false);

  const onClose = () => {
    setVisible(false);
  };

  // add backhandler to clear the visible state
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      onClose();
      return false;
    });
    return () => backHandler.remove();
  }, []);

  async function ClearCallHistory() {
    try {
      setVisible(false);
      await CallService.deleteAllCalls();
      ToastAndroid.showWithGravity('Call history cleared', ToastAndroid.LONG, ToastAndroid.CENTER);
    } catch (err) {
      console.log(err);
    }
  }

  return (
    <Menu
      onOpen={() => setVisible(true)}
      opened={visible}
      onClose={onClose}
      onBackdropPress={() => setVisible(false)}
      renderer={NotAnimatedContextMenu}
    >
      <MenuTrigger
        onPress={() => setVisible(true)}
        style={{ padding: 5 }}
        children={<EntypoIcons name="dots-three-vertical" size={18} color="black" />}
      />

      <MenuOptions
        customStyles={{
          optionsContainer: {
            padding: 10,
            borderRadius: 10,
            alignItems: 'center',
          },
        }}
      >
        <MenuOption onSelect={ClearCallHistory}>
          <Text style={{ color: colors.gray_80, fontSize: 16, fontWeight: '600' }}>
            Delete call history
          </Text>
        </MenuOption>
      </MenuOptions>
    </Menu>
  );
};
