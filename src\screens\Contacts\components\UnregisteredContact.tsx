import React from 'react';
import { Text, View } from 'react-native';
import UserAvatar from '../../../component/utils/UserAvatar';
import { IContact } from '../../../device-storage/realm/schemas/ContactSchema';
import { AppStyles } from '../../../theme/appStyles';

type UnregisteredContactProps = {
  contact: IContact;
};

const UnregisteredContact = (props: UnregisteredContactProps) => {
  return (
    <View style={[AppStyles.profileCard]}>
      <UserAvatar width={50} />
      <Text style={[AppStyles.baseText, { fontWeight: 600 }]}>{props.contact.name}</Text>
    </View>
  );
};

export default UnregisteredContact;
