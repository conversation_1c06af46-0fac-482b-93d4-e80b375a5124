import { Realm } from 'realm';
import { realmSchemaNames } from '../schemas/schemaNames';
import { ChatSpaceSchema, IChatSpace, RemoteChatSpace } from '../schemas/ChatSpaceSchema';
import { getDateFromString, getDateInMillies } from '../../../lib/lib';
import { safeRealmWrite } from '../lib';
import { MembershipStatus } from '../../../types/chats.types';
import { ConversationSettingsRepo } from './ConversationSettingsRepo';
import { ChatSpacesResponse } from '../../../types/chatSpace.types';
import { getConversationDataFromLocalChatSpace } from '../../../lib/chatLib';
import { ChatService, RealmChatSpace } from '../../../service/ChatService';
import { ChatSpace } from '../../../types/socketPayload.type';

export class ChatSpaceRepo {
  private static schemaName = realmSchemaNames.chat_space;
  constructor() {}

  static findRealmObjectById(realm: Realm, chatSpaceId: string) {
    return realm.objectForPrimaryKey(ChatSpaceSchema, chatSpaceId);
  }
  static createOrUpdate(
    realm: Realm,
    remoteChatSpace: RemoteChatSpace,
    membershipStatus: MembershipStatus,
  ): ChatSpaceSchema {
    const existingChatSpace = this.findRealmObjectById(realm, remoteChatSpace.chatSpaceId);
    if (!existingChatSpace) {
      const conversationSettings = ConversationSettingsRepo.getOrcreate(
        realm,
        remoteChatSpace.chatSpaceId,
      );
      const newChatSpace: IChatSpace = {
        id: remoteChatSpace.chatSpaceId,
        name: remoteChatSpace.name,
        type: remoteChatSpace.type,
        description: remoteChatSpace.description,
        displayPic: remoteChatSpace.displayPic,
        isPrivate: remoteChatSpace.isPrivate,
        createdBy: remoteChatSpace.createdBy,
        membershipStatus: membershipStatus,
        inviteCode: remoteChatSpace.inviteCode,
        memberCount: remoteChatSpace.memberCount,
        createdAt: getDateInMillies(remoteChatSpace.createdAt) || Date.now(),
        updatedAt: getDateInMillies(remoteChatSpace.updatedAt) || Date.now(),
        conversationSettings,
      };
      return safeRealmWrite(realm, () => realm.create(ChatSpaceSchema, newChatSpace));
    }
    //  If exists just update the chatSpace
    safeRealmWrite(realm, () => {
      existingChatSpace.name = remoteChatSpace.name;
      existingChatSpace.type = remoteChatSpace.type;
      existingChatSpace.description = remoteChatSpace.description;
      existingChatSpace.displayPic = remoteChatSpace.displayPic;
      existingChatSpace.isPrivate = remoteChatSpace.isPrivate;
      existingChatSpace.createdBy = remoteChatSpace.createdBy;
      existingChatSpace.membershipStatus = membershipStatus ?? existingChatSpace.membershipStatus;
      existingChatSpace.inviteCode = remoteChatSpace.inviteCode;
      existingChatSpace.memberCount = remoteChatSpace.memberCount;
      existingChatSpace.updatedAt = getDateInMillies(remoteChatSpace.updatedAt) || Date.now();
    });

    return existingChatSpace;
  }

  static syncChatSpacesAndConversations = (realm: Realm, response: ChatSpacesResponse) => {
    const allSpaces = [...response.groups, ...response.channels];

    const createdSpaces: ChatSpaceSchema[] = [];

    safeRealmWrite(realm, () => {
      for (const space of allSpaces) {
        const membershipStatus = space.role;
        const created = ChatSpaceRepo.createOrUpdate(realm, space, membershipStatus);
        createdSpaces.push(created);
      }
      for (const space of createdSpaces) {
        const conversation = getConversationDataFromLocalChatSpace(space);
        ChatService.createChatspaceBulk(conversation);
      }
    });
  };

  static updateMemberCount(
    realm: Realm,
    conversationId: string,
    newMembers: number,
    removedMembers: number,
  ) {
    const chatSpace = this.findRealmObjectById(realm, conversationId);
    if (!chatSpace) return;

    safeRealmWrite(realm, () => {
      const prevCount = typeof chatSpace.memberCount === 'number' ? chatSpace.memberCount : 0;

      let newCount = prevCount + (newMembers || 0) - (removedMembers || 0);

      if (newCount < 0) newCount = 0;

      if (newCount !== prevCount) {
        chatSpace.memberCount = newCount;
      }
    });
  }

  static updateChatSpace = (realm: Realm, data: Partial<ChatSpace>) => {
    if (!data.chatSpaceId) return;

    const existingConversation = this.findRealmObjectById(realm, data.chatSpaceId);
    if (!existingConversation) return;

    safeRealmWrite(realm, () => {
      if (data.name !== undefined) existingConversation.name = data.name;
      if (data.description !== undefined) existingConversation.description = data.description;
      if (data.displayPic !== undefined) existingConversation.displayPic = data.displayPic;
      if (data.isPrivate !== undefined) existingConversation.isPrivate = data.isPrivate;
      if (data.type !== undefined) existingConversation.type = data.type;
      if (data.createdBy !== undefined) existingConversation.createdBy = data.createdBy;
      // if (data.isLiveStreaming !== undefined)
      //   existingConversation.isLiveStreaming = data.isLiveStreaming;
      existingConversation.updatedAt = Date.now();
    });

    return existingConversation;
  };

  static updateMemberRole(realm: Realm, conversationId: string, role: any) {
    const chatSpace = this.findRealmObjectById(realm, conversationId);
    if (!chatSpace) {
      console.warn(`Conversation with ID ${conversationId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      chatSpace.membershipStatus = role;
    });
  }

  static delete(realm: Realm, conversationId: string) {
    const chatSpace = this.findRealmObjectById(realm, conversationId);
    if (!chatSpace) return;
    safeRealmWrite(realm, () => {
      realm.delete(chatSpace);
    });
  }
}
