import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

export function AppStateListener() {
  const appState = useRef<AppStateStatus>(AppState.currentState);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      // if (
      //   appState.current.match(/background|inactive/) &&
      //   nextAppState === 'active'
      // ) {
      //   console.log('App has come to the foreground!');
      //   // Here you can trigger Redux rehydration, fetch updates, etc.
      // }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  return null; // This component only listens to app state changes
}
