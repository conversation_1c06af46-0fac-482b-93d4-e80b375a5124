import { Comment } from './CommentContainer';

export const mockComments: Comment[] = [
  // {
  //   liveStreamId: '1',
  //   senderId: '1',
  //   message: 'So inspiring! ❤️',
  //   displayName: 'Fat<PERSON> Khan',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=1',
  //   chatId: '1',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '2',
  //   message: 'Saludos from Spain! 🎉',
  //   displayName: '<PERSON>',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=2',
  //   chatId: '2',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '3',
  //   message: 'Great energy! 💫',
  //   displayName: '<PERSON><PERSON>',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=3',
  //   chatId: '3',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '4',
  //   message: 'Loving this livestream! 🌟✨',
  //   displayName: "<PERSON> O'Sullivan",
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=4',
  //   chatId: '4',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '5',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '5',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '6',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '6',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '7',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '7',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '8',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '8',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '9',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '9',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '10',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '10',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '11',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '11',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '12',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '12',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '13',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '13',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '14',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '14',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '15',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '15',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '16',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '16',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '17',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '17',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '18',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '18',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '19',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '19',
  //   chatRank: 'viewer',
  // },
  // {
  //   liveStreamId: '1',
  //   senderId: '20',
  //   message: 'This is awesome! 🔥',
  //   displayName: 'Emma Johansson',
  //   displayImageUrl: 'https://i.pravatar.cc/40?img=5',
  //   chatId: '20',
  //   chatRank: 'viewer',
  // },
];
