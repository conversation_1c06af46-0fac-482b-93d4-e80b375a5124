import React, { FC } from 'react';
import { View, TextInput, StyleSheet, Image, TextInputProps, TouchableOpacity } from 'react-native';
import { Images } from '../Assets/Images';
import { wp } from '../utils/Constants/dimensionUtils';

interface SearchBarProps extends TextInputProps {
  placeholder: string;
  onChangeText: (text: string) => void;
  value: string;
  activeTab:string;
  uploadMusic:(any:any)=>void;
}

const SearchBar: FC<SearchBarProps> = ({ placeholder, onChangeText, value, activeTab,uploadMusic,...rest }) => {
  var isActive=false
  console.log(activeTab)
  if(activeTab=='Music'){
    isActive=true
  }
  return (
    <View style={{flexDirection:"row",justifyContent:'center',alignItems:'center',paddingTop:15}}>
    <View style={[styles.container,,{width:isActive? '70%':'90%'}]}>
      <Image
        style={styles.image}
        source={require('../Assets/Images/Search.png')}
      />
      <TextInput
        style={styles.input}
        placeholder={placeholder}
        placeholderTextColor="#aaa"
        onChangeText={onChangeText}
        value={value}
        {...rest}
      />
    </View>
      {isActive ==true && <TouchableOpacity onPress={uploadMusic} style={{ height: 50, width: '18%', justifyContent: "center", alignItems: 'center', borderRadius: 10, backgroundColor: '#f0f0f0',}}>
        <Image source={Images.Upload} style={{ height: wp(6), width: wp(6), resizeMode: 'contain' }} />
      </TouchableOpacity>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 50,
    width: '70%',
    marginRight: 10,
    borderRadius: 10,
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 16,
    color: '#333',
  },
  image: {
    marginLeft: 17,
    marginRight: 10,
  },
});

export default React.memo(SearchBar);
