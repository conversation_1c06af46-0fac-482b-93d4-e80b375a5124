import { Platform, Alert, Linking, PermissionsAndroid } from 'react-native';

const showPermissionAlert = (permissionName: string) => {
  Alert.alert(
    'Permission Required',
    `${permissionName} permission is required. Open settings to allow access.`,
    [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Open Settings',
        onPress: async () => {
          try {
            await new Promise((resolve) => setTimeout(resolve, 500)); // Small delay
            Platform.OS === 'ios' ? Linking.openSettings() : Linking.openSettings();
          } catch (error) {
            console.log('Failed to open settings:', error);
          }
        },
      },
    ],
    { cancelable: false },
  );
};

const checkAndRequestPermission = async (
  permission: string | string[] | undefined,
  permissionName: string,
): Promise<boolean> => {
  if (!permission) return true;

  if (Array.isArray(permission)) {
    for (const p of permission) {
      const granted = await checkAndRequestPermission(p, permissionName);
      if (!granted) return false;
    }
    return true;
  }

  const status = await PermissionsAndroid.check(permission);
  if (status === PermissionsAndroid.RESULTS.GRANTED) return true;

  if (
    status === PermissionsAndroid.RESULTS.BLOCKED ||
    status === PermissionsAndroid.RESULTS.UNAVAILABLE
  ) {
    showPermissionAlert(permissionName);
    return false;
  }

  if (status === PermissionsAndroid.RESULTS.DENIED) {
    const result = await PermissionsAndroid.request(permission);
    if (result === PermissionsAndroid.RESULTS.GRANTED) return true;
    showPermissionAlert(permissionName);
    return false;
  }
  return false;
};

export const requestCameraPermission = async () => {
  const cameraPermission = Platform.select({
    ios: PermissionsAndroid.PERMISSIONS.CAMERA,
    android: PermissionsAndroid.PERMISSIONS.CAMERA,
  });
  return await checkAndRequestPermission(cameraPermission, 'Camera');
};

export const requestMicrophonePermission = async () => {
  const microphonePermission = Platform.select({
    ios: PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    android: PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
  });
  return await checkAndRequestPermission(microphonePermission, 'Microphone');
};

export const requestStoragePermission = async () => {
  const isAndroid13 = Platform.OS === 'android' && Platform.Version >= 33;
  const permission = isAndroid13
    ? [
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_AUDIO,
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_VISUAL_USER_SELECTED,
      ]
    : [
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ];
  return await checkAndRequestPermission(permission, 'Storage');
};

export default async function requestPermissions(): Promise<boolean> {
  try {
    const cameraGranted = await requestCameraPermission();
    if (!cameraGranted) return false;

    const microphoneGranted = await requestMicrophonePermission();
    if (!microphoneGranted) return false;

    // const storageGranted = await requestStoragePermission();
    // if (!storageGranted) return false;

    return true;
  } catch (error) {
    console.error('Error requesting permissions:', error);
    return false;
  }
}
