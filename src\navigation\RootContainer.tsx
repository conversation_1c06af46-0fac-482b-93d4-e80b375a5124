import React, { FC, useEffect, useRef } from 'react';
import { NavigationContainer, createNavigationContainerRef } from '@react-navigation/native';
import { StatusBar, Linking, Text, View } from 'react-native';
import StackNavigator from './Navigator';
import { colors } from '../theme/colors';
import { SCREENS } from './screenNames';

import CallOverlay from '../component/CallOverlay';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export const navigationRef = createNavigationContainerRef();

const linking = {
  prefixes: ['myapp://', 'chatbucket://'], // multiple schemes
  config: {
    screens: {
      IncomingCallScreen: 'call/dummy',
    },
  },
};

// Helper: parse query parameters from deep link URL
function parseQueryParams(url: string) {
  const params: Record<string, string> = {};
  const query = url.split('?')[1];
  if (query) {
    query.split('&').forEach((pair) => {
      const [key, value] = pair.split('=');
      params[key] = decodeURIComponent(value);
    });
  }
  return params;
}

const RootContainer: FC = () => {
  // Queue deep link if navigation is not ready
  const pendingDeepLinkRef = useRef<string | null>(null);
  const insets = useSafeAreaInsets();
  const handleDeepLink = (url: string) => {
    console.log('[Notif-Log]   deep link in hanlde deep link func ', url);

    const params = parseQueryParams(url);
    if (navigationRef.isReady()) {
      navigationRef.navigate(SCREENS.IncomingCallScreen, params);
    } else {
      pendingDeepLinkRef.current = url;
    }
  };

  useEffect(() => {
    // Background / resume deep links
    const subscription = Linking.addEventListener('url', (event) => {
      console.log('[Notif-Log]   deep link in event listener ', event.url);

      handleDeepLink(event.url);
    });

    // Cold start
    Linking.getInitialURL().then((url) => {
      console.log('[Notif-Log]   deep link in getInitialURL ', url);

      if (url) handleDeepLink(url);
    });

    return () => subscription.remove();
  }, []);

  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linking}
      onReady={() => {
        console.log('[Notif-Log]   deep link in onReady ', pendingDeepLinkRef.current);

        if (pendingDeepLinkRef.current) {
          console.log('[Notif-Log]   deep link in onReady ', pendingDeepLinkRef.current);

          handleDeepLink(pendingDeepLinkRef.current);
          pendingDeepLinkRef.current = null;
        }
      }}
    >
      <StatusBar barStyle="light-content" backgroundColor={colors.mainPurple} translucent={true} />

      <View style={{ top: insets.top, zIndex: 5 }}>
        <CallOverlay />
      </View>
      <StackNavigator />
    </NavigationContainer>
  );
};

export default RootContainer;
