// App.tsx
import React from 'react';
import {
  SafeAreaView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  ImageSourcePropType,
} from 'react-native';

const Stats: React.FC<{ label: string; value: string }> = ({ label, value }) => {
  return (
    <View style={styles.statContainer}>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );
};

const BMainScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Connect, belong and see{'\n'}the world anew—together.</Text>
      <Image source={{ uri: '' }} style={styles.image} resizeMode="contain" />
      <View style={styles.statsRow}>
        <Stats label="Blind" value="4,15,45" />
        <Stats label="Volunteers" value="78,87,987" />
      </View>
      <TouchableOpacity style={styles.primaryButton}>
        <Text style={styles.primaryButtonText}>I need visual assistance</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.secondaryButton}>
        <Text style={styles.secondaryButtonText}>I’d like to volunteer</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default BMainScreen;

// ---- Styles and constants ----
export const Colors = {
  primary: '#6C5CE7',
  secondary: '#fff',
  text: '#000',
  secondaryText: '#6C6C6C',
};

export const Typography = {
  title: {
    fontSize: 24,
    fontWeight: '600' as const,
    textAlign: 'center' as const,
    marginBottom: 24,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700' as const,
    textAlign: 'center' as const,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.secondaryText,
    textAlign: 'center' as const,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    textAlign: 'center' as const,
  },
};

export const Spacing = {
  containerPadding: 16,
  buttonMarginTop: 16,
  statsMarginVertical: 24,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.containerPadding,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.secondary,
  },
  title: Typography.title,
  image: {
    width: 200,
    height: 200,
    marginBottom: Spacing.statsMarginVertical,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: Spacing.statsMarginVertical,
  },
  statContainer: {
    alignItems: 'center',
  },
  statValue: Typography.statValue,
  statLabel: Typography.statLabel,
  primaryButton: {
    width: '100%',
    paddingVertical: 14,
    backgroundColor: Colors.primary,
    borderRadius: 8,
    marginBottom: 12,
  },
  primaryButtonText: {
    ...Typography.buttonText,
    color: Colors.secondary,
  },
  secondaryButton: {
    width: '100%',
    paddingVertical: 14,
    borderColor: Colors.primary,
    borderWidth: 2,
    borderRadius: 8,
  },
  secondaryButtonText: {
    ...Typography.buttonText,
    color: Colors.primary,
  },
});
