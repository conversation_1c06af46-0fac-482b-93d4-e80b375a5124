import { RealmProvider } from '../device-storage/realm/realm';
import SocketProvider from '../socket-client/SocketProvider';
import { UserProvider } from './UserProvider';
import { MediasoupProvider } from './RtcProvider';
import CallProvider from './CallProvider';
import { MainCallScreenOverlay } from '../screens/Call/MainCallScreen';
import { NetInfoProvider } from './NetInfoProvider';
import { MenuProvider } from 'react-native-popup-menu';
import { DataProvider } from '../component/media-editor/utils/Context';
import { TimeProvider } from '../component/media-editor/utils/Context/TimeContext';

const RootProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <RealmProvider>
      <MenuProvider>
        <NetInfoProvider>
          <UserProvider>
            <SocketProvider>
              <MediasoupProvider>
                <CallProvider>
                  <DataProvider>
                    <TimeProvider>{children}</TimeProvider>
                  </DataProvider>
                  <MainCallScreenOverlay />
                </CallProvider>
              </MediasoupProvider>
            </SocketProvider>
          </UserProvider>
        </NetInfoProvider>
      </MenuProvider>
    </RealmProvider>
  );
};

export default RootProviders;
