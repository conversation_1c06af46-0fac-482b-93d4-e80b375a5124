import { SafeAreaView, StyleSheet, Text, View, TouchableOpacity, Image } from 'react-native';
import React, { useState } from 'react';
import { IMAGES } from '../assets/Images';
import { colors } from '../theme/colors';
import { commonFontStyle, hp } from '../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { AppStyles } from '../theme/appStyles';
import RenderUserIcon from './RenderUserIcon';
import { useTranslation } from 'react-i18next';
import { SCREENS } from '../navigation/screenNames';
import { navigateTo } from '../utils/commonFunction';

import LiveStreamIconSvg from '../assets/svgIcons/LiveStreamIconSvg';
import ChatIconSvg from '../assets/svgIcons/ChatIconSvg';
import LocationIconSvg from '../assets/svgIcons/LocationIconSvg';
import { FontAwesome6Icons } from '../utils/vectorIcons';
import ScannerSVG from '../assets/svgIcons/ScannerSVG';
import MenuSVG from '../assets/svgIcons/MenuSVG';

type Props = {
  type: any;
  contactsLength?: number;
  onPressMenu?: () => void;
};

const HomeHeader = ({ type, contactsLength, onPressMenu }: Props) => {
  // const { t } = useTranslation();

  let tabData = [
    {
      icon: <ChatIconSvg />,
      name: 'Chats',
      onPress: () => {
        // do nothing,
      },
    },
    // {
    //   icon: <LiveStreamIconSvg />,
    //   name: 'Live streams',
    //   onPress: () => {
    //     navigateTo(SCREENS.LiveStreamFeeds);
    //   },
    // },
    // {
    //   icon: <LocationIconSvg />,
    //   name: 'Locations',
    //   onPress: () => {
    //     // do nothing
    //   },
    // },
  ];
  const navigation = useNavigation();
  const [selectedTabIndex, setselectedTabIndex] = useState(0);

  if (type == 'home') {
    return (
      <SafeAreaView>
        <View style={styles.headerMainView}>
          <View style={styles.header}>
            {tabData.map((tab, idx) => {
              return (
                <TouchableOpacity
                  key={tab.name}
                  onPress={tab.onPress}
                  style={{
                    backgroundColor:
                      selectedTabIndex === idx ? colors.overlayWhite_5 : 'transparent',
                    padding: 15,
                    paddingHorizontal: selectedTabIndex === idx ? 30 : 15,
                    borderRadius: 30,
                  }}
                >
                  <View style={[styles.tabIcon]}>
                    {tab.icon}
                    {selectedTabIndex === idx && <Text style={[styles.tabText]}>{tab.name}</Text>}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
          <TouchableOpacity style={styles.moreMenu} onPress={onPressMenu}>
            <MenuSVG />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  } else if (type == 'contact') {
    return (
      <SafeAreaView>
        <View style={[styles.headerMainView, { paddingLeft: 0 }]}>
          <View style={[styles.header, { gap: 0 }]}>
            <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerBackView}>
              <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <View>
              <Text style={{ ...commonFontStyle(600, 18, colors.white) }}>{'Contacts'}</Text>
              <Text style={{ ...commonFontStyle(400, 14, colors.white) }}>
                {contactsLength} {'Contacts'}
              </Text>
            </View>
          </View>
          <View style={[styles.header, { gap: 0 }]}>
            {/* <TouchableOpacity style={{ padding: hp(2) }}>
              <MenuSVG />
            </TouchableOpacity> */}
          </View>
        </View>
      </SafeAreaView>
    );
  } else if (type == 'scancontact') {
    return (
      <SafeAreaView>
        <View style={[styles.headerMainView, { paddingLeft: 0 }]}>
          <View style={[styles.header, { gap: 0 }]}>
            <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerBackView}>
              <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <View>
              <Text style={{ ...commonFontStyle(600, 18, colors.white) }}>{'New Contact'}</Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }
};

export default HomeHeader;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',

    marginTop: 30,
  },
  selectedView: {
    backgroundColor: colors._7155C3_purple,
    paddingVertical: 10,
    paddingHorizontal: 25,
    borderRadius: 50,
  },
  tabIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  tabText: {
    ...commonFontStyle(500, 18, colors.white),
  },
  headerMainView: {
    paddingLeft: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1.5),
    height: hp(13),
  },
  moreMenu: {
    padding: hp(2),
    marginTop: 30,
  },
  moreMenuStyle: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  headerBackView: {
    height: 18 + hp(2),
    width: 18 + hp(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerBackIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
    transform: [{ rotate: '180deg' }],
  },
});
