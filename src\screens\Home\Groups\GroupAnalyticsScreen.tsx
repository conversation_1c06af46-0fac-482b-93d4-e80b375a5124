import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, ActivityIndicator, ScrollView } from 'react-native';
import CommonView from '../../../component/CommonView';
import { colors } from '../../../theme/colors';
import { RouteProp, useRoute } from '@react-navigation/native';
import { getGroupAnalytics } from '../../../service/ChatSpacesService';
import { IMAGES } from '../../../assets/Images';

type GroupAnalyticsScreenParams = {
  GroupAnalyticsScreen: {
    userData: string;
  };
};

interface IMediaTypeCounts {
  text: number;
  image: number;
  video: number;
  audio: number;
  document: number;
  location: number;
  contact: number;
}

interface ITopContributor {
  _id: string;
  userId: string;
  totalMessages: number;
  name: string;
  image: string;
}
export interface IGroupAnalytics {
  _id: string;
  name: string;
  type: string;
  description: string;
  displayPic: string | null;
  isPrivate: boolean;
  inviteLink: string | null;
  createdBy: string;
  inviteCode: string;
  memberCount: number;
  chatType: string;
  messageCounts: IMediaTypeCounts;
  chatSpaceId: string;
  createdAt: string; 
  updatedAt: string; 
  __v: number;
  totalMemberCount: number;
  adminCount: number;
  newThisWeek: number;
  mediaShared: IMediaTypeCounts;
  topContributors: ITopContributor[];
}

const GroupAnalyticsScreen = () => {
  const route = useRoute<RouteProp<GroupAnalyticsScreenParams, 'GroupAnalyticsScreen'>>();
  const conversationId = route.params.userData;
  const [loading, setLoading] = useState(true);
  const [groupData, setGroupData] = useState<IGroupAnalytics | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const data = await getGroupAnalytics(conversationId);
        setGroupData(data);
      } catch (error) {
        console.error('Failed to fetch analytics', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [conversationId]);

  const formatDate = (dateString?: string | number | Date) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <CommonView headerTitle="Group analytics">
        <ActivityIndicator size="large" color={colors.back_opacity_10} />
      </CommonView>
    );
  }

  return (
    <CommonView
      headerTitle="Group analytics"
      // renderRight={() => (
      //   <View style={styles.headerIcons}>
      //     <ShareIconSVG color={colors.white} size={23} />
      //     <ThreeDotsSVG size={19} />
      //   </View>
      // )}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Group summary</Text>
        <Text style={styles.date}>Created on : {formatDate(groupData?.createdAt)}</Text>

        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>Total members : {groupData?.totalMemberCount}</Text>
        </View>
        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>Admins : {groupData?.adminCount}</Text>
        </View>
        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>New This Week: {groupData?.newThisWeek}</Text>
        </View>

        <Text style={styles.sectionTitle}>Media shared</Text>
        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>Images: {groupData?.mediaShared?.image ?? 0}</Text>
        </View>
        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>Videos: {groupData?.mediaShared?.video ?? 0}</Text>
        </View>
        <View style={styles.summaryBox}>
          <Text style={styles.summaryText}>Documents: {groupData?.mediaShared?.document ?? 0}</Text>
        </View>

        <Text style={styles.sectionTitle}>Top contributors</Text>
        {groupData?.topContributors?.map((contributor: ITopContributor) => (
          <View key={contributor._id} style={styles.contributorRow}>
            <View style={styles.avatarWrapper}>
              <Image
                source={contributor.image ? { uri: contributor.image } : IMAGES.profile_image}
                style={styles.avatar}
              />
            </View>
            <View>
              <Text style={styles.name}>{contributor.name}</Text>
              <Text style={styles.messageCount}>{contributor.totalMessages} messages</Text>
            </View>
          </View>
        ))}
      </ScrollView>
    </CommonView>
  );
};

export default GroupAnalyticsScreen;

const styles = StyleSheet.create({
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 17,
  },
  sectionTitle: {
    fontSize: 18,
    lineHeight: 25,
    verticalAlign: 'middle',
    fontWeight: '600',
    marginTop: 20,
    marginBottom: 6,
    color: colors.black_23,
  },
  date: {
    fontSize: 15,
    color: colors.gray_80,
    marginBottom: 10,
  },
  summaryBox: {
    borderRadius: 15,
    borderColor: colors._E3E3E3,
    borderWidth: 0.77,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  summaryText: {
    fontSize: 16,
    color: colors.black_23,
    lineHeight: 31,
    verticalAlign: 'middle',
  },
  contributorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  avatarWrapper: {
    marginRight: 12,
    position: 'relative',
  },
  avatar: {
    width: 46,
    height: 46,
    borderRadius: 23,
  },
  name: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.black_23,
  },
  messageCount: {
    fontSize: 14,
    color: colors.gray_80,
  },
});
