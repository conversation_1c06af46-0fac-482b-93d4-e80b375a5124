import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import RNVideo from 'react-native-video';
import OverlayComponent from '../liveStreams/OverlayComponent';

import LiveStreamHeader from '../liveStreams/LiveStreamHeader';
import { Comment } from '../liveStreams/CommentContainer';

import { ViewerLiveStreamSession } from '../../../../api/Chatspace/chatspace.api';
import useLivestreamSpace from '../../../../hooks/channels/useLivestreamSpace';

// Background Component
const LiveStreamBackground: React.FC = () => {
  return (
    <View
      style={{
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        backgroundColor: 'blue',
      }}
    ></View>
  );

  //   return (
  //     <RNVideo
  //       source={require('../../../../../test-media/Dummyvid.mp4')}
  //       style={{
  //         position: 'absolute',
  //         top: 0,
  //         left: 0,
  //         bottom: 0,
  //         right: 0,
  //         backgroundColor: 'blue',
  //       }}
  //       //   resizeMode="cover"
  //       //   repeat
  //     />
  //   );
};

type DummyScreenParams = {
  screenprops: {
    liveStreamInfo: ViewerLiveStreamSession;
    chatSpaceId: string;
    liveStreamId: string;
  };
};

// Main LiveStream Screen Component
const DummyScreen: React.FC = () => {
  //   const {params} = useRoute<RouteProp<DummyScreenParams, 'screenprops'>>();

  return (
    <View>
      <Text> Dummy Screen</Text>
    </View>
  );
};

export default DummyScreen;

// use them when we split the components,

export const DummyScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
});

function useOverlayVisibility({ headerOffset = -100, bottomOffset = 200, duration = 300 } = {}) {
  const [hide, setHide] = useState(false);

  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  const [bottomHidden, setBottomHidden] = useState(false);
  const bottomTranslateY = useRef(new Animated.Value(0)).current;
  const bottomOpacity = useRef(new Animated.Value(1)).current;

  const hideHeader = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const showHeader = () => {
    translateY.setValue(-100);
    opacity.setValue(0);

    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideBottomView = () => {
    Animated.parallel([
      Animated.timing(bottomTranslateY, {
        toValue: 200, // push it down
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => setBottomHidden(true));
  };

  const showBottomView = () => {
    setBottomHidden(false);
    bottomTranslateY.setValue(200);
    bottomOpacity.setValue(0);
    Animated.parallel([
      Animated.timing(bottomTranslateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(bottomOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  function toggleOverlay() {
    if (hide) {
      showHeader();
      showBottomView();
    } else {
      hideHeader();
      hideBottomView();
    }
    setHide((p) => !p);
  }

  return {
    toggleOverlay,
    bottomHidden,
    bottomTranslateY,
    bottomOpacity,
    translateY,
    opacity,
  };
}
