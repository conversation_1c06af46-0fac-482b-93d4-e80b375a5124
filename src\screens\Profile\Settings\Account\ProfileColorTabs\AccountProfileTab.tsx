import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { colors } from '../../../../../theme/colors';
import { useMe } from '../../../../../hooks/util/useMe';

const AccountProfileTab = () => {
  const { userPreferencesState } = useMe();

  const [selectedColor, setSelectedColor] = useState(
    userPreferencesState.userPreferences?.account?.profileColors?.primary ||
      colors.nameTabColors[0],
  );

  const colorRows = [
    colors.nameTabColors.slice(0, 6),
    colors.nameTabColors.slice(6, 12),
    colors.nameTabColors.slice(12, 18),
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={[styles.previewContainer, { backgroundColor: selectedColor }]}>
        <View style={styles.profileInfo}>
          <Image
            source={require('../../../../../assets/Image/beautiful.png')}
            style={styles.avatar}
          />
          <Text style={styles.nameText}><PERSON></Text>
          <Text style={styles.usernameText}>@wilson</Text>
        </View>
      </View>

      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Select a color</Text>
        </View>

        <View style={styles.colorGridContainer}>
          {colorRows.map((row, rowIndex) => (
            <View key={`row-${rowIndex}`} style={styles.colorRow}>
              {row.map((color, index) => (
                <TouchableOpacity
                  key={`color-${rowIndex}-${index}`}
                  activeOpacity={0.7}
                  onPress={() => {
                    setSelectedColor(color);

                    userPreferencesState.updatePreferences('account', {
                      profileColors: { primary: color, secondary: color, opacity: 1 },
                    });
                  }}
                >
                  <View
                    style={[
                      styles.colorOuterCircle,
                      selectedColor === color && { borderColor: color, borderWidth: 2 },
                    ]}
                  >
                    <View style={[styles.colorInnerCircle, { backgroundColor: color }]} />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  previewContainer: {
    backgroundColor: colors.previewBackground,
    marginVertical: 10,
    borderRadius: 12,
    margin: 15,
    padding: 15,
    paddingVertical: 25,
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  profileInfo: {
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: 'white',
    marginBottom: 15,
  },
  nameText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  usernameText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  sectionContainer: {
    padding: 15,
    backgroundColor: colors.sectionBackground,
    margin: 15,
    borderRadius: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.black_23,
  },
  eyedropperButton: {
    width: 45,
    height: 45,
    backgroundColor: colors.gray_f3,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorGridContainer: {
    alignItems: 'center',
  },
  colorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  colorOuterCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorInnerCircle: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 2,
    borderColor: 'white',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  colorPickerContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
  },
  colorPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    color: colors.black_23,
  },
  colorPreview: {
    marginBottom: 15,
    height: 40,
    borderRadius: 8,
  },
  colorPanel: {
    marginBottom: 15,
    borderRadius: 8,
  },
  hueSlider: {
    marginBottom: 15,
    height: 30,
    borderRadius: 8,
  },
  opacitySlider: {
    height: 30,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.gray_f3,
  },
  applyButton: {
    backgroundColor: colors.mainPurple,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.black_23,
  },
  applyButtonText: {
    color: 'white',
  },
});

export default AccountProfileTab;
