import React from 'react';
import { View, Text, Switch, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '../../../../theme/colors';

interface StreamToggleOptionProps {
  label: string;
  value?: boolean;
  onToggle: (val: boolean) => void;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  switchStyle?: ViewStyle;
  icon?: React.ReactNode;
}

const StreamToggleOption: React.FC<StreamToggleOptionProps> = ({
  label,
  value = false,
  onToggle,
  containerStyle,
  textStyle,
  switchStyle,
  icon,
}) => {
  return (
    <View style={[styles.toggleRow, containerStyle]}>
      <View style={styles.labelContainer}>
        {icon && <View style={styles.iconContainer}>{icon}</View>}
        <Text style={[styles.toggleText, textStyle]}>{label}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        thumbColor={colors.white}
        trackColor={{
          false: colors.gray_80,
          true: colors.mainPurple,
        }}
        style={switchStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  toggleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 4,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  toggleText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  iconContainer: {
    width: 20,
    height: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default StreamToggleOption;
