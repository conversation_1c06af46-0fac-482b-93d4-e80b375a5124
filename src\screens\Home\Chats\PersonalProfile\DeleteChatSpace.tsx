import { View, TouchableOpacity, Text } from 'react-native';
import DeleteSVG from '../../../../assets/svgIcons/DeleteSVG';
import { colors } from '../../../../theme/colors';
import { useState } from 'react';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../../types/chats.types';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import DeleteChatSpaceModal from './DeleteChatSpaceModal';

type DeleteChatSpaceProps = {
  conversationInfo: ConversationInfo | null;
};

const DeleteChatSpace: React.FC<DeleteChatSpaceProps> = ({ conversationInfo }) => {
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const conversationId = conversationInfo?.id || '';
  const isGroup = conversationInfo?.type === ConversationType.GROUP;

  return (
    <View>
      <View
        style={{
          marginVertical: 10,
        }}
      >
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 10,
            gap: 10,
          }}
          onPress={() => setDeleteModal(true)}
        >
          <DeleteSVG color={colors.thick_red} size={20} />
          <Text
            style={{
              fontSize: 16,
              color: colors.thick_red,
              fontWeight: '400',
            }}
          >
            {isGroup ? 'Delete Group' : 'Delete Channel'}
          </Text>
        </TouchableOpacity>
      </View>
      <DeleteChatSpaceModal
        conversationInfo={conversationInfo}
        isVisible={deleteModal}
        onClose={() => setDeleteModal(false)}
      />
    </View>
  );
};

export default DeleteChatSpace;
