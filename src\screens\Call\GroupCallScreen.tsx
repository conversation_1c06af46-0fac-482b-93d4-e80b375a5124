import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import CommonView from '../../component/CommonView';
import { useTranslation } from 'react-i18next';
import { colors } from '../../theme/colors';

import RenderUserIcon from '../../component/RenderUserIcon';
import { actuatedNormalize, commonFontStyle } from '../../theme/fonts';
import { FlatList } from 'react-native';
import { useEffect, useState } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { FeatherIcons } from '../../utils/vectorIcons';
import { CallHistoryItemNormalized } from '../../device-storage/realm/schemas/CallSchema';
import { ConversationSchema } from '../../device-storage/realm/schemas/ConversationSchema';
import { calculateDateInfo } from './components/CallHistoryItem';

export type GroupCallScreenInfoParams = {
  GroupCallScreenInfo: {
    callDetails: CallHistoryItemNormalized;
    groupConversation: ConversationSchema;
  };
};

const GroupCallScreen = () => {
  const route = useRoute<RouteProp<GroupCallScreenInfoParams, 'GroupCallScreenInfo'>>();
  const { callDetails, groupConversation } = route.params;
  const [attendedPeople, setAttendedPeople] = useState<GroupCallItemProps[]>([]);
  const [missedPeople, setMissedPeople] = useState<GroupCallItemProps[]>([]);
  const participants = callDetails.participantDetails ?? [];
  const invitedUserIdsDetails = callDetails.invitedUserIdsDetails ?? [];

  const { t } = useTranslation();

  useEffect(() => {
    const attendedPeople: GroupCallItemProps[] = participants.map((item) => {
      return {
        _id: item._id,
        title: item.name,
        callStatus: item._id === callDetails.initiator ? 'Outgoing' : 'Incoming',
        date: calculateDateInfo(callDetails),
        handlePress: () => {},
        image: item.image,
      };
    });
    // attendedPeople.unshift({
    //   _id: callDetails.initiator,
    //   title:
    //     callDetails.initiatorDetails?.name ?? callDetails.initiatorDetails?.username ?? 'Unknown',
    //   callStatus: 'Outgoing',
    //   date: calculateDateInfo(callDetails),
    //   handlePress: () => {},
    //   isInitiator: true,
    //   image: callDetails.initiatorDetails?.image,
    // });
    setAttendedPeople(attendedPeople);

    const missedPeople: GroupCallItemProps[] = invitedUserIdsDetails
      .filter(
        (invitedUser) =>
          !attendedPeople.find((attendedUser) => attendedUser._id === invitedUser._id),
      )
      .map((item) => {
        return {
          _id: item._id,
          title: item.name,
          callStatus: 'Missed',
          date: calculateDateInfo(callDetails),
          handlePress: () => {},
          image: item.image,
        };
      });
    setMissedPeople(missedPeople);
  }, []);

  console.log('attendedPeople', attendedPeople);

  return (
    <CommonView headerTitle={t('Group Call Details')} containerStyle={styles.container}>
      <View style={{}}>
        <Text
          style={{
            color: colors.gray_80,
            fontSize: 16,
            fontWeight: '600',
            marginVertical: 10,
          }}
        >
          Attended People
        </Text>

        <FlatList
          showsVerticalScrollIndicator={false}
          data={attendedPeople}
          renderItem={({ item }) => (
            <GroupCallItem
              _id={item._id}
              title={item.title}
              callStatus={item.callStatus}
              date={item.date}
              handlePress={item.handlePress}
              isInitiator={item.isInitiator}
              image={item.image}
            />
          )}
          keyExtractor={(item, index) => item?._id}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
          style={{}}
          onEndReachedThreshold={0.5}
        />
        <Text
          style={{
            color: colors.gray_80,
            fontSize: 16,
            fontWeight: '600',
            marginVertical: 10,
          }}
        >
          Missed People
        </Text>

        <FlatList
          showsVerticalScrollIndicator={false}
          data={missedPeople}
          renderItem={({ item }) => (
            <GroupCallItem
              _id={item._id}
              title={item.title}
              callStatus={item.callStatus}
              date={item.date}
              handlePress={item.handlePress}
              image={item.image}
            />
          )}
          keyExtractor={(item, index) => item?._id}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
          style={{ marginBottom: 50 }}
          onEndReachedThreshold={0.5}
        />
      </View>
    </CommonView>
  );
};

export default GroupCallScreen;

type GroupCallItemProps = {
  _id: string;
  title: string;
  callStatus: string;
  date: string;
  handlePress: () => void;
  isInitiator?: boolean;
  image?: string;
};
function GroupCallItem({
  title,
  callStatus,
  date,
  handlePress,
  isInitiator,
  image,
}: GroupCallItemProps) {
  return (
    <View style={styles.itemContainer}>
      <View
        style={{
          paddingRight: 10,
        }}
      >
        <RenderUserIcon size={50} url={image} />
      </View>

      <TouchableOpacity style={{ ...styles.textContainer }} onPress={handlePress}>
        <Text style={styles.name}>{title}</Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <RenderIcon callStatus={callStatus} />

          <Text style={styles.details}>{date}</Text>
        </View>
      </TouchableOpacity>

      {isInitiator && (
        <View
          style={{
            paddingHorizontal: 8,
            paddingVertical: 3,
            marginLeft: 15,
            borderRadius: 20,
            backgroundColor: colors.gray_cc,
          }}
        >
          <Text style={{ color: colors.gray_80, fontSize: 12, fontWeight: 'bold' }}>Caller</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {},
  valueText: {
    ...commonFontStyle(600, 18, colors.black_23),
  },
  header: {
    ...commonFontStyle(600, 18, colors.black_23),
    fontWeight: 700,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.black,
  },
  details: {
    ...commonFontStyle(400, 12, colors.gray_80),
    marginLeft: 5,
  },

  newCallStyle: {
    position: 'absolute',
    bottom: 120,
    right: 16,
    borderRadius: 100,
    elevation: 18,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  newCallStyle1: {
    width: 60,
    height: 60,
  },
});

// function parseTitle(item: CallHistoryItemNormalized, user: IUser | null) {
//   const isInitiator = item.initiator === user?._id.toString();
//   const invitedUsers = item.invitedUserIdsDetails ?? [];
//   const isGroupCall = invitedUsers.length > 1;
//   let title = '';

//   if (item.originType === 'groupConversation') {
//     const groupConversation: { conversation: ConversationSchema } | null = safeJsonParse(
//       item.originJson,
//     );
//     if (groupConversation) {
//       title = groupConversation.conversation.displayName;
//     } else {
//       title = 'Unknown group';
//     }

//     return title;
//   }

//   if (isGroupCall) {
//     if (isInitiator) {
//       if (invitedUsers.length === 2) {
//         title = `${invitedUsers[0].name} and ${invitedUsers[1].name}`;
//       } else if (invitedUsers.length === 3) {
//         title = `${invitedUsers[0].name}, ${invitedUsers[1].name} and ${1} other`;
//       } else {
//         title = `${invitedUsers[0].name} , ${invitedUsers[1].name} and ${
//           invitedUsers.length - 2
//         } others`;
//       }
//     } else {
//       const invitedUsersWithoutMe = invitedUsers.filter(
//         (item) => item._id !== user?._id.toString(),
//       );
//       if (invitedUsersWithoutMe.length === 1) {
//         title = `${item.initiatorDetails?.name} and ${invitedUsersWithoutMe[0].name}`;
//       } else if (invitedUsersWithoutMe.length === 2) {
//         title = `${item.initiatorDetails?.name}, ${invitedUsersWithoutMe[0].name} and ${1} other`;
//       } else {
//         title = `${item.initiatorDetails?.name} , ${invitedUsersWithoutMe[0].name} and ${
//           invitedUsersWithoutMe.length - 1
//         } others`;
//       }
//     }
//   } else {
//     if (isInitiator) {
//       title = invitedUsers[0]?.name ?? invitedUsers[0]?.username;
//     } else {
//       title =
//         item.initiatorContact?.name?.trim() ?? item.initiatorDetails?.name?.trim() ?? 'unknown';
//     }
//   }

//   return title;
// }

// function parseStatus(item: CallHistoryItemNormalized) {
//   if (item.callStatus === 'outgoing') {
//     return 'Outgoing';
//   } else if (
//     item.callStatus === 'missed' ||
//     item.callStatus === 'rejected' ||
//     item.callStatus === 'ignored'
//   ) {
//     return 'Missed';
//   } else {
//     return 'Incoming';
//   }
// }

// function calculateDateInfo(item: CallHistoryItemNormalized) {
//   const dateObj = new Date(item.createdAt);

//   if (!isNaN(dateObj.getTime())) {
//     const options: Intl.DateTimeFormatOptions = {
//       hour: 'numeric',
//       minute: 'numeric',
//       hour12: true,
//     };

//     const time = dateObj.toLocaleString('en-US', options);
//     const month = dateObj.toLocaleString('en-US', { month: 'short' });
//     const day = dateObj.getDate();

//     return `${time} on ${month} ${day}`;
//   }

//   return 'Invalid date';
// }

function RenderIcon({ callStatus }: { callStatus: string }) {
  return (
    <>
      {callStatus === 'Incoming' && (
        <FeatherIcons name="arrow-down-left" size={14} color={'green'} />
      )}
      {callStatus === 'Missed' && <FeatherIcons name="arrow-down-left" size={14} color={'red'} />}
      {callStatus === 'Outgoing' && (
        <FeatherIcons name="arrow-up-right" size={14} color={'green'} />
      )}
    </>
  );
}
