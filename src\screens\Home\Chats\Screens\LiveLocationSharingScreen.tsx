import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  AppState,
  AppStateStatus,
  Platform,
  Alert,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import CommonView from '../../../../component/CommonView';
import ThreeDotsSVG from '../../../../assets/svgIcons/ThreeDotsSVG';
import { dayPipe, getCurrentLocation, navigateTo } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { commonFontStyle, hexToRgba } from '../../../../theme/fonts';
import { Menu } from 'react-native-material-menu';
import MapView, { MapType, Marker } from 'react-native-maps';
import ButtonPurple from '../../../../component/ButtonPurple';
import SelecteUnselectSVG from '../../../../assets/svgIcons/SelecteUnselectSVG';
import { NavigationRouteParams } from '../../../../navigation/navigationParams';
import { useMe } from '../../../../hooks/util/useMe';
import Geolocation from '@react-native-community/geolocation';
import { colors } from '../../../../theme/colors';
import useSocket from '../../../../socket-client/useSocket';
import { notifEvents } from '../../../../socket-client/notificationTypes';
import ModalWrapper from '../../../../component/ModalWrapper';
import { ChatService } from '../../../../service/ChatService';
import { ChatSocket } from '../../../../socket-client/ChatSocket';

interface IProps {
  data?: any;
}

interface MapCoords {
  latitude: number;
  longitude: number;
  latitudeDelta?: number;
  longitudeDelta?: number;
}

interface LocationData {
  latitude: number;
  longitude: number;
  globalId: string;
  receiverId: string;
  senderId: string;
  timestamp: number;
}

const LiveLocationSharingScreen = ({ data }: IProps) => {
  const route = useRoute<RouteProp<NavigationRouteParams, 'LiveLocation'>>();
  const navigation = useNavigation();
  const userData: any = route?.params?.userData;
  const mapData: any = route?.params?.mapData;
  const { user: me } = useMe();
  const { socket } = useSocket();

  const isMyData = mapData?.senderId === me?._id;
  const [mapType, setMapType] = useState<MapType>('standard');
  const [menuVisible, setMenuVisible] = useState<boolean>(false);
  const [showTraffic, setShowTraffic] = useState<boolean>(false);
  const [stopSharingModal, setStopSharingModal] = useState<boolean>(false);
  const [isSharing, setIsSharing] = useState<boolean>(isMyData);
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now());
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isReceiving, setIsReceiving] = useState<boolean>(!isMyData);
  const [isStopped, setIsStopped] = useState<boolean>(mapData?.isStopped || false);

  const [userCoords, setUserCoords] = useState<MapCoords>({
    latitude: mapData?.latitude || 17.4026282,
    longitude: mapData?.longitude || 78.5641064,
    latitudeDelta: 0.004,
    longitudeDelta: 0.004,
  });
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);

  const watchIdRef = useRef<number | null>(null);
  const mapViewRef = useRef<MapView>(null);
  const countdownRef = useRef<string>(getRemainingTimeString(mapData?.expiresAt));
  console.log("countdownRef", countdownRef.current)
  // 🔄 Force re-render without using state

  // Interval ref
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const appStateRef = useRef<string>(AppState.currentState);
  const socketListenerRef = useRef<((data: LocationData) => void) | null>(null);

  // console.log('---------------------000000000000000----------------------', userData);
  console.log(
    '---------------------000000000000000----------------------',
    JSON.stringify(mapData, null, 2),
  );
  console.log('isMyData----------------------', isMyData);

  const getLiveLocationAndUpdata = () => {
    if (!mapData?.location?.isStopped) {
      if (isMyData) {
        const watchId = Geolocation.watchPosition(
          (position) => {
            console.log('Position updated:', position, {
              receiverId: userData?.id,
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
            setUserCoords((prev) => ({
              ...prev,
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            }));
            socket.emit(notifEvents.messages.liveLocation, {
              receiverId: userData?.id,
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
            ChatService.updateLocationStatus(
              mapData?.globalId,
              false,
              position.coords.latitude,
              position.coords.longitude,
            );
            setLastUpdateTime(Date.now());
          },
          (error) => console.log(error),
          { enableHighAccuracy: true, distanceFilter: 1, interval: 1000, fastestInterval: 2000 },
        );
        watchIdRef.current = watchId;
      } else {
        const listener = (data: LocationData) => {
          console.log(
            'datalistener==========================]',
            JSON.stringify(data, null, 2),
            userData?.id,
          );
          if (data?.senderId === userData?._id) {
            // make sure update is from correct sender
            console.log('listenerStateUpdated--------------------------------', data);
            setUserCoords((prev) => ({
              ...prev,
              latitude: data.latitude,
              longitude: data.longitude,
            }));
            setLastUpdateTime(Date.now());
            ChatService.updateLocationStatus(mapData?.globalId, false, data.latitude, data.longitude);
          }
        };
        socket.on(notifEvents.messages.liveLocation, listener);
        socketListenerRef.current = listener;
      }
    }
  }



  useEffect(() => {
    if (mapData?.expiresAt) {
      intervalRef.current = setInterval(() => {
        countdownRef.current = getRemainingTimeString(mapData?.expiresAt);

        // force UI refresh
        forceUpdate();

        if (countdownRef.current === "0m" || countdownRef.current.startsWith("0")) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          if (isMyData) {
            onStopSharing()
          }
        }
      }, 1000);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [mapData?.expiresAt]);



  useEffect(() => {
    getLiveLocationAndUpdata();

    return () => {
      if (watchIdRef.current !== null) {
        Geolocation.clearWatch(watchIdRef.current);
      }
      if (socketListenerRef.current) {
        socket.off(notifEvents.messages.liveLocation, socketListenerRef.current);
      }
    };
  }, []);

  function getRemainingTimeString(futureTime: number | Date): string {
    const now = Date.now();
    const future = futureTime instanceof Date ? futureTime.getTime() : futureTime;

    let diff = future - now;

    if (diff <= 0) {
      return '0m';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  const onStopSharing = async () => {
    setStopSharingModal(false);
    const currentLocationData = await getCurrentLocation();
    console.log('currentLocationData-stopsharing------------------------', currentLocationData);

    console.log('lkajsdklfjaksjdfjaskdjfjadlkfj');
    try {
      ChatSocket.sendLiveLocationStopped(socket!, {
        globalId: mapData?.globalId,
        receiverId: mapData?.receiverId,
        latitude: currentLocationData.latitude,
        longitude: currentLocationData.longitude,
      });
      ChatService.updateLocationStatus(
        mapData?.globalId as string,
        true,
        currentLocationData.latitude,
        currentLocationData.longitude,
      );
      console.log('------------');
      setIsStopped(true)
    } catch (error) {
      Alert.alert('Error', 'Failed to stop live location sharing.');
    }
  };

  return (
    <CommonView
      customHeader={() => {
        return (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 21,
              paddingTop: 9,
              paddingBottom: 18,
            }}
          >
            <View style={{ flex: 1, alignItems: 'center', flexDirection: 'row' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <BackArrowSVG
                  color={colors.white}
                  style={{ marginRight: 17 }}
                  onPress={() => {
                    navigation.goBack();
                  }}
                />
                <Image
                  source={
                    userData?.displayPic
                      ? { uri: userData?.displayPic }
                      : require('../../../../assets/Image/beautiful.png')
                  }
                  style={styles.userImage}
                />
                {!userData?.isActive && (
                  <View style={styles.online}>
                    <View style={styles.dot} />
                  </View>
                )}
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  onPress={() => {
                    navigateTo(SCREENS.PersonalProfileScreen, {
                      userData,
                    });
                  }}
                >
                  <View>
                    <Text style={[styles.text, { color: colors.white }]}>
                      {userData?.displayName?.length > 13
                        ? userData?.displayName?.substring(0, 12) + '...'
                        : userData?.displayName}
                    </Text>
                    <Text
                      style={{ color: colors.white, fontSize: 12, fontWeight: '400' }}
                      numberOfLines={1}
                    >
                      {!userData?.isActive ? 'Online' : `Last seen ${dayPipe(1747654594812)}`}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            <Menu
              style={styles.menuContainer}
              visible={menuVisible}
              animationDuration={100}
              anchor={
                <TouchableOpacity
                  onPress={() => {
                    setMenuVisible(true);
                  }}
                  hitSlop={{ top: 10, bottom: 10, right: 10, left: 10 }}
                >
                  <ThreeDotsSVG color={colors.white} size={19} />
                </TouchableOpacity>
              }
              onRequestClose={() => {
                setMenuVisible(false);
              }}
            >
              <View style={[styles.menuContent, { width: 150 }]}>
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMapType('standard');
                    setMenuVisible(false);
                  }}
                >
                  <Text style={styles.menuItemText}>Map view</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMapType('satellite');
                    setMenuVisible(false);
                  }}
                >
                  <Text style={styles.menuItemText}>Satellite view</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMapType('terrain');
                    setMenuVisible(false);
                  }}
                >
                  <Text style={styles.menuItemText}>Terrain view</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMenuVisible(false);
                    setShowTraffic(!showTraffic);
                  }}
                >
                  <View
                    style={[
                      styles.trafficOption,
                      { width: '100%', justifyContent: 'space-between' },
                    ]}
                  >
                    <Text style={[styles.menuItemText, {}]}>Show traffic</Text>
                    <SelecteUnselectSVG isSelected={showTraffic} size={14} />
                  </View>
                </TouchableOpacity>
              </View>
            </Menu>
          </View>
        );
      }}
      containerStyle={{ paddingBottom: 0 }}
    >
      {locationError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{locationError}</Text>
        </View>
      )}

      <View style={{ flex: 1, borderRadius: 15, overflow: 'hidden' }}>
        <MapView
          ref={mapViewRef}
          style={{ flex: 1 }}
          mapType={mapType}
          initialRegion={userCoords}
          showsTraffic={showTraffic}
          showsUserLocation={isSharing}
          showsMyLocationButton={isSharing}
        >
          {!isSharing && (
            <Marker
              coordinate={userCoords}
              image={require('../../../../assets/locationPin.png')}
              title={userData?.displayName}
            />
          )}
        </MapView>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 15,
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          <Image
            source={
              userData?.displayPic
                ? { uri: userData?.displayPic }
                : require('../../../../assets/Image/beautiful.png')
            }
            style={styles.userImage}
          />

          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
            <TouchableOpacity
              onPress={() => {
                navigateTo(SCREENS.PersonalProfileScreen, {
                  userData,
                });
              }}
            >
              <View>
                <Text style={[styles.text]} numberOfLines={1}>
                  {isMyData ? 'You' : userData?.displayName}
                </Text>
                <Text
                  style={{ color: colors.gray_80, fontSize: 14, fontWeight: '400' }}
                  numberOfLines={1}
                >
                  {countdownRef.current.startsWith("0")
                    ? "Location ended"
                    : countdownRef.current + " left"}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {isMyData && !isStopped ? (
          <ButtonPurple
            title="Stop sharing"
            textStyle={{ fontSize: 16, fontWeight: '600', color: colors._FF0000_red }}
            extraStyle={{
              backgroundColor: hexToRgba(colors._FF0000_red, 0.2),
              height: 43,
              paddingHorizontal: 26,
              paddingVertical: 12,
            }}
            onPress={() => {
              setStopSharingModal(true);
            }}
          />
        ) : null}
      </View>

      <ModalWrapper isVisible={stopSharingModal} onCloseModal={() => setStopSharingModal(false)}>
        <View style={{ width: '100%' }}>
          <Text
            style={{ fontSize: 16, fontWeight: '500', color: colors.black_23, marginBottom: 16 }}
          >
            Stop Sharing Location
          </Text>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: 14,
            }}
          >
            {/* todo: enable live location  */}
            <ButtonPurple
              extraStyle={{ flex: 1, height: 50, backgroundColor: colors.gray_f3 }}
              textStyle={{ fontSize: 16, fontWeight: 'semibold' }}
              withRightIcon={false}
              titleColor={colors.black_23}
              onPress={() => {
                setStopSharingModal(false);
              }}
              title={'Cancel'}
            />
            <ButtonPurple
              extraStyle={{ flex: 1, height: 50, backgroundColor: colors._F11010_red }}
              textStyle={{ fontSize: 16, fontWeight: 'semibold' }}
              withRightIcon={false}
              titleColor={colors.white}
              onPress={() => { onStopSharing(); navigation.goBack() }}
              title={'Stop'}
            />
          </View>
        </View>
      </ModalWrapper>
    </CommonView>
  );
};

const styles = StyleSheet.create({
  online: {
    position: 'absolute',
    width: 14,
    height: 14,
    backgroundColor: colors.mainPurple,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 7,
    right: 11,
    bottom: 5,
  },
  dot: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: colors._33C200_green,
  },
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  text: {
    color: colors.black_23,
    fontSize: 16,
    fontWeight: '400',
  },
  menuContent: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
  },
  menuItem: {
    paddingVertical: 5,
  },
  menuItemText: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  menuContainer: {
    marginTop: 5,
    borderRadius: 8,
  },
  trafficOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginRight: 15,
  },
  errorContainer: {
    backgroundColor: hexToRgba(colors._FF0000_red, 0.1),
    padding: 10,
    borderRadius: 5,
    margin: 10,
  },
  errorText: {
    color: colors._FF0000_red,
    textAlign: 'center',
  },
});

export default LiveLocationSharingScreen;
