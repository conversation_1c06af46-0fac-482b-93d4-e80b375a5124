import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet, FlatList } from 'react-native';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { colors } from '../../../theme/colors';
import { hp } from '../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import FavouriteItem from '../../../component/FavouriteItem';
import { IContact } from '../../../device-storage/realm/schemas/ContactSchema';
import { useContacts } from '../../../hooks/contacts/useContacts';
import { useFilteredContacts } from '../../../hooks/common/useFilterContacts';
import SearchInput from '../../../component/SearchInput';
import { useMe } from '../../../hooks/util/useMe';
import { errorToast, generateUUID } from '../../../utils/commonFunction';
import { UserSchema } from '../../../device-storage/realm/schemas/UserSchema';

const FavouriteScreen = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();

  const { registeredContacts, unregisteredContacts } = useContacts();
  const { favoriteContact, getContactId, isContactFavorited, currentFavorites } =
    useFavoriteContacts();
  const [search, setSearch] = useState('');

  const { filteredRegistered } = useFilteredContacts(
    registeredContacts.map((item) => item),
    [],
    search,
  );

  const favouritesOnly = React.useMemo(
    () => (filteredRegistered || []).filter((c: any) => currentFavorites.includes(c?.id)),
    [filteredRegistered, currentFavorites],
  );

  const renderFavouriteItem = ({ item }: { item: UserSchema }) => {
    const isFavourite = isContactFavorited(item);

    return (
      <FavouriteItem data={item} onPressFavourite={favoriteContact} isFavourite={isFavourite} />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Favourites" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.searchView}>
          <SearchInput
            value={search}
            onChangeText={(text) => {
              setSearch(text);
            }}
          />
        </View>
        <View style={styles.sectionContainer}>
          <FlatList
            data={favouritesOnly}
            renderItem={renderFavouriteItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  searchView: {
    paddingHorizontal: hp(2),
    marginBottom: hp(1),
  },
  sectionContainer: {
    flex: 1,
    paddingHorizontal: hp(1),
  },
  listContainer: {
    paddingBottom: hp(2),
  },
});

export default FavouriteScreen;

export const useFavoriteContacts = () => {
  const { userPreferencesState } = useMe();

  const currentFavorites = userPreferencesState.userPreferences?.favorites?.contacts || [];
  const [isUpdating, setIsUpdating] = React.useState(false);

  const getContactId = (contact: Partial<UserSchema>): string => {
    return contact.id!;
  };

  const isContactFavorited = (contact: Partial<UserSchema>): boolean => {
    const contactId = getContactId(contact);
    return currentFavorites.includes(contactId);
  };

  const favoriteContact = async (contact: Partial<UserSchema>) => {
    if (isUpdating) return; // prevent concurrent toggles
    setIsUpdating(true);
    const contactId = getContactId(contact as UserSchema);

    const updatedFavorites = currentFavorites.includes(contactId)
      ? currentFavorites.filter((id) => id !== contactId)
      : [...currentFavorites, contactId];

    try {
      await userPreferencesState.updatePreferences('favorites', {
        contacts: updatedFavorites,
      });
    } catch (error) {
      errorToast('Failed to update favourites');
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    currentFavorites,
    getContactId,
    isContactFavorited,
    favoriteContact,
    isUpdating,
  };
};
