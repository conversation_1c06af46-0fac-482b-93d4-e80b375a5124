import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"


const UnreadSVG = ({
    size = 24,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={(size * 18) / 22} // Maintain original aspect ratio
            viewBox="0 0 22 18"
            fill="none"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.182 3.545a3.545 3.545 0 117.09 0 3.545 3.545 0 01-7.09 0zm3.545-1.909a1.91 1.91 0 100 3.819 1.91 1.91 0 000-3.819z"
                fill={color}
            />
            <Path
                d="M8.41 1.09h3.317a.818.818 0 010 1.637H8.455c-1.55 0-2.65.001-3.503.088-.838.085-1.346.247-1.74.51-.387.258-.72.59-.978.978-.263.393-.425.901-.51 1.74-.087.853-.088 1.953-.088 3.502 0 1.55.001 2.65.088 3.502.085.84.247 1.347.51 1.74.259.388.59.72.978.98.394.262.901.424 1.74.509.853.087 1.953.087 3.503.087h4.363c1.55 0 2.65 0 3.502-.087.84-.085 1.347-.247 1.74-.51.388-.259.72-.591.979-.978.263-.394.424-.902.51-1.74.086-.853.087-1.953.087-3.503V9a.818.818 0 111.637 0v.59c0 1.494 0 2.678-.096 3.623-.099.968-.305 1.777-.777 2.484a5.186 5.186 0 01-1.43 1.43c-.707.472-1.516.678-2.484.777-.945.096-2.129.096-3.622.096H8.409c-1.494 0-2.677 0-3.622-.096-.968-.099-1.777-.305-2.484-.777a5.184 5.184 0 01-1.43-1.43c-.472-.707-.678-1.516-.777-2.484C0 12.268 0 11.084 0 9.591V9.5c0-1.494 0-2.677.096-3.622.099-.969.305-1.777.777-2.484a5.182 5.182 0 011.43-1.43c.707-.472 1.516-.679 2.484-.777.945-.096 2.128-.096 3.622-.096z"
                fill={color}
            />
        </Svg>
    )
}

export default UnreadSVG
