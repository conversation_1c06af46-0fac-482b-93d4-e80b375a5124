package com.chatbucket.filters

import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.bridge.ReadableMap

class FilteredVideoViewManager : SimpleViewManager<FilteredVideoView>() {
    override fun getName() = "FilteredVideoView"

    override fun createViewInstance(reactContext: ThemedReactContext): FilteredVideoView {
        return FilteredVideoView(reactContext)
    }

    @ReactProp(name = "source")
    fun setSource(view: FilteredVideoView, uri: String) {
        view.setVideoPath(uri)
    }

    @ReactProp(name = "filter")
    fun setFilter(view: FilteredVideoView, filter: String) {
        view.setFilter(filter)
    }

    @ReactProp(name = "isSoundOn", defaultBoolean = true)
    fun setSoundOn(view: FilteredVideoView, isSoundOn: Boolean) {
        view.setSoundOn(isSoundOn)
    }

    @ReactProp(name = "resizeMode")
    fun setResizeMode(view: FilteredVideoView, resizeMode: String?) {
        view.setResizeMode(resizeMode ?: "contain")
    }

    @ReactProp(name = "audioSource")
    fun setAudioSource(view: FilteredVideoView, uri: String?) {
        view.setAudioPath(uri)
    }

    @ReactProp(name = "isPause")
    fun setPause(view: FilteredVideoView, isPause: Boolean?) {
        println("@=> setPause method called: $isPause")
        if (isPause == true) {
            view.onPause() // Call the overridden onPause method
        } else {
            view.onResume() // Call the overridden onResume method
        }
    }

    @ReactProp(name = "seekTo")
    fun setSeekTo(view: FilteredVideoView, positionMs: Int) {
        if (positionMs >= 0) {
            view.seekTo(positionMs)
        }
    }

    @ReactProp(name = "restart")
    fun setRestart(view: FilteredVideoView, restart: Boolean) {
        if (restart) {
            view.restart()
        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): Map<String, Any>? {
        return mapOf(
            "onProgress" to mapOf("registrationName" to "onProgress")
        )
    }
}