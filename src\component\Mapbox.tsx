import React, { useState, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
});

const MapBox = () => {
  const [showMarkers, setShowMarkers] = useState(false);

  // Force show markers after a delay as fallback
  useEffect(() => {
    const timer = setTimeout(() => {
      console.log('⏰ Timeout - showing markers anyway');
      setShowMarkers(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <MapView
      provider={PROVIDER_GOOGLE}
      style={styles.map}
      initialRegion={{
        latitude: 17.385,
        longitude: 78.4867,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      }}
      onMapReady={() => {
        console.log('✅ Map is ready');
        setShowMarkers(true);
      }}
      onMapLoaded={() => {
        console.log('✅ Map is loaded');
        setShowMarkers(true);
      }}
      onLayout={(event) => {
        console.log('📐 Map layout:', event.nativeEvent.layout);
      }}
      onRegionChange={(region) => {
        console.log('🗺️ Region changed:', region);
      }}
      onRegionChangeComplete={(region) => {
        console.log('🎯 Region change complete:', region);
        setShowMarkers(true);
      }}
    >
      <Marker
        coordinate={{ latitude: 17.385, longitude: 78.4867 }}
        title="Current Location"
        description="You're here"
        identifier="marker1"
        onPress={() => console.log('Marker 1 pressed')}
      />
      <Marker
        coordinate={{ latitude: 17.402954, longitude: 78.564089 }}
        title="Other Marker"
        description="Another location"
        pinColor="red"
        identifier="marker2"
        onPress={() => console.log('Marker 2 pressed')}
        draggable
      />
    </MapView>
  );
};

const MapScreen = () => {
  console.log('MapScreen render');

  return (
    <View style={styles.container}>
      <MapBox />
    </View>
  );
};

export default MapScreen;
