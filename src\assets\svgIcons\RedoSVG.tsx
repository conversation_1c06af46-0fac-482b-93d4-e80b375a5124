import React from 'react';
import { Svg, Path } from 'react-native-svg';

interface RedoSVGProps {
  size?: number;
  color?: string;
}

const RedoSVG: React.FC<RedoSVGProps> = ({ size = 16, color = '#232323' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 16 16" fill="none">
      <Path
        d="M8.00098 0.170898C11.4615 0.172557 14.5103 2.44579 15.5 5.76172C16.7365 9.9054 14.38 14.2672 10.2363 15.5039C6.14697 16.7243 1.84771 14.4442 0.546875 10.4014L1.24316 10.1953C2.19688 13.1202 4.91026 15.1223 7.99902 15.1582H8.00195C11.9433 15.1313 15.1313 11.9433 15.1582 8.00195V8C15.1314 4.05862 11.9433 0.869632 8.00195 0.842773H7.99805C5.34607 0.878013 2.92841 2.3684 1.70508 4.72168L1.5752 4.9707H4.09668V5.69629H0.170898V1.77051H0.896484V4.42188L1.2373 4.43555C1.25896 4.17793 1.33362 3.92727 1.45898 3.70117C2.86717 1.57428 5.21535 0.265317 7.75488 0.175781L8.00098 0.170898Z"
        fill={color}
        stroke={color}
        strokeWidth="0.341343"
      />
    </Svg>
  );
};

export default RedoSVG; 