import React, { useState, useRef } from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  Text,
  Image,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import HeaderBackWithTitle from '../../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { CButton } from '../../../../../common';
import { IMAGES } from '../../../../../assets/Images';
import { SCREENS } from '../../../../../navigation/screenNames';
import Clipboard from '@react-native-clipboard/clipboard';
import PinInputField from '../../../../../component/Common/PinInputField';
import { CommonActions } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';

const Enable2FAScreen = () => {
  const navigation = useNavigation();
  const [verificationCode, setVerificationCode] = useState<string[]>(['', '', '', '', '', '']);
  const [secret, setSecret] = useState('ABCDEF123456....');
  const [isCodeValid, setIsCodeValid] = useState(false);
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=Example';

  const handleCodeChange = (newCode: string[]) => {
    setVerificationCode(newCode);
    setIsCodeValid(newCode.every((digit) => digit !== ''));
  };

  const handleSubmit = () => {
    navigation.goBack();
  };

  const copyToClipboard = () => {
    Clipboard.setString(secret);
    setCopiedToClipboard(true);
    setTimeout(() => {
      setCopiedToClipboard(false);
    }, 2000);
  };

  const handleInputFocus = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Enable 2FA" onBack={() => navigation.goBack()} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.whiteContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Text style={styles.title}>Scan this QR-code</Text>
          <Text style={styles.subtitle}>Using Google Authenticator app</Text>

          <View style={styles.qrContainer}>
            <Image source={{ uri: qrCodeUrl }} style={styles.qrCode} resizeMode="contain" />
          </View>

          <Text style={styles.orText}>--------- Or ---------</Text>

          <Text style={styles.secretTitle}>Paste this code in app</Text>
          <TouchableOpacity onPress={copyToClipboard} style={styles.secretContainer}>
            <View style={styles.secretRow}>
              <Text style={styles.secretCode}>{secret}</Text>
              <TouchableOpacity onPress={copyToClipboard} style={styles.copyIconContainer}>
                <Ionicons name={'copy-outline'} size={24} color={colors.black} />
                <Image source={IMAGES.copyCode} style={styles.copyIcon} resizeMode="contain" />
              </TouchableOpacity>
            </View>
            {copiedToClipboard && <Text style={styles.copiedText}>Copied to clipboard</Text>}
          </TouchableOpacity>

          <Text style={styles.verifyTitle}>Enter verification code</Text>

          <View style={styles.codeInputContainer}>
            <PinInputField
              length={6}
              value={verificationCode}
              onChange={handleCodeChange}
              containerStyle={styles.pinContainer}
              inputStyle={styles.codeInput}
              focusedStyle={styles.codeInputFocused}
            />
          </View>

          <CButton
            onPress={handleSubmit}
            cStyle={[styles.button, !isCodeValid && styles.buttonDisabled]}
            clickable={!isCodeValid}
          >
            <Text style={styles.buttonText}>Submit</Text>
          </CButton>

          <View style={styles.keyboardSpacer} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  keyboardView: {
    flex: 1,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingTop: hp(2),
    paddingHorizontal: hp(2),
    marginTop: 8,
    marginBottom: hp(2),
  },
  scrollContent: {
    paddingTop: hp(4),
    flexGrow: 1,
  },
  title: {
    ...commonFontStyle(600, 18, colors.black_23),
    textAlign: 'center',
    marginTop: hp(2),
  },
  subtitle: {
    ...commonFontStyle(400, 14, colors.gray_86),
    textAlign: 'center',
    marginTop: hp(0.5),
    marginBottom: hp(3),
  },
  qrContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: hp(3),
  },
  qrCode: {
    width: 200,
    height: 200,
    borderRadius: 8,
  },
  orText: {
    ...commonFontStyle(500, 16, colors.black_23),
    textAlign: 'center',
    marginBottom: hp(2),
  },
  secretContainer: {
    alignSelf: 'center',
    marginBottom: hp(4),
    backgroundColor: colors.gray_f3,
    padding: hp(2),
    width: '70%',
    borderRadius: 12,
  },
  secretRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  copyIconContainer: {
    marginRight: 10,
    padding: 5,
  },
  copyIcon: {
    width: 20,
    height: 20,
    tintColor: colors.mainPurple,
    left: 20,
  },
  secretTitle: {
    ...commonFontStyle(500, 15, colors.gray_86),
    textAlign: 'center',
    marginBottom: hp(2),
  },
  secretCode: {
    ...commonFontStyle(600, 18, colors.mainPurple),
    letterSpacing: 1,
  },
  copiedText: {
    ...commonFontStyle(400, 12, colors.mainPurple),
    textAlign: 'center',
    marginTop: hp(1),
  },
  verifyTitle: {
    ...commonFontStyle(600, 16, colors.black_23),
    textAlign: 'center',
    marginBottom: hp(2),
  },
  codeInputContainer: {
    marginBottom: hp(2),
    alignItems: 'center',
  },
  pinContainer: {
    justifyContent: 'center',
  },
  codeInput: {
    width: 45,
    height: 50,
    borderRadius: 8,
    borderWidth: 1.5,
    borderColor: colors.gray_f3,
    backgroundColor: colors.white,
    fontSize: 20,
    color: colors.black_23,
    marginHorizontal: 5,
  },
  codeInputFocused: {
    borderColor: colors.mainPurple,
  },
  button: {
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    width: '100%',
  },
  buttonDisabled: {
    backgroundColor: colors._DADADA_gray,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  keyboardSpacer: {
    height: Platform.OS === 'ios' ? hp(10) : hp(5),
  },
});

export default Enable2FAScreen;
