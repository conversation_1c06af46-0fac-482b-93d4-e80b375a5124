import React, { useEffect, useState } from 'react';
import ModalWrapper from '../../../../component/ModalWrapper';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { GroupCallInfo } from '../Components/CallChatItem';

interface JoinGroupCallModalProps {
  isVisible: boolean;
  onCloseModal: () => void;
  selectedMessage: IMessage | null;
}

function JoinGroupCallModal({ isVisible, onCloseModal, selectedMessage }: JoinGroupCallModalProps) {
  const [loading, setLoading] = useState(true);
  const [callData, setCallData] = useState<GroupCallInfo | null>(null);
  useEffect(() => {
    if (isVisible && selectedMessage) {
      console.log(selectedMessage);
      if (
        selectedMessage.messageType === MessageType.EVENT &&
        selectedMessage.eventType === MessageEventType.GROUP_CALL
      ) {
        const callDataRawStr = selectedMessage.eventPayload;
        const parsedEventPayload = safeJsonParse<string>(callDataRawStr);
        const callData = parsedEventPayload
          ? safeJsonParse<GroupCallInfo>(parsedEventPayload)
          : null;
        setCallData(callData);
        if (callData) {
          // getCallDetails
        }
        // setLoading(false);
      }
    }
  }, [isVisible, selectedMessage]);

  const onJoinCall = () => {};
  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
      {loading && (
        <View style={styles.container}>
          <ActivityIndicator size="large" color={colors.mainPurple} />
        </View>
      )}
      {!loading && (
        <View style={styles.container}>
          <TouchableOpacity onPress={onCloseModal} style={[styles.button, styles.closeButton]}>
            <Text style={[styles.buttonText, styles.closeButtonTextColor]}>Close</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={onJoinCall} style={[styles.button, styles.joinButton]}>
            <Text style={[styles.buttonText, styles.joinButtonTextColor]}>Join</Text>
          </TouchableOpacity>
        </View>
      )}
    </ModalWrapper>
  );
}

export default JoinGroupCallModal;

import { StyleSheet } from 'react-native';
import { colors } from '../../../../theme/colors';
import {
  IMessage,
  MessageEventType,
  MessageType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { EventType } from '@notifee/react-native';
import { safeJsonParse } from '../../../../common/utils';

export const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    padding: 10,
    borderRadius: 14,
    width: '48%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    backgroundColor: colors.gray_f3,
  },
  joinButton: {
    backgroundColor: colors.mainPurple,
  },
  buttonText: {
    fontWeight: '600',
    fontSize: 18,
  },
  closeButtonTextColor: {
    color: colors.black,
  },
  joinButtonTextColor: {
    color: colors.white,
  },
});
