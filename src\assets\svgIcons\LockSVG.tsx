import React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"
import { colors } from "../../theme/colors"


interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}


const LockSVG: React.FC<IconProps> = ({
    size = 16,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={(size * 15) / 20}
            height={size}
            viewBox="0 0 15 20"
            fill="none"
            {...restProps}
        >
            <Path
                d="M7.25 0a5.415 5.415 0 00-5.438 5.438v2.416A1.78 1.78 0 000 9.667v7.854a1.78 1.78 0 001.813 1.812h10.874a1.78 1.78 0 001.813-1.812V9.667a1.78 1.78 0 00-1.813-1.813V5.437A5.415 5.415 0 007.25 0zm6.042 9.667v7.854c0 .362-.242.604-.604.604H1.812c-.362 0-.604-.242-.604-.604V9.667c0-.363.242-.604.605-.604H12.686c.363 0 .605.241.605.604zM3.02 7.854V5.437A4.203 4.203 0 017.25 1.208a4.203 4.203 0 014.23 4.23v2.416H3.02z"
                fill={color}
            />
            <Path
                d="M7.25 10.874a1.78 1.78 0 00-1.813 1.813c0 .785.484 1.45 1.209 1.692v1.329c0 .362.242.604.604.604s.604-.242.604-.604v-1.33a1.76 1.76 0 001.208-1.691 1.78 1.78 0 00-1.812-1.813zm0 2.417c-.362 0-.604-.242-.604-.604s.242-.604.604-.604.604.242.604.604c0 .363-.242.604-.604.604z"
                fill={color}
            />
        </Svg>
    )
}

export default LockSVG
