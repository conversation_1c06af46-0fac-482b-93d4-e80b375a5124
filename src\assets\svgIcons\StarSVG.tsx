import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
  size?: number;
  color?: string;
}

const StarSVG: React.FC<SvgComponentProps> = ({
  size = 22,
  color = "#fff",
  ...props
}) => {

  return (
    <Svg
      width={size}
      height={(size * 20) / 22} // Maintain aspect ratio (original is 22x20)
      viewBox="0 0 22 20"
      fill="none"
      {...props}
    >
      <Path
        d="M21.31 7.553c-.161-.496-.644-.836-1.325-.935l-5.4-.781L12.17.967C11.866.351 11.39 0 10.867 0c-.523 0-.998.352-1.303.966L7.15 5.836l-5.4.782c-.68.099-1.164.44-1.325.935-.162.496.028 1.054.52 1.532l3.908 3.791-.923 5.354c-.121.704.085 1.125.28 1.354.226.268.558.416.932.416.283 0 .585-.083.896-.246l4.83-2.527 4.83 2.527c.312.163.613.246.896.246.375 0 .706-.148.933-.416.195-.23.4-.65.28-1.354l-.923-5.354 3.907-3.79c.493-.479.683-1.037.52-1.533zm-1.379.655l-4.139 4.016a.61.61 0 00-.177.542l.978 5.67a.764.764 0 010 .34c-.037-.001-.143-.011-.323-.106l-5.117-2.677a.617.617 0 00-.572 0L5.465 18.67c-.18.095-.286.105-.324.105a.763.763 0 01.001-.338l.977-5.671a.61.61 0 00-.177-.542L1.803 8.208a.766.766 0 01-.2-.274.772.772 0 01.324-.104l5.72-.827a.615.615 0 00.463-.335l2.558-5.16a.767.767 0 01.2-.274c.039.029.116.108.199.274l2.558 5.16c.09.18.263.306.463.335l5.72.827a.771.771 0 01.324.104.768.768 0 01-.2.274z"
        fill={color}
      />
    </Svg>
  );
};

export default StarSVG;
