import { Dispatch, SetStateAction } from 'react';

interface Image {
  id: number;
  startTime: number;
  trimEndTime: number;
  trimStartTime?: number;
  tempUri?: string;
}

export const updateTime = (
  setImages: Dispatch<SetStateAction<Image[]>>,
  setOverlapExtra: Dispatch<SetStateAction<Image>>,
  activeStoryIdx: number,
  trimStartTime: number,
  trimEndTime: number,
  tempUri: string,
) => {
  setImages((prevImages) =>
    prevImages.map((image) => {
      if (image.id == activeStoryIdx) {
        setOverlapExtra((prev) => ({
          ...prev,
          trimStartTime,
          trimEndTime,
          tempUri,
        }));
        return {
          ...image,
          trimStartTime,
          trimEndTime,
          tempUri,
        };
      }
      return image;
    }),
  );
};
