import React, { FC } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  ViewStyle,
  StyleProp,
  StyleSheetProperties,
  ImageStyle,
  StyleSheet,
} from 'react-native';
import { hp } from '../../utils/Constants/dimensionUtils';

interface HeaderComponentProps {
  activePaint: boolean;
  mainContainer?: ViewStyle;
  flash: string;
  bckBtnContiner?: StyleProp<ViewStyle>;
  bckBtn?: ImageStyle;
  captured: boolean;
  selectedMusicIndex?: boolean;
  toggelModal: (input: any) => void;
  bcSource: string;
  onSet: (input: any) => void;
  isBackCamera: boolean;
  // isAudioSelected: boolean;
  // onClickMusic: () => void;
}

const HeaderComponent: FC<HeaderComponentProps> = ({
  mainContainer,
  activePaint,
  bckBtnContiner,
  flash,
  selectedMusicIndex,
  toggelModal,
  captured = false,
  bcSource = null,
  onSet,
  isBackCamera = false,
  // isAudioSelected=false,
  // onClickMusic
}) => {
  return (
    <View style={[styles.headview, mainContainer]}>
      <TouchableOpacity style={[styles.iconbackground, bckBtnContiner]}>
        <Image
          resizeMode="contain"
          source={bcSource || require('../../Assets/Images/Backicon.png')}
          style={styles.backicon}
        />
      </TouchableOpacity>
      {/* {selectedMusicIndex !== null ? (
                <TouchableOpacity style={[styles.iconbackground, bckBtnContiner]} onPress={toggelModal}>
                    <Image
                        resizeMode="contain"
                        source={bcSource || require('../../Assets/Images/Music.png')}
                        style={styles.backicon}
                    />
                </TouchableOpacity>
            ) : null} */}
      {/* {isAudioSelected && <TouchableOpacity style={styles.musicLogo} onPress={onClickMusic}>
              <Image source={require('../../Assets/Images/Musicicon.png')} style={{ height: 21.28, width: 17 }} />
            </TouchableOpacity>} */}
      {isBackCamera && (
        <TouchableOpacity style={[styles.iconbackground, bckBtnContiner]} onPress={onSet}>
          <Image
            source={
              captured
                ? require('../../Assets/Images/Tickicon.png')
                : flash == 'on'
                ? require('../../Assets/Images/FlashActive.png')
                : require('../../Assets/Images/Flash.png')
            }
            style={captured ? { height: hp(5), width: hp(5) } : styles.backicon}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  headview: {
    marginTop: hp(7),
    marginHorizontal: hp(2.5),
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 100001,
  },
  iconbackground: {
    backgroundColor: '#0000004D',
    height: hp(5),
    width: hp(5),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40,
  },
  backicon: {
    height: hp(3),
    width: hp(3),
    alignSelf: 'center',
  },
  musicLogo: {
    backgroundColor: '#0000004D',
    height: hp(5.5),
    width: hp(5.5),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40,
  },
});

export default React.memo(HeaderComponent);
