import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image, ScrollView, Text } from 'react-native';
import { colors } from '../../../../../theme/colors';
import PlusSVG from '../../../../../assets/svgIcons/PlusSVG';
import { presetWallpapers } from '../../../../../assets/presetWallpapers';

type CustomWallpaper = {
  id: number;
  source: { uri: string };
  localImage?: any;
  remoteUrl?: string;
};

interface ChatImageTabProps {
  onImageSelect: (id: number) => void;
  selectedImageId?: number;
  selectedCustomId?: number;
  onAddCustomImage: () => void;
  onCustomImageSelect?: (customWallpaper: CustomWallpaper) => void;
  customWallpapers?: CustomWallpaper[];
}

const ChatImageTab: React.FC<ChatImageTabProps> = ({
  onImageSelect,
  selectedImageId,
  selectedCustomId,
  onAddCustomImage,
  onCustomImageSelect,
  customWallpapers = [],
}) => {
  const handleCustomWallpaperPress = (customWallpaper: CustomWallpaper) => {
    if (onCustomImageSelect) {
      onCustomImageSelect(customWallpaper);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imageRow}>
        {/* Plus icon card */}
        <TouchableOpacity
          style={[styles.wallpaperItemLarge, styles.plusCard, styles.wallpaperItemSpacing]}
          activeOpacity={0.7}
          onPress={onAddCustomImage}
        >
          <View style={styles.plusIconContainer}>
            <PlusSVG size={36} color={colors.mainPurple} />
          </View>
        </TouchableOpacity>

        {/* Custom wallpapers */}
        {customWallpapers.map((wallpaper, idx) => (
          <TouchableOpacity
            key={`custom-${wallpaper.id}-${idx}`}
            activeOpacity={0.7}
            onPress={() => handleCustomWallpaperPress(wallpaper)}
            style={[
              styles.wallpaperItemLarge,
              selectedCustomId === wallpaper.id && styles.selectedWallpaperItem,
              styles.wallpaperItemSpacing,
            ]}
          >
            <Image
              source={wallpaper.source}
              style={styles.wallpaperThumbnailLarge}
              resizeMode="cover"
            />
          </TouchableOpacity>
        ))}

        {/* Preset wallpapers */}
        {presetWallpapers.map((wallpaper, idx) => (
          <TouchableOpacity
            key={`preset-${wallpaper.id}`}
            activeOpacity={0.7}
            onPress={() => onImageSelect(wallpaper.id)}
            style={[
              styles.wallpaperItemLarge,
              selectedImageId === wallpaper.id && styles.selectedWallpaperItem,
              idx !== presetWallpapers.length - 1 && styles.wallpaperItemSpacing,
            ]}
          >
            <Image
              source={wallpaper.source}
              style={styles.wallpaperThumbnailLarge}
              resizeMode="cover"
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  imageRow: {
    flexDirection: 'row',
  },
  wallpaperItemLarge: {
    width: 90,
    height: 110,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent',
    backgroundColor: colors.gray_f3,
    marginVertical: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  wallpaperThumbnailLarge: {
    width: '100%',
    height: '100%',
    borderRadius: 18,
  },
  wallpaperItemSpacing: {
    marginRight: 10,
  },
  selectedWallpaperItem: {
    borderColor: colors.mainPurple,
  },
  plusCard: {
    backgroundColor: colors.gray_f3,
    borderStyle: 'dashed',
    borderColor: colors.mainPurple,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  plusIconContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ChatImageTab;
