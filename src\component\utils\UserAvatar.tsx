import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Image } from 'react-native';
import { IMAGES } from '../../assets/Images';
import { ImageProps } from 'react-native';

interface UserAvatarProps {
  width?: number;
  imgUrl?: string;
  style?: ImageProps['style'];
}

const getStyles = (width: number) =>
  StyleSheet.create({
    avatarImage: {
      width: width,
      height: width,
      borderRadius: width / 2,
    },
    container: {
      width: width,
      height: width,
      borderRadius: width / 2,
      overflow: 'hidden',
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

const UserAvatar = ({ width = 50, imgUrl, style, ...imageProps }: UserAvatarProps) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  const styles = getStyles(width);

  const isValidUrl = imgUrl && typeof imgUrl === 'string' && imgUrl.includes('https');

  return (
    <TouchableOpacity activeOpacity={0.7} style={styles.container}>
      {/* Dummy image - always visible */}
      <Image
        {...imageProps}
        source={IMAGES.profile_image}
        style={[styles.avatarImage, { position: 'absolute' }]}
        resizeMode="cover"
      />

      {/* URL image - only visible after loaded */}
      {isValidUrl && (
        <Image
          {...imageProps}
          source={{ uri: imgUrl }}
          style={[styles.avatarImage, { opacity: isImageLoaded ? 1 : 0 }]}
          resizeMode="cover"
          onLoadEnd={() => setIsImageLoaded(true)}
        />
      )}
    </TouchableOpacity>
  );
};

export default UserAvatar;
