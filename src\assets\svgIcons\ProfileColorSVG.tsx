import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const ProfileColorSVG = ({ size = 20, color = '#232323', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 20 22" fill="none" {...props}>
    <Path fillRule="evenodd" clipRule="evenodd" d="M17.2199 13.4698C17.2199 13.4698 19.6899 15.9937 19.6899 18.4997C19.6899 20.1557 18.3449 21.4998 16.6899 21.4998C15.0339 21.4998 13.6899 20.1557 13.6899 18.4997C13.6899 15.9937 16.1589 13.4698 16.1589 13.4698C16.4519 13.1768 16.9269 13.1768 17.2199 13.4698ZM16.6899 15.1367C16.4439 15.4437 16.1489 15.8507 15.8829 16.3157C15.5129 16.9627 15.1899 17.7287 15.1899 18.4997C15.1899 19.3277 15.8619 19.9997 16.6899 19.9997C17.5169 19.9997 18.1899 19.3277 18.1899 18.4997C18.1899 17.7287 17.8659 16.9627 17.4959 16.3157C17.2299 15.8507 16.9349 15.4437 16.6899 15.1367Z" fill={color}/>
    <Path fillRule="evenodd" clipRule="evenodd" d="M16.301 11.6827L10.644 17.3397C9.941 18.0427 8.987 18.4377 7.993 18.4377C6.998 18.4377 6.044 18.0427 5.341 17.3397L1.099 13.0977C0.396 12.3937 0 11.4397 0 10.4457C0 9.45069 0.396 8.49669 1.099 7.79369L7.462 1.43069C7.755 1.13769 8.23 1.13769 8.523 1.43069L16.301 9.20769C16.984 9.89169 16.984 10.9997 16.301 11.6827ZM15.24 10.6227C15.338 10.5247 15.338 10.3667 15.24 10.2687L7.993 3.02169L2.16 8.85469C1.738 9.27669 1.5 9.84869 1.5 10.4457C1.5 11.0427 1.738 11.6147 2.16 12.0367L6.402 16.2787C6.824 16.7007 7.396 16.9377 7.993 16.9377C8.589 16.9377 9.162 16.7007 9.584 16.2787L15.24 10.6227Z" fill={color}/>
    <Path fillRule="evenodd" clipRule="evenodd" d="M9.73502 2.64375C10.028 2.93675 10.028 3.41175 9.73502 3.70475C9.44202 3.99675 8.96702 3.99675 8.67402 3.70475L6.25002 1.27975C5.95702 0.98775 5.95702 0.51175 6.25002 0.21975C6.54302 -0.07325 7.01802 -0.07325 7.31102 0.21975L9.73502 2.64375Z" fill={color}/>
    <Path fillRule="evenodd" clipRule="evenodd" d="M15.8709 9.69531C16.2849 9.69531 16.6208 10.0313 16.6208 10.4453C16.6208 10.8593 16.2849 11.1953 15.8709 11.1953H1.04785C0.633852 11.1953 0.297852 10.8593 0.297852 10.4453C0.297852 10.0313 0.633852 9.69531 1.04785 9.69531H15.8709Z" fill={color}/>
  </Svg>
);

export default ProfileColorSVG; 