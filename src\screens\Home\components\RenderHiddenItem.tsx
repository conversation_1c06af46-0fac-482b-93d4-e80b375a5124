import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useRef } from 'react';
import { Menu } from 'react-native-material-menu';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import { useTranslation } from 'react-i18next';

import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import MuteSVG from '../../../assets/svgIcons/MuteSVG';
import PinSVG from '../../../assets/svgIcons/PinSVG';
import LockSVG from '../../../assets/svgIcons/LockSVG';
import ArchivedSVG from '../../../assets/svgIcons/ArchivedSVG';
import ClearSVG from '../../../assets/svgIcons/ClearSVG';
import MenuSVG from '../../../assets/svgIcons/MenuSVG';
import UnArchiveSVG from '../../../assets/svgIcons/UnArchiveSVG';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';

interface IProps {
  item: IConversation;
  onPressDeleteChat?: () => void;
  onPressIcon?: (type: string) => void;
  type: 'Archive' | 'Home' | 'Channel' | 'Group';
  hideDeleteButton?: boolean;
}

const itemArray = [
  { id: 1, title: 'Mute', image: <MuteSVG />, type: 'MUTE' },
  { id: 2, title: 'Pin', image: <PinSVG />, type: 'PIN' },
  // { id: 3, title: 'Lock', image: <LockSVG size={18} />, type: 'LOCK' },
  { id: 4, title: 'Archive', image: <ArchivedSVG size={17} />, type: 'ARCHIVE' },
  { id: 5, title: 'Clear chat', image: <ClearSVG />, type: 'CLEAR' },
];

const itemArray2 = [
  { id: 0, title: 'Unarchive', image: <UnArchiveSVG />, type: 'ARCHIVE' },
  { id: 1, title: 'Mute', image: <MuteSVG />, type: 'MUTE' },
  { id: 2, title: 'Pin', image: <PinSVG />, type: 'PIN' },
  // { id: 3, title: 'Lock', image: <LockSVG size={18} />, type: 'LOCK' },
  { id: 5, title: 'Clear chat', image: <ClearSVG />, type: 'CLEAR' },
];

const RenderHiddenItem = ({
  item,
  onPressDeleteChat,
  onPressIcon = () => {},
  type = 'Home',
  hideDeleteButton = false,
}: IProps) => {
  const { t } = useTranslation();
  const menuRef = useRef<any>(null); // Create a ref for the Menu component

  const onHide = () => {
    setTimeout(() => {
      menuRef.current?.hide();
    }, 200);
  };

  const data = itemArray?.filter((list: any) => {
    if (item?.lastMessage == null) {
      return list?.type !== 'CLEAR';
    }
    return list;
  });

  const data2 = itemArray2?.filter((list: any) => {
    if (item?.lastMessage == null) {
      return list?.type !== 'CLEAR';
    }
    return list;
  });

  const onShow = () => {
    setTimeout(() => {
      menuRef.current?.show(); // Open the menu
    }, 300);
  };

  const onPress = (itemType: string) => {
    onHide(); // Close the menu
    onPressIcon(itemType);
  };

  const compareObjects = (obj1: any, obj2: any) => {
    const missingKeys = [];

    const findMissingKeys = (base: any, compare: any, path = '') => {
      for (const key in base) {
        const fullPath = path ? `${path}.${key}` : key;

        if (!(key in compare)) {
          missingKeys.push(fullPath);
        } else if (typeof base[key] === 'object' && base[key] !== null) {
          findMissingKeys(base[key], compare[key], fullPath);
        }
      }
    };

    findMissingKeys(obj1, obj2);

    return missingKeys.length;
  };

  return (
    <View style={styles.hiddenRow}>
      {!hideDeleteButton && (
        <TouchableOpacity style={styles.deleteButton} onPress={onPressDeleteChat}>
          <DeleteSVG size={21} color={colors.white} />
        </TouchableOpacity>
      )}

      <Menu
        ref={menuRef} // Attach the ref to the Menu component
        style={styles.menuMainStyle}
        animationDuration={100}
        anchor={
          <TouchableOpacity
            style={[styles.deleteButton, { backgroundColor: colors.mainPurple }]}
            onPress={() => {
              // getData();
              onShow();
            }}
          >
            {/* <Image source={IMAGES.menuIcon} style={styles.menuIcon} /> */}
            <MenuSVG width={15} height={17} color={colors.white} />
          </TouchableOpacity>
        }
        onRequestClose={onHide} // Close the menu on request
      >
        <View style={styles.menuStyle}>
          {(type === 'Archive' ? data2 : data).map(
            ({ type: itemType, id, title, image }, index) => (
              <TouchableOpacity
                onPress={() => onPress(itemType)}
                key={id}
                style={styles.menuItemStyle}
              >
                {image}

                <Text style={styles.menuItemText}>
                  {itemType === 'MUTE' && item?.conversationSettings?.muteUntil == 'always'
                    ? t('Unmute')
                    : itemType === 'PIN' && item?.conversationSettings?.isPinned
                    ? t('Unpin')
                    : t(title)}
                </Text>
              </TouchableOpacity>
            ),
          )}
        </View>
      </Menu>
    </View>
  );
};

export default RenderHiddenItem;

const styles = StyleSheet.create({
  menuMainStyle: {
    marginTop: 20,
    marginHorizontal: -75,
    borderRadius: 10,
    padding: 5,
  },
  menuStyle: {
    gap: 20,
    padding: 10,
    height: 'auto',
  },
  hiddenRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: 120,
    alignSelf: 'flex-end',
  },
  deleteButton: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors._F61B1B_red,
    width: 60,
  },
  deleteIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  menuIcon: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  menuItemStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  menuItemText: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
});
