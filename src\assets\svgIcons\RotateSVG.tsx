import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const RotateSVG: React.FC<SvgComponentProps> = ({
    size = 24,
    color = "#353535",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            {...props}
        >
            <Path
                d="M23.833 13.281a11.947 11.947 0 01-21.994 5.034v3.45a.92.92 0 11-1.839 0v-6.13a.92.92 0 01.92-.92h6.13a.92.92 0 010 1.839H2.968a10.098 10.098 0 0019.044-3.472.92.92 0 111.828.2h-.006zm-.841-12.055a.92.92 0 00-.92.92v3.45A11.947 11.947 0 00.078 10.63a.92.92 0 00.816 1.014.937.937 0 00.1 0 .92.92 0 00.913-.82 10.098 10.098 0 0119.037-3.467h-4.083a.92.92 0 100 1.84h6.13a.92.92 0 00.92-.92V2.146a.92.92 0 00-.92-.92z"
                fill={color}
            />
        </Svg>
    );
};

export default RotateSVG;

