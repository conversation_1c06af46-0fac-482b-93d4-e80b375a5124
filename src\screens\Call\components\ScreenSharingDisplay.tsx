import React, { useState, useRef, useEffect } from 'react';
import { colors } from '../../../theme/colors';
import { View, TouchableOpacity, FlatList } from 'react-native';

import CallMemberTile from './CallMembertile';
import { _openAppSetting } from '../../../utils/locationHandler';
import { useCallContext } from '../../../Context/CallProvider';
import { Participant } from '../../../types/calls.types';
import { hp, wp } from '../../../theme/fonts';
import { Text } from 'react-native-gesture-handler';

type ScreenSharingDisplayProps = {
  showingFooter: boolean;
  participants: Participant[];
  selectUser(participant: Participant, idx: number): void;
  fullScreenMode: boolean;
};
export default function ScreenSharingDisplay({
  showingFooter,
  participants,
  selectUser,
  fullScreenMode,
}: ScreenSharingDisplayProps) {
  const { callDetails, stopScreenSharing } = useCallContext();
  return (
    <>
      <View
        style={{
          // shoould cover entire screen.
          backgroundColor: colors.mainPurple,
          width: '100%',
          height: '100%',
        }}
      >
        <View
          style={{
            width: wp(90),
            height: wp(90),
            backgroundColor: colors._7A5DCB_purple,
            borderRadius: 20,
            position: 'relative',
            marginHorizontal: 'auto',
            top: hp(10),
            justifyContent: 'center',
            alignItems: 'center',
            gap: 10,
          }}
        >
          <Text style={{ color: 'white', fontWeight: 400, fontSize: 20 }}>
            You are sharing screen...
          </Text>
          <TouchableOpacity
            onPress={async () => {
              await stopScreenSharing();
            }}
            style={{
              padding: 15,
              paddingHorizontal: 25,
              backgroundColor: colors.thick_red,
              borderRadius: 50,
            }}
          >
            <Text style={{ color: 'white', fontWeight: 400, fontSize: 17 }}>Stop Sharing</Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            position: 'absolute',
            bottom: showingFooter ? 200 : 120,
            paddingHorizontal: 15,
            right: participants?.length > 1 ? 0 : 20,
            height: 220,
            width: '100%',
          }}
        >
          {callDetails.type === 'video' && (
            <FlatList
              data={callDetails.participants}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={(item) => item.participantId}
              renderItem={({ item, index }) => (
                <View
                  style={{
                    width: 130,
                    height: 210,
                    borderRadius: 40,
                    backgroundColor: 'red',
                  }}
                >
                  <CallMemberTile
                    callDetails={callDetails}
                    participent={item}
                    idx={index}
                    isScreenSharing={false}
                    selectUser={selectUser}
                    fullScreenMode={fullScreenMode}
                  />
                </View>
              )}
              ItemSeparatorComponent={() => <View style={{ width: 10 }} />}
              contentContainerStyle={{ paddingHorizontal: 10 }}
            />
          )}
        </View>
      </View>
    </>
  );
}
