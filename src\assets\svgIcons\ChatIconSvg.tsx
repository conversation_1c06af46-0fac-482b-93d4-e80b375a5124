import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

function ChatIconSvg(props :SvgProps) {
  return (
    <Svg
      width={23}
      height={23}
      viewBox="0 0 23 23"
      fill="none"
      {...props}
    >
      <Path
        d="M1 21.916l-.707-.707A1 1 0 001 22.916v-1zm3.063-3.063l.707.707a1 1 0 000-1.414l-.707.707zm2.747-6.07a1 1 0 100 2v-2zm4.648 2a1 1 0 000-2v2zM6.81 8.133a1 1 0 100 2v-2zm9.296 2a1 1 0 000-2v2zm5.81 1.324h-1a9.458 9.458 0 01-9.458 9.458v2c6.328 0 11.458-5.13 11.458-11.458h-1zM1 11.458h1A9.458 9.458 0 0111.458 2V0C5.13 0 0 5.13 0 11.458h1zM11.458 1v1a9.458 9.458 0 019.458 9.458h2C22.916 5.13 17.786 0 11.458 0v1zm0 20.916v-1H1v2h10.458v-1zm-7.395-3.063l.707-.707A9.426 9.426 0 012 11.458H0c0 3.164 1.284 6.03 3.356 8.102l.707-.707zM1 21.916l.707.707L4.77 19.56l-.707-.707-.707-.707L.293 21.21l.707.707zm5.81-8.134v1h4.648v-2H6.81v1zm0-4.648v1h9.296v-2H6.81v1z"
        fill="#fff"
      />
    </Svg>
  )
}

export default ChatIconSvg
