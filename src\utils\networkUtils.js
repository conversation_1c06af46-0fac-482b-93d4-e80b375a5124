import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, { AxiosError } from 'axios';
// import { LogoutApiCall, generatehash, getmwdata } from '../utils';
import DeviceInfo from 'react-native-device-info';
import { NativeModules } from 'react-native';
// import { AuthorizationStatus } from '@notifee/react-native';
import { getAsyncToken, getAsyncUserInfo } from '../utils/asyncStorage';
import { Client } from '../lib/Client';
import Logger from '../lib/Logger';

const { SimInfo } = NativeModules;

class NetworkUtils {
  constructor({ baseUrl }) {
    this.baseUrl = baseUrl;
  }

  get(endpoint, params) {
    return this.requestHttpJSON('GET', `${this.baseUrl}${endpoint}`, params);
  }

  post(endpoint, params, isFormData = false) {
    return this.requestHttpJSON('POST', `${this.baseUrl}${endpoint}`, params, isFormData);
  }

  put(endpoint, params, isFormData = false) {
    return this.requestHttpJSON('PUT', `${this.baseUrl}${endpoint}`, params, isFormData);
  }

  patch(endpoint, params, isFormData = false) {
    return this.requestHttpJSON('PATCH', `${this.baseUrl}${endpoint}`, params, isFormData);
  }

  delete(endpoint, params) {
    return this.requestHttpJSON('DELETE', `${this.baseUrl}${endpoint}`, params);
  }

  async requestHttpJSON(method, url, params, isFormData = false) {
    try {
      const useragent = await DeviceInfo.getUserAgent();
      const uniqueId = await DeviceInfo.getUniqueId();
      const token = await Client.AuthToken.get();
      const headers = {
        'User-Agent': useragent,
        versionnumber: DeviceInfo.getBuildNumber(),
        deviceId: uniqueId,
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json',
        Accept: 'application/json',
        Authorization: Client.getBearerToken(token),
      };

      const options = { method, url, headers, data: params };
      const response = await axios(options);
      return { statusCode: response.status, body: response.data };
    } catch (error) {
      const axiosError = error;
      const isNetworkError = !axiosError.response; // No response indicates network error
      if (isNetworkError) {
        Logger.error(`Network error for ${url}: ${axiosError.message}`);
        return {
          statusCode: null,
          body: axiosError.message,
          isNetworkError: true,
        };
      }

      Logger.error(`HTTP error for ${url}: ${axiosError.message}`);
      return {
        statusCode: axiosError.response?.status || null,
        body: axiosError.response?.data || axiosError.message,
        isNetworkError: false,
      };
    }
  }
}

export default NetworkUtils;
