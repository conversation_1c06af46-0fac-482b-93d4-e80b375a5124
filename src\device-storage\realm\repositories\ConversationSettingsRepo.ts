import { getMuteUntilString } from '../../../lib/chatLib';
import { safeRealmWrite } from '../lib';
import { ConversationSettingsSchema } from '../schemas/ConversationSettingsSchema';
import { realmSchemaNames } from '../schemas/schemaNames';
import { Realm } from 'realm';

export class ConversationSettingsRepo {
  private static schemaName = realmSchemaNames.conversation_settings;

  constructor() {}

  static getOrcreate(realm: Realm, conversationId: string) {
    const settings = realm.objectForPrimaryKey(ConversationSettingsSchema, conversationId);
    if (settings) return settings;
    return safeRealmWrite(realm, () => {
      return realm.create(ConversationSettingsSchema, { id: conversationId });
    });
  }

  static get(realm: Realm, conversationId: string) {
    return realm.objectForPrimaryKey(ConversationSettingsSchema, conversationId);
  }

  static updateDisappearDuration(
    realm: Realm,
    conversationId: string,
    disappearDuration: number | null,
  ) {
    const settings = this.get(realm, conversationId);
    if (!settings) {
      console.error(
        `ConversationSettingsRepo: updateDisappearDuration: No settings found for conversationId ${conversationId}, creating new settings.`,
      );
      return;
    }
    safeRealmWrite(realm, () => {
      settings.disappearDuration = disappearDuration || undefined;
    });
  }

  static toggleConvSettPin(realm: Realm, conversationId: string) {
    const settings = this.get(realm, conversationId);
    if (!settings) {
      console.error('no settings found-------');
      return;
    }
    safeRealmWrite(realm, () => {
      settings.isPinned = settings.isPinned ? false : true;
    });
  }

  static toggleConvSettArchived(realm: Realm, conversationId: string) {
    const settings = this.get(realm, conversationId);
    if (!settings) {
      console.error('no settings found-------');
      return;
    }
    safeRealmWrite(realm, () => {
      settings.isArchived = settings.isArchived ? false : true;
    });
  }

  static toggleMute(realm: Realm, conversationId: string, muteUntil?: string | undefined) {
    const settings = this.get(realm, conversationId);
    if (!settings) {
      console.error('no settings found-------');
      return;
    }
    const getMuteUntilDateString = getMuteUntilString(muteUntil);
    safeRealmWrite(realm, () => {
      settings.muteUntil = getMuteUntilDateString;
    });
  }

  static updateTranslationOptions(
    realm: Realm,
    conversationId: string,
    field: 'translateText' | 'translateVoiceMessages' | 'translateCalls',
    value: boolean,
  ) {
    const settings = this.get(realm, conversationId);
    if (!settings) return;

    safeRealmWrite(realm, () => {
      settings[field] = value;
    });
  }

  static updateTextLanguage(realm: Realm, conversationId: string, textLanguage: string) {
    const settings = this.get(realm, conversationId);
    if (!settings) return;
    safeRealmWrite(realm, () => {
      settings.translationLanguage = textLanguage;
    });
  }

  static toggleChatspaceFavourite(realm: Realm, conversationId: string, isFavourite: boolean) {
    const settings = this.get(realm, conversationId);
    if (!settings) return;
    safeRealmWrite(realm, () => {
      settings.isFavourite = isFavourite;
    });
  }
}
