import { useEffect, useRef, useState } from 'react';

export const useCommonFilteredList = <T extends Record<string, any>>(
  list: T[],
  searchText: string,
) => {
  const initialListRef = useRef<T[]>(list);
  const [filteredList, setFilteredList] = useState<T[]>(list);

  useEffect(() => {
    const query = searchText.trim().toLowerCase();

    if (!query) {
      setFilteredList(initialListRef.current);
    } else {
      const filtered = initialListRef.current.filter((item) =>
        JSON.stringify(item).toLowerCase().includes(query),
      );
      setFilteredList(filtered);
    }
  }, [searchText]);

  return { filteredList };
};
