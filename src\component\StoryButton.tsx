// import {
//   FlatList,
//   Image,
//   ScrollView,
//   StyleSheet,
//   Text,
//   TouchableOpacity,
//   View,
// } from "react-native";
// import React, { useEffect, useState } from "react";
// import { commonFontStyle, hp } from "../theme/fonts";
// import { IMAGES } from "../assets/Images";
// import { colors } from "../theme/colors";
// import ReactNativeModal from "react-native-modal";
// import { useTranslation } from "react-i18next";

// import { getAsyncViewStoryId } from "../utils/asyncStorage";
// import RenderUserIcon from "./RenderUserIcon";

// type Props = {
//   onPressAdd?: () => void;
//   onPressProfile?: (value: any, index: any) => void;
//   isVisible?: boolean;
//   setIsVisible?: (value: boolean) => void;
//   isNewStoryAdded: boolean;
//   onPressStoryButton?: () => void;
//   onPressDraft: () => void;
// };

// const StoryButton = ({
//   onPressAdd = () => {},
//   onPressProfile = () => {},
//   isVisible,
//   setIsVisible = () => {},
//   isNewStoryAdded,
//   onPressStoryButton = () => {},
//   onPressDraft = () => {},
// }: Props) => {
//   const [showPeople, setShowPeople] = useState(false);
//   const { t } = useTranslation();

//   const [myStatus, setMyStatus] = useState<any>([]);
//   const [draftStatus, setDraftStatus] = useState<any>([]);
//   const [viewedStatus, setViewedStatus] = useState<any>([]);

//   useEffect(() => {
//     if (isVisible) {
//       setShowPeople(isVisible);
//     }
//   }, [isVisible]);

//   useEffect(() => {
//     (async () => {
//       const storedIds = await getAsyncViewStoryId();
//       setViewedStatus(storedIds);
//     })();
//   }, [isVisible]);

//   const onClose = () => {
//     setShowPeople(false);
//     setIsVisible(false);
//   };
//   const onPress = (i: any, index?: any) => {
//     setShowPeople(false);
//     onPressProfile(i, index);
//   };

//   useEffect(() => {
//     if (ownStatus.length > 0) {
//       const newStatus = ownStatus?.filter(
//         (i: any) => i?.scheduleStatus === "POST"
//       );
//       const drafts = ownStatus?.filter(
//         (i: any) => i?.scheduleStatus === "DRAFTED"
//       );
//       setDraftStatus(drafts);
//       setMyStatus(newStatus);
//     }
//   }, [ownStatus]);
//   return (
//     <View style={styles.storyButton}>
//       {/* {!showPeople && ( */}
//       <TouchableOpacity
//         onPress={() => {
//           onPressStoryButton();
//           setShowPeople(true);
//         }}
//         style={styles.mainButton}
//       >
//         <Image source={IMAGES.storiesIcon} style={styles.mainIcon} />

//         {isNewStoryAdded && <View style={styles.dot} />}
//       </TouchableOpacity>
//       {/* )} */}
//       <ReactNativeModal
//         isVisible={showPeople}
//         onBackdropPress={onClose}
//         style={styles.modalView}
//         backdropColor="transparent"
//       >
//         <View style={styles.mainView}>
//           <ScrollView
//             showsVerticalScrollIndicator={false}
//             contentContainerStyle={{
//               maxHeight: "100%",
//             }}
//           >
//             <TouchableOpacity
//               onPress={() => {
//                 onClose();
//                 onPressAdd();
//               }}
//             >
//               <Image source={IMAGES.addStory} style={styles.addButton} />
//               <Text numberOfLines={1} style={styles.addText}>
//                 {t("Add")}
//               </Text>
//             </TouchableOpacity>
//             <FlatList
//               data={otherUserStatus}
//               renderItem={({ item, index }) => {
//                 return (
//                   <TouchableOpacity onPress={() => onPress("other", index)}>
//                     <View
//                       style={{
//                         ...styles.peopleView,
//                         borderWidth: item?.statuses?.length <= 1 ? 2 : 0,
//                         borderColor: viewedStatus?.includes(item?.userId)
//                           ? colors.mainPurple
//                           : colors._CCCCCC_gray,
//                       }}
//                     >
//                       <RenderUserIcon
//                         url={item?.profile}
//                         size={"90%"}
//                         imageStyle={styles.peopleImage}
//                       />

//                       {item?.statuses?.length <= 1
//                         ? null
//                         : item.statuses?.map((_: any, index: any) => {
//                             const angle =
//                               (360 / item?.statuses?.length) * index;
//                             return (
//                               <View
//                                 key={index}
//                                 style={[
//                                   styles.dash,
//                                   {
//                                     backgroundColor: !viewedStatus.includes(
//                                       item?.userId
//                                     )
//                                       ? colors.mainPurple
//                                       : colors._CCCCCC_gray,
//                                     width: 12, // Dash width
//                                     height: 2, // Dash length
//                                     transform: [
//                                       { rotate: `${angle}deg` },
//                                       { translateY: -50 / 2 }, // Push dashes outward
//                                     ],
//                                     // borderRadius: 10,
//                                   },
//                                 ]}
//                               />
//                             );
//                           })}
//                     </View>

//                     <Text numberOfLines={1} style={styles.addText}>
//                       {item.username}
//                     </Text>
//                   </TouchableOpacity>
//                 );
//               }}
//               ListHeaderComponent={() => (
//                 <>
//                   {myStatus.length > 0 ? (
//                     <>
//                       <TouchableOpacity onPress={() => onPress("my")}>
//                         <View
//                           style={{
//                             ...styles.peopleView,
//                             borderWidth: 2,
//                             borderColor: colors.mainPurple,
//                           }}
//                         >
//                           <RenderUserIcon
//                             url={user?.image}
//                             size={"90%"}
//                             imageStyle={styles.peopleImage}
//                           />
//                         </View>

//                         <Text numberOfLines={1} style={styles.addText}>
//                           {t("Your")}
//                         </Text>
//                       </TouchableOpacity>

//                       <View style={styles.line} />
//                     </>
//                   ) : null}

//                   {draftStatus.length > 0 ? (
//                     <>
//                       <TouchableOpacity
//                         onPress={() => {
//                           setShowPeople(false);
//                           onPressDraft();
//                         }}
//                       >
//                         <View
//                           style={{
//                             ...styles.peopleView,
//                             borderWidth: 2,
//                             borderColor: colors.gray_80,
//                           }}
//                         >
//                           <RenderUserIcon
//                             url={user?.image}
//                             size={"90%"}
//                             imageStyle={styles.peopleImage}
//                           />
//                         </View>

//                         <Text numberOfLines={1} style={styles.addText}>
//                           {t("Draft")}
//                         </Text>
//                       </TouchableOpacity>

//                       <View style={styles.line} />
//                     </>
//                   ) : null}
//                 </>
//               )}
//             />
//           </ScrollView>
//         </View>
//       </ReactNativeModal>
//     </View>
//   );
// };

// export default StoryButton;

// const styles = StyleSheet.create({
//   storyButton: {
//     position: "absolute",
//     bottom: 130,
//     right: hp(2),
//   },
//   mainButton: {
//     height: 50,
//     width: 50,
//     backgroundColor: colors.white,
//     borderRadius: 50 / 2,
//     shadowColor: "#000",
//     shadowOffset: {
//       width: 0,
//       height: 5,
//     },
//     shadowOpacity: 0.34,
//     shadowRadius: 6.27,

//     elevation: 10,
//     justifyContent: "center",
//     alignItems: "center",
//   },
//   mainIcon: {
//     height: 30,
//     width: 30,
//   },
//   mainView: {
//     backgroundColor: colors.white,
//     bottom: 100,
//     right: hp(2),
//     width: 60,
//     maxHeight: "65%",
//     borderRadius: 100,
//     shadowColor: "#000",
//     shadowOffset: {
//       width: 0,
//       height: 5,
//     },
//     shadowOpacity: 0.34,
//     shadowRadius: 6.27,

//     elevation: 10,
//     padding: 5,
//     overflow: "hidden",
//   },
//   modalView: {
//     alignItems: "flex-end",
//     justifyContent: "flex-end",
//     margin: 0,
//   },
//   addButton: {
//     height: 50,
//     width: 50,
//     resizeMode: "contain",
//   },
//   peopleView: {
//     height: 50,
//     width: 50,
//     backgroundColor: colors.white,
//     borderRadius: 50 / 2,
//     justifyContent: "center",
//     alignItems: "center",
//     // borderWidth: 2,
//     // borderColor: colors.mainPurple,
//   },
//   addText: {
//     ...commonFontStyle(500, 13, colors.black_23),
//     textAlign: "center",
//     marginTop: 3,
//     marginBottom: 8,
//   },
//   peopleImage: {
//     height: "90%",
//     width: "90%",
//     borderRadius: 50,
//   },
//   line: {
//     width: "100%",
//     height: 2,
//     backgroundColor: colors.gray_f3,
//     marginBottom: 10,
//   },
//   dot: {
//     height: 8,
//     width: 8,
//     backgroundColor: colors.mainPurple,
//     borderRadius: 8 / 2,
//     position: "absolute",
//     top: 5,
//     right: 1,
//   },

//   dash: {
//     position: "absolute",
//     backgroundColor: "purple", // Dash color
//     borderRadius: 2, // Rounded dash ends
//   },
// });
