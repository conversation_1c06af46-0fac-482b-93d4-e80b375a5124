// import React, { useCallback, useMemo, useState } from 'react';
// import { View, TouchableOpacity, FlatList } from 'react-native';
// import { Canvas, Image as SkiaImage, ColorMatrix, useImage, rect, rrect, useVideo } from '@shopify/react-native-skia';
// import { hp } from '../utils/Constants/dimensionUtils';
// import { filters } from '../utils/Data';
// import { useSharedValue } from 'react-native-reanimated';

// interface dataType {
//   id: string;
//   name: string;
//   matrix: number[];
// }

// interface FilterCanvasProps {
//   selectedImage: { uri: string };
//   setSelectedImage?: (matrix: any) => void;
//   data: dataType[];
// }

// const ComposeImageFilter = React.memo(({ source, matrix, onSelectFilter, isSelected }: { source: string, matrix: number[], onSelectFilter: () => void, isSelected: string }) => {
//   var types = source && source.includes('video') ? 'video' : 'image'
//   const size = useMemo(() => isSelected ? hp(5.3) : hp(6), []);
//   const roundedRect = useMemo(() => rrect(rect(0, 0, size, size), 8, 8), [size]);

//   const SkiaImageComp = (src) => {
//     console.log(src)
//     const image = useImage(source);
//     console.log(image)
//     if (!image) return null;

//     return (
//       <SkiaImage x={0} y={0} width={size} height={size} image={src} clip={roundedRect} fit="cover">
//         <ColorMatrix matrix={matrix} />
//       </SkiaImage>
//     )
//   }
// //   const SkiaVideoComp = (src) => {
// //     const paused = useSharedValue(false);
// //     const seek = useSharedValue(1);
// //     const looping = useSharedValue(true);
// //     const volume = useSharedValue(1);
// //     console.log(src)
// //     const videoData = types === 'video' ? useVideo(src, { paused, seek, looping, volume }) : { currentFrame: '', size: '' };
// //     const { currentFrame } = videoData || {};
// //     console.log(currentFrame)
// // console.log(types === 'video')
// //     if (!currentFrame) return null;
// //     return (
// //       <SkiaImage x={0} y={0} width={size} height={size} image={currentFrame} clip={roundedRect} fit="cover">
// //         <ColorMatrix matrix={matrix} />
// //       </SkiaImage>
// //     )
// //   }
//   const SkiaVideoComp = React.memo(({ src, matrix, size, roundedRect }) => {
//     const paused = useSharedValue(true); // Keep video paused to use as a static frame
//     const seek = useSharedValue(1); // Seek to the 1st second
//     const looping = useSharedValue(false);
//     const volume = useSharedValue(0);

//     const videoData = useVideo(src, { paused, seek, looping, volume });
//     const { currentFrame } = videoData || {};

//     if (!currentFrame) return null;

//     return (
//       <SkiaImage x={0} y={0} width={size} height={size} image={currentFrame} clip={roundedRect} fit="cover">
//         <ColorMatrix matrix={matrix} />
//       </SkiaImage>
//     );
//   });
//   const RaltedComponent = types === 'video' ? <SkiaVideoComp src={source} /> : <SkiaImageComp src={source} />
//   console.log(RaltedComponent)
//   return (
//     <TouchableOpacity onPress={onSelectFilter} style={{ alignSelf: 'center', justifyContent: 'center' }}>
//       <Canvas style={{ height: size, width: size, borderRadius: hp(1) }}>
//         {RaltedComponent}
//       </Canvas>
//     </TouchableOpacity>
//   );
// });

// const FilterWrapperCanvas: React.FC<FilterCanvasProps> = React.memo(({ selectedImage, setSelectedImage, data }) => {
//   const [selectedFilter, setSelectedFilter] = useState<any>(null);
//   const onPressToSelect = useCallback((item: dataType) => {
//     setSelectedImage?.(item.matrix);
//     setSelectedFilter(item.id);
//   }, [setSelectedImage]);

//   const renderItem = useCallback(({ item }: { item: any }) => {
//     FilterItem = item?.id === selectedFilter
//     console.log(selectedImage)
//     return (
//       <View style={{ marginRight: 5, marginBottom: 5, borderRadius: 8, justifyContent: 'center', alignItems: 'center', borderWidth: FilterItem ? 2 : 0, borderColor: '#6A4DBB', padding: 1 }}>
//         <ComposeImageFilter
//           source={selectedImage[0]?.uri}
//           matrix={item?.matrix}
//           onSelectFilter={() => onPressToSelect(item)}
//           isSelected={FilterItem}
//         />
//       </View>
//     )
//   }, [selectedImage]);

//   return (
//     <FlatList
//       data={filters}
//       renderItem={renderItem}
//       keyExtractor={(item) => item.id}
//       numColumns={6}
//       contentContainerStyle={{
//         justifyContent: 'space-between',
//         width: '100%',
//       }}
//       bounces={false}
//     />
//   );
// });

// export default React.memo(FilterWrapperCanvas);
import React, { useCallback, useMemo, useState } from 'react';
import { View, TouchableOpacity, FlatList } from 'react-native';
import {
  Canvas,
  Image as SkiaImage,
  ColorMatrix,
  useImage,
  rect,
  rrect,
} from '@shopify/react-native-skia';
import { hp } from '../utils/Constants/dimensionUtils';
import { filters } from '../utils/Data';

interface dataType {
  id: string;
  name: string;
  matrix: number[];
}

interface FilterCanvasProps {
  selectedImage: { uri: string };
  setSelectedImage?: (matrix: any) => void;
  data: dataType[];
  thumb: string;
}

const ComposeImageFilter = React.memo(
  ({
    source,
    matrix,
    onSelectFilter,
    isSelected,
    type,
  }: {
    type: string;
    source: string;
    matrix: number[];
    onSelectFilter: () => void;
    isSelected: string;
  }) => {
    // console.log('source-->>>',source)
    const image = useImage(source);
    // console.log('image after skia-->',image)
    const size = useMemo(() => (isSelected ? hp(5.3) : hp(6)), []);

    const roundedRect = useMemo(() => rrect(rect(0, 0, size, size), 8, 8), [size]);

    if (!image) return null;

    return (
      <TouchableOpacity
        onPress={onSelectFilter}
        style={{ alignSelf: 'center', justifyContent: 'center' }}
      >
        <Canvas style={{ height: size, width: size, borderRadius: hp(1) }}>
          <SkiaImage
            x={0}
            y={0}
            width={size}
            height={size}
            image={image}
            clip={roundedRect}
            fit="cover"
          >
            <ColorMatrix matrix={matrix} />
          </SkiaImage>
        </Canvas>
      </TouchableOpacity>
    );
  },
);

const FilterWrapperCanvas: React.FC<FilterCanvasProps> = React.memo(
  ({ selectedImage, setSelectedImage, data, thumb }) => {
    // console.log('--filter!! img canvas--',selectedImage);

    const [selectedFilter, setSelectedFilter] = useState<any>(null);
    const onPressToSelect = useCallback(
      (item: dataType) => {
        setSelectedImage?.(item);
        setSelectedFilter(item.id);
      },
      [setSelectedImage],
    );

    const renderItem = useCallback(
      ({ item }: { item: any }) => {
        FilterItem = item?.id === selectedFilter;
        return (
          <View
            style={{
              marginRight: 5,
              marginBottom: 5,
              borderRadius: 8,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: FilterItem ? 2 : 0,
              borderColor: '#6A4DBB',
              padding: 1,
            }}
          >
            <ComposeImageFilter
              source={selectedImage[0]?.uri}
              matrix={item?.matrix}
              type={selectedImage[0]?.type}
              onSelectFilter={() => onPressToSelect(item)}
              isSelected={FilterItem}
            />
          </View>
        );
      },
      [selectedImage],
    );

    return (
      <FlatList
        data={filters}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={6}
        contentContainerStyle={{
          justifyContent: 'space-between',
          width: '100%',
        }}
        bounces={false}
      />
    );
  },
);

export default React.memo(FilterWrapperCanvas);
