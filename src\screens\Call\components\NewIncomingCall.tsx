import React from 'react';
import { Modal, View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import useCallState from '../../../hooks/calls/useCallState';
import { useCallContext } from '../../../Context/CallProvider';
//this is not in figma so did'nt use
const NewIncomingCallModal = ({ newIncomingCall }: { newIncomingCall: boolean }) => {
  // const { acceptNewIncomingCall, rejectNewIncomingCall } = useCallContext();
  // const { getNewCallDetails } = useCallState();

  // if (!newIncomingCall) return null;

  // const callDetails = getNewCallDetails();

  // if (!callDetails || !callDetails.initiatorDetails) return null;

  // const callerName = callDetails.initiatorDetails.name || 'Someone';
  // const callType = callDetails.type === 'video' ? 'Video Call' : 'Audio Call';
  // const isGroupCall = callDetails.isGroupCall;

  return (
    <Modal transparent animationType="fade" visible={newIncomingCall}>
      {/* <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>
            Incoming {isGroupCall ? 'Group' : ''} {callType}
          </Text>
          <Text style={styles.caller}>{callerName} is calling you</Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => {
                console.log('Call Accepted');
                acceptNewIncomingCall();
              }}
            >
              <Text style={styles.buttonText}>Accept</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.rejectButton} onPress={rejectNewIncomingCall}>
              <Text style={styles.buttonText}>Reject</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View> */}
    </Modal>
  );
};

export default NewIncomingCallModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#222',
    textAlign: 'center',
  },
  caller: {
    fontSize: 18,
    marginBottom: 24,
    color: '#444',
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  rejectButton: {
    backgroundColor: '#F44336',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
});
