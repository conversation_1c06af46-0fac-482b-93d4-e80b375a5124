import * as React from "react"
import Svg, { Circle, Path, SvgProps } from "react-native-svg"

interface Props extends SvgProps {
  size?: number
  backgroundColor?: string
  pathColor?: string
  withOutBg?: boolean
}

const MusicSVG: React.FC<Props> = ({
  size = 32,
  backgroundColor = "#6A4DBB",
  pathColor = "#CDC5E8",
  withOutBg = false,
  ...props
}) => {

    const width = (size * 13) / 17 // maintain aspect ratio based on viewBox (13x17)

  return (
    <>
     {withOutBg? 
     <Svg
      width={width}
      height={size}
      viewBox="0 0 13 17"
      fill="none"
      {...props}
    >
      <Path
        d="M11.36 2.522a3.68 3.68 0 01-1.04-.3C9.33 1.758 8.817.969 8.59.543A1.01 1.01 0 007.451.03c-.45.1-.776.513-.776.977v7.201A4.348 4.348 0 000 11.878c0 2.404 1.954 4.358 4.346 4.358a4.37 4.37 0 004.358-4.359V3.624c.225.15.476.3.752.426.513.238 1.052.4 1.628.489a1.008 1.008 0 001.14-.865 1.027 1.027 0 00-.865-1.152zM4.345 14.207a2.333 2.333 0 01-2.33-2.33c0-1.29 1.052-2.329 2.33-2.329 1.277 0 2.33 1.04 2.33 2.33s-1.04 2.329-2.33 2.329z"
        fill={pathColor}
      />
    </Svg>
    : <Svg
        width={size}
        height={size}
        viewBox="0 0 32 32"
        fill="none"
        {...props}
        >
        <Circle cx={16} cy={16} r={16} fill={backgroundColor} />
        <Path
          d="M21.127 10.886a3.674 3.674 0 01-1.04-.3c-.99-.463-1.503-1.253-1.728-1.678a1.01 1.01 0 00-1.14-.514c-.45.1-.776.514-.776.977v7.201a4.348 4.348 0 00-6.675 3.67c0 2.404 1.953 4.358 4.345 4.358a4.37 4.37 0 004.359-4.358v-8.254c.225.15.476.301.751.426a5.98 5.98 0 001.628.489 1.008 1.008 0 001.14-.864 1.027 1.027 0 00-.864-1.153zm-7.014 11.685a2.333 2.333 0 01-2.33-2.33c0-1.29 1.053-2.329 2.33-2.329 1.278 0 2.33 1.04 2.33 2.33s-1.04 2.33-2.33 2.33z"
          fill={pathColor}
          />
      </Svg>}
    </>
  )
}

export default MusicSVG



