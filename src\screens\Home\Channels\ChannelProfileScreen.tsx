import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Platform,
  ToastAndroid,
  Alert,
} from 'react-native';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp, wp } from '../../../theme/fonts';
import { IMAGES } from '../../../assets/Images';
import CustomImage from '../../../component/CustomImage';
import CommonView from '../../../component/CommonView';
import Video from 'react-native-video';
import { useTranslation } from 'react-i18next';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import ButtonPurple from '../../../component/ButtonPurple';
import { IChatScreenProps } from '../Chats/ChatSpecificScreen';
import useConversations from '../../../hooks/conversations/useConversations';
import { ChatSocket } from '../../../socket-client/ChatSocket';
import { ChatService } from '../../../service/ChatService';
import useSocket from '../../../socket-client/useSocket';
import { ChatSpace } from '../../../types/socketPayload.type';
import { ChannelType, ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { useFetchMessages } from '../../../hooks/chats/messages/useFetchMessages';
import { navigateTo, showToast } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import BellSVG from '../../../assets/svgIcons/BellSVG';
import TranslateSVG from '../../../assets/svgIcons/TranslateSVG';
import EditSVG from '../../../assets/svgIcons/EditSVG';
import useConversationInfo from '../../../device-storage/realm/hooks/useConversationInfo';
import ChatScreenSkeleton from '../../../component/chats/ChatScreenSkeleton';
import { MembershipStatus } from '../../../types/chats.types';

type ChannelProfileScreenRouteParams = {
  channelData: IChatScreenProps;
  id: string;
  // followersCount: number;
};

const ChannelProfileScreen = () => {
  const { t } = useTranslation();
  const { socket } = useSocket();
  const navigation = useNavigation();
  const route =
    useRoute<
      RouteProp<{ ChannelProfileScreen: ChannelProfileScreenRouteParams }, 'ChannelProfileScreen'>
    >();
  const { channelData: userData } = route.params;
  const id = route.params.id;
  const { conversationInfo } = useConversationInfo(id);
  const [isFollowing, setIsFollowing] = useState(userData.isFollowing);
  const { messages } = useFetchMessages(conversationInfo?.id as string);
  // console.log('====>(CHANNELPROFILE)conversatonInfo<====', conversationInfo);
  const sharedImages = messages.filter((msg) => msg.messageType === 'image' && !!msg.mediaUrl);
  const liveConversationRealm = ChatService.getLiveConversation(userData.id);

  const liveConversation = useMemo(() => {
    if (!liveConversationRealm) return null;
    return JSON.parse(JSON.stringify(liveConversationRealm));
  }, [liveConversationRealm]);

  const handleFollow = async () => {
    const chatSpaceId = conversationInfo?.id as string;

    const newData: ChatSpace = {
      chatSpaceId: chatSpaceId,
      name: conversationInfo?.displayName ?? '',
      description: conversationInfo?.bio ?? '',
      displayPic: conversationInfo?.displayPic ?? '',
      type: ConversationType.CHANNEL,
      createdBy: '',
      role: ChannelType.MEMBER,
      isDeleted: false,
      _id: chatSpaceId,
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      isPrivate: false,
      inviteLink: null,
      isLiveStreaming: false,
      memberCount: conversationInfo?.memberCount ?? 0,
      inviteCode: '',
      lastMessageTimestamp: Date.now(),
    };

    ChatSocket.joinChannel(socket, chatSpaceId, async (response) => {
      if (response?.error) return;
      ChatService.createChatSpace(newData);
      ChatService.updateMemberCount(chatSpaceId, 1, 0);
      setIsFollowing(true);
      navigation.pop(2);
      showToast(`You are now following "${conversationInfo?.displayName ?? 'this channel'}"`);
    });
  };

  const handleUnfollow = async () => {
    const chatSpaceId = conversationInfo?.id;

    ChatSocket.emitLeaveChatspace(socket, chatSpaceId as string, async (response) => {
      if (response?.error) return;
      await ChatService.deleteConversation(chatSpaceId as string);
      socket?.emit('chatSpaces.deleted', { chatSpaceId });
      setIsFollowing(false);
      navigation.pop(2);
      showToast(`You unfollowed "${conversationInfo?.displayName ?? 'this channel'}"`);
    });
  };

  if (!conversationInfo) return <ChatScreenSkeleton />;
  if (conversationInfo.type !== ConversationType.CHANNEL) return <ChatScreenSkeleton />;
  return (
    <CommonView
      headerTitle={t('Channel')}
      containerStyle={styles.container}
      renderRight={() =>
        conversationInfo.membershipStatus === MembershipStatus.OWNER ? (
          <View style={styles.customHeader}>
            <TouchableOpacity
              onPress={() =>
                navigateTo(SCREENS.CreateChannelScreen, {
                  isEdit: true,
                  userData: {
                    chatspaceId: conversationInfo?.id,
                  },
                })
              }
            >
              <EditSVG size={21} color={colors.white} />
            </TouchableOpacity>
          </View>
        ) : null
      }
    >
      <View style={{ gap: 2 }}>
        <View style={[styles.row, { justifyContent: 'space-between' }]}>
          <View style={styles.row}>
            <View style={{ position: 'relative', marginRight: 10 }}>
              <View
                style={{
                  width: 68,
                  height: 68,
                  borderRadius: 39,
                  borderWidth: 2,
                  borderColor: colors.mainPurple,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Image
                  source={
                    liveConversation?.displayPic
                      ? { uri: liveConversation?.displayPic }
                      : { uri: conversationInfo?.displayPic }
                  }
                  style={{ width: 50, height: 50, borderRadius: 25 }}
                />
              </View>
              <View
                style={{
                  position: 'absolute',
                  bottom: 4,
                  right: 4,
                  width: 15,
                  height: 15,
                  borderRadius: 8,
                  backgroundColor: colors._33C200_green,
                  borderWidth: 2,
                  borderColor: 'white',
                }}
              />
            </View>
            <Text style={styles.nameStyle}>
              {liveConversation?.displayName
                ? liveConversation?.displayName
                : conversationInfo?.displayName}
            </Text>
          </View>

          {userData?.conversation?.role !== 'owner' && (
            <ButtonPurple
              title={isFollowing ? 'Following' : 'Follow'}
              onPress={() => {
                if (isFollowing) {
                  handleUnfollow();
                } else {
                  handleFollow();
                }
              }}
              extraStyle={{
                backgroundColor: colors.mainPurple,
                paddingHorizontal: 16,
                height: 45,
                borderRadius: 10,
                marginRight: 10,
              }}
              textStyle={{
                color: colors.white,
                fontWeight: '500',
              }}
            />
          )}
        </View>
        <Text style={[styles.description, { marginBottom: 8 }]}>
          {liveConversation?.description ? liveConversation?.description : conversationInfo?.bio}
        </Text>
      </View>

      <View>
        <View style={styles.statsRow}>
          {userData?.conversation?.role === 'member' && (
            <StatBox value={conversationInfo?.memberCount || 0} title={t('Followers')} />
          )}
          {userData?.conversation?.role === 'owner' && (
            <TouchableOpacity
              onPress={() =>
                navigateTo('FollowersScreen', {
                  chatSpaceId: userData.id,
                })
              }
            >
              <StatBox value={conversationInfo?.memberCount || 0} title={t('Followers')} />
            </TouchableOpacity>
          )}
        </View>
      </View>
      <View>
        <Text style={styles.sectionTitle}>{t('Posted media')}</Text>

        {sharedImages.length > 0 ? (
          <FlatList
            horizontal
            data={sharedImages}
            keyExtractor={(item) => item.localId}
            contentContainerStyle={{ gap: 10, paddingVertical: 5 }}
            renderItem={({ item }) => (
              <Image source={{ uri: item.mediaUrl }} style={styles.mediaThumb} />
            )}
          />
        ) : (
          <Text style={{ color: colors.gray_80, fontSize: 12, marginVertical: 5 }}>
            No shared media yet
          </Text>
        )}
      </View>
      <TouchableOpacity style={styles.msgItem} onPress={() => {}}>
        <BellSVG size={22} />
        <Text style={styles.msgText}>{t('Notifications')}</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.msgItem}
        onPress={() => navigateTo(SCREENS.TranslationMessages, { userData, conversationInfo })}
      >
        <TranslateSVG size={20} />
        <Text style={styles.msgText}>{t('Translate messages')}</Text>
      </TouchableOpacity>
    </CommonView>
  );
};

const StatBox = ({ value, title }: { value: number; title: string }) => {
  return (
    <View style={styles.statBox}>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: hp(2),
    paddingBottom: hp(1),
  },
  container: {
    backgroundColor: colors.white,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  nameStyle: {
    color: colors.black_23,
    fontSize: 18,
    fontWeight: '600',
    display: 'flex',
    flexWrap: 'wrap',
    width: wp(40),
  },
  description: {
    color: colors.black_23,
    marginTop: 4,
    paddingHorizontal: 6,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 3,
  },
  statBox: {
    width: wp(28),
    height: hp(10),
    borderRadius: 10,
    backgroundColor: colors.gray_f3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    color: colors.black_23,
    fontSize: 18,
    fontWeight: '600',
  },
  statLabel: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
  },
  sectionTitle: {
    ...commonFontStyle(500, 16, colors.gray_80),
    marginTop: 10,
    fontWeight: '800',
  },
  mediaThumb: {
    height: 100,
    width: 100,
    borderRadius: 15,
    backgroundColor: colors.gray_f3,
  },
  msgItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 10,
  },
  msgText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.gray_f3,
  },
  extraStyle: {
    width: 98,
    height: 29,
    borderRadius: 9,
    paddingHorizontal: 12,
  },
});

export default ChannelProfileScreen;
