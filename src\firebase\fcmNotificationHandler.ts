import { Socket } from 'socket.io-client';
import { Realm } from '@realm/react';
import { notifEvents, NotificationType } from '../socket-client/notificationTypes';
import { IncomingMessagePayload } from '../types/socketPayload.type';
import { ChatService } from '../service/ChatService';
import { showIncomingCallNotification } from './notifications/callNoitificationHandler';

export type FCMNotification = {
  data: {
    type: NotificationType;
    body: string;
  };
};

export const onFCMNotification = async (socket: Socket, fcmData: FCMNotification, realm: Realm) => {
  ChatService.setSocket(socket);
  ChatService.setRealm(realm);

  const notificationType = fcmData.data.type;
  const parsedData = {
    ...fcmData.data,
    body: typeof fcmData.data.body === 'string' ? JSON.parse(fcmData.data.body) : fcmData.data.body,
  };

  switch (notificationType) {
    case notifEvents.messages.incoming:
      ChatService.onIncomingMessage((parsedData as IncomingMessagePayload).body);
      break;
    case notifEvents.calls.incoming:
      showIncomingCallNotification(parsedData.body);
      break;

    default:
      break;
  }
};
