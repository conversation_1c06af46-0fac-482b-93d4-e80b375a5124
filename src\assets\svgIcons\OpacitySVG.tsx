import React from 'react';
import { Svg, Defs, <PERSON>tern, Rect, G, <PERSON> } from 'react-native-svg';

interface OpacitySVGProps {
  size?: number;
}

const OpacitySVG: React.FC<OpacitySVGProps> = ({ size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Defs>
      <Pattern id="checkerboardLight" patternUnits="userSpaceOnUse" width="4" height="4">
        <Rect x="0" y="0" width="2" height="2" fill="#E0E0E0" />
        <Rect x="2" y="2" width="2" height="2" fill="#E0E0E0" />
        <Rect x="2" y="0" width="2" height="2" fill="#fff" />
        <Rect x="0" y="2" width="2" height="2" fill="#fff" />
      </Pattern>
      <Pattern id="checkerboardDark" patternUnits="userSpaceOnUse" width="4" height="4">
        <Rect x="0" y="0" width="2" height="2" fill="#B0B0B0" />
        <Rect x="2" y="2" width="2" height="2" fill="#B0B0B0" />
        <Rect x="2" y="0" width="2" height="2" fill="#E0E0E0" />
        <Rect x="0" y="2" width="2" height="2" fill="#E0E0E0" />
      </Pattern>
    </Defs>
    <G opacity={0.8}>
      {/* Left half circle */}
      <Path
        d="M12 2
          a 10 10 0 0 0 0 20
          a 10 10 0 0 0 0 -20
          Z"
        fill="url(#checkerboardLight)"
      />
      {/* Right half circle */}
      <Path
        d="M12 2
          a 10 10 0 0 1 0 20
          a 10 10 0 0 1 0 -20
          Z"
        fill="url(#checkerboardDark)"
      />
    </G>
  </Svg>
);

export default OpacitySVG; 