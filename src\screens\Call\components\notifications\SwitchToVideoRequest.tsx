import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { BlurView } from '@react-native-community/blur';
import { useCallContext } from '../../../../Context/CallProvider';
import { CallScreenModalKey } from '../../MainCallScreen';
import { zIndex } from '../../../../utils/Filters';

const { width } = Dimensions.get('screen');
type Props = {
  closeModal: (modal: CallScreenModalKey) => void;
};
const SwitchToVideoRequest = ({ closeModal }: Props) => {
  const { callDetails, switchTovidoResponse, requestSwitchToVideo } = useCallContext();
  return (
    <View style={styles.wrapper}>
      <BlurView
        style={StyleSheet.absoluteFill}
        blurType="dark"
        blurAmount={15}
        reducedTransparencyFallbackColor="rgba(0, 0, 0, 0.7)"
      />
      <View style={styles.inner}>
        <Text style={styles.text}>You want to switch to video call</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            onPress={() => {
              closeModal('showSwitchToVideoRequest');
            }}
            style={styles.button}
          >
            <Text style={styles.buttonText}>No</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              requestSwitchToVideo();
              closeModal('showSwitchToVideoRequest');
            }}
            style={styles.button}
          >
            <Text style={styles.buttonText}>Yes</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    bottom: 40,
    width: '100%',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    overflow: 'hidden',

    zIndex: zIndex.level_3,
  },
  inner: {
    width: '100%',
    alignItems: 'center',
  },
  text: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    height: 42,
    borderRadius: 22,
    backgroundColor: 'rgba(244, 240, 240, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
  },
});

export default SwitchToVideoRequest;
