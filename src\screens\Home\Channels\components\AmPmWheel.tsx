import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';

import { NativeSyntheticEvent, NativeScrollEvent } from 'react-native';
const ITEM_HEIGHT = 60;
const DATA = ['AM', 'PM'];

type AmpmProps = {
  selected: 'AM' | 'PM';
  setSelected: React.Dispatch<React.SetStateAction<'AM' | 'PM'>>;
};
function AmPmWheel({ selected, setSelected }: AmpmProps) {
  const [scrollY, setScrollY] = useState(0);
  const listRef = useRef<FlatList<string>>(null);

  const handleScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    setScrollY(e.nativeEvent.contentOffset.y);
  };

  const handleScrollEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = e.nativeEvent.contentOffset.y;
    const index = Math.round(offsetY / ITEM_HEIGHT);
    const newValue = DATA[index];
    if (newValue !== selected) {
      console.log('AM/PM changed to:', newValue);
      setSelected(newValue as 'AM' | 'PM');
    }
  };

  const renderItem = ({ item, index }: { item: string; index: number }) => {
    const centerIndex = Math.round(scrollY / ITEM_HEIGHT);
    const isSelected = index === centerIndex;

    return (
      <View style={styles22.itemContainer}>
        <Text style={[styles22.itemText, isSelected && styles22.selectedText]}>{item}</Text>
      </View>
    );
  };

  React.useEffect(() => {
    const index = DATA.indexOf(selected);
    listRef.current?.scrollToOffset({ offset: index * ITEM_HEIGHT, animated: false });
  }, []);

  return (
    <View style={styles22.wheelContainer}>
      <FlatList
        ref={listRef}
        data={DATA}
        keyExtractor={(item) => item}
        renderItem={renderItem}
        getItemLayout={(_, index) => ({
          length: ITEM_HEIGHT,
          offset: ITEM_HEIGHT * index,
          index,
        })}
        showsVerticalScrollIndicator={false}
        snapToInterval={ITEM_HEIGHT}
        decelerationRate="fast"
        onScroll={handleScroll}
        onMomentumScrollEnd={handleScrollEnd}
        scrollEventThrottle={4}
        bounces={false}
        contentContainerStyle={{
          paddingVertical: ITEM_HEIGHT,
        }}
      />
    </View>
  );
}

const styles22 = StyleSheet.create({
  wheelContainer: {
    top: 10,
    height: ITEM_HEIGHT * 2.5,
    overflow: 'hidden',
    marginVertical: -10,
  },
  itemContainer: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: -10,
  },
  itemText: {
    fontSize: 20,
    color: '#888',
  },
  selectedText: {
    fontSize: 28,
    color: '#000',
    fontWeight: 400,
  },
});

export default AmPmWheel;
