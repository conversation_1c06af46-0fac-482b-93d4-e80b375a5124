import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import HeartOutlineSvg from '../../../../assets/svgIcons/HeartOutlineSvg';
import ShareOutlineSvg from '../../../../assets/svgIcons/ShareOutlineSvg';
import StickerOutlineSvg from '../../../../assets/svgIcons/StickerOutlineSvg';
import { useKeyboard } from '../../../../utils/useKeyboard';
import EyeOutlineSvg from '../../../../assets/svgIcons/EyeOutlineSvg';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { colors } from '../../../../theme/colors';

import RNVideo from 'react-native-video';

interface LiveStreamHeaderProps {
  title: string;
  coins: number;
  viewers: number;
  onBackPress: () => void;
  elapsedTime: string | null;
}

// Header Component
const LiveStreamHeader: React.FC<LiveStreamHeaderProps> = ({
  title,
  coins,
  viewers,
  onBackPress,
  elapsedTime,
}) => {
  return (
    <View style={LiveStreamHeaderStyles.header}>
      <TouchableOpacity onPress={onBackPress} style={LiveStreamHeaderStyles.backButton}>
        <BackArrowSVG size={30} />
      </TouchableOpacity>

      <View style={LiveStreamHeaderStyles.headerCenter}>
        <Text style={LiveStreamHeaderStyles.headerTitle}>{title}</Text>
        <View style={LiveStreamHeaderStyles.coinsContainer}>
          <View style={LiveStreamHeaderStyles.coinBadge}>
            <Text style={LiveStreamHeaderStyles.coinText}>🪙 {coins}</Text>
          </View>
          <View style={LiveStreamHeaderStyles.coinBadge}>
            <Text style={LiveStreamHeaderStyles.coinText}>🪙 {coins}</Text>
          </View>

          <View style={LiveStreamHeaderStyles.avatarGroup}>
            <Image
              source={{ uri: 'https://i.pravatar.cc/30?img=1' }}
              style={LiveStreamHeaderStyles.miniAvatar}
            />
            <Image
              source={{ uri: 'https://i.pravatar.cc/30?img=2' }}
              style={LiveStreamHeaderStyles.miniAvatar}
            />
          </View>
        </View>
      </View>

      <View style={LiveStreamHeaderStyles.liveIndicator}>
        <View
          style={{
            backgroundColor: 'red',
            borderRadius: 100,
            height: 7,
            width: 7,
          }}
        />
        {elapsedTime === null ? (
          <ActivityIndicator size="small" color="white" />
        ) : (
          <Text style={LiveStreamHeaderStyles.liveText}> {elapsedTime} </Text>
        )}
      </View>
    </View>
  );
};

export default LiveStreamHeader;

export const LiveStreamHeaderStyles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    width: '100%',
    top: 0,
    gap: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flex: 1,
    marginLeft: 8,
    backgroundColor: colors.overlayWhite_10,
    padding: 10,
    borderRadius: 12,
    paddingHorizontal: 15,
  },
  headerTitle: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '400',
    marginBottom: 4,
  },
  coinsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinBadge: {
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
  },
  coinText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  avatarGroup: {
    flexDirection: 'row',
    marginLeft: 4,
  },
  miniAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginLeft: -4,
    borderWidth: 1,
    borderColor: colors.white,
  },
  liveIndicator: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 3,
    justifyContent: 'center',
  },
  liveText: {
    color: colors.white,
    fontSize: 20,
    fontWeight: '400',
  },
});
