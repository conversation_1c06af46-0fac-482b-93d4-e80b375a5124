import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { ToastConfig } from 'react-native-toast-message';
import { SCREEN_WIDTH, commonFontStyle, hp } from '../theme/fonts';
import { colors } from '../theme/colors';
import { zIndex } from '../utils/Filters';

export const toastConfig: ToastConfig = {
  success: ({ text1, type }: any) =>
    type === 'success' && (
      <SafeAreaView style={styles.toastContainer}>
        <View style={styles.textStyleToastSuccess}>
          <Text style={styles.textStyleToast}>{text1}</Text>
        </View>
      </SafeAreaView>
    ),

  error: ({ text1, type }: any) =>
    type === 'error' && (
      <SafeAreaView style={styles.toastContainer}>
        <View style={styles.toastStyle}>
          <Text style={styles.textStyleToast}>{text1}</Text>
        </View>
      </SafeAreaView>
    ),

  info: ({ text1, type }: any) =>
    type === 'info' &&
    text1 && (
      <SafeAreaView style={styles.toastContainer}>
        <View style={styles.toastContainerInfo}>
          <Text style={styles.textStyleToast}>{text1}</Text>
        </View>
      </SafeAreaView>
    ),
};

const styles = StyleSheet.create({
  toastStyle: {
    backgroundColor: 'red',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: SCREEN_WIDTH,
    zIndex: zIndex.level_10,
  },
  textStyleToastSuccess: {
    backgroundColor: 'green',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: SCREEN_WIDTH,
    zIndex: zIndex.level_10,
  },
  toastContainerInfo: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginHorizontal: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    bottom: hp(10),
    zIndex: zIndex.level_10,
  },
  textStyleToast: {
    ...commonFontStyle(500, 14, colors.white),
    textAlign: 'center',
  },
  toastContainer: {
    zIndex: zIndex.level_10,
  },
});
