// components/RedirectToHome.tsx
import React, { useEffect } from 'react';
import { View } from 'react-native';
import { navigateTo } from '../../utils/commonFunction';
import { SCREENS } from '../../navigation/screenNames';

const RedirectToHome = () => {
  useEffect(() => {
    navigateTo(SCREENS.HomeScreen);
  }, []);

  return <View style={{ flex: 1, backgroundColor: '#FFF' }} />;
};

export default RedirectToHome;
