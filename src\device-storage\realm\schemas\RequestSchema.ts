import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';

export class RequestSchema extends Realm.Object<IRequestData> implements IRequestData {
  chatSpaceId!: string;
  requesterId!: string;
  accepterId?: string;
  rejectedById?: string;
  requestName?: string;
  count?: number;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.request,
    primaryKey: 'chatSpaceId',
    properties: {
      chatSpaceId: 'string',
      requesterId: 'string',
      accepterId: 'string?',
      rejectedById: 'string?',
      requestName: 'string?',
      count: 'int?',
    },
  };
}

export interface IRequestData {
  chatSpaceId: string;
  requesterId: string;
  accepterId?: string;
  rejectedById?: string;
  requestName?: string;
  count?: number;
}
