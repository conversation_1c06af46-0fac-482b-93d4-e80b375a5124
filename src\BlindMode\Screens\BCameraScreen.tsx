// App.tsx
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, StatusBar } from 'react-native';
import { EntypoIcons, Ionicons } from '../../utils/vectorIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '../../theme/colors';
import CameraRenderer, { CameraRendererRef, ModeOptionsType } from '../Components/Camera';

import { GeneratedSummaryState, useWebRTCStream } from '../hooks/useWebrtchhook';

export default function BCameraScreen() {
  const [mode, setMode] = useState<'Photo' | 'Video'>('Photo');
  const CameraRendererRef = useRef<CameraRendererRef>(null);
  const [selectedModeOption, setSelectedModeOption] = useState<ModeOptionsType>();
  const { localStream, status, startStreaming, stopStreaming, flipCamera, generatedSummaryState } =
    useWebRTCStream();

  function handleModeOptions(option: ModeOptionsType, flipMode?: boolean) {
    if (flipMode) {
      setMode((prev) => (prev === 'Photo' ? 'Video' : 'Photo'));
    }
    setSelectedModeOption(option);
  }
  function flipMode() {
    setMode((prev) => (prev === 'Photo' ? 'Video' : 'Photo'));
    setSelectedModeOption(undefined);
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={'white'} barStyle="dark-content" translucent />
      <View
        style={{
          padding: 10,
          flexDirection: 'row',
          width: '100%',
          justifyContent: 'space-between',
          backgroundColor: 'white',
          alignItems: 'center',
        }}
      >
        <ModeSwitcher mode={mode} onChange={flipMode} />

        {/* Action Buttons */}
        <ActionButtons flipCamera={flipCamera} generatedSummaryState={generatedSummaryState} />
      </View>
      <CameraRenderer
        mode={mode}
        ref={CameraRendererRef}
        handleModeOptions={handleModeOptions}
        selectedModeOptionProp={selectedModeOption}
        localStream={localStream}
        status={status}
        startStreaming={startStreaming}
        stopStreaming={stopStreaming}
        generatedSummaryState={generatedSummaryState}
      />

      {/* Bottom Bar */}
      {/* <BottomBar /> */}
    </SafeAreaView>
  );
}

type ModeSwitcherProps = {
  mode: 'Photo' | 'Video';
  onChange: (mode: 'Photo' | 'Video') => void;
};

const ModeSwitcher: React.FC<ModeSwitcherProps> = ({ mode, onChange }) => (
  <View style={styles.switcherContainer}>
    {['Photo', 'Video'].map((m) => (
      <TouchableOpacity
        key={m}
        onPress={() => onChange(m as 'Photo' | 'Video')}
        style={{
          padding: 10,
          borderRadius: 25,
          backgroundColor: mode === m ? 'white' : 'transparent',
          paddingHorizontal: 20,
        }}
      >
        <Text style={[styles.switcherText, mode === m && styles.switcherActive]}>{m}</Text>
      </TouchableOpacity>
    ))}
  </View>
);

type ActionButtonsProps = {
  flipCamera: () => void;
  generatedSummaryState: GeneratedSummaryState;
};

const ActionButtons: React.FC<ActionButtonsProps> = ({ flipCamera, generatedSummaryState }) => (
  <View style={styles.actionContainer}>
    {/* <TouchableOpacity style={styles.actionButton}>
      <Ionicons name="document-text-outline" size={22} color="#333" />
    </TouchableOpacity> */}
    <TouchableOpacity style={styles.actionButton} onPress={flipCamera}>
      <Ionicons name="camera-reverse" size={22} color="#333" />
    </TouchableOpacity>
    {/* {generatedSummaryState.summary != '' && (
      <TouchableOpacity style={styles.actionButton} onPress={flipCamera}>
        <EntypoIcons name="sound" size={22} color="#333" />
      </TouchableOpacity>
    )} */}
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#EEF2FF', // bluish background
    justifyContent: 'space-between',
  },
  // Mode Switcher
  switcherContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,

    backgroundColor: colors._F4F4F4_gray,
    padding: 5,
    borderRadius: 50,
  },
  switcherText: {
    fontSize: 16,
    color: '#555',
  },
  switcherActive: {
    color: '#5A4BFF',
    fontWeight: 'bold',
  },
  // Action Buttons
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    margin: 16,
    gap: 12,
  },
  actionButton: {
    backgroundColor: colors._F4F4F4_gray,
    padding: 12,
    borderRadius: 50,
  },
  // Document Preview
  previewContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  previewImage: {
    width: '90%',
    height: '90%',
    borderRadius: 8,
  },
  // Bottom Bar
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    elevation: 5,
  },
  captureButton: {
    backgroundColor: '#5A4BFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
  },
  captureText: {
    color: '#fff',
    fontWeight: '600',
  },
  options: {
    flexDirection: 'row',
    gap: 16,
  },
  optionText: {
    color: '#333',
    fontSize: 14,
  },
});
