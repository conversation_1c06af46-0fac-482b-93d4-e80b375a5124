import { BottomTabBarProps, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TouchableOpacity, Text, View } from 'react-native';
import { colors } from '../../theme/colors';

import BCallScreen from '../Screens/BCallScreen';
import BlindHomeScreen from '../Screens/BHomeScreen';
import BCameraScreen from '../Screens/BCameraScreen';
import BSettingScreen from '../Screens/BSettingScreen';

import { EntypoIcons, Ionicons } from '../../utils/vectorIcons';

const Tab = createBottomTabNavigator();

function MyTabbar(props: BottomTabBarProps) {
  const Tabs = props.state.routes.map((route, index) => {
    const label = props.descriptors[route.key].options.tabBarLabel;
    const labelName = typeof label === 'string' ? label : 'Label';

    return (
      <TouchableOpacity
        key={index}
        onPress={() => props.navigation.navigate(route.name === 'BCameraScreenTab' ? "BScanScreen" : route.name)}
        style={{
          alignItems: 'center',
          gap: 5,
        }}
      >
        <View
          style={{
            backgroundColor: colors._F4F4F4_gray,
            height: 53, width: 53, justifyContent: 'center', alignItems: 'center',
            borderRadius: 10,
          }}
        >
          {getIcon(route.name, props.state.routes[props.state.index].name, 25)}
        </View>
        <Text
          style={{
            fontSize: 16,
            fontWeight: 'bold',
            color:
              props.state.routes[props.state.index].name === route.name
                ? colors.mainPurple
                : colors.gray_80,
          }}
        >
          {labelName}
        </Text>
      </TouchableOpacity>
    );
  });

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'center',
        padding: 10,
        gap: 28,
        backgroundColor: 'white',
      }}
    >
      {Tabs}
    </View>
  );
}

export function BTabNavigator() {
  return (
    <Tab.Navigator
      tabBar={(props) => <MyTabbar {...props} />}
      initialRouteName={'BHomeScreenTab'}
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
      }}
    >
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Home',
          headerShown: false,
        })}
        name={'BHomeScreenTab'}
        component={BlindHomeScreen}
      />
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Call',
          headerShown: false,
        })}
        name={'BCallScreenTab'}
        component={BCallScreen}
      />

      {/* call Screens  */}
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Scan',
          title: '',
          headerStyle: {
            shadowColor: colors.black,
            elevation: 50,
          },
        })}
        name={'BCameraScreenTab'}
        component={BCameraScreen}
      />
      <Tab.Screen
        options={({ navigation }) => ({
          tabBarLabel: 'Settings',
          headerShown: false,
        })}
        name={'BSettingScreenTab'}
        component={BSettingScreen}
      />
    </Tab.Navigator>
  );
}

function getIcon(route: string, selectedRoute: string, size?: number) {
  switch (route) {
    case 'BHomeScreenTab':
      return (
        <EntypoIcons
          name="home"
          color={selectedRoute === 'BHomeScreenTab' ? colors.mainPurple : colors.gray_80}
          size={size}
        />
      );
    case 'BCallScreenTab':
      return (
        <EntypoIcons
          name="phone"
          color={selectedRoute === 'BCallScreenTab' ? colors.mainPurple : colors.gray_80}
          size={size}
          style={{ transform: [{ rotateZ: '90deg' }] }}
        />
      );
    case 'BCameraScreenTab':
      return (
        <EntypoIcons
          name="camera"
          color={selectedRoute === 'BCameraScreenTab' ? colors.mainPurple : colors.gray_80}
          size={size}
          style={{ transform: [{ scaleX: -1 }] }}
        />
      );
    case 'BSettingScreenTab':
      return (
        <Ionicons
          name="settings"
          color={selectedRoute === 'BSettingScreenTab' ? colors.mainPurple : colors.gray_80}
          size={size}
        />
      );
    default:
      return null;
  }
}
