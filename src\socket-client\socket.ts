import messaging from '@react-native-firebase/messaging';
import { io, Socket } from 'socket.io-client';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './SocketHandler';
import { Client } from '../lib/Client';

let socket: Socket<any, any> | null = null;

// let socketURL = 'wss://server.chatbucket.chat/';
// let socketURL = 'https://test-server.chatbucket.chat/';
// let socketURL = 'ws://10.10.0.121:8088/';
let socketURL = 'ws://161.35.37.230:8081/';
// let socketURL = 'ws://10.98.98.188:8088/';
// let socketURL = 'ws://10.33.227.59:8088/';
// let socketURL = 'ws://10.20.20.47:8088/';
// let socketURL = 'ws://10.33.227.59:8088/';

// Initialize the socket connection with the access token
export const initSocket = (
  accessToken: string,
  lastSynced: string | null,
  fcmToken: string,
  reinitialize?: boolean,
): Socket<any, any> => {
  if (socket && !reinitialize) {
    console.log(`Returning existing socket instance:`, socket);
    return socket;
  }
  socket = io(socketURL, {
    query: {
      token: accessToken,
      fcmToken: fcmToken,
      lastSynced: lastSynced,
    },
    secure: true,
    transports: ['websocket', 'polling'],
  });
  console.log('Socket instance created:', socket);
  return socket;
};

export const initAuthSocket = (): Socket<any, any> => {
  socket = io(socketURL + '/auth', { transports: ['websocket'] });
  return socket;
};

export const getSocket = (): Socket<any, any> => {
  if (!socket) {
    throw new Error('Socket not initialized. Call initSocket first.');
  }
  return socket;
};

// Disconnect the socket and reset the instance
export const disconnectSocket = (): void => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

export const connectToSocketServer = async (reinitialize?: boolean) => {
  console.log('Connecting to socket server...');
  const socket = await initSocketAsync(reinitialize);
  handleSocketEvents(socket);
  return socket;
};

export const handleSocketEvents = (socket: Socket) => {
  socket.removeAllListeners();

  // Log incoming and outgoing events
  socket.onAny((event, ...args) => {
    console.log(`[incoming]: ${event}`, ...args);
  });
  socket.onAnyOutgoing((event, ...args) => {
    console.log(`[outgoing]: ${event}`, ...args);
  });

  // Handle socket events
  socket.on('connect', () => {
    SocketHandler.handleConnection();
  });

  socket.on('authenticated', SocketHandler.handleAuthentication);
  socket.on('auth_error', SocketHandler.handleAuthenticationError);
  socket.on('connect_error', SocketHandler.handleErrors);
  socket.on('ERROR', (data) => {
    console.log(data);
  });
  socket.on('disconnect', SocketHandler.handleDisconnection);

  return () => {
    socket.removeAllListeners();
  };
};

export const clearSocket = () => {
  if (socket) socket.disconnect();
  socket = null;
};

export const initSocketAsync = async (reinitialize?: boolean) => {
  const accessToken = (await Client.AuthToken.get()) || '';
  const fcmToken = await messaging().getToken();
  const lastSynced = (await Client.LastSyncedDate.get()) || '';
  const socket = initSocket(accessToken, lastSynced, fcmToken, reinitialize);
  return socket;
};

let videoSummarySocket: Socket<any, any> | null = null;

export const initVideoSummarySocket = () => {
  const url = 'http://10.10.0.203:8080/';
  // const url = 'http://192.168.1.4:8080/';
  const socket = io(url, { transports: ['websocket'] });
  videoSummarySocket = socket;

  return socket;
};

export const getVideoSummarySocket = () => {
  console.log(videoSummarySocket?.connected);
  if (!videoSummarySocket || !videoSummarySocket?.connected) {
    console.log('Initializing video summary socket...');
    const vidSummSocket = initVideoSummarySocket();
    return vidSummSocket;
  }
  console.log('Video summary socket already initialized.');
  return videoSummarySocket;
};

export const clearVideoSummarySocket = () => {
  if (videoSummarySocket) videoSummarySocket.disconnect();
  videoSummarySocket = null;
};

export type AppSocket = Socket<any, any>;
