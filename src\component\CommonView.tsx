import { SafeAreaView, ScrollView, StyleSheet, Text, View, ViewStyle } from 'react-native';
import React from 'react';
import GradientView from './GradientView';
import { AppStyles } from '../theme/appStyles';
import HeaderBackWithTitle from './HeaderBackWithTitle';
import { androidVersion, hp, statusBarHeight } from '../theme/fonts';
import { colors } from '../theme/colors';
import { SafeAreaView as PackgeSafeAreaView } from 'react-native-safe-area-context';

interface IProps {
  headerTitle?: string;
  headerColor?: string;
  renderRight?: () => void;
  children?: React.ReactNode;
  customHeader?: () => React.ReactNode;
  containerStyle?: ViewStyle;
  isScrollable?: boolean;
  contentContainerStyle?: ViewStyle;
  headerContainerStyle?: ViewStyle;
}

const CommonView = ({
  headerTitle,
  renderRight,
  children,
  customHeader,
  containerStyle,
  isScrollable,
  headerColor,
  contentContainerStyle,
  headerContainerStyle,
}: IProps) => {
  return (
    <GradientView color={headerColor}>
      <View style={[AppStyles.flex]}>
        <SafeAreaView>
          {customHeader ? (
            customHeader()
          ) : (
            <HeaderBackWithTitle
              title={headerTitle}
              renderRight={renderRight}
              containerStyle={[{ paddingHorizontal: 20 }, headerContainerStyle]}
            />
          )}
        </SafeAreaView>
        {isScrollable ? (
          <View
            style={[
              AppStyles.bottomWhiteViewWithoutPadding,
              {
                flex: 1,
                overflow: 'hidden',
                backgroundColor: colors._F6F6F6_gray,
                paddingHorizontal: 20,
                paddingTop: hp(2),
              },
              containerStyle,
            ]}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={contentContainerStyle}
            >
              {children}
            </ScrollView>
          </View>
        ) : (
          <View
            style={[
              AppStyles.bottomWhiteViewWithoutPadding,
              {
                flex: 1,
                paddingBottom: hp(2),
                overflow: 'hidden',
                backgroundColor: colors._F6F6F6_gray,
                paddingHorizontal: 20,
                paddingTop: hp(2),
              },
              containerStyle,
            ]}
          >
            {children}
          </View>
        )}
      </View>
      {/* </PackgeSafeAreaView> */}
    </GradientView>
  );
};

export default CommonView;

const styles = StyleSheet.create({});
