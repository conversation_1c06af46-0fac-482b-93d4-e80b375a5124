import { KeyboardAvoidingView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CommonView from '../../../component/CommonView';
import { hp } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import { AppStyles } from '../../../theme/appStyles';
import HorizontalListView from '../../../component/HorizontalListView';
import SearchInput from '../../../component/SearchInput';

import ChannelsScreen from '../Channels/ChannelsScreen';
import GroupsScreen from '../Groups/GroupsScreen';
import AllChatScreen from '../All/AllChatScreen';
import ChatsScreen from '../Chats/ChatsScreen';

type Props = {};

const NewMessageScreen = ({}: Props) => {
  const [userData, setUserData] = useState<any[]>();
  const tabs = ['All', 'Chats', 'Groups', 'Channels', 'My Family'];
  const [searchText, setSearchText] = useState('');
  const [selectedItem, setSelectedItem] = useState('All');

  // const renderRight = () => {
  //   return (
  //     <View>
  //       <Text style={{ color: colors.white, fontWeight: '600', fontSize: 15 }}>{'Select all'}</Text>
  //     </View>
  //   );
  // };

  return (
    <CommonView
      headerTitle="New Message"
      // renderRight={renderRight}
      containerStyle={{ backgroundColor: colors.white, paddingHorizontal: 0, paddingTop: 0 }}
    >
      <View style={styles.container}>
        <View style={styles.searchView}>
          <SearchInput value={searchText} onChangeText={setSearchText} />
        </View>
        <HorizontalListView
          data={tabs}
          defaultSelectedItem="All"
          selectedItem={setSelectedItem}
          contentContainerStyle={{
            paddingLeft: '5%',
            paddingRight: '2%',
            marginTop: hp(2),
            gap: 10,
          }}
          bottomLine={false}
          styleViewTwo={true}
        />

        {selectedItem === 'Chats' ? (
          <>
            <ChatsScreen searchText={searchText} />
          </>
        ) : selectedItem === 'Channels' ? (
          <>
            <ChannelsScreen searchText={searchText} />
          </>
        ) : selectedItem === 'Groups' ? (
          <>
            <GroupsScreen searchText={searchText} />
          </>
        ) : selectedItem === 'All' ? (
          <>
            <AllChatScreen searchText={searchText} />
          </>
        ) : null}

        {/* <KeyboardAvoidingView>
                    <View style={{ backgroundColor: '#ff0000', flex: 1, padding: 50 }}>

                    </View>
                </KeyboardAvoidingView> */}
      </View>
    </CommonView>
  );
};

export default NewMessageScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  searchView: {
    marginTop: hp(2),
    paddingHorizontal: hp(2),
  },
  innerContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  line: {
    width: 45,
    height: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 10,
    borderRadius: 10,
    alignSelf: 'center',
  },
});
