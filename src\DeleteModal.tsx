import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import ModalWrapper from './component/ModalWrapper';
import { hp } from './theme/fonts';
import { colors } from './theme/colors';
import ButtonPurple from './component/ButtonPurple';
import { t } from 'i18next';


interface DeleteConfirmModalProps {
  isVisible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  message?: string;
}

const DeleteConfirmModal = ({
  isVisible,
  onConfirm,
  onCancel,
  message = 'Are you sure you want to delete this chat?',
}: DeleteConfirmModalProps) => {
  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCancel}>
      <View style={styles.container}>
        <Text style={styles.messageText}>{message}</Text>
        <View style={styles.buttonRow}>
            <ButtonPurple
              extraStyle={{ flex: 1,backgroundColor:colors.gray_cc,marginRight: hp(2),}}
              titleColor={colors.white}
              title="Cancel"
              onPress={onCancel}
              textStyle={{ fontSize: 14 }}
            />
            <ButtonPurple
              extraStyle={{ flex: 1, backgroundColor: colors._F61B1B_red }}
              title="Delete"
              onPress={onConfirm}
              textStyle={{ fontSize: 14 }}
            />
        </View>
        </View>
    </ModalWrapper>
  );
};

export default DeleteConfirmModal;

const styles = StyleSheet.create({
  container: {
    paddingVertical: hp(2),
    paddingHorizontal: hp(1.5),
  },
  messageText: {
    fontSize: 16,
    color: colors.black,
    textAlign: 'left',
    marginBottom: hp(3),
  },
buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between', 
  },
   cancelButton: {
    flex: 1,
    backgroundColor: colors.gray_cc,
    marginRight: hp(2), // add gap between buttons
    width: 60,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: colors._F61B1B_red,
    width: 120,
  },

});
