import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { IUserPreferences } from '../device-storage/realm/schemas/UserPrefSchema';
import {
  getUserPreferences,
  updateAccountPreferences,
  updateChatPreferences,
  updatePrivacyPreferences,
  updateNotificationPreferences,
  updateFavoriteContacts,
} from '../api/Userservice/userPreferences.api';

export const isDeviceOnline = async (): Promise<boolean> => {
  try {
    const state = await NetInfo.fetch();
    return !!state.isConnected;
  } catch (error) {
    console.error('Network check failed:', error);
    return false;
  }
};

const getPrefKey = () => `user_preferences`;

export class UserPreferencesService {
  static async getUserPref(): Promise<IUserPreferences> {
    const key = getPrefKey();
    const stored = await AsyncStorage.getItem(key);
    if (!stored) {
      throw new Error(`UserPreferences not found in local storage`);
    }
    return JSON.parse(stored);
  }

  static async saveUserPref(userPref: IUserPreferences): Promise<void> {
    const key = getPrefKey();
    const serialized = JSON.stringify({
      ...userPref,
      updatedAt: Date.now(),
    });
    await AsyncStorage.setItem(key, serialized);
  }

  static async updateSection<K extends keyof IUserPreferences>(
    section: K,
    updates: Partial<IUserPreferences[K]>,
  ) {
    const prefs = await this.getUserPref();
    const currentSection = prefs[section];

    const updatedSection =
      currentSection && typeof currentSection === 'object' && !Array.isArray(currentSection)
        ? { ...currentSection, ...updates }
        : updates;

    const updated: IUserPreferences = {
      ...prefs,
      [section]: updatedSection as IUserPreferences[K],
      updatedAt: Date.now(),
    };

    await this.saveUserPref(updated);

    switch (section) {
      case 'account':
        await updateAccountPreferences(updated.account);
        break;
      case 'chats':
        await updateChatPreferences(updated.chats);
        break;
      case 'privacy':
        await updatePrivacyPreferences(updated.privacy);
        break;
      case 'notifications':
        await updateNotificationPreferences(updated.notifications);
        break;
      case 'favorites':
        // await updateFavoriteChatSpaces( updated.favorites.chatSpaces );
        await updateFavoriteContacts({ contacts: updated.favorites.contacts });
        break;
      case 'appLanguage':
        break;
      default:
        break;
    }
  }

  static async createUserPref(): Promise<IUserPreferences> {
    try {
      const isOnline = await isDeviceOnline();

      let userPref: IUserPreferences;
      if (!isOnline) {
        userPref = await this.getUserPref();
      } else {
        userPref = await getUserPreferences();
        await this.saveUserPref(userPref);
      }

      return userPref;
    } catch (err) {
      console.log('Failed to create/load user preferences:', err);
      throw err;
    }
  }

  static async clearUserPref(): Promise<void> {
    try {
      await AsyncStorage.removeItem(getPrefKey());
    } catch (err) {
      console.error('Failed to clear user preferences:', err);
      throw err;
    }
  }
}
