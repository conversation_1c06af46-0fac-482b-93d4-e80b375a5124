import React, { useEffect, useRef, useState } from 'react';
import { PermissionsAndroid, Platform, View, Text, StyleSheet } from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Geolocation from '@react-native-community/geolocation';
import { IMAGES } from '../../../assets/Images';
import { Image } from 'react-native';

const CurrentLocationMap = () => {
  const [region, setRegion] = useState(null);
  const mapRef = useRef(null);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      let granted = false;

      if (Platform.OS === 'android' && Platform.Version < 33) {
        const permissions = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        ]);

        granted =
          permissions['android.permission.ACCESS_FINE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_COARSE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED;
      } else {
        granted = true;
      }

      if (granted) {
        getCurrentLocation();
      } else {
        console.log('Location permission denied');
      }
    } catch (error) {
      console.log('Permission error:', error);
    }
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const region = {
          latitude,
          longitude,
          latitudeDelta: 0.002,
          longitudeDelta: 0.0,
        };
        setRegion(region);
        mapRef.current?.animateToRegion(region, 1000);
      },
      (error) => {
        console.log('Location error:', error);
      },
      { enableHighAccuracy: true, timeout: 15000 },
    );
  };

  return (
    <View style={styles.container}>
      {region ? (
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={StyleSheet.absoluteFillObject}
          region={region}
          showsUserLocation={false} // hide default blue dot
        >
          <Marker
            coordinate={region}
            anchor={{ x: 0.5, y: 1 }}
            style={{}}
            image={require('../../../assets/pin.png')} // <-- Use your marker icon here
          />
        </MapView>
      ) : (
        <Text style={styles.loadingText}>Fetching current location...</Text>
      )}
    </View>
  );
};

export default CurrentLocationMap;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    flex: 1,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 16,
  },
});
