import * as React from "react";
import Svg, { <PERSON>, <PERSON>, G, Path } from "react-native-svg";

function ContactAvatarSVG({ width = 44, height = 44, color = "#D7CEF2", ...props }) {
    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 44 44"
            fill="none"
            {...props}
        >
            <Circle cx={22} cy={22} r={22} fill="#F1ECFF" />
            <Mask
                id="mask0"
                maskUnits="userSpaceOnUse"
                x={0}
                y={0}
                width={44}
                height={44}
            >
                <Circle cx={22} cy={22} r={22} fill="#F5F1FF" />
            </Mask>
            <G mask="url(#mask0)" fill={color}>
                <Path d="M21.567 25.847a7.643 7.643 0 100-15.286 7.643 7.643 0 000 15.286zM37.136 40.014C37.136 42.979 32.6 44 21.85 44 11.1 44 6.562 42.98 6.562 40.014c0-6.497 3.401-11.102 10-13.211 1.497.921 3.282 1.447 5.287 1.447 2.006 0 3.79-.526 5.288-1.447 6.598 2.108 10 6.713 10 13.21z" />
            </G>
        </Svg>
    );
}

export default ContactAvatarSVG;
