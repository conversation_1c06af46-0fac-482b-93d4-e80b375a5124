import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import BellSVG from '../../../../assets/svgIcons/BellSVG';
import TimerSVG from '../../../../assets/svgIcons/TimerSVG';
import TranslateSVG from '../../../../assets/svgIcons/TranslateSVG';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { SCREENS } from '../../../../navigation/screenNames';
import { navigateTo } from '../../../../utils/commonFunction';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { colors } from '../../../../theme/colors';
import { useState } from 'react';
import ConversationMuteModal from './NotificationSettings/ConversationMuteModal';

type ConversationSettingsProps = {
  conversationInfo: ConversationInfo | null;
};

const ConversationSettings: React.FC<ConversationSettingsProps> = ({ conversationInfo }) => {
  const conversationId = conversationInfo?.id;

  const isP2P = conversationInfo?.type === ConversationType.P2P;
  const isGroup = conversationInfo?.type === ConversationType.GROUP;
  const isChannel = conversationInfo?.type === ConversationType.CHANNEL;
  const [showMuteModal, setShowMuteModal] = useState(false);

  return (
    <>
      <View style={styles.section}>
        {(isP2P || isGroup || isChannel) && (
          <>
            <TouchableOpacity style={styles.settingRow} onPress={() => setShowMuteModal(true)}>
              <BellSVG size={20} />
              <Text style={styles.settingLabel}>Notifications</Text>
            </TouchableOpacity>

            {/* <TouchableOpacity style={styles.settingRow}>
            <LockSVG size={20} color={colors.black_23} />
            <Text style={styles.settingLabel}>Chat Lock</Text>
          </TouchableOpacity> */}
            {conversationInfo?.type != ConversationType.CHANNEL && (
              <TouchableOpacity
                style={styles.settingRow}
                onPress={() =>
                  navigateTo(SCREENS.DisappearingMessagesScreen, {
                    conversationInfo: conversationInfo,
                  })
                }
              >
                <TimerSVG />
                <Text style={styles.settingLabel}>Disappearing Messages</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => navigateTo(SCREENS.TranslationMessages, { userData: conversationId })}
            >
              <TranslateSVG size={20} />

              <Text style={styles.settingLabel}>Translate Messages</Text>
            </TouchableOpacity>

            {/* {isUserAdminOrOwner && (liveRequestData?.count ?? 0) > 0 && (
            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => navigateTo(SCREENS.GroupRequestsScreen, { userData: user })}
            >
              <Text style={styles.settingLabel}>Join Requests</Text>
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{liveRequestData?.count}</Text>
              </View>
            </TouchableOpacity>
          )} */}
          </>
        )}

        {/* {isGroup && (
        <TouchableOpacity
          style={styles.settingRow}
          onPress={() => navigateTo(SCREENS.GroupInviteScreen, { userData: user })}
        >
          <LinkSVG size={20} color={colors.black_23} />
          <Text style={styles.settingLabel}>Invite people</Text>
        </TouchableOpacity>
      )} */}
      </View>
      <ConversationMuteModal
        conversationInfo={conversationInfo}
        isVisible={showMuteModal}
        onClose={() => setShowMuteModal(false)}
      />
    </>
  );
};

export default ConversationSettings;

const styles = StyleSheet.create({
  section: {
    marginBottom: 10,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: colors.black,
    fontWeight: '400',
  },
});
