import React, { useEffect, useRef } from 'react';
import { Animated, View, ViewStyle } from 'react-native';

interface WaveformProps {
  peaks: number[];
  barWidth?: number;
  barSpacing?: number;
  barColor?: string;
  playedColor?: string;
  height?: number;
  currentTime: number;
  duration: number;
  shouldAnimate: boolean; // NEW
}

const Waveform: React.FC<WaveformProps> = ({
  peaks = [],
  barWidth = 2,
  barSpacing = 4,
  barColor = 'blue',
  playedColor = 'red',
  height = 10,
  currentTime,
  duration,
  shouldAnimate = true,
}) => {
  const animatedValues = useRef<Animated.Value[]>([]);

  // Initialize animated values
  if (animatedValues.current.length !== peaks.length) {
    animatedValues.current = peaks.map(() => new Animated.Value(0));
  }

  // useEffect(() => {
  //     const progress = currentTime / duration;

  //     animatedValues.current.forEach((animatedValue, index) => {
  //         const barProgress = index / peaks.length;
  //         const toValue = barProgress <= progress ? 1 : 0;

  //         Animated.timing(animatedValue, {
  //             toValue,
  //             duration: 300,
  //             useNativeDriver: false,
  //         }).start();
  //     });
  // }, [currentTime, duration]);

  useEffect(() => {
    if (!shouldAnimate) return; // skip animation if paused

    const progress = currentTime / duration;

    animatedValues.current.forEach((animatedValue, index) => {
      const barProgress = index / peaks.length;
      const toValue = barProgress <= progress ? 1 : 0;

      Animated.timing(animatedValue, {
        toValue,
        duration: 300,
        useNativeDriver: false,
      }).start();
    });
  }, [currentTime, duration, shouldAnimate]);

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center', height }}>
      {peaks.map((value, index) => {
        const backgroundColor = animatedValues.current[index].interpolate({
          inputRange: [0, 1],
          outputRange: [barColor, playedColor],
        });

        const barStyle: ViewStyle = {
          width: barWidth,
          height: value * height,
          marginRight: barSpacing,
          borderRadius: 2,
          backgroundColor,
        };

        return <Animated.View key={index} style={barStyle} />;
      })}
    </View>
  );
};

export default Waveform;
