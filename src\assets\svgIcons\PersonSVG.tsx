import * as React from "react";
import Svg, { Circle, Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const PersonSVG: React.FC<SvgComponentProps & React.ComponentProps<typeof Svg>> = ({
    size = 26,
    color = "gray",
    ...props
}) => {

    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 26 26"
            fill="none"
            {...props}
        >
            <Circle opacity={0.5} cx={12.9993} cy={6.50033} r={4.33333} fill={color} />
            <Path
                opacity={0.5}
                d="M21.667 18.958c0 2.692 0 4.875-8.666 4.875-8.667 0-8.667-2.183-8.667-4.875s3.88-4.875 8.667-4.875c4.786 0 8.666 2.183 8.666 4.875z"
                fill={color}
            />
        </Svg>
    );

};

export default PersonSVG;


