import React, { useRef } from 'react';
import { TouchableOpacity, Animated, StyleSheet, ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { colors } from '../theme/colors';

interface Props {
  onRefresh: () => void;
  disabled?: boolean;
  style?: ViewStyle;
}

const RefreshButton: React.FC<Props> = ({ onRefresh, disabled, style }) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  const handlePress = () => {
    Animated.timing(rotateAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      rotateAnim.setValue(0); // Reset animation
    });

    onRefresh();
  };

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={disabled}
      style={[styles.buttonContainer, style]}
    >
      <Animated.View style={{ transform: [{ rotate }] }}>
        <Icon name="refresh" size={30} color={colors.white} />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default RefreshButton;

const styles = StyleSheet.create({
  buttonContainer: {
    width: 70,
    height: 70,
    backgroundColor: colors._7A5DCB_purple,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    shadowOffset: { width: 0, height: 2 },
    position: 'absolute',
    bottom: 30,
    left: '50%',
    transform: [{ translateX: -35 }],
  },
});
