import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface OutgoingSVGProps {
  size?: number;
  color?: string;
}

const OutgoingSVG: React.FC<OutgoingSVGProps> = ({
  size = 14,
  color = "#FF5050",
  ...props
}) => {
  return (
    <Svg
      width={(size * 9) / 12} 
      height={size}
      viewBox="0 0 9 12"
      fill="none"
      {...props}
    >
      <Path
        d="M1.465 2.004a.721.721 0 01.451-.33h0L6.08.557a.722.722 0 01.758.258h0c.058.075.1.16.125.252L8.08 5.236a.722.722 0 01-1.393.374l-.002-.005-.663-2.47-4.359 7.918-.001.003a.72.72 0 01-1.25-.719L4.78 2.4l-2.49.667h-.002a.721.721 0 01-.823-1.063z"
        fill={color}
        stroke={color}
        strokeWidth={0.3}
      />
    </Svg>
  );
};

export default OutgoingSVG;
