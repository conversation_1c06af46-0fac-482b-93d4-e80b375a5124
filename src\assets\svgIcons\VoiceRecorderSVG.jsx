import React from "react"
import Svg, { Circle, Path } from "react-native-svg"

function VoiceRecorderSVG({ size = 50, color = "#6A4DBB", background = "#fff", ...restProps }) {

    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 50 50"
            fill="none"
            {...restProps}
        >
            <Circle cx={25} cy={25} r={25} fill={color} />
            <Path
                d="M32.76 24v1c0 3.866-3.26 7-7.28 7m-7.28-8v1c0 3.866 3.26 7 7.28 7m0 0v3m0 0h3.12m-3.12 0h-3.12m3.12-6c-2.297 0-4.16-1.79-4.16-4v-6c0-2.21 1.863-4 4.16-4 2.298 0 4.16 1.79 4.16 4v6c0 2.21-1.862 4-4.16 4z"
                stroke={background}
                strokeWidth={1.5}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </Svg>
    )
}

export default VoiceRecorderSVG

