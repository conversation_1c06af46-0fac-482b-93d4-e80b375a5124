import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather'; // Using Feather icons as an example
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp, wp } from '../../../../theme/fonts';

// Define the shape of a single menu option
export type ModalOption = {
  label: string;
  icon: string;
  onPress: () => void;
  isDestructive?: boolean;
};

type MemberActionsModalProps = {
  isVisible: boolean;
  onClose: () => void;
  options: ModalOption[];
  position: { top?: number; bottom?: number; left?: number; right?: number };
};

const MemberActionsModal = ({ isVisible, onClose, options, position }: MemberActionsModalProps) => {
  return (
    <Modal visible={isVisible} transparent animationType="fade" onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={StyleSheet.absoluteFill}>
          <View style={[styles.modalContent, position]}>
            {options.map((option, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  option.onPress();
                  onClose();
                }}
                style={styles.optionRow}
              >
                <Icon
                  name={option.icon}
                  size={20}
                  color={option.isDestructive ? colors.red_ff4444 : colors.gray_80}
                />
                <Text style={[styles.optionText, option.isDestructive && styles.destructiveText]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    position: 'absolute',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: hp(1),
    width: wp(60),
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1.5),
    paddingHorizontal: hp(1),
  },
  optionText: {
    ...commonFontStyle(500, 15, colors.gray_80),
    marginLeft: wp(3),
  },
  destructiveText: {
    color: colors.red_ff4444,
  },
});

export default MemberActionsModal;
