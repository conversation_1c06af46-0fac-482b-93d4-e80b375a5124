import { StyleSheet, Text, ToastAndroid, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../theme/fonts';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import MessageStatusIcon from '../../../../component/PersonalChat/MessageStatusIcon';
import { dayPipe } from '../../../../utils/commonFunction';
import Sound from 'react-native-sound';
import Slider from '@react-native-community/slider';
import MediaPlaySVG from '../../../../assets/svgIcons/MediaPlaySVG';
import PauseSVG from '../../../../assets/svgIcons/PauseSVG';
import Waveform from '../../../../component/WaveForm';
import MusicSVG from '../../../../assets/svgIcons/MusicSVG';
import MicSVG2 from '../../../../assets/svgIcons/MicSVG2';
import { MessageStatus, MessageType } from '../../../../device-storage/realm/schemas/MessageSchema';
import LottieView from 'lottie-react-native';
import RNFS from 'react-native-fs';
import { ChatService } from '../../../../service/ChatService';
import { useMe } from '../../../../hooks/util/useMe';
import { useNetInfo } from '@react-native-community/netinfo';

interface IProps {
  isMyData: boolean;
  msgData: any;
  audioDuration?: number;
}

// Global state to track currently playing audio
let currentlyPlayingId: string | null = null;
let isStarted: boolean | null = false;
const activeComponents: Map<string, { stop: () => void; pause: () => void }> = new Map();

const AudioWavesComponent = ({ isMyData, msgData, audioDuration: propAudioDuration }: IProps) => {
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(1);
  const [audioDuration, setAudioDuration] = useState<number>(0);
  const [isSliding, setIsSliding] = useState<boolean>(false);
  const [hasStarted, setHasStarted] = useState<boolean>(false); // Track if playback has started
  const [stateLocalPath, setStateLocalPath] = useState<string>(msgData?.localPath ? msgData?.localPath : '');
  const [loading, setLoading] = useState<boolean>(false);
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;
  const playbackListener = useRef<any>(null);
  const componentId = useRef(`audio_${msgData?.localId}`).current;
  const { isAudioPlaying, pauseAudioPlaying } = useMe()
  const netInfo = useNetInfo()

  const peaks = Array.isArray(msgData?.audioWavesArray?.toJSON?.())
    ? msgData.audioWavesArray.toJSON()
    : [];

  function formatToMinutesSeconds(seconds: number) {
    if (typeof seconds !== 'number' || seconds < 0) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${('0' + mins).slice(-2)}:${('0' + secs).slice(-2)}`;
  }

  const getAudioDuration = (audioUrl: string) => {
    Sound.setCategory('Playback');
    const sound = new Sound(audioUrl, '', (error) => {
      if (error) {
        console.log('Error loading sound:', error);
        setAudioDuration(0);
        return;
      }
      const soundDuration = sound.getDuration();
      setAudioDuration(soundDuration);
      setDuration(soundDuration);
      sound.release();
    });
  };

  // Clean up all player resources
  const cleanupPlayer = async () => {
    try {
      if (playbackListener.current) {
        playbackListener.current.remove();
        playbackListener.current = null;
      }
      pauseAudioPlaying?.(false)
      await audioRecorderPlayer.stopPlayer();
      await audioRecorderPlayer.removePlayBackListener()
    } catch (error) {
      console.log('Cleanup error:', error);
    }
    setIsPlaying(false);
    setCurrentTime(0);
    setHasStarted(false);
  };

  // Register this component's controls
  useEffect(() => {
    const controls = {
      stop: cleanupPlayer,
      pause: async () => {
        await audioRecorderPlayer.pausePlayer();
        setIsPlaying(false);
      },
    };

    activeComponents.set(componentId, controls);

    return () => {
      // Cleanup when component unmounts
      activeComponents.delete(componentId);
      if (currentlyPlayingId === componentId) {
        currentlyPlayingId = null;
      }
      cleanupPlayer();
    };
  }, []);

  // Initialize duration
  useEffect(() => {
    if (msgData?.mediaUrl) {
      if (propAudioDuration && propAudioDuration > 0) {
        setAudioDuration(propAudioDuration);
        setDuration(propAudioDuration);
      } else {
        getAudioDuration(msgData.mediaUrl);
      }
    }
  }, [msgData?.mediaUrl, propAudioDuration]);

  // Stop other playing audios
  const stopOtherAudios = () => {
    if (currentlyPlayingId && currentlyPlayingId !== componentId) {
      const controls = activeComponents.get(currentlyPlayingId);
      if (controls) {
        controls.stop();
        isStarted = false;
      }
    }
    currentlyPlayingId = componentId;
  };

  const onStartPlay = async () => {
    try {
      stopOtherAudios();
      setHasStarted(true); // Mark as started when playback begins
      const localPath = `${RNFS.CachesDirectoryPath}/${msgData?.fileName}`;
      let playPath = stateLocalPath;

      if (!playPath || playPath.trim() === '') {
        if (!netInfo.isConnected) {
          return ToastAndroid.show("Please check your internet connection", ToastAndroid.SHORT)
        }
        setLoading(true);
        const download = await RNFS.downloadFile({
          fromUrl: msgData?.mediaUrl,
          toFile: localPath,
        }).promise;

        if (download.statusCode === 200) {
          ChatService.updateLocalPath(msgData?.globalId, localPath)
          playPath = localPath;
          setStateLocalPath(localPath)
          setLoading(false);
        } else {
          setLoading(false);
          ToastAndroid.show('Failed to download audio', ToastAndroid.SHORT);
          playPath = msgData?.mediaUrl
          return;
        }
      }

      await audioRecorderPlayer.startPlayer(playPath);
      await audioRecorderPlayer.seekToPlayer(Math.floor(currentTime * 1000));
      setIsPlaying(true);
      pauseAudioPlaying?.(true);

      playbackListener.current = audioRecorderPlayer.addPlayBackListener((e) => {
        // Only update if this is the currently playing audio
        if (currentlyPlayingId !== componentId) return;

        const curr = e.currentPosition / 1000;
        const dur = e.duration / 1000;

        if (!isSliding) {
          setCurrentTime(curr);
        }
        setDuration(dur);

        if (curr >= dur - 0.1) {
          cleanupPlayer();
          isStarted = false;
        }
      });
    } catch (error) {
      console.log('Error starting playback:', error);
      ToastAndroid.show('Network error', ToastAndroid.SHORT);
      setHasStarted(false);
      setIsPlaying(false);
    }
  };

  const onPausePlay = async () => {
    try {
      await audioRecorderPlayer.pausePlayer();
      setIsPlaying(false);
      pauseAudioPlaying?.(false)
      if (currentlyPlayingId === componentId) {
        currentlyPlayingId = null;
      }
    } catch (error) {
      console.log('Error pausing playback:', error);
    }
  };

  const togglePlayback = () => {
    if (isPlaying) {
      onPausePlay();
    } else {
      onStartPlay();
    }
  };

  const onSlidingStart = () => {
    setIsSliding(true);
    if (isPlaying) {
      onPausePlay();
    }
  };

  const onSlidingComplete = async (value: number) => {
    setIsSliding(false);
    setCurrentTime(value);
    try {
      await audioRecorderPlayer.seekToPlayer(Math.floor(value * 1000));
      if (!isPlaying) {
        onStartPlay();
      }
    } catch (error) {
      console.log('Error seeking:', error);
    }
  };

  const examplePeaks = [
    0.2, 0.5, 0.3, 0.6, 0.8, 0.4, 0.7, 0.9, 0.6, 0.3, 0.2, 0.5, 0.7, 0.4, 0.6, 0.3, 0.6, 0.8, 0.4,
    0.7, 0.9, 0.6, 0.3,
  ];

  const onValueChange = (value: number) => {
    if (isSliding) {
      setCurrentTime(value);
    }
  };

  const displayTime = () => {
    // Show current time if:
    // 1. Currently playing
    // 2. Or has started but paused (middle of playback)
    if (isPlaying || (hasStarted && currentTime > 0)) {
      return formatToMinutesSeconds(currentTime);
    }
    // Otherwise show total duration
    return formatToMinutesSeconds(duration);
  };

  const forceStopPlayer = async () => {
    try {
      if (playbackListener.current) {
        playbackListener.current.remove();
        playbackListener.current = null;
      }
      await audioRecorderPlayer.stopPlayer();
      await audioRecorderPlayer.removePlayBackListener(); // 🔑 remove ALL listeners
      pauseAudioPlaying?.(false);
    } catch (error) {
      console.log('Force stop error:', error);
    }
    setIsPlaying(false);
    setCurrentTime(0);
    setHasStarted(false);
  };


  useEffect(() => {
    if (!isAudioPlaying && isPlaying) {
      forceStopPlayer();
    }
  }, [isAudioPlaying]);


  // useEffect(() => {
  //   getAudioDuration(msgData?.mediaUrl);
  //   return () => {
  //     onStopPlay();
  //   };
  // }, []);

  return (
    <View
      style={[
        styles.fileContainer,
        {
          flex: 1,
          alignSelf: isMyData ? 'flex-end' : 'flex-start',
          backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
          borderTopLeftRadius: isMyData ? 16 : 1,
          borderBottomRightRadius: isMyData ? 1 : 16,
          minWidth: hp(28),
        },
      ]}
    >
      <View
        style={{
          paddingHorizontal: 10,
          paddingVertical: 5,
          backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
          borderRadius: 10,
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          gap: 8,
        }}
      >
        {msgData?.status == MessageStatus.UPLOADING || loading ? (
          <LottieView
            source={require('../../../../assets/animatedLottiImages/loader.json')}
            autoPlay
            loop
            style={{ width: 32, height: 32 }}
          />
        ) : msgData?.messageType === MessageType.AUDIO ? (
          <MusicSVG
            size={32}
            pathColor={isMyData ? colors._CDC5E8_purple : colors.gray_f3}
            backgroundColor={isMyData ? colors.mainPurple : colors._464646_gray}
          />
        ) : (
          <View
            style={{
              width: 32,
              height: 32,
              backgroundColor: isMyData ? colors.mainPurple : colors._464646_gray,
              borderRadius: 20,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <MicSVG2 size={13} color={isMyData ? colors._CDC5E8_purple : colors.gray_f3} />
          </View>
        )}
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity disabled={msgData?.status == MessageStatus.UPLOADING || loading} onPress={togglePlayback} style={{ marginRight: 6 }}>
              {!isPlaying ? (
                <MediaPlaySVG
                  size={28}
                  color={isMyData ? colors.mainPurple : colors._464646_gray}
                  backgroundColor={'transparent'}
                />
              ) : (
                <PauseSVG
                  size={28}
                  iconColor={isMyData ? colors.mainPurple : colors._464646_gray}
                  backgroundColor={'transparent'}
                />
              )}
            </TouchableOpacity>
            {/* <Waveform
              peaks={examplePeaks}
              barWidth={4}
              barSpacing={4}
              currentTime={currentTime}
              duration={duration}
              playedColor={isMyData ? colors.mainPurple : colors.black_23}
              barColor={isMyData ? colors._B2A8D0_gray : colors._C8C8C8_gray}
              height={40}
              shouldAnimate={isPlaying}
            /> */}
            <View
              style={{
                flex: 1,
                marginHorizontal: -10,
              }}
            >
              <Slider
                style={{
                  flex: 1,
                  width: '100%',
                  height: 20,
                }}
                minimumValue={0}
                maximumValue={duration || 1}
                value={currentTime}
                minimumTrackTintColor={isMyData ? colors._7A5DCB_purple : colors._464646_gray}
                maximumTrackTintColor={isMyData ? colors.mainPurple : colors._464646_gray}
                thumbTintColor={isMyData ? colors.mainPurple : colors._464646_gray}
                onSlidingStart={onSlidingStart}
                onSlidingComplete={onSlidingComplete}
                onValueChange={onValueChange}
              />
            </View>
          </View>

          <Text
            style={[
              styles.fileText,
              {
                color: isMyData ? colors._7A6A90_purple : colors._464646_gray,
                fontSize: 12,
                marginLeft: 6,
                // flex: 1,
              },
            ]}
            numberOfLines={1}
          >
            {msgData?.messageType === MessageType.AUDIO ? msgData?.fileName : 'Voice Message'}
          </Text>
        </View>
      </View>
      {/* <View style={{ flex: 1, marginHorizontal: 6 }}> */}

      {/* </View> */}

      <View
        style={{
          flexDirection: isMyData ? 'row' : 'row-reverse',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: 6,
        }}
      >
        <Text
          style={{
            color: isMyData ? colors._7A6A90_purple : colors.black,
            fontSize: 13,
            marginRight: 4,
          }}
        >
          {displayTime()}
        </Text>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={{
              color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
              fontSize: 12,
              marginRight: 4,
            }}
          >
            {dayPipe(msgData?.createdAt, 'time')}
          </Text>

          {isMyData && <MessageStatusIcon status={msgData?.status} />}
        </View>
      </View>
    </View>
  );
};

export default AudioWavesComponent;

const styles = StyleSheet.create({
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    width: hp(30),
    padding: 8,
    // width: '90%',
    borderRadius: 16,
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
});
