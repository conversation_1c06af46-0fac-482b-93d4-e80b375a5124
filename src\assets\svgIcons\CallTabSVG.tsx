import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface CallSVGProps {
  size?: number;
  color?: string;
}

const CallSVG: React.FC<CallSVGProps> = ({
  size = 23,
  color = "gray",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 23 23"
      fill="none"
      {...props}
    >
      <Path
        d="M17.981 23.001c-.02 0-.101 0-.115-.007C8.138 22.947.102 14.958 0 5.183A5.225 5.225 0 011.597 1.36 4.907 4.907 0 015.284.008c.175.013.351.027.527.06.291.048.582.122.86.224a.674.674 0 01.432.507l1.63 7.015a.713.713 0 01-.365.778l-1.833.968a12.44 12.44 0 006.853 6.88l.94-1.847a.673.673 0 01.758-.352l7.083 1.657c.223.054.419.223.493.44.102.284.176.575.224.872.04.264.067.535.067.806-.006 2.74-2.232 4.979-4.972 4.985zM4.986 1.354c-.92 0-1.786.345-2.462.987A3.876 3.876 0 001.346 5.17c.102 9.038 7.536 16.432 16.574 16.48 2.05-.008 3.673-1.638 3.673-3.633 0-.197-.013-.393-.047-.582v-.007a2.214 2.214 0 00-.06-.29l-6.197-1.455-.974 1.914a.688.688 0 01-.846.325c-3.917-1.373-7.07-4.533-8.422-8.463a.678.678 0 01.338-.825l1.914-1.008-1.434-6.17-.284-.06a3.727 3.727 0 00-.385-.041h-.21z"
        fill={color}
      />
    </Svg>
  );
};

export default CallSVG;
