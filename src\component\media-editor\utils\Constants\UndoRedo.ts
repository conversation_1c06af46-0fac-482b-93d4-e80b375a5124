import { useState, useCallback } from "react";

function useUndoRedo<T>(initialState: T) {
  const [past, setPast] = useState<T[]>([]);
  const [present, setPresent] = useState<T>(initialState);
  const [future, setFuture] = useState<T[]>([]);

  const canUndo = past.length > 0;
  const canRedo = future.length > 0;

  // Generic state update function (handles objects & arrays)
  const updatePresent = useCallback(
    (newState: T) => {
      setPast((prevPast) => [...prevPast, present]); // Save history
      setPresent(newState); // Update state
      setFuture([]); // Clear redo stack
    },
    [present]
  );

  const undo = useCallback(() => {
    if (canUndo) {
      setFuture((prevFuture) => [present, ...prevFuture]); // Move current state to future
      const previousState = past[past.length - 1];
      setPast((prevPast) => prevPast.slice(0, -1)); // Remove last state from past
      setPresent(previousState);
    }
  }, [past, present, canUndo]);

  const redo = useCallback(() => {
    if (canRedo) {
      setPast((prevPast) => [...prevPast, present]); // Move current state to past
      const nextState = future[0];
      setFuture((prevFuture) => prevFuture.slice(1)); // Remove first item from future
      setPresent(nextState);
    }
  }, [future, present, canRedo]);

  return { present, canUndo, canRedo, undo, redo, updatePresent };
}

export default useUndoRedo;
