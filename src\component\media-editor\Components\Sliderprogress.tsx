import React, { useEffect, useMemo, useState } from 'react';
import { Button, StyleSheet, TextInput, View } from 'react-native';
import { PanGestureHandler, GestureHandlerGestureEvent } from 'react-native-gesture-handler';
import Animated, {
  measure,
  runOnJS,
  runOnUI,
  useAnimatedGestureHandler,
  useAnimatedProps,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  useDerivedValue,
} from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

const SLIDER_WIDTH = 132;
const KNOB_RADIUS = 10;
const SLIDER_START = KNOB_RADIUS;
const SLIDER_END = SLIDER_WIDTH - KNOB_RADIUS;
const MAX_PROGRESS = 20;

const AnimatedPath = Animated.createAnimatedComponent(Path);

interface SliderProps {
  initialvalue: number;
  animationEnabled: boolean;
  onProgress?: (value: string) => void;
  strokeWidthFun?: (value: number) => void;
}

const springConfig = () => {
  'worklet';
  return {
    stiffness: 1000,
    damping: 500,
    mass: 2,
    overshootClamping: true,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  };
};

function Slider({ initialvalue = 10, animationEnabled, onProgress, strokeWidthFun }: SliderProps) {
  const getInitialTranslateXFromProgress = (progress: number) => {
    return SLIDER_START + ((SLIDER_END - SLIDER_START) * progress) / MAX_PROGRESS;
  };

  const translateX = useSharedValue(getInitialTranslateXFromProgress(initialvalue));

  const progress = useSharedValue(initialvalue.toString());

  const gestureHandler = useAnimatedGestureHandler<GestureHandlerGestureEvent, { startX: number }>({
    onStart: (_, ctx) => {
      ctx.startX = translateX.value;
    },
    onActive: (event: any, ctx) => {
      const nextX = ctx.startX + event.translationX;
      translateX.value = Math.min(Math.max(nextX, SLIDER_START), SLIDER_END);
    },
    onEnd: (event) => {
      const stepWidth = (SLIDER_END - SLIDER_START) / MAX_PROGRESS;
      let x = translateX.value;

      const clampedX = Math.min(Math.max(x, SLIDER_START), SLIDER_END);
      let snappedStep = Math.round((clampedX - SLIDER_START) / stepWidth);

      // Clamp snappedStep to [0, MAX_PROGRESS]
      snappedStep = Math.max(0, Math.min(snappedStep, MAX_PROGRESS));

      const finalX = SLIDER_START + snappedStep * stepWidth;

      translateX.value = withSpring(finalX, {
        ...springConfig(),
        velocity: Number(event.velocityX),
      });

      const newProgress = snappedStep.toString();
      progress.value = newProgress;

      if (onProgress) {
        runOnJS(onProgress)(newProgress);
      }

      if (strokeWidthFun) {
        const brushSize = finalX * 0.16;
        runOnJS(strokeWidthFun)(brushSize);
      }
    },
  });

  const animatedKnobStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value - KNOB_RADIUS }],
  }));

  const derivedPath = useDerivedValue(() => {
    const x = translateX.value;
    const control = x + (SLIDER_WIDTH - x) * 0.04;
    const tail = (SLIDER_WIDTH - x) * 0.0001;
    return `M0 8L${x} 0.321A5 5 0 0 1 ${control} 5.312v1.534a5 5 0 0 1-${tail} 3.998z`;
  });

  const animatedProps = useAnimatedProps(() => ({
    d: derivedPath.value,
  }));

  return (
    <View style={styles.sliderContainer}>
      <Svg width={SLIDER_WIDTH} height={12}>
        <Path d="M126.698.321A5 5 0 0 1 132 5.312v1.534a5 5 0 0 1-5.151 4.998L0 8z" fill="gray" />
        <AnimatedPath animatedProps={animatedProps} fill="#fff" />
      </Svg>

      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.knob, animatedKnobStyle]} />
      </PanGestureHandler>
    </View>
  );
}

export default Slider;

const styles = StyleSheet.create({
  sliderContainer: {
    width: SLIDER_WIDTH,
    height: 40,
    justifyContent: 'center',
    marginLeft: 13,
  },
  knob: {
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: 'white',
    position: 'absolute',
    top: '50%',
    marginTop: -9.8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
});
