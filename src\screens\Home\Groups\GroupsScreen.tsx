import { StyleSheet, Text, TouchableOpacity, View, ScrollView } from 'react-native';
import React, { useMemo, useRef, useState } from 'react';
import { colors } from '../../../theme/colors';
import { FeatherIcons } from '../../../utils/vectorIcons';
import { useTranslation } from 'react-i18next';
import { commonFontStyle } from '../../../theme/fonts';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import useConversations from '../../../hooks/conversations/useConversations';
import { SwipeListView } from 'react-native-swipe-list-view';
import ChatItemCard from '../All/ChatItemCard';
import { ChatScreenParams, IChatScreenProps } from '../Chats/ChatSpecificScreen';
import ExploreGroups from './ExploreGroups';

const GroupsScreen = ({ searchText }: { searchText: string }) => {
  const { t } = useTranslation();
  const { unarchivedGroups: groups, unarchivedMyGroups: myGroups } = useConversations();
  const swipeListRef = useRef<any>(undefined);

  const [isSelectionEnabled, setIsSelectionEnabled] = useState(false);
  const [selectedGroups, setSelectedGroups] = useState<any[]>([]);
  const [showAllMyGroups, setShowAllMyGroups] = useState(false);
  const [showAllGroups, setShowAllGroups] = useState(false);

  const isSearching = searchText?.trim().length > 0;

  const filteredMyGroups = useMemo(() => {
    if (!isSearching) return myGroups;
    const lower = searchText.toLowerCase();
    return myGroups.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower),
    );
  }, [myGroups, searchText, isSearching]);

  const filteredGroups = useMemo(() => {
    if (!isSearching) return groups;
    const lower = searchText.toLowerCase();
    return groups.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower),
    );
  }, [groups, searchText, isSearching]);

  const visibleMyGroups = useMemo(() => {
    if (isSearching) {
      return filteredMyGroups;
    }
    return showAllMyGroups ? filteredMyGroups : filteredMyGroups.slice(0, 5);
  }, [filteredMyGroups, showAllMyGroups, isSearching]);

  const visibleGroups = useMemo(() => {
    if (isSearching) {
      return filteredGroups;
    }
    return showAllGroups ? filteredGroups : filteredGroups.slice(0, 5);
  }, [filteredGroups, showAllGroups, isSearching]);

  const onSelect = (group: any) => {
    const isSelected = selectedGroups.some((c) => c.id === group.id);

    if (isSelectionEnabled) {
      setSelectedGroups((prev) => {
        if (isSelected) {
          const updated = prev.filter((c) => c.id !== group.id);
          if (updated.length === 0) {
            setIsSelectionEnabled(false);
          }
          return updated;
        } else {
          return [...prev, group];
        }
      });
    } else {
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
      const conversation =
        myGroups.find((conv: any) => conv.id === group.id) ||
        groups.find((conv: any) => conv.id === group.id);
      if (!conversation) return;
      const userData: IChatScreenProps = {
        displayName: group.displayName,
        displayPic: group.displayPic,
        chatSpaceId: group.chatSpaceId,
        type: group.type,
        id: group.id,
        isActive: group.isActive,
        conversation: conversation,
      };
      const convParams: ChatScreenParams = { convId: userData.id };
      navigateTo(SCREENS.ChatSpecificScreen, {
        userData,
        data: convParams,
      });
    }
  };

  const renderItem = ({ item }: any) => (
    <ChatItemCard
      type="group"
      item={item}
      selectedUser={selectedGroups}
      isSelectionEnabled={isSelectionEnabled}
      onSelect={onSelect}
    />
  );

  const ListHeader = () => (
    <TouchableOpacity
      style={styles.row}
      onPress={() => navigateTo(SCREENS.CreateGroupScreen, { _isType: 'group' })}
    >
      <View style={styles.imageContainer}>
        <FeatherIcons name="users" size={20} color={colors.white} />
      </View>
      <Text style={styles.groupText}>{t('New group')}</Text>
    </TouchableOpacity>
  );

  const combinedData: any[] = [];

  // Add a header (static, not swipeable)
  combinedData.push({ rowType: 'listHeader', id: 'listHeader' });

  // My Groups section
  if (visibleMyGroups.length > 0) {
    combinedData.push({ rowType: 'sectionHeader', id: 'myGroupsHeader', title: 'My Groups' });
    combinedData.push(
      ...visibleMyGroups.map((item) => ({ rowType: 'item', section: 'myGroups', ...item })),
    );
  }

  // Group Chats section
  if (visibleGroups.length > 0) {
    combinedData.push({ rowType: 'sectionHeader', id: 'groupsHeader', title: 'Group Chats' });
    combinedData.push(
      ...visibleGroups.map((item) => ({ rowType: 'item', section: 'groups', ...item })),
    );
  }

  // Explore Groups footer
  combinedData.push({ rowType: 'exploreGroups', id: 'exploreGroups' });

  return (
    <View style={styles.container}>
      {isSearching ? (
        <ScrollView style={styles.container}>
          <ListHeader />
          {filteredMyGroups.length > 0 && (
            <Text
              style={[styles.sectionTitle, { marginLeft: 21, marginBottom: 10, marginTop: 10 }]}
            >
              My Groups
            </Text>
          )}
          {filteredMyGroups.length > 0 &&
            filteredMyGroups.map((item) => (
              <ChatItemCard
                key={item.id}
                type="group"
                item={item}
                selectedUser={selectedGroups}
                isSelectionEnabled={isSelectionEnabled}
                onSelect={onSelect}
              />
            ))}
          {filteredGroups.length > 0 && (
            <Text
              style={[styles.sectionTitle, { marginLeft: 21, marginBottom: 10, marginTop: 10 }]}
            >
              Group Chats
            </Text>
          )}
          {filteredGroups.length > 0 &&
            filteredGroups.map((item) => (
              <ChatItemCard
                key={item.id}
                type="group"
                item={item}
                selectedUser={selectedGroups}
                isSelectionEnabled={isSelectionEnabled}
                onSelect={onSelect}
              />
            ))}
          <ExploreGroups searchText={searchText} />
        </ScrollView>
      ) : (
        <SwipeListView
          ref={swipeListRef}
          data={combinedData}
          keyExtractor={(item) => item.id.toString()}
          rightOpenValue={-120}
          disableRightSwipe
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            switch (item.rowType) {
              case 'listHeader':
                return <ListHeader />;

              case 'sectionHeader':
                return (
                  <View style={styles.sectionHeader}>
                    <Text style={styles.sectionTitle}>{item.title}</Text>
                    {item.title === 'My Groups' && myGroups.length > 5 && (
                      <TouchableOpacity onPress={() => setShowAllMyGroups(!showAllMyGroups)}>
                        <Text style={styles.showMoreText}>
                          {showAllMyGroups ? 'Show less' : 'Show more'}
                        </Text>
                      </TouchableOpacity>
                    )}
                    {item.title === 'Group Chats' && groups.length > 5 && (
                      <TouchableOpacity onPress={() => setShowAllGroups(!showAllGroups)}>
                        <Text style={styles.showMoreText}>
                          {showAllGroups ? 'Show less' : 'Show more'}
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                );

              case 'item':
                return renderItem({ item });

              case 'exploreGroups':
                return <ExploreGroups searchText={searchText} />;

              default:
                return null;
            }
          }}
        />
      )}
    </View>
  );
};

export default GroupsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    marginBottom: 50,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingLeft: 21,
    marginVertical: 10,
  },
  imageContainer: {
    backgroundColor: colors.mainPurple,
    borderRadius: 40,
    height: 50,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 21,
    marginVertical: 10,
  },
  sectionTitle: {
    color: colors.gray_80,
    fontSize: 16,
    fontWeight: '500',
  },
  showMoreText: {
    ...commonFontStyle(500, 14, colors.mainPurple),
  },
});
