import React, { useRef, useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  View,
  StyleSheet,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Dimensions,
} from 'react-native';

interface TimeWheelProps {
  value: number;
  onValueChange: (value: number) => void;
  type: 'hour' | 'minute';
  sideItemOpacity?: number;
}

const ITEM_HEIGHT = 80;
const SCREEN_HEIGHT = Dimensions.get('window').height;

export default function TimeWheel({
  value,
  onValueChange,
  type,
  sideItemOpacity = 0.3,
}: TimeWheelProps) {
  const listRef = useRef<FlatList<number>>(null);
  const [scrollY, setScrollY] = useState(0);

  const data =
    type === 'hour'
      ? Array.from({ length: 12 }, (_, i) => i + 1)
      : Array.from({ length: 60 }, (_, i) => i);

  useEffect(() => {
    const index = data.indexOf(value);
    if (index >= 0) {
      listRef.current?.scrollToIndex({ index, animated: false });
    }
  }, [value]);

  const handleScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    setScrollY(e.nativeEvent.contentOffset.y);
  };

  const handleScrollEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetY = e.nativeEvent.contentOffset.y;
    const index = Math.round(offsetY / ITEM_HEIGHT);
    const newValue = data[index];
    if (newValue !== value) {
      onValueChange(newValue);
    }
  };

  const renderItem = ({ item, index }: { item: number; index: number }) => {
    const display = item.toString().padStart(2, '0');

    const centerIndex = Math.round(scrollY / ITEM_HEIGHT);
    const isSelected = index === centerIndex;
    let opacity = 1;
    if (!isSelected) {
      if (Math.abs(index - centerIndex) === 1) {
        opacity = sideItemOpacity;
      } else {
        opacity = sideItemOpacity * 0.5;
      }
    }
    return (
      <View style={styles.itemContainer}>
        <Text style={[styles.itemText, isSelected && styles.selectedText, { opacity }]}>
          {display}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.wheelContainer}>
      <FlatList
        ref={listRef}
        data={data}
        keyExtractor={(item) => item.toString()}
        renderItem={renderItem}
        getItemLayout={(_, index) => ({
          length: ITEM_HEIGHT,
          offset: ITEM_HEIGHT * index,
          index,
        })}
        showsVerticalScrollIndicator={false}
        snapToInterval={ITEM_HEIGHT}
        decelerationRate="fast"
        onScroll={handleScroll}
        onMomentumScrollEnd={handleScrollEnd}
        scrollEventThrottle={16}
        bounces={false}
        contentContainerStyle={{
          paddingVertical: ITEM_HEIGHT,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  wheelContainer: {
    height: ITEM_HEIGHT * 3,
    overflow: 'hidden',
  },
  itemContainer: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemText: {
    fontSize: 48,
    color: '#888',
  },
  selectedText: {
    fontSize: 64,
    color: '#000',
    fontWeight: 400,
  },
});
