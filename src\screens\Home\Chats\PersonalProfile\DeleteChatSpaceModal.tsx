import {
  View,
  TouchableOpacity,
  Text,
  Image,
  ActivityIndicator,
  InteractionManager,
} from 'react-native';
import ModalWrapper from '../../../../component/ModalWrapper';
import { colors } from '../../../../theme/colors';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { useState } from 'react';
import { deleteChatSpace } from '../../../../api/Chatspace/chatspace.api';
import { ChatService } from '../../../../service/ChatService';
import { safeResetIfInsideDeletedChat, showToast } from '../../../../utils/commonFunction';
import { IMAGES } from '../../../../assets/Images';
import { useNavigation } from '@react-navigation/native';

type DeleteChatSpaceModalProps = {
  conversationInfo: ConversationInfo | null;
  isVisible: boolean;
  onClose: () => void;
};

const DeleteChatSpaceModal: React.FC<DeleteChatSpaceModalProps> = ({
  conversationInfo,
  isVisible,
  onClose,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const conversationId = conversationInfo?.id || '';
  const navigation = useNavigation();
  const isGroup = conversationInfo?.type === 'group';

  const handleDeleteGroup = async (chatSpaceId: string) => {
    try {
      setIsDeleting(true);
      const res = await deleteChatSpace(chatSpaceId);

      if (res) {
        InteractionManager.runAfterInteractions(() => {
          ChatService.deleteConversation(chatSpaceId);
        });
        safeResetIfInsideDeletedChat(chatSpaceId);
        navigation.pop(2);
        showToast(isGroup ? 'Group deleted successfully' : 'Channel deleted successfully');
        onClose();
      } else {
        showToast(isGroup ? 'Failed to delete group' : 'Failed to delete channel');
        console.error('Failed to delete group');
      }
    } catch (error) {
      console.error('Delete group error:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onClose}>
      <View>
        <Text style={{ fontSize: 16, fontWeight: '500', marginBottom: 16, color: colors.gray_80 }}>
          {isGroup ? 'Delete Group' : 'Delete Channel'}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
          <Image
            source={
              conversationInfo?.displayPic
                ? { uri: conversationInfo?.displayPic }
                : IMAGES.profile_image
            } // Replace with actual image
            style={{ width: 48, height: 48, borderRadius: 24, marginRight: 12 }}
          />
          <View>
            <Text style={{ fontSize: 15, fontWeight: '600', color: colors.black_23 }}>
              {conversationInfo?.displayName}
            </Text>
            <Text style={{ fontSize: 15, fontWeight: '400', color: '#666' }}>
              {conversationInfo?.type}
            </Text>
          </View>
        </View>

        <Text style={{ fontSize: 15, fontWeight: '400', marginBottom: 12, color: colors.black_23 }}>
          {isGroup
            ? 'Confirm deletion and exit from the group?'
            : 'Confirm deletion and exit from the Channel?'}
        </Text>

        <TouchableOpacity
          style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 20 }}
          // onPress={() => setDeleteForAll(prev => !prev)}
        >
          <View
            style={{
              width: 20,
              height: 20,
              borderRadius: 10,
              borderWidth: 1,
              borderColor: 'rgba(218, 218, 218, 1)',
              marginRight: 10,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: 'red' }} />
            {/* )} */}
          </View>
          <Text style={{ fontSize: 15, fontWeight: '400', color: colors.black_23 }}>
            {isGroup ? 'Delete the group for all members' : 'Delete the channel for all followers'}{' '}
          </Text>
        </TouchableOpacity>

        {/* Buttons */}
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'rgba(243, 243, 243, 1)',
              paddingVertical: 12,
              borderRadius: 15,
              marginRight: 10,
            }}
            onPress={onClose}
          >
            <Text
              style={{
                textAlign: 'center',
                color: colors.black_23,
                fontWeight: '500',
                fontSize: 16,
              }}
            >
              Cancel
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'rgba(236, 11, 11, 1)',
              paddingVertical: 12,
              borderRadius: 15,
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              opacity: isDeleting ? 0.7 : 1, // fade when loading
            }}
            disabled={isDeleting} // disable while deleting
            onPress={() => handleDeleteGroup(conversationId)}
          >
            {isDeleting ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <>
                {/* <View style={{ marginRight: 8 }}>
                      <DeleteSVG color={colors.white} size={20} />
                    </View> */}
                <Text
                  style={{
                    color: 'rgba(255, 255, 255, 1)',
                    fontWeight: '500',
                    fontSize: 16,
                  }}
                >
                  {isGroup ? 'Delete Group' : 'Delete Channel'}
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </ModalWrapper>
  );
};

export default DeleteChatSpaceModal;
