import React, { memo } from 'react';
import { View, StyleSheet } from 'react-native';
import StickerItem from '../StickerHandler/StickerItem';
import GestureHandler from '../GesturehandlerComponent/GestureHandler';
import FilterImgCanvas from '../FilterImgCanvas';

export interface Sticker {
  uri: string;
  id: string;
  initialX?: number;
  initialY?: number;
  rotate?: number;
  scale?: number;
  zIndex?: number;
}

export interface Image {
  uri: string;
  id: string;
  initialX?: number;
  initialY?: number;
  rotate?: number;
  scale?: number;
  zIndex?: number;
}

export interface Text {
  id: string | number;
  text: string;
  color: string;
  fontFamily?: string;
  assType?: number;
  initialX?: number;
  initialY?: number;
  rotate?: number;
  scale?: number;
}

interface RendererProps {
  stickers: Sticker[];
  images: Image[];
  texts: Text[];
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number | string, type: string, bool: boolean) => boolean;
  deleteItem: (id: number | string, type: string) => void;
  onTextPress: (id: string | number, text: string) => void;
  activeTextId: string | number | null;
}

type LayerItem = {
  id: string | number;
  zIndex?: number;
};

const ElementRenderer: React.FC<RendererProps> = ({
  stickers,
  images,
  texts,
  itemDragging,
  isDeletable,
  deleteItem,
  onTextPress,
  activeTextId,
}) => {
  function getTopZIndexItem(
    additionalImages: LayerItem[],
    stickers: LayerItem[],
    texts: LayerItem[],
  ) {
    const allItems: LayerItem[] = [...additionalImages, ...stickers, ...texts];
    if (allItems.length == 0) return null;

    return allItems.reduce((top, current) =>
      (current.zIndex ?? 0) > (top.zIndex ?? 0) ? current : top,
    );
  }

  let topItemzIdx: LayerItem | null = null;

  if (stickers.length > 0 || images.length > 0 || texts.length > 0) {
    topItemzIdx = getTopZIndexItem(images, stickers, texts);
  }

  return (
    <View style={styles.container}>
      {images.map((image, idx) => (
        <GestureHandler
          key={image.id}
          type="image"
          id={image.id}
          index={idx}
          itemDragging={itemDragging}
          yPos={image.initialY || 0}
          xPos={image.initialX || 0}
          isDeletable={isDeletable}
          rotate={image.rotate || 0}
          scale={image.scale || 1}
          zIndex={image.zIndex}
          topItemzIdx={topItemzIdx}
          deleteItem={deleteItem}
        >
          <FilterImgCanvas images={image} index={idx} />
        </GestureHandler>
      ))}
      {stickers.map((sticker, idx) => (
        <StickerItem
          key={sticker.id}
          index={idx}
          id={sticker.id}
          sticker={sticker}
          itemDragging={itemDragging}
          isDeletable={isDeletable}
          topItemzIdx={topItemzIdx}
          zIndex={sticker.zIndex}
          deleteItem={deleteItem}
        />
      ))}
      {texts.map((text, idx) => (
        <GestureHandler
          key={text.id}
          type="text"
          id={text.id}
          index={idx}
          itemDragging={itemDragging}
          yPos={text.initialY || 0}
          xPos={text.initialX || 0}
          isDeletable={isDeletable}
          rotate={text.rotate || 0}
          scale={text.scale || 1}
          zIndex={text.zIndex || 1}
          topItemzIdx={topItemzIdx}
          deleteItem={deleteItem}
          textProps={{
            text: text.text,
            color: text.color || '#000',
            fontSize: text.fontSize || 24,
            fontFamily: text.fontFamily || undefined,
            fontWeight: text.fontWeight || '600',
          }}
          isOkayPressed={activeTextId ? activeTextId == text.id : false}
          onSelectText={() => onTextPress(text.id, text.text)}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 202,
  },
});

export default memo(ElementRenderer);
