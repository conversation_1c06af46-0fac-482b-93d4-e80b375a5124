import { Realm, Results } from 'realm';
import { realmSchemaNames } from '../schemas/schemaNames';
import { ServerMessage } from '../../../types/socketPayload.type';
import {
  ContactMessageSchema,
  IContactMessage,
  ILocationMessage,
  IMessage,
  LocationMessageSchema,
  MessageEventType,
  MessageSchema,
  MessageStatus,
  MessageType,
} from '../schemas/MessageSchema';
import { getMe, getUser } from '../../../utils/ApiService';
import { ComposedMessage, IUser } from '../../../types/index.types';
import { toTimestampMillis } from '../../../lib/lib';
import { generateLocalMessageId, safeRealmWrite } from '../lib';
import { ConversationRepo } from './ConversationRepo';
import { t } from 'i18next';
import { ChatService } from '../../../service/ChatService';
import { UserSchema } from '../schemas/UserSchema';
import { UserRepo } from './UserRepo';

export class MessageRepo {
  private static schemaName = realmSchemaNames.message;
  constructor() {}

  // ========== utility functions below =========

  private static createEmbeddedLocation(realm: Realm, location?: ILocationMessage) {
    return location
      ? realm.create(LocationMessageSchema, location, Realm.UpdateMode.Never)
      : undefined;
  }

  private static createEmbeddedContact(realm: Realm, contact?: IContactMessage) {
    return contact
      ? realm.create(ContactMessageSchema, contact, Realm.UpdateMode.Never)
      : undefined;
  }

  // =========== utility functions above =========

  static findByGlobalId(realm: Realm, globalId: string): IMessage | undefined {
    return realm
      .objects(this.schemaName)
      .filtered('globalId == $0', globalId)[0]
      ?.toJSON() as unknown as IMessage;
  }

  static findRealmObjectByGlobalId(realm: Realm, globalId: string) {
    return realm.objects(MessageSchema).filtered('globalId == $0', globalId)[0];
  }

  static findRealmObjectByLocalId(realm: Realm, localId: string) {
    return realm.objectForPrimaryKey(MessageSchema, localId);
  }

  // !!make sure to use this method in a write transaction!!
  static saveOrUpdateIncomingMessage(
    realm: Realm,
    recievedMessage: ServerMessage & {
      translatedText?: string;
      sender: UserSchema;
    },
    conversationId: string,
  ) {
    const existingMessage = this.findRealmObjectByGlobalId(realm, recievedMessage._id);

    let newMsgData;
    if (existingMessage) {
      newMsgData = {
        ...existingMessage,
        status: recievedMessage.status,
        sender: recievedMessage.sender,
        text: recievedMessage.text,
        translatedText: recievedMessage.translatedText,
        mediaUrl: recievedMessage.mediaUrl,
        location:
          recievedMessage.location &&
          ({
            latitude: recievedMessage.location.latitude,
            longitude: recievedMessage.location.longitude,
            type: recievedMessage.location.type,
            ...(recievedMessage.location.type == 'live' && {
              expiresAt: recievedMessage.location.expiresAt,
              isStopped: recievedMessage.location.isStopped,
            }),
          } as LocationMessageSchema),
        contact:
          recievedMessage.contact &&
          ({
            name: recievedMessage.contact.name,
            phoneNumber: recievedMessage.contact.phoneNumber,
            ...(recievedMessage.contact.userId && { userId: recievedMessage.contact.userId }),
          } as ContactMessageSchema),
        deliveredAt: toTimestampMillis(recievedMessage.deliveredAt),
        seenAt: toTimestampMillis(recievedMessage.seenAt),
        isPinned: existingMessage.isPinned ? true : false,
        updatedAt: Date.now(),
      };
    } else {
      newMsgData = {
        ...recievedMessage,
        sender: recievedMessage.sender,
        receiverId: recievedMessage.receiverId,
        senderId: recievedMessage.senderId,
        localId: generateLocalMessageId(),
        globalId: recievedMessage._id,
        conversationType: recievedMessage.conversationType,
        conversationId,
        status: recievedMessage.status,
        messageType: recievedMessage.messageType,
        text: recievedMessage.text,
        translatedText: recievedMessage.translatedText,
        mediaUrl: recievedMessage.mediaUrl,
        location:
          recievedMessage.location &&
          ({
            latitude: recievedMessage.location.latitude,
            longitude: recievedMessage.location.longitude,
            type: recievedMessage.location.type,
            ...(recievedMessage.location.type == 'live' && {
              expiresAt: recievedMessage.location.expiresAt,
              isStopped: recievedMessage.location.isStopped,
            }),
          } as LocationMessageSchema),
        contact:
          recievedMessage.contact &&
          ({
            name: recievedMessage.contact.name,
            phoneNumber: recievedMessage.contact.phoneNumber,
            ...(recievedMessage.contact.userId && { userId: recievedMessage.contact.userId }),
          } as ContactMessageSchema),
        replyToMessageId: recievedMessage.replyToMessageId,
        deliveredAt: toTimestampMillis(recievedMessage.deliveredAt),
        seenAt: toTimestampMillis(recievedMessage.seenAt),
        isPinned: recievedMessage.isPinned ? true : false,
        eventType: recievedMessage.eventType,
        eventPayload: recievedMessage.eventPayload
          ? JSON.stringify(recievedMessage.eventPayload)
          : undefined,
        targetUserIds: recievedMessage.targetUserIds,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
    }

    return realm.create(MessageSchema, newMsgData, Realm.UpdateMode.Modified);
  }

  // !!make sure to use this method in a write transaction!!
  static saveOrUpdateSenderMessage(realm: Realm, message: ComposedMessage) {
    const newMsgData: Partial<MessageSchema> = {
      ...message,
      localId: generateLocalMessageId(),
      conversationId: message.receiverId,
      location:
        message.messageType === MessageType.LOCATION
          ? ({
              latitude: message.location.latitude,
              longitude: message.location.longitude,
              type: message.location.type,
              ...(message.location.type == 'live' && {
                expiresAt: message.location.expiresAt,
                isStopped: message.location.isStopped,
              }),
            } as LocationMessageSchema)
          : undefined,
      contact:
        message.messageType === MessageType.CONTACT
          ? ({
              name: message.contact.name,
              phoneNumber: message.contact.phoneNumber,
              ...(message.contact.userId && { userId: message.contact.userId }),
            } as ContactMessageSchema)
          : undefined,
      status: message?.isMedia ? MessageStatus.UPLOADING : MessageStatus.PENDING,
      eventPayload: undefined,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    if (message.messageType === MessageType.EVENT) {
      newMsgData.eventPayload = JSON.stringify(message.eventPayload);
    }
    return realm.create(
      MessageSchema,
      newMsgData,
      Realm.UpdateMode.Modified,
    ) as unknown as MessageSchema;
  }

  static edit(realm: Realm, message: ComposedMessage) {
    if (message.messageType !== MessageType.TEXT || !message.localId) return;
    const existingMessage = this.findRealmObjectByLocalId(realm, message.localId);
    if (!existingMessage) return null;
    safeRealmWrite(realm, () => {
      existingMessage.text = message.text;
      existingMessage.status = MessageStatus.PENDING;
      existingMessage.isTextEdited = true;
    });
    return existingMessage;
  }

  static deleteAll(realm: Realm) {
    realm.write(() => {
      realm.delete(realm.objects(MessageSchema));
    });
  }

  static deleteAllByConversationId(realm: Realm, conversationId: string) {
    const messages = realm.objects(MessageSchema).filtered('conversationId == $0', conversationId);
    if (messages.length === 0) return;
    realm.write(() => {
      messages.forEach((msg) => {
        msg.isCleared = true;
      });
    });
  }

  static findByLocalId(realm: Realm, localId: string) {
    const message = realm.objectForPrimaryKey(this.schemaName, localId);
    return message;
  }

  static ackSentMessage(realm: Realm, message: ServerMessage) {
    console.log('ackSentMessage-----------------', message);
    const existingMessage = this.findByLocalId(realm, message.senderLocalId);
    if (!existingMessage) {
      console.error(
        'Message not found, ensure you only ack the messages that are sent by you',
        message,
      );
      return;
    }

    let message_status = existingMessage.status;

    if (message_status === MessageStatus.SEEN || message_status === MessageStatus.DELIVERED) {
      console.log(`Message already marked ${message_status}.Skipping updating ack status`);
    } else {
      message_status = message.status;
    }

    // realm.write(() => {
    safeRealmWrite(realm, () => {
      realm.create(
        this.schemaName,
        {
          ...existingMessage,
          mentionedIds: message.mentionedIds,
          status: message_status,
          globalId: message._id,
        },
        Realm.UpdateMode.Modified,
      );
    });
    // });
  }

  static updateMessageStatus(realm: Realm, messageGlobalId: string, status: MessageStatus) {
    const message = this.findByGlobalId(realm, messageGlobalId);
    if (!message) {
      console.log('Message not found', messageGlobalId);
      return;
    }
    safeRealmWrite(realm, () => {
      realm.create(
        this.schemaName,
        {
          ...message,
          status,
        },
        Realm.UpdateMode.Modified,
      );
    });
  }

  static markMessageAsDelivered(realm: Realm, messageGlobalId: string) {
    const message = this.findByLocalId(realm, messageGlobalId);
    if (!message) {
      console.log('Message not found', messageGlobalId);
      return;
    }
    if (message.status === MessageStatus.DELIVERED || message.status === MessageStatus.SEEN) {
      return;
    }
    safeRealmWrite(realm, () => {
      realm.create(
        this.schemaName,
        {
          ...message,
          localId: message.localId,
          status: MessageStatus.DELIVERED,
          deliveredAt: Date.now(),
        },
        Realm.UpdateMode.Modified,
      );
    });
  }

  static markMessageAsSeen(realm: Realm, messageGlobalId: string) {
    const message = this.findByLocalId(realm, messageGlobalId);
    if (!message) {
      console.log('Message not found, trying to mark as seen', messageGlobalId);
      return;
    }

    if (message.status === MessageStatus.SEEN) {
      return;
    }

    safeRealmWrite(realm, () => {
      message.status = MessageStatus.SEEN;
      message.seenAt = Date.now();
    });
  }

  static findPendingMessages(realm: Realm) {
    return realm
      .objects(this.schemaName)
      .filtered('status == $0', MessageStatus.PENDING)
      .toJSON() as unknown as IMessage[];
  }

  static getAll(realm: Realm) {
    return realm.objects(this.schemaName);
  }

  static getMessagesNotSeenByMe(
    realm: Realm,
    conversationId: string,
    ownerId: string,
  ): Results<MessageSchema> {
    const messages = realm
      .objects(MessageSchema)
      .filtered(
        'conversationId == $0 && isSeenByMe == $1 && senderId != $2',
        conversationId,
        false,
        ownerId,
      );

    return messages;
  }

  static markMessagesAsSeenByMe(realm: Realm, conversationId: string, ownerId: string): string[] {
    const messages = realm
      .objects(MessageSchema)
      .filtered(
        'conversationId == $0 && isSeenByMe == $1 && senderId != $2',
        conversationId,
        false,
        ownerId,
      );

    if (messages.length === 0) return [];

    safeRealmWrite(realm, () => {
      messages.forEach((msg) => {
        msg.isSeenByMe = true;
      });
    });

    return messages.map((msg) => msg.globalId!);
  }

  static findRecentMessageByConversationId(realm: Realm, conversationId: string) {
    const messages = realm
      .objects(MessageSchema)
      .filtered('conversationId == $0', conversationId)
      .sorted('createdAt', true); // true = descending order

    return messages[0] || null;
  }

  static deleteManyByLocalId(realm: Realm, localIds: string[]) {
    if (!localIds.length) return;
    let globalIds: string[] = [];
    safeRealmWrite(realm, () => {
      const messagesToDelete = realm.objects(MessageSchema).filtered('localId IN $0', localIds);
      console.log('messagesToDelete', messagesToDelete.toJSON());
      globalIds = messagesToDelete.map((msg) => msg.globalId!);
      realm.delete(messagesToDelete);
    });
    return globalIds;
  }

  static toggleMsgPin(realm: Realm, messageId: string, unPinTime?: number | undefined) {
    const message = this.findRealmObjectByLocalId(realm, messageId);
    if (message) {
      safeRealmWrite(realm, () => {
        message.isPinned = message.isPinned ? false : true;
        message.unPinTime = unPinTime ? unPinTime : undefined;
      });
    }
  }

  static deleteManyByGlobalId(realm: Realm, globalIds: string[]) {
    if (!globalIds.length) return;
    safeRealmWrite(realm, () => {
      const messagesToDelete = realm.objects(MessageSchema).filtered('globalId IN $0', globalIds);
      realm.delete(messagesToDelete);
    });
  }

  static async updateMemberCounts(message: ServerMessage) {
    const { eventType, receiverId, targetUserIds } = message;
    const me = await getMe();
    const currentUserId = me._id;
    const isCurrentUserBeingAdded = targetUserIds?.includes(currentUserId);

    if (isCurrentUserBeingAdded) return;

    if (eventType === MessageEventType.USER_JOINED) {
      ChatService.updateMemberCount(receiverId, 1, 0);
    } else if (eventType === MessageEventType.USER_LEFT) {
      ChatService.updateMemberCount(receiverId, 0, 1);
    } else if (eventType === MessageEventType.MEMBER_ADDED) {
      ChatService.updateMemberCount(receiverId, targetUserIds?.length ?? 0, 0);
    } else if (eventType === MessageEventType.MEMBER_REMOVED) {
      ChatService.updateMemberCount(receiverId, 0, targetUserIds?.length ?? 0);
    }
  }

  static disappearMessagesAfetr(realm: Realm) {
    const now = Date.now();

    try {
      const expiredMessages = realm
        .objects(MessageSchema)
        .filtered('disappearAfter != null AND disappearAfter <= $0', now);

      if (expiredMessages.length > 0) {
        safeRealmWrite(realm, () => {
          realm.delete(expiredMessages);
        });
      }
    } catch (err) {
      console.error('Failed to delete disappearing messages', err);
    }
  }

  static updatePinStatus(
    realm: Realm,
    globalId: string,
    pinData: {
      status: boolean;
      userId: string;
      pinnedHours?: number;
    },
  ) {
    const message = this.findRealmObjectByGlobalId(realm, globalId);
    if (message) {
      safeRealmWrite(realm, () => {
        message.isPinned = pinData.status;
        if (pinData.status) {
          message.pinnedAt = Date.now();
          message.pinnedBy = pinData.userId;
          // Calculate expiry time based on hours
          message.pinnedUntil = Date.now() + (pinData.pinnedHours || 24) * 60 * 60 * 1000;
        } else {
          message.pinnedAt = undefined;
          message.pinnedBy = undefined;
          message.pinnedUntil = undefined;
        }
      });
    }
  }

  static getSavedMessages(realm: Realm, messageType: MessageType) {
    return realm
      .objects(MessageSchema)
      .filtered('isSaved == true AND messageType == $0', messageType)
      .sorted('createdAt');
  }
}
