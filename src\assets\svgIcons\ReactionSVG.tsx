import * as React from "react"
import Svg, { Path } from "react-native-svg"

function ReactionSVG({ size = 16, color = "#232323", ...props }) {
  const width = size;
  const height = (size * 16) / 17; // Maintain aspect ratio (original width: 17, height: 16)

  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 17 16"
      fill="none"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.737 7.543v-1.58a.395.395 0 01.79 0v1.58a.395.395 0 01-.79 0zm4.737 0v-1.58a.395.395 0 01.79 0v1.58a.395.395 0 01-.79 0zm-4.342 3.552a.395.395 0 010-.79h4.736a.395.395 0 010 .79H6.132zM1.789 8.332c0 3.7 3.01 6.71 6.711 6.71 3.7 0 6.71-3.01 6.71-6.71s-3.01-6.71-6.71-6.71-6.71 3.01-6.71 6.71zm-.789 0c0-4.135 3.365-7.5 7.5-7.5s7.5 3.365 7.5 7.5-3.365 7.5-7.5 7.5S1 12.467 1 8.332z"
        fill={color}
        stroke={color}
        strokeWidth={0.3}
      />
    </Svg>
  )
}

export default ReactionSVG
