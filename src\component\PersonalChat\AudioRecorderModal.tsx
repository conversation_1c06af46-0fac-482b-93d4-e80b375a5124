import {
  Image,
  ImageBackground,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Easing,
  TextInput,
  ScrollView,
  ToastAndroid,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import ModalWrapper from '../ModalWrapper';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import { IMAGES } from '../../assets/Images';
import { AppStyles } from '../../theme/appStyles';
import AudioRecorderPlayer, {
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  AVModeIOSOption,
  AudioEncoderAndroidType,
  AudioSet,
  AudioSourceAndroidType,
} from 'react-native-audio-recorder-player';
import Animated, {
  ReduceMotion,
  SlideInRight,
  ZoomIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import Voice from '@react-native-voice/voice';
import { useIsFocused } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import Api from '../../utils/api';
import { MessageType } from '../../device-storage/realm/schemas/MessageSchema';
import VoiceRecorderSVG from '../../assets/svgIcons/VoiceRecorderSVG';
import RefreshSVG from '../../assets/svgIcons/RefreshSVG';
import RecordingPlayPauseSVG from '../../assets/svgIcons/RecordingPlayPauseSVG';
import DeleteSVG from '../../assets/svgIcons/DeleteSVG';
import SendSVG from '../../assets/svgIcons/SendSVG';
import MediaPlaySVG from '../../assets/svgIcons/MediaPlaySVG';
import PauseSVG from '../../assets/svgIcons/PauseSVG';
import { formatBytes } from '../../utils/commonFunction';
import { ChatService } from '../../service/ChatService';
import { FontAwesome6Icons } from '../../utils/vectorIcons';
import { useMe } from '../../hooks/util/useMe';
import { useKeyboard } from '../../utils/useKeyboard';

type Props = {
  isVisible: boolean;
  onCloseModal: () => void;
  type?: 'chat' | 'channel';
  handleSendMessage?: (value: any) => void;
};

const audioRecorderPlayer = new AudioRecorderPlayer();

const AudioRecorderModal = ({
  isVisible,
  onCloseModal,
  type = 'chat',
  handleSendMessage = async () => {},
}: Props) => {
  const scrollRef = useRef<ScrollView>(null);
  const [scrollPos, setScrollPos] = useState(0);
  const [audioType, setAudioType] = useState('audio');
  const [startRecording, setstartRecording] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordSecs, setRecordSecs] = useState(0);
  const [recordTime, setRecordTime] = useState('');
  const [playTime, setPlayTime] = useState('');
  const [duration, setDuration] = useState('');
  const [voiceMsg, setVoiceMsg] = useState('');
  const [voiceText, setVoiceText] = useState('');
  const [voiceStarted, setvoiceStarted] = useState(false);

  const { t } = useTranslation();

  const customEasing = (value: number) => {
    'worklet';
    // Perform calculations here
    return value; // Ensure a number is returned
  };
  const [temp, setTemp] = useState([0]);
  const tempRef = useRef({ temp: [0], isPlaying: false });
  const width = useSharedValue(10);
  const isFocused = useIsFocused();
  const { keyboardHeight, keyboardVisible } = useKeyboard();

  const [isPaused, setIsPaused] = useState(false);
  const { isAudioPlaying, pauseAudioPlaying } = useMe();

  const onCloseAll = () => {
    setvoiceStarted(false);
    setVoiceText('');
    setstartRecording(false);
    setIsPlaying(false);
    setIsRecording(false);
    onStopPlay();
    onStopRecord();
    Voice.stop();
    Voice.destroy().then(Voice.removeAllListeners);

    tempRef.current = { temp: [0], isPlaying: false };
  };

  const millisecondsToTime = (milliseconds: any) => {
    var minutes = Math.floor(milliseconds / 60000);
    var seconds: any = ((milliseconds % 60000) / 1000).toFixed(0);
    return minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
  };

  const getPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);
        return granted['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const audioSet: AudioSet = {
    AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
    AudioSourceAndroid: AudioSourceAndroidType.MIC,
    AVModeIOS: AVModeIOSOption.measurement,
    AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
    AVNumberOfChannelsKeyIOS: 2,
    AVFormatIDKeyIOS: AVEncodingOption.aac,
  };

  const normalize = (metering: number) => {
    const minDb = -80; // You can clamp very low values
    const maxDb = 0;
    const clamped = Math.max(minDb, metering);
    return ((clamped - minDb) / (maxDb - minDb)) * 100; // scale to 0–100
  };

  const onStartRecord = async () => {
    const hasPermission = await getPermission();
    if (!hasPermission) {
      return;
    }
    setIsPlaying(false);
    tempRef.current = { temp: [0], isPlaying: true };
    const result = await audioRecorderPlayer.startRecorder(undefined, audioSet, true);

    audioRecorderPlayer.addRecordBackListener((e) => {
      if (tempRef.current.isPlaying && typeof e.currentMetering === 'number') {
        const height = Math.max(10, normalize(e.currentMetering) * 0.5); // between 10–50 px
        const temp1 = [...tempRef.current.temp, height];
        console.log('height', height);
        tempRef.current.temp = temp1;
        setTemp(temp1);

        width.value = width.value + 10 + 5;
      }

      setRecordSecs(e.currentPosition);
      setRecordTime(millisecondsToTime(e.currentPosition));
      return;
    });

    setIsRecording(true);
  };

  const onStopRecord = async () => {
    const result = await audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();
    setRecordSecs(0);
    setIsRecording(false);
    setIsPaused(false);
    console.log('onStopRecord---', result);
    setVoiceMsg(result);
    tempRef.current = { temp: [0], isPlaying: false };

    return result;
  };

  const onResumeRecord = async () => {
    console.log('Resuming recording...');
    const hasPermission = await getPermission();
    if (!hasPermission) return;

    try {
      await audioRecorderPlayer.resumeRecorder();

      audioRecorderPlayer.addRecordBackListener((e) => {
        console.log('e-->', e);
        setRecordSecs(e.currentPosition);
        setRecordTime(millisecondsToTime(e.currentPosition));
      });

      setIsRecording(true);
      setIsPaused(false);
    } catch (error) {
      console.error('Error resuming recording:', error);
    }
  };

  // Pause recording
  const onPauseRecord = async () => {
    console.log('Pausing recording...');
    try {
      await audioRecorderPlayer.pauseRecorder(); // Pauses the recorder

      setIsRecording(false); // Update the recording state
      setIsPaused(true); // Set the paused state
    } catch (error) {
      console.error('Error pausing recording:', error);
    }
  };

  const onStartPlay = async () => {
    const msg = await audioRecorderPlayer.startPlayer();

    audioRecorderPlayer.addPlayBackListener((e) => {
      setPlayTime(millisecondsToTime(e.currentPosition));
      setDuration(millisecondsToTime(e.duration));
      return;
    });
    console.log(msg);
    setIsPlaying(true);
  };

  const onStopPlay = async () => {
    audioRecorderPlayer.stopPlayer();
    audioRecorderPlayer.removePlayBackListener();
    setIsPlaying(false);
  };

  const style = useAnimatedStyle(() => {
    return {
      width: withTiming(
        width.value,
        {
          duration: 100,
          easing: customEasing,
          reduceMotion: ReduceMotion.Never,
        },

        () => {},
      ),
    };
  });

  useEffect(() => {
    onCloseAll();
  }, [isFocused]);

  useEffect(() => {
    //Setting callbacks for the process status
    Voice.onSpeechStart = onSpeechStart;
    Voice.onSpeechEnd = onSpeechEnd;
    Voice.onSpeechError = onSpeechError;
    Voice.onSpeechResults = onSpeechResults;
    Voice.onSpeechPartialResults = onSpeechPartialResults;

    return () => {
      //destroy the process after switching the screen
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const onSpeechStart = (e: any) => {
    //Invoked when .start() is called without error
    console.log('onSpeechStart: ', e);
  };

  const onSpeechEnd = (e: any) => {
    //Invoked when SpeechRecognizer stops recognition
    console.log('onSpeechEnd: ', e);
    setvoiceStarted(false);
  };

  const onSpeechError = (e: any) => {
    //Invoked when an error occurs.
    console.log('onSpeechError: ', e);
    setvoiceStarted(false);
  };

  const onSpeechResults = (e: any) => {
    setVoiceText((prv) => {
      return prv + ' ' + e.value[0];
    });
  };

  const onSpeechPartialResults = (e: any) => {};

  const startRecognizing = async () => {
    if (voiceStarted) {
      setvoiceStarted(false);
      // setVoiceText("");
      await Voice.stop();
    }

    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Microphone permission denied');
          return;
        }
      }

      try {
        await Voice.start('en-US');
      } catch (error) {
        console.log(error);
      }
      setvoiceStarted(true);
    } catch (e) {
      setvoiceStarted(false);
      //eslint-disable-next-line
      console.error(e);
    }
  };

  useEffect(() => {
    if (playTime == duration) {
      setIsPlaying(false);
    }
  }, [playTime, duration]);

  const onSendVoiceText = () => {
    if (type === 'channel') {
    } else {
      let messageData: { text: string; messageType: string } = {
        text: voiceText?.trim(),
        messageType: 'audioText',
      };
      handleSendMessage(messageData);
      // sendData(Emit_Event.SEND_MESSAGE, {
      //     userId: activeChat?.opponentDetails?._id,
      //     message: voiceText.trim(),
      //     messageType: "TEXT", // TEXT, IMAGE, VIDEO, MUSIC, LOCATION, DOCUMENT
      //     isSilent: isSilentMessage,
      // });
      // dispatchAction(dispatch, SET_SELETED_MESSAGE, undefined);
      // if (isSilentMessage) {
      //     dispatchAction(dispatch, IS_SILENT_MESSAGE, false);
      // }
    }
    onCloseAll();
    onCloseModal();
  };

  const onSendAudio = async () => {
    // Ensure recording is stopped before sending
    let voice = voiceMsg;

    if (isRecording || recordSecs > 0) {
      // If still recording or had recorded but not finalized
      voice = await onStopRecord();
    }

    // Also stop playback if it’s running
    if (isPlaying) {
      await onStopPlay();
    }

    onCloseModal();
    if (voice) {
      const obj = {
        uri: voice,
        type: 'audio/mp4',
        name: `${Date.now()}.mp4`,
      };
      let formData = new FormData();
      formData.append('file', obj);
      const floarTemp = temp.map(Math.floor);

      let messageData: {
        audioUrl: string;
        messageType: MessageType;
        temp: number[];
        isMedia: boolean;
      } = {
        audioUrl: '',
        messageType: MessageType.VOICE,
        temp: floarTemp,
        isMedia: true,
      };

      const sentData: any = await handleSendMessage(messageData);
      try {
        const link = await Api.post('upload', formData, true);
        if (link.body?.data?.url && link.body?.status) {
          await ChatService.updateMediaMessage(
            sentData?.localId,
            link.body?.data?.url,
            link.body?.data?.size,
          );
        } else {
          await ChatService.deleteMediaMessage([sentData?.localId]);
          ToastAndroid.show('Audio upload failed', ToastAndroid.SHORT);
        }
      } catch (error) {
        console.log('error audioModal', JSON.stringify(error, null, 2));
      }

      onCloseAll();
    }
  };

  useEffect(() => {
    if (isAudioPlaying) {
      pauseAudioPlaying?.(false);
    }
  }, [isAudioPlaying]);

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
      <View style={{ paddingBottom: keyboardVisible ? keyboardHeight : 0, flexGrow: 1 }}>
        <View>
          {!startRecording ? (
            <View>
              <Text
                style={{
                  marginBottom: 10,
                  marginTop: 5,
                  fontSize: 16,
                  fontWeight: '600',
                  color: colors.black_23,
                }}
              >
                {t('Sending options')}
              </Text>
              <View style={styles.tabView}>
                <TouchableOpacity
                  onPress={() => setAudioType('audio')}
                  style={[
                    styles.tabInnerView,
                    {
                      backgroundColor: audioType == 'audio' ? colors.white : colors.gray_f3,
                      // elevation: audioType == 'audio' ? 0.2 : 0,
                    },
                  ]}
                >
                  <Text style={{ fontSize: 16, fontWeight: '500', color: colors.black_23 }}>
                    {t('Only audio')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setAudioType('text')}
                  style={[
                    styles.tabInnerView,
                    {
                      backgroundColor: audioType !== 'audio' ? colors.white : colors.gray_f3,
                      // elevation: audioType !== 'audio' ? 0.2 : 0,
                    },
                  ]}
                >
                  <Text style={{ fontSize: 16, fontWeight: '500', color: colors.black_23 }}>
                    {t('Only text')}
                  </Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.bottomText}>{t('Tap to start recording')}</Text>
              <TouchableOpacity
                onPress={() => {
                  setstartRecording(true);
                  if (audioType == 'audio') {
                    // tempRef.current.isPlaying = !tempRef.current.isPlaying;
                    onStartRecord();
                  } else {
                    startRecognizing();
                  }
                }}
                style={styles.voiceView}
              >
                <VoiceRecorderSVG size={65} color={colors.mainPurple} background={colors.white} />
              </TouchableOpacity>
            </View>
          ) : audioType == 'audio' ? (
            <View>
              <Text style={[styles.title, { textAlign: 'center', marginTop: 10 }]}>
                {isRecording ? t('Recording..') : t('Paused')}
              </Text>
              <View style={styles.recordingView}>
                <TouchableOpacity
                  onPress={() => {
                    onStopRecord(), onStartRecord();
                  }}
                  style={styles.retakeView}
                >
                  <RefreshSVG color={colors.black_23} size={20} isReset={true} />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    if (isRecording) {
                      onPauseRecord(); // Pause if recording is active
                    } else if (isPaused) {
                      onResumeRecord(); // Resume if paused
                    } else {
                      onStopRecord();
                      onStartRecord();
                    }
                  }}
                >
                  <RecordingPlayPauseSVG
                    isRecording={isRecording}
                    color={colors.mainPurple}
                    size={125}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    onCloseAll();
                  }}
                  style={[styles.retakeView, { backgroundColor: colors.red_opacity_10 }]}
                >
                  <DeleteSVG color={colors._F61B1B_red} size={18} />
                </TouchableOpacity>
              </View>
              <View style={styles.footerView}>
                {!isRecording && (
                  <TouchableOpacity
                    onPress={() => {
                      if (isPlaying) {
                        onStopPlay();
                      } else {
                        console.log('callinggggggggg');
                        onStopRecord();
                        setTimeout(() => {
                          onStartPlay();
                        }, 200);
                      }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center', gap: 7 }}

                    // style={[styles.playView]}
                  >
                    {isPlaying ? (
                      <PauseSVG
                        size={38}
                        iconColor={colors.mainPurple}
                        backgroundColor={colors.white}
                      />
                    ) : (
                      <MediaPlaySVG
                        size={38}
                        color={colors.mainPurple}
                        backgroundColor={colors.white}
                      />
                    )}
                  </TouchableOpacity>
                )}
                <Text style={styles.timetext}>{isRecording ? recordTime : playTime}</Text>

                <View style={AppStyles.flex}>
                  <Animated.View
                    style={{
                      height: 50,
                      display: 'flex',
                      flexDirection: 'row-reverse',
                      flex: 1,
                    }}
                  >
                    <Animated.View
                      entering={SlideInRight}
                      style={[
                        {
                          flexDirection: 'row',
                          width: '50%',
                          gap: 3,
                          alignItems: 'center',
                          flex: 1,
                          overflow: 'hidden',
                        },
                        // style,
                      ]}
                    >
                      {temp.map((t, index) => (
                        <Animated.View
                          entering={ZoomIn}
                          key={index}
                          style={{
                            height: t,
                            width: 3,
                            borderRadius: 200,
                            backgroundColor: '#BDBDBD',
                          }}
                        />
                      ))}
                    </Animated.View>
                  </Animated.View>
                </View>
                <TouchableOpacity
                  // style={styles.sendView}
                  onPress={() => {
                    onSendAudio();
                  }}
                >
                  <SendSVG size={47} color={colors.white} backgroundColor={colors.mainPurple} />
                  {/* <Image source={IMAGES.sendButton} style={styles.innerIcon} /> */}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View>
              <Text style={[styles.title, { textAlign: 'center', marginTop: 10 }]}>
                {voiceStarted ? t('Recording..') : t('Paused')}
              </Text>
              <View style={styles.recordingView}>
                <TouchableOpacity
                  onPress={async () => {
                    setVoiceText('');
                    startRecognizing();
                  }}
                  style={styles.retakeView}
                >
                  <RefreshSVG color={colors.black_23} size={20} isReset={true} />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={async () => {
                    if (voiceStarted) {
                      await Voice.stop();
                    } else {
                      startRecognizing();
                    }
                  }}
                >
                  <RecordingPlayPauseSVG
                    isRecording={voiceStarted}
                    color={colors.mainPurple}
                    size={125}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={async () => {
                    onCloseAll();
                  }}
                  style={[styles.retakeView, { backgroundColor: colors.red_opacity_10 }]}
                >
                  <DeleteSVG color={colors._F61B1B_red} size={18} />
                </TouchableOpacity>
              </View>
              {voiceText && (
                <View>
                  <View style={[styles.voiceTextView, { maxHeight: 120, position: 'relative' }]}>
                    <ScrollView
                      ref={scrollRef}
                      showsVerticalScrollIndicator
                      onScroll={(e) => setScrollPos(e.nativeEvent.contentOffset.y)}
                      scrollEventThrottle={16}
                    >
                      <Text style={styles.voiceText}>{voiceText}</Text>
                    </ScrollView>
                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        padding: 6,
                        backgroundColor: colors.gray_f3,
                        borderRadius: 8,
                      }}
                      onPress={() => {
                        scrollRef.current?.scrollTo({
                          y: Math.max(scrollPos - 60, 0),
                          animated: true,
                        });
                      }}
                    >
                      <FontAwesome6Icons name="caret-up" size={15} color={colors.black_23} />
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        position: 'absolute',
                        bottom: 4,
                        right: 4,
                        padding: 6,
                        backgroundColor: colors.gray_f3,
                        borderRadius: 8,
                      }}
                      onPress={() => {
                        scrollRef.current?.scrollTo({ y: scrollPos + 60, animated: true });
                      }}
                    >
                      <FontAwesome6Icons name="caret-down" size={15} color={colors.black_23} />
                    </TouchableOpacity>
                  </View>
                  <View style={styles.line} />
                  <View style={styles.footerView}>
                    <TextInput
                      style={styles.inputView}
                      value={voiceText}
                      onChangeText={setVoiceText}
                      multiline={false}
                    />
                    <TouchableOpacity onPress={() => onSendVoiceText()}>
                      <SendSVG size={47} color={colors.white} backgroundColor={colors.mainPurple} />
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          )}
        </View>
      </View>
    </ModalWrapper>
  );
};

export default AudioRecorderModal;

const styles = StyleSheet.create({
  title: {
    // ...commonFontStyle(600, 16, colors.black_23),
    marginBottom: hp(1.5),
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '500',
  },
  tabView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray_f3,
    borderRadius: 15,
    padding: 8,
    height: 54,
  },
  tabInnerView: {
    height: 38,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  tabText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  voiceView: {
    alignSelf: 'center',
    marginBottom: 30,
  },
  sendView: {
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50 / 2,
    backgroundColor: colors.mainPurple,
  },
  microIcon: {
    height: 34,
    width: 34,
  },
  bottomText: {
    textAlign: 'center',
    color: colors.black_23,
    fontSize: 18,
    fontWeight: '600',
    marginTop: 45,
    marginBottom: 12,
  },
  recordingView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: hp(3),
    alignSelf: 'center',
    // marginTop: hp(2),
    marginBottom: hp(4),
  },
  middleIcon: {
    height: 120,
    width: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingIcon: {
    height: 33,
    width: 33,
    resizeMode: 'contain',
  },
  retakeView: {
    backgroundColor: colors.gray_f3,
    height: 46,
    width: 46,
    borderRadius: 46 / 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playView: {
    backgroundColor: colors.white,
    height: 40,
    width: 40,
    borderRadius: 40 / 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  footerView: {
    backgroundColor: colors.gray_f3,
    padding: 10,
    borderRadius: 15,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  timetext: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  voiceTextView: {
    padding: hp(2),
    borderRadius: 15,
    backgroundColor: colors.gray_f3,
  },
  voiceText: {
    ...commonFontStyle(400, 16, colors.black_23),
    overflow: 'hidden',
  },
  line: {
    height: 1,
    backgroundColor: colors._DADADA_gray,
    marginVertical: hp(2),
  },
  inputView: {
    flex: 1,
    maxHeight: 100,
    ...commonFontStyle(600, 16, colors.black_23),
  },
});
