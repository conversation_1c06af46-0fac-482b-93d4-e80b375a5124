import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../theme/fonts';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { MessageType } from '../../../../device-storage/realm/schemas/MessageSchema';
import MediaPlaySVG from '../../../../assets/svgIcons/MediaPlaySVG';
import PauseSVG from '../../../../assets/svgIcons/PauseSVG';
import Waveform from '../../../../component/WaveForm';
import DoubleTicksSVG from '../../../../assets/svgIcons/DoubleTicksSVG';
import { dayPipe } from '../../../../utils/commonFunction';
import DownloadSVG from '../../../../assets/svgIcons/DownloadSVG';
import { ChatService } from '../../../../service/ChatService';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import GalleryPlaneSVG from '../../../../assets/svgIcons/GalleryPlaneSVG';

interface IProps {
  isMyData: boolean;
  msgData: any;
  otheUserName: string;
  myUserId?: string;
  scrollToItem?: (value: any) => void;
}

const ReplyMessageCard = ({
  isMyData,
  msgData,
  otheUserName,
  myUserId,
  scrollToItem = () => { },
}: IProps) => {
  const replyedMsgData = ChatService.getMessageByGlobalId(msgData?.replyToMessageId);
  // console.log("🚀 ~ ReplyMessageCard ~ replyedMsgData:", JSON.stringify(replyedMsgData, null, 2))

  const isYourMsgReply = replyedMsgData?.senderId == myUserId;
  // console.log("🚀 ~ ReplyMessageCard ~ isYourMsgReply:", isYourMsgReply)

  return (
    <TouchableOpacity
      onPress={() => {
        scrollToItem(replyedMsgData?.localId);
      }}
    >
      <View
        style={[
          styles.fileContainer,
          {
            flex: 1,
            alignSelf: isMyData ? 'flex-end' : 'flex-start',
            backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
            borderTopLeftRadius: isMyData ? 16 : 1,
            borderBottomRightRadius: isMyData ? 1 : 16,
            minWidth: hp(28),
          },
        ]}
      >
        <View
          style={{
            paddingLeft: 11,
            backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
            borderRadius: 8,
            borderLeftColor: colors.mainPurple,
            borderLeftWidth: 4,
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <View
            style={{ paddingVertical: replyedMsgData?.messageType === MessageType.IMAGE ? 0 : 8 }}
          >
            <Text
              style={[
                styles.fileText,
                { color: colors.mainPurple, fontSize: 14, fontWeight: '800', marginBottom: 3 },
              ]}
            >
              {isYourMsgReply ? 'You' : otheUserName}
            </Text>
            {replyedMsgData?.messageType == MessageType.IMAGE ? (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                <GalleryPlaneSVG size={10} color={colors.black_23} />
                <Text numberOfLines={1} style={styles.lastMessage}>
                  Photo
                </Text>
              </View>
            ) : (
              <Text
                style={[styles.fileText, { color: colors.black, fontSize: 14 }]}
                numberOfLines={2}
              >
                {replyedMsgData?.text}
              </Text>
            )}
          </View>
          {replyedMsgData?.messageType == MessageType.IMAGE ? (
            <Image
              source={{ uri: replyedMsgData?.mediaUrl }}
              style={{ width: 49, height: 49, borderTopRightRadius: 8, borderBottomRightRadius: 8 }}
              resizeMode="cover"
            />
          ) : null}
        </View>

        {/* Right side: time + tick */}
        <View>
          <View style={{}}>
            {msgData?.messageType === MessageType.IMAGE ? (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                <GallerySVG size={14} color={colors.gray_80} />
                <Text numberOfLines={1} style={styles.lastMessage}>
                  Photo
                </Text>
              </View>
            ) : (
              <Text
                style={{
                  color: isMyData ? colors._7A6A90_purple : colors.black,
                  fontSize: 14,
                  marginVertical: 6,
                }}
              >
                {msgData?.text}
              </Text>
            )}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' }}>
            <Text
              style={{
                color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                fontSize: 12,
                marginRight: 4,
              }}
            >
              {dayPipe(msgData?.createdAt, 'time')}
            </Text>
            {isMyData && <DoubleTicksSVG size={18} color={colors._7A6A90_purple} />}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ReplyMessageCard;

const styles = StyleSheet.create({
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 16,
    // marginBottom: 22,
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  lastMessage: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
    // flexShrink: 1,
  },
});
