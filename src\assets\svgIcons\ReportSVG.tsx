import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

interface IconProps {
  size?: number;
  color?: string;
}

const ReportSVG: React.FC<IconProps> = ({
  size = 17,
  color = 'red',
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 16) / 17} 
      viewBox="0 0 17 16"
      fill="none"
      {...props}
    >
      <Path
        d="M0 6.488c0-.655.384-1.224.94-1.495a1.644 1.644 0 01-.232-.845c0-.71.45-1.319 1.084-1.557a1.644 1.644 0 01-.198-.784C1.594.89 2.346.143 3.27.143h7.604l1.982.742V0H17v11.08h-4.143v-.944h-.57l-3.129 3.592-.269 1.47a.982.982 0 01-.968.802h-.575a2.004 2.004 0 01-2.01-1.994c0-.691.134-1.366.398-2.005l.598-1.508H2.18A1.673 1.673 0 01.503 8.83c0-.344.106-.664.286-.93A1.662 1.662 0 010 6.49zm16.004-5.5h-2.151v9.103h2.15V.988zM2.18 9.505h5.612l-1.133 2.86-.003.008a4.245 4.245 0 00-.325 1.633 1.01 1.01 0 001.014 1.005h.565l.316-1.725 3.604-4.139h1.026V1.941l-2.164-.81H3.271a.68.68 0 00-.681.676.68.68 0 00.68.676h2.496v.989H2.385a.68.68 0 00-.681.676.68.68 0 00.68.676H4.88v.988H1.677a.68.68 0 00-.68.676.68.68 0 00.68.676H4.8v.989H2.18a.68.68 0 00-.68.676.68.68 0 00.68.676z"
        fill={color}
      />
    </Svg>
  );
};

export default ReportSVG;
