import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  LayoutAnimation,
  Platform,
  UIManager,
  Image,
} from 'react-native';
import { colors } from '../theme/colors';
import { hp } from '../theme/fonts';
import bottomArrow from '../assets/Icons/bottomArrow.png';

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

export interface AccordionItem {
  title: string;
  content: string;
}

interface AccordionProps {
  items: AccordionItem[];
  initiallyOpen?: number[];
}

const Accordion: React.FC<AccordionProps> = ({ items, initiallyOpen = [] }) => {
  const [openIndexes, setOpenIndexes] = useState<number[]>(initiallyOpen);

  const toggleSection = (index: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setOpenIndexes((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index],
    );
  };

  return (
    <View style={styles.container}>
      {items.map((item, idx) => (
        <View key={idx} style={[styles.section, idx !== items.length - 1 && styles.sectionBorder]}>
          <TouchableOpacity
            style={styles.header}
            onPress={() => toggleSection(idx)}
            activeOpacity={1}
          >
            <Text style={styles.title}>{item.title}</Text>
            <Image
              source={bottomArrow}
              style={[
                styles.arrowImg,
                openIndexes.includes(idx) && { transform: [{ rotate: '180deg' }] },
              ]}
              resizeMode="contain"
            />
          </TouchableOpacity>
          {openIndexes.includes(idx) && (
            <View style={styles.content}>
              <Text style={styles.answer}>{item.content}</Text>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: hp(1),
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionBorder: {
    borderBottomWidth: 0.5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(2),
    paddingHorizontal: hp(2),
    backgroundColor: colors._F6F6F6_gray,
  },
  title: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '600',
    flex: 1,
    letterSpacing: 0.1,
  },
  arrowImg: {
    width: 12,
    height: 12,
    marginLeft: 12,
  },
  content: {
    paddingHorizontal: hp(2),
    paddingBottom: hp(2),
    paddingTop: hp(1),
    backgroundColor: colors.overlayWhite_10,
  },
  answer: {
    fontSize: 15,
    color: colors.black_23,
    lineHeight: 22,
  },
});

export default Accordion;
