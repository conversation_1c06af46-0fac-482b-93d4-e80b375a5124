package com.chatbucket.filters

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

class FilteredVideoModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String = "FilteredVideoModule"

    @ReactMethod
    fun releaseVideo() {
        // This is a placeholder; actual cleanup would need to reference the FilteredVideoView instance
        // For now, rely on the ref approach in React Native
    }
}