import React, { useState, useEffect } from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Linking,
  Modal,
  PermissionsAndroid,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  ToastAndroid,
  TouchableOpacity,
  View,
} from 'react-native';
import MapView, { Callout, Circle, Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { commonColors, imagePaths } from '../../../constants';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome6 from 'react-native-vector-icons/FontAwesome6';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Geolocation from '@react-native-community/geolocation';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoadingModal from '../../../Components/LoadingModal';
const MapComponent = () => {
  const router = useRoute();
  const { siteDetails, locationType, buildingIndex } = router.params;
  const navigation = useNavigation();
  const mapRef = React.useRef(null);
  const [currentPosition, setCurrentPosition] = useState(null);
  const [address, setAddress] = useState('');
  const [markerPosition, setMarkerPosition] = useState(null);
  const [mapType, setMapType] = useState('standard');
  const [modalVisible, setModalVisible] = useState(false);
  const [towerLatlon, setTowerLatLon] = useState(null);
  const [savedlocations, setSavedLocations] = useState({});
  const [loading, setLoading] = useState(false);

  console.log('siteDetails', locationType, buildingIndex);

  const getSiteDetails = async () => {
    if (siteDetails) {
      setTowerLatLon({
        latitude: parseFloat(siteDetails.Latitude),
        longitude: parseFloat(siteDetails.Longitude),
        IPVendorName: siteDetails.IPVendorName,
        SiteType: siteDetails.SiteType,
        IPSiteID: siteDetails.IPSiteID,
      });
    }
  };

  // const getSiteDetails = async () => {
  //   let savedLocation = null;

  //   try {
  //     if (locationType === 'auto') {
  //       savedLocation = await AsyncStorage.getItem(`Saved_Locations/auto/${siteDetails.TaskID}`);
  //     } else if (locationType === 'representative') {
  //       savedLocation = await AsyncStorage.getItem(`Saved_Locations/representative/${siteDetails.TaskID}`);
  //     } else if (locationType === 'building') {
  //       savedLocation = await AsyncStorage.getItem(`Saved_Locations/building/${siteDetails.TaskID}/${buildingIndex}`);
  //     }

  //     if (savedLocation) {
  //       const parsedLocation = JSON.parse(savedLocation);
  //       setMarkerPosition({
  //         latitude: parsedLocation.latitude,
  //         longitude: parsedLocation.longitude,
  //       });
  //     }
  //   } catch (error) {
  //     console.log('Error retrieving saved location:', error);
  //   }
  // };

  useEffect(() => {
    requestLocationPermission();
    getSiteDetails();
  }, []);

  //request for permmistion of maps
  const requestLocationPermission = async () => {
    try {
      let granted = false;
      if (Number(Platform.Version) >= 33) {
        granted = true; // Assume 'granted' for newer versions if required logic applies
      } else {
        // Request multiple permissions for older Android versions
        const permissions = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
        ]);

        // Check the results of each permission
        granted =
          permissions['android.permission.ACCESS_FINE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_COARSE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_BACKGROUND_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED;
      }

      if (granted) {
        getCurrentLocation(); // Call this function if permission is granted
      } else {
        console.log('Location permission denied');
        // Optionally open settings if the permission is denied
        Linking.openSettings();
      }
    } catch (err) {
      console.log('Error requesting location permission:', err);
    }
  };

  // console.log(markerPosition);

  const getCurrentLocation = () => {
    try {
      Geolocation.getCurrentPosition(
        (info) => {
          const { latitude, longitude } = info.coords;
          // console.log('map', latitude, longitude, info);/
          setCurrentPosition({ latitude, longitude });
          setMarkerPosition({ latitude, longitude });
          updateAddress(latitude, longitude);
        },
        (error) => console.log(error),
        { enableHighAccuracy: false, timeout: 50000 },
      );
    } catch (error) {
      console.log('Catch block error:', error);
    }
  };

  //here the address is not showing jst showing the lat and lon of the moving marker
  const updateAddress = async (latitude, longitude) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=AIzaSyAjzJ24t6KAnbJ_H3buz96PHmnFeK9ZZ78`, //google map  key
      );
      const responseJson = await response.json();
      // console.log('responseJson', responseJson);
      setAddress(
        responseJson?.results[0]?.formatted_address ||
          (markerPosition
            ? `Latitude: ${markerPosition?.latitude.toFixed(
                5,
              )}, Longitude: ${markerPosition?.longitude.toFixed(5)}`
            : 'No address found!'),
      );
    } catch (error) {
      console.log(error);
    }
  };

  // Haversine formula to calculate the distance between two coordinates
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const toRadians = (degree) => (degree * Math.PI) / 180;
    const R = 6371e3; // Earth's radius in meters
    const φ1 = toRadians(lat1);
    const φ2 = toRadians(lat2);
    const Δφ = toRadians(lat2 - lat1);
    const Δλ = toRadians(lon2 - lon1);

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  };

  // Check distance and save location if within 100 meters
  // const checkDistanceAndSaveLocation = (latitude, longitude) => {
  //   if (towerLatlon) {
  //     const distance = calculateDistance(
  //       latitude,
  //       longitude,
  //       towerLatlon.latitude,
  //       towerLatlon.longitude,
  //     );

  //     if (distance <= 100 ||(siteDetails?.range === true&& distance > 60)) {
  //       saveLocation({latitude, longitude});
  //       navigation.goBack();
  //       console.log(`Location saved! Distance: ${distance.toFixed(2) , latitude,longitude} meters`);
  //     } else {
  //       Alert.alert(
  //         'Location Alert!',
  //         `Please ensure you are in ${
  //           siteDetails?.range ? '60' : '100'
  //         } meters range to invoke the survey!`,
  //         [
  //           {
  //             text: '',
  //             style: 'cancel',
  //           },
  //           {
  //             text: 'OK',
  //             onPress: () => {
  //               navigation.goBack();
  //             },
  //           },
  //         ],
  //         {cancelable: false},
  //       );
  //       console.log(`Distance is too far: ${distance.toFixed(2)} meters`);
  //     }
  //   }
  // };

  const checkDistanceAndSaveLocation = (latitude, longitude) => {
    if (towerLatlon) {
      const distance = calculateDistance(
        latitude,
        longitude,
        towerLatlon.latitude,
        towerLatlon.longitude,
      );

      console.log(`Calculated Distance: ${distance.toFixed(2)} meters for ${locationType}`);

      // ✅ Apply different distance rules based on location type
      const maxDistance = locationType === 'building' ? 60 : 100;

      if (distance <= maxDistance) {
        saveLocation({ latitude, longitude });
        setTimeout(() => {
          navigation.goBack();
        }, 800);
        console.log(
          `Location saved! Distance: ${distance.toFixed(2)} meters at ${latitude}, ${longitude}`,
        );
      } else {
        Alert.alert(
          'Location Alert!',
          `Please ensure you are within ${maxDistance} meters range to invoke the survey!`,
          [
            {
              text: '',
              style: 'cancel',
            },
            {
              text: 'OK',
              onPress: () => {
                navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );
        console.log(`Distance too far: ${distance.toFixed(2)} meters`);

        return;
      }
    }
  };

  // Save location function
  // const saveLocation = async location => {
  //   try {
  //     // Assuming you append the location to savedLocations state
  //     await AsyncStorage.setItem(
  //       `Saved_Locations/${siteDetails.TaskID}`,
  //       JSON.stringify(location),
  //     );
  //     console.log('Location saved successfully!', JSON.stringify(location));
  //   } catch (error) {
  //     console.log('Error saving location:', error);
  //   }
  // };
  const saveLocation = async (latitude, longitude) => {
    console.log('lat--', latitude, longitude, 'Type:', locationType, 'Building:', buildingIndex);

    try {
      let key = '';

      if (locationType === 'auto') {
        key = `Saved_Locations/auto/${siteDetails.TaskID}`;
      } else if (locationType === 'representative') {
        key = `Saved_Locations/representative/${siteDetails.TaskID}`;
      } else if (locationType === 'building' && buildingIndex !== null) {
        key = `Saved_Locations/building/${siteDetails.TaskID}/${buildingIndex}`;
      } else {
        console.log('Invalid locationType:', locationType);
        return;
      }

      await AsyncStorage.setItem(key, JSON.stringify({ latitude, longitude }));
      console.log(`Location saved successfully for ${locationType}:`, latitude, longitude);
    } catch (error) {
      console.log('Error saving location:', error);
    }
  };

  const centerOnCurrentLocation = async (current) => {
    if (towerLatlon) {
      const distance = calculateDistance(
        current && current?.latitude,
        current && current?.longitude,
        towerLatlon.latitude,
        towerLatlon.longitude,
      );

      if (distance > 100 || (siteDetails?.range === true && distance > 60)) {
        try {
          await AsyncStorage.removeItem(`Saved_Locations/${siteDetails.TaskID}`);
        } catch (error) {
          console.log('Error clearing savedLoaction:', error);
        }
        ToastAndroid.show(
          `Please ensure you are in ${
            siteDetails?.range ? '60' : '100'
          } meters range to invoke the survey!`,
          ToastAndroid.SHORT,
        );
      }
    }

    if (currentPosition) {
      mapRef.current.animateToRegion(
        {
          ...currentPosition,
          latitudeDelta: 0.0011,
          longitudeDelta: 0.0011,
        },
        1000,
      );
      setMarkerPosition(currentPosition);
    }
  };

  const towerLocation = () => {
    if (towerLatlon) {
      mapRef.current.animateToRegion(
        {
          ...towerLatlon,
          latitudeDelta: 0.0011,
          longitudeDelta: 0.0011,
        },
        1000,
      );
      setMarkerPosition(towerLatlon);
    }
  };

  //for the address based on the map drag
  const handleRegionChange = (region) => {
    setMarkerPosition({
      latitude: region?.latitude,
      longitude: region?.longitude,
    });
    updateAddress(region.latitude, region.longitude);
  };

  const initialRegion = {
    latitude: 17.4360302,
    longitude: 78.382893,
    latitudeDelta: 0.0011,
    longitudeDelta: 0.0011,
  };

  function mapTypesHandler(type) {
    setMapType(type);
    setModalVisible(false);
  }

  const getsavedLocation = async () => {
    // try {
    //   const savedLocation = await AsyncStorage.getItem(
    //     `Saved_Locations/${siteDetails.TaskID}`,
    //   );

    let savedLocation = null;

    try {
      if (locationType === 'auto') {
        savedLocation = await AsyncStorage.getItem(`Saved_Locations/auto/${siteDetails.TaskID}`);
      } else if (locationType === 'representative') {
        savedLocation = await AsyncStorage.getItem(
          `Saved_Locations/representative/${siteDetails.TaskID}`,
        );
      } else if (locationType === 'building') {
        savedLocation = await AsyncStorage.getItem(
          `Saved_Locations/building/${siteDetails.TaskID}/${buildingIndex}`,
        );
      }

      if (savedLocation) {
        const parseSavedLocation = JSON.parse(savedLocation);
        console.log('TCL: getsavedLocation -> parseSavedLocation', parseSavedLocation);
        setSavedLocations({
          savedLatitude: parseSavedLocation?.latitude?.latitude || '',
          savedLongitude: parseSavedLocation?.latitude?.longitude || '',
        });
      }
    } catch (error) {
      console.log('Error retrieving saved location:', error);
    }
  };

  useEffect(() => {
    getsavedLocation();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      setTimeout(() => {
        setLoading(false);
      }, 2000);
    }, []),
  );

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        // showsTraffic
        // showsUserLocation={true}
        onRegionChangeComplete={handleRegionChange}
        mapType={mapType}
        region={
          siteDetails?.iconMap === true
            ? siteDetails?.mapIconLat && siteDetails?.mapIconLong
              ? {
                  latitude: Number(siteDetails?.mapIconLat), // Convert to number
                  longitude: Number(siteDetails?.mapIconLong), // Convert to number
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.001,
                }
              : {
                  latitude: towerLatlon?.latitude,
                  longitude: towerLatlon?.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.001,
                }
            : currentPosition
            ? {
                latitude: currentPosition.latitude,
                longitude: currentPosition.longitude,
                latitudeDelta: 0.0011,
                longitudeDelta: 0.0011,
              }
            : initialRegion
        }
      >
        {towerLatlon && (
          <>
            <Circle
              center={towerLatlon}
              radius={siteDetails && siteDetails?.iconMap === false ? 60 : 100}
              strokeWidth={3}
              strokeColor={commonColors.orange}
              fillColor={'rgba(247, 162, 59,0.4)'}
            />
            <Circle
              center={towerLatlon}
              radius={siteDetails && siteDetails?.iconMap === false ? 100 : 0}
              strokeWidth={3}
              strokeColor={'rgb(115, 104, 237)'}
              fillColor={'rgba(115, 104, 237,0.4)'}
            />
          </>
        )}
        {/* this is for tower location click on tower icon */}
        <Marker
          image={imagePaths.tower}
          style={{
            display:
              (siteDetails.mapIconLatitude &&
                siteDetails.mapIconLongitude &&
                siteDetails?.iconMap === true) ||
              siteDetails?.iconMap === false
                ? ''
                : 'none',
          }}
          coordinate={{
            latitude: towerLatlon && towerLatlon.latitude,
            longitude: towerLatlon && towerLatlon.longitude,
          }}
          anchor={{ x: 0.45, y: 0.55 }}
        >
          {/* Custom Icon as a Child */}
          {/* <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 2},
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}>
            <MaterialIcons
              name="cell-tower"
              size={60}
              color={commonColors.ramboll}
            />
          </View> */}

          <Callout>
            <View style={{ width: '160%' }}>
              <Text style={[styles.markerTitleHeading, { fontSize: moderateScale(13) }]}>
                Current Site
              </Text>
              <Text style={styles.markerTitleHeading}>
                Infra Provider:
                <Text style={styles.markerTitle}>{towerLatlon?.IPVendorName}</Text>
              </Text>
              <Text style={styles.markerTitleHeading}>
                IP ID:
                <Text style={styles.markerTitle}>{towerLatlon?.IPSiteID}</Text>
              </Text>
              <Text style={styles.markerTitleHeading}>
                Site Type:
                <Text style={styles.markerTitle}>{towerLatlon?.SiteType}</Text>
              </Text>
              <Text style={styles.markerTitleHeading}>
                Latitude:
                <Text style={styles.markerTitle}>{towerLatlon?.latitude}</Text>
              </Text>
              <Text style={styles.markerTitleHeading}>
                Longitude:
                <Text style={styles.markerTitle}>{towerLatlon?.longitude}</Text>
              </Text>
            </View>
          </Callout>
        </Marker>

        {/* adjcent building */}
        {siteDetails.mapIconLatitude &&
          siteDetails.mapIconLongitude &&
          siteDetails?.iconMap === true && (
            // <Marker
            //   image={imagePaths.mapBuilding}
            //   coordinate={{
            //     latitude: siteDetails && siteDetails.mapIconLatitude,
            //     longitude: siteDetails && siteDetails.mapIconLongitude,
            //   }}
            //   anchor={{x: 0.5, y: 0.5}}>
            //   <Callout>
            //     <View style={{width: '160%'}}>
            //       <Text
            //         style={[
            //           styles.markerTitleHeading,
            //           {fontSize: moderateScale(13)},
            //         ]}>
            //         Adjcent Building
            //       </Text>

            //       <Text style={styles.markerTitleHeading}>
            //         Latitude:
            //         <Text style={styles.markerTitle}>
            //           {siteDetails &&
            //             Number(siteDetails.mapIconLatitude).toFixed(5)}
            //         </Text>
            //       </Text>
            //       <Text style={styles.markerTitleHeading}>
            //         Longitude:
            //         <Text style={styles.markerTitle}>
            //           {siteDetails &&
            //             Number(siteDetails.mapIconLongitude).toFixed(5)}
            //         </Text>
            //       </Text>
            //     </View>
            //   </Callout>
            // </Marker>

            // this is for showing the adjcent building on the map (click on builidng icon)
            <Marker
              coordinate={{
                latitude: siteDetails && siteDetails.mapIconLatitude,
                longitude: siteDetails && siteDetails.mapIconLongitude,
              }}
              anchor={{ x: 0.5, y: 0.5 }}
            >
              {/* Custom Icon as a Child */}
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}
              >
                <FontAwesome name="building-o" size={45} color={commonColors.ramboll} />
              </View>

              <Callout>
                <View style={{ width: '160%' }}>
                  <Text style={[styles.markerTitleHeading, { fontSize: moderateScale(10) }]}>
                    Adjacent Building
                  </Text>

                  <Text style={styles.markerTitleHeading}>
                    Lat:
                    <Text style={styles.markerTitle}>
                      {siteDetails && Number(siteDetails.mapIconLatitude).toFixed(6)}
                    </Text>
                  </Text>
                  <Text style={styles.markerTitleHeading}>
                    Long:
                    <Text style={styles.markerTitle}>
                      {siteDetails && Number(siteDetails.mapIconLongitude).toFixed(6)}
                    </Text>
                  </Text>
                </View>
              </Callout>
            </Marker>
          )}
      </MapView>

      {/* this is red marker on the map */}
      {siteDetails && siteDetails?.iconMap === false && markerPosition && (
        <>
          <View style={styles.customMarker}>
            <View
              style={{
                position: 'absolute',
                top: 36,
              }}
            >
              <View style={styles.calloutContainer}>
                <Text style={styles.calloutText}>
                  Latitude: {markerPosition?.latitude.toFixed(6)}, Longitude:
                  {markerPosition?.longitude.toFixed(6)}
                </Text>
              </View>
            </View>
            <Image source={imagePaths.mapPin} style={styles.markerImage} />
          </View>
        </>
      )}

      {/* this is the top card of the map */}
      <View style={{ paddingHorizontal: moderateScale(6) }}>
        <View style={styles.card}>
          {towerLatlon && (
            <Text style={styles.headings}>
              {`Location details of Site  (${towerLatlon.IPVendorName} - ${towerLatlon.IPSiteID} (${towerLatlon.SiteType}))`}
            </Text>
          )}

          <Text style={styles.desc}>
            from NEP Portal -
            {towerLatlon && (
              <Text style={styles.latLanTxt}>
                {towerLatlon.latitude.toFixed(6)},{towerLatlon.longitude.toFixed(6)}
              </Text>
            )}
          </Text>

          <Text style={styles.headings}>
            {siteDetails?.iconMap && siteDetails.mapIconLatitude && siteDetails.mapIconLongitude
              ? `Adj Building on Map (${Number(siteDetails.mapIconLatitude).toFixed(6)}, ${Number(
                  siteDetails.mapIconLongitude,
                ).toFixed(6)})`
              : siteDetails?.iconMap && siteDetails.mapIconLat && siteDetails.mapIconLong
              ? 'GPS Location on Map'
              : 'Current Location on Map'}

            {siteDetails?.iconMap === false ||
            (siteDetails.mapIconLat && siteDetails.mapIconLong) ? (
              <MaterialIcons name="push-pin" size={12} color={commonColors.red} />
            ) : (
              <FontAwesome name="building-o" size={16} color={commonColors.black} />
            )}
          </Text>

          <Text
            numberOfLines={1}
            style={[
              styles.headings,
              {
                textTransform: 'capitalize',
                color: commonColors.ramboll,
              },
            ]}
          >
            {siteDetails?.iconMap && siteDetails.mapIconLatitude && siteDetails.mapIconLongitude
              ? `Lat/Long as per survey (${Number(
                  siteDetails.Latitude || savedlocations.savedLatitude,
                ).toFixed(6)}, ${Number(
                  siteDetails.Longitude || savedlocations.savedLongitude,
                ).toFixed(6)})`
              : siteDetails?.iconMap && siteDetails.mapIconLat && siteDetails.mapIconLong
              ? `${siteDetails.mapIconLat},${siteDetails.mapIconLong}`
              : address}
          </Text>
        </View>
      </View>

      {/* This is for show and hide the icons on the map */}
      {siteDetails && siteDetails?.iconMap === false && (
        <View
          style={{
            position: 'absolute',
            bottom: 30,
            left: Dimensions.get('window').width / 3.5,
            // display: router.params ? 'none' : '',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: moderateScale(16),
            }}
          >
            <TouchableOpacity
              style={styles.smallIconsContainer}
              onPress={() =>
                checkDistanceAndSaveLocation(
                  currentPosition && currentPosition?.latitude,
                  currentPosition && currentPosition?.longitude,
                )
              }
            >
              <Entypo name="save" size={30} color={commonColors.white} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.smallIconsContainer}
              onPress={() => centerOnCurrentLocation(currentPosition && currentPosition)}
            >
              <MaterialIcons name="my-location" size={30} color={commonColors.white} />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.smallIconsContainer]} onPress={towerLocation}>
              <MaterialIcons name="cell-tower" size={30} color={commonColors.white} />
            </TouchableOpacity>
          </View>
        </View>
      )}
      {/* maptype */}
      <View
        style={{
          position: 'absolute',
          bottom: 100,
          right: Dimensions.get('window').width / 12,
        }}
      >
        <TouchableOpacity
          style={[styles.smallIconsContainer]}
          onPress={() => {
            setModalVisible(!modalVisible);
          }}
        >
          {/* <Image
            source={imagePaths.mapType}
            style={{width: 25, height: 25}}
            resizeMode="contain"
          /> */}
          <MaterialCommunityIcons name="layers" size={30} color={commonColors.white} />
        </TouchableOpacity>
      </View>
      {modalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            setModalVisible(false);
          }}
        >
          <Pressable
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
            onPress={() => setModalVisible(false)}
          >
            <Pressable
              onPress={() => setModalVisible(true)}
              style={{
                width: '80%',
                padding: moderateScale(16),
                backgroundColor: commonColors.white,
                borderRadius: moderateScale(4),
              }}
            >
              <View style={{ marginBottom: verticalScale(14) }}>
                <Text
                  style={{
                    fontSize: moderateScale(15),
                    fontWeight: 'bold',
                    color: commonColors.black,
                    marginLeft: moderateScale(5),
                  }}
                >
                  Map Types
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  gap: 60,
                }}
              >
                <TouchableOpacity
                  onPress={() => mapTypesHandler('standard')}
                  style={{
                    flexDirection: 'row',
                    gap: moderateScale(20),
                    alignItems: 'center',
                    marginBottom: verticalScale(16),
                  }}
                >
                  {mapType === 'standard' ? (
                    <MaterialIcons name="radio-button-checked" size={26} color="#009df0" />
                  ) : (
                    <MaterialIcons name="radio-button-off" size={26} color="#000" />
                  )}
                  <Text
                    style={{
                      color: '#000',
                      fontSize: moderateScale(13),
                      fontWeight: '600',
                      textTransform: 'capitalize',
                    }}
                  >
                    Default
                  </Text>
                </TouchableOpacity>
                <View>
                  <Image
                    source={imagePaths.Defaultmap}
                    style={{ width: 50, height: 50 }}
                    resizeMode="contain"
                  />
                </View>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  gap: 55,
                  marginVertical: 5,
                }}
              >
                <TouchableOpacity
                  onPress={() => mapTypesHandler('satellite')}
                  style={{
                    flexDirection: 'row',
                    gap: moderateScale(20),
                    alignItems: 'center',
                    marginBottom: verticalScale(16),
                  }}
                >
                  {mapType === 'satellite' ? (
                    <MaterialIcons name="radio-button-checked" size={26} color="#009df0" />
                  ) : (
                    <MaterialIcons name="radio-button-off" size={26} color="#000" />
                  )}
                  <Text
                    style={{
                      color: '#000',
                      fontSize: moderateScale(13),
                      fontWeight: '600',
                      textTransform: 'capitalize',
                    }}
                  >
                    Satellite
                  </Text>
                </TouchableOpacity>
                <View>
                  <Image
                    source={imagePaths.Satelitetmap}
                    style={{ width: 50, height: 50 }}
                    resizeMode="contain"
                  />
                </View>
              </View>
              <View style={{ flexDirection: 'row', gap: 60 }}>
                <TouchableOpacity
                  onPress={() => mapTypesHandler('terrain')}
                  style={{
                    flexDirection: 'row',
                    gap: moderateScale(20),
                    alignItems: 'center',
                    marginBottom: verticalScale(16),
                  }}
                >
                  {mapType === 'terrain' ? (
                    <MaterialIcons name="radio-button-checked" size={26} color="#009df0" />
                  ) : (
                    <MaterialIcons name="radio-button-off" size={26} color="#000" />
                  )}
                  <Text
                    style={{
                      color: '#000',
                      fontSize: moderateScale(13),
                      fontWeight: '600',
                      textTransform: 'capitalize',
                    }}
                  >
                    Terrain
                  </Text>
                </TouchableOpacity>
                <View>
                  <Image
                    source={imagePaths.Terrainmap}
                    style={{ width: 50, height: 50 }}
                    resizeMode="contain"
                  />
                </View>
              </View>
            </Pressable>
          </Pressable>
        </Modal>
      )}
      {loading && <LoadingModal visible={loading} title={'Loading...'} />}
    </View>
  );
};

export default MapComponent;
