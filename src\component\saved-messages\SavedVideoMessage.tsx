import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useMe } from '../../hooks/util/useMe';
import { IMessage } from '../../device-storage/realm/schemas/MessageSchema';
import Video from 'react-native-video';
import { colors } from '../../theme/colors';
import { IMAGES } from '../../assets/Images';
import CustomImage from '../../component/CustomImage';

type SavedVideoMessageProps = {
  message: IMessage;
  onPress?: () => void; // For selection handling
};

const TICK_SEEN = '✓✓';
const TICK_DELIVERED = '✓';

const SavedVideoMessage = ({ message, onPress }: SavedVideoMessageProps) => {
  const { user: me } = useMe();
  const isMyMsg = message.senderId === me?._id;

  // Format the timestamp as hh:mm am/pm
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12 || 12;
    return `${hours}:${minutes} ${ampm}`;
  };

  return (
    <TouchableOpacity
      style={[styles.messageRow, isMyMsg ? styles.rightAlign : styles.leftAlign]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.messageBubble, isMyMsg ? styles.myBubble : styles.otherBubble]}>
        {message.mediaUrl && (
          <View style={styles.videoContainer}>
            <Video
              source={{ uri: message.videoThumbnail || message.mediaUrl }}
              style={styles.videoThumb}
              resizeMode="cover"
              paused={true}
              controls={false}
            />
            {/* <CustomImage
              source={IMAGES.playIcon} // Replace with your play button icon
              size={30}
              containerStyle={styles.playIcon}
              tintColor={colors.white}
            /> */}
          </View>
        )}
        <View style={styles.statusRow}>
          <Text style={styles.timestamp}>{formatTime(message.createdAt)}</Text>
          {isMyMsg && (
            <Text style={styles.tick}>
              {message.seenAt ? TICK_SEEN : message.deliveredAt ? TICK_DELIVERED : ''}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  messageRow: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 10,
  },
  leftAlign: {
    justifyContent: 'flex-start',
  },
  rightAlign: {
    justifyContent: 'flex-end',
  },
  messageBubble: {
    maxWidth: '75%',
    borderRadius: 14,
    padding: 10,
    marginBottom: 2,
    elevation: 1,
  },
  myBubble: {
    backgroundColor: '#e7e9ef',
  },
  otherBubble: {
    backgroundColor: '#fff',
  },
  videoContainer: {
    position: 'relative',
    marginBottom: 5,
  },
  videoThumb: {
    width: 150,
    height: 150,
    borderRadius: 8,
    backgroundColor: '#ddd',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: 4,
    gap: 8,
    justifyContent: 'flex-end',
  },
  timestamp: {
    fontSize: 11,
    color: '#868995',
    marginRight: 2,
  },
  tick: {
    fontSize: 14,
    color: '#7c7ca2',
    marginLeft: 3,
  },
});

export default SavedVideoMessage;
