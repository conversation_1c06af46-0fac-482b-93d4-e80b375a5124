import { getCallInfoApi } from '../api/calls/calls.api';

import {
  CallHistoryItem,
  CallHistoryStatus,
  CallSchema,
} from '../device-storage/realm/schemas/CallSchema';

import CallService from '../service/CallService';
import {
  CallDetails,
  CallOrigin,
  incomingCallData,
  RoomType,
  StartCallRes,
} from '../types/calls.types';
import { Socket } from 'socket.io-client';
import { requestCameraPermission, requestMicrophonePermission } from '../utils/usePermission';
import { ToastAndroid } from 'react-native';

type PersistedCallData = {
  roomId: string;
  startedAt: Date;
};

export const persistCallData = async (callDetails: PersistedCallData) => {
  // localStorage.removeItem("currentCallDetails");
  // localStorage.setItem("currentCallDetails", JSON.stringify(callDetails));
};

export const getPersistedCallDetails = () => {
  // const storedCallDetails = localStorage.getItem('currentCallDetails');
  // if (storedCallDetails) {
  //   const persistedCallDetails: PersistedCallData =
  //     JSON.parse(storedCallDetails);
  //   return persistedCallDetails;
  // }

  return null;
};

export const clearPersistedCallDetails = () => {
  // localStorage.removeItem('currentCallDetails');
};

export const verifyPersistedCallStatus = (
  socket: Socket,
): Promise<{ status: boolean; room?: RoomType }> => {
  return new Promise((resolve, rej) => {
    const persistedCallDetails = getPersistedCallDetails();
    if (!persistedCallDetails) {
      return resolve({ status: false });
    }
    // socket.emit(
    //   callSocketEvents.CALL_STATUS,
    //   {roomId: persistedCallDetails.roomId},
    //   (data: {status: boolean; room?: any; message?: string}) => {
    //     if (!data.status) {
    //       clearPersistedCallDetails();
    //       return resolve({status: false});
    //     }
    //     return resolve({...data, status: true});
    //   },
    // );
  });
};

type writeCallHistoryParams = CallDetails & {
  callStatus?: CallHistoryStatus;
  isOngoingCall?: boolean;
  createdAt?: Date;
};

export const writeIncomingHistory = (incomingCallDetails: incomingCallData) => {
  try {
    const callData: Partial<CallSchema> = {
      callId: incomingCallDetails.callId,
      callType: 'audio',
      callStatus: 'incoming',
      createdAt: new Date(),
      initiator:
        incomingCallDetails.initiatorDetails?._id ||
        incomingCallDetails.initiatorDetails?.id ||
        incomingCallDetails.initiatorId,
      participants: [],
      invitedUserIds: [],
      origin: incomingCallDetails?.origin,
      scheduledAt: null,
      isOngoingCall: false,
      syncPending: true,
      originType: incomingCallDetails?.origin?.type,
    };

    CallService.upsertCall(callData);
  } catch (err) {
    console.error(err);
  }
};

type writeOutgoingHistoryParams = StartCallRes & {
  origin: CallOrigin;
};

export const writeOutgoingHistory = (callDetails: writeOutgoingHistoryParams) => {
  try {
    const callData: Partial<CallSchema> = {
      callId: callDetails.callId,
      callType: callDetails.callType,
      callStatus: 'outgoing',
      createdAt: new Date(),
      initiator: callDetails.initiatorId,
      participants: [],
      invitedUserIds: [],
      origin: callDetails.origin,
      scheduledAt: null,
      isOngoingCall: true,
      syncPending: true,
      originType: callDetails?.origin?.type,
    };

    CallService.upsertCall(callData);
  } catch (err) {
    console.error(err);
  }
};

export const writeForegroundCallEndInfo = async (callId: string, callStatus: CallHistoryStatus) => {
  try {
    const callInfo = await getCallInfoApi(callId);
    const callHistoryRecord = CallService.getCallById(callId);
    if (callInfo && callHistoryRecord) {
      const callData: Partial<CallSchema> = {
        callId: callId,
        initiator: callInfo.initiatorId,
        participants: callInfo.participants.map((item) => item._id),
        invitedUserIds: callInfo.invitedUserIds.map((item) => item._id),
        scheduledAt: null,
        isOngoingCall: false,
        syncPending: callInfo.status === 'ended' ? false : true,
        callStatus: callStatus,
        participantsJson: JSON.stringify(callInfo.participants),
        invitedUserIdsJson: JSON.stringify(callInfo.invitedUserIds),
        initiatorJson: JSON.stringify(callInfo.initiatorDetails),
        createdAt: callHistoryRecord.createdAt || new Date(),
      };

      CallService.upsertCall(callData);
    }
  } catch (err) {
    console.error(err);
  }
};

export const updateCallHistoryOnEnd = async (callId: string, callStatus: CallHistoryStatus) => {
  try {
    const callInfo = await getCallInfoApi(callId);
    if (callInfo && callInfo.status !== 'ended') {
      // do not update if the call is still ongoing

      return Promise.resolve({
        callId,
        callStatus,
        status: false,
      });
    }
    const callHistoryRecord = CallService.getCallById(callId);
    if (callInfo && callHistoryRecord) {
      const callData: Partial<CallSchema> = {
        callId: callId,
        initiator: callInfo.initiatorId,
        participants: callInfo.participants.map((item) => item._id),
        invitedUserIds: callInfo.invitedUserIds.map((item) => item._id),
        scheduledAt: null,
        isOngoingCall: callInfo.invitedUserIds.length > 1 ? true : false,
        syncPending: callInfo.status === 'ended' ? false : true,
        callStatus: callStatus,
        participantsJson: JSON.stringify(callInfo.participants),
        invitedUserIdsJson: JSON.stringify(callInfo.invitedUserIds),
        initiatorJson: JSON.stringify(callInfo.initiatorDetails),
        createdAt: callHistoryRecord.createdAt || new Date(),
      };

      CallService.upsertCall(callData);
    }
  } catch (err) {
    console.error(err);
  }
};

// use it to creat the call history
export const writeToCallHistory = (callDetails: writeCallHistoryParams) => {
  const callData: Partial<CallSchema> = {
    callId: callDetails.callId,
    callType: callDetails.type,
    callStatus: callDetails.callStatus,
    createdAt: callDetails.createdAt || new Date(),
    initiator: callDetails.initiatorId,
    participants: callDetails.participants.map((item) => item.userId),
    invitedUserIds: callDetails.recipients.map((item) => item.userId || ''),
    origin: callDetails.origin,
    scheduledAt: null,
    isOngoingCall: callDetails.isOngoingCall,
    syncPending: true,
    originType: callDetails?.origin?.type,
  };

  CallService.upsertCall(callData);
};

// use it to update the call history
export const updateCallHistory = (
  callId: string,
  callDetails: Partial<Omit<CallHistoryItem, 'callId'>>,
) => {
  const callData: Partial<CallSchema> = {
    callId: callId,
    ...callDetails,
  };
  CallService.upsertCall(callData);
};

export async function getCallPermissions(props: { callType: 'audio' | 'video' }) {
  try {
    const micPermission = await requestMicrophonePermission();
    if (micPermission !== 'granted') {
      ToastAndroid.show('Microphone permission denied', ToastAndroid.SHORT);
      return false;
    }
    if (props.callType === 'video') {
      let resp = await requestCameraPermission();
      if (resp !== 'granted') {
        ToastAndroid.show('Camera permission denied', ToastAndroid.SHORT);
        return false;
      }
    }
    return true;
  } catch (err) {
    return false;
  }
}
