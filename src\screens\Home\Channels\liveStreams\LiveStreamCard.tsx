// components/LiveStreamCard.tsx
import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';
import { FlatList, ScrollView } from 'react-native-gesture-handler';

type LiveStreamCardProps = {
  title: string;
  viewers: string;
  price: string;
  imageUrl: string;
};

export const LiveStreamCard: React.FC<LiveStreamCardProps> = ({
  title,
  viewers,
  price,
  imageUrl,
}) => {
  return (
    <TouchableOpacity style={styles.card}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.overlay}>
        <Text style={styles.price}>⭐ {price}</Text>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.viewerRow}>
          {/* <Ionicons name="eye" size={14} color="#fff" /> */}
          <Text style={styles.viewerText}>{viewers}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '47%',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 16,
  },
  image: {
    width: '100%',
    height: 160,
  },
  overlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#0008',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  price: {
    color: '#fff',
    fontWeight: '600',
  },
  infoContainer: {
    padding: 8,
    backgroundColor: '#0006',
  },
  title: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  viewerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewerText: {
    color: '#fff',
    marginLeft: 4,
  },
});
