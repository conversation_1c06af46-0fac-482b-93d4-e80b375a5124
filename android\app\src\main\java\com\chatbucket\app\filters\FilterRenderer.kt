package com.chatbucket.filters

import android.content.Context
import android.graphics.SurfaceTexture
import android.media.MediaMetadataRetriever
import android.media.MediaPlayer
import android.opengl.GLES11Ext
import android.opengl.GLES20
import android.opengl.GLSurfaceView
import android.os.Build
import android.util.Log
import android.view.Surface
import androidx.annotation.RequiresApi
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class FilterRenderer(
        private val context: Context,
        private val glSurfaceView: GLSurfaceView
) : GLSurfaceView.Renderer {
    private var videoPlayer: MediaPlayer? = null
    private var audioPlayer: MediaPlayer? = null
    private var surfaceTexture: SurfaceTexture? = null
    private var surface: Surface? = null
    private var textureId: Int = 0
    private var filterShader: FilterShader? = null
    private var currentFilter: String = "default"
    private var resizeMode: String = "contain"
    private var progressThread: Thread? = null
    private var isReleased = false
    private var videoWidth: Int = 0
    private var videoHeight: Int = 0
    private var isSoundOn: Boolean = true
    private var hasCustomAudio: Boolean = false
    private var videoRotation: Int = 0
    private var isVideoPlayerPrepared = false
    private var isAudioPlayerPrepared = false

    private var vertexBuffer: FloatBuffer = createDefaultVertexBuffer()
    private var texCoordBuffer: FloatBuffer = ByteBuffer.allocateDirect(4 * 2 * 4)
            .order(ByteOrder.nativeOrder())
            .asFloatBuffer()
            .apply {
                put(floatArrayOf(0f, 1f, 1f, 1f, 0f, 0f, 1f, 0f))
                position(0)
            }

    override fun onSurfaceCreated(unused: GL10?, config: EGLConfig?) {
        textureId = createExternalTexture()
        surfaceTexture = SurfaceTexture(textureId)
        surface = Surface(surfaceTexture)

        filterShader = FilterShader(context)
        filterShader?.init()
        filterShader?.setFilter(currentFilter)

        surfaceTexture?.setOnFrameAvailableListener {
            if (!isReleased) glSurfaceView.requestRender()
        }

        videoPlayer?.setSurface(surface)
        videoPlayer?.isLooping = true
        updateAudioVolumes()
        if (isVideoPlayerPrepared) {
            videoPlayer?.start()
            syncAudioPlayback()
            startProgressUpdates()
        }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        GLES20.glViewport(0, 0, width, height)
        updateVertexBuffer(width, height)
    }

    override fun onDrawFrame(gl: GL10?) {
        if (!isReleased) {
            GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
            surfaceTexture?.updateTexImage()
            filterShader?.draw(textureId, vertexBuffer, texCoordBuffer)
        }
    }

    fun setVideoPath(path: String) {
        if (isReleased) return
        resetVideoPlayer()
        videoPlayer = MediaPlayer().apply {
            setDataSource(path)
            setSurface(surface)
            prepareAsync()
            setOnPreparedListener {
                isVideoPlayerPrepared = true
                val nativeWidth = it.videoWidth
                val nativeHeight = it.videoHeight
                val retriever = MediaMetadataRetriever()
                try {
                    retriever.setDataSource(path)
                    val rotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toIntOrNull() ?: 0
                    <EMAIL> = rotation
                    <EMAIL> = nativeWidth
                    <EMAIL> = nativeHeight
                } catch (e: Exception) {
                    <EMAIL> = 0
                    <EMAIL> = nativeWidth
                    <EMAIL> = nativeHeight
                } finally {
                    retriever.release()
                }
                updateAudioVolumes()
                it.start()
                filterShader?.setFilter(currentFilter)
                updateVertexBuffer(glSurfaceView.width, glSurfaceView.height)
                glSurfaceView.requestRender()
                startProgressUpdates()
                syncAudioPlayback()
            }
            setOnCompletionListener {
                stopProgressUpdates()
                it.seekTo(0)
                it.start()
                syncAudioPlayback()
                startProgressUpdates()
            }
            setOnErrorListener { _, what, extra ->
                isVideoPlayerPrepared = false
                true
            }
        }
    }

    fun setAudioPath(uri: String?) {
        if (isReleased) return
        resetAudioPlayer()
        hasCustomAudio = uri != null
        if (uri != null) {
            audioPlayer = MediaPlayer().apply {
                setDataSource(uri)
                isLooping = false
                prepareAsync()
                setOnPreparedListener {
                    isAudioPlayerPrepared = true
                    updateAudioVolumes()
                    if (isVideoPlayerPrepared && videoPlayer?.isPlaying == true) {
                        start()
                        syncAudioToVideo()
                    }
                }
                setOnCompletionListener {
                    if (isVideoPlayerPrepared && videoPlayer?.isPlaying == true) {
                        val videoPosition = videoPlayer?.currentPosition ?: 0
                        val audioDuration = it.duration
                        if (videoPosition < (videoPlayer?.duration ?: 0)) {
                            it.seekTo(0)
                            it.start()
                        }
                    }
                }
                setOnErrorListener { _, what, extra ->
                    isAudioPlayerPrepared = false
                    true
                }
            }
        }
        updateAudioVolumes()
    }

    fun setFilter(filter: String) {
        if (isReleased) return
        currentFilter = filter
        filterShader?.setFilter(filter)
        glSurfaceView.requestRender()
    }

    fun setSoundOn(isSoundOn: Boolean) {
        if (isReleased) return
        this.isSoundOn = isSoundOn
        updateAudioVolumes()
    }

    fun setResizeMode(mode: String) {
        if (isReleased) return
        resizeMode = mode
        updateVertexBuffer(glSurfaceView.width, glSurfaceView.height)
        glSurfaceView.requestRender()
    }

    fun seekTo(positionMs: Int) {
        if (isReleased) return
        if (isVideoPlayerPrepared) {
            videoPlayer?.seekTo(positionMs)
        }
        if (hasCustomAudio && isAudioPlayerPrepared && audioPlayer != null) {
            if (positionMs == 0) {
                audioPlayer?.seekTo(0)
            } else {
                syncAudioToVideo()
            }
        }
        startProgressUpdates()
    }

    fun release() {
        isReleased = true
        stopProgressUpdates()
        resetVideoPlayer()
        resetAudioPlayer()
        surface?.release()
        surface = null
        surfaceTexture?.release()
        surfaceTexture = null
        filterShader?.release()
        filterShader = null
        if (textureId != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(textureId), 0)
            textureId = 0
        }
    }

    private fun resetVideoPlayer() {
        videoPlayer?.apply {
            stop()
            reset()
            release()
        }
        videoPlayer = null
        isVideoPlayerPrepared = false
        stopProgressUpdates()
    }

    private fun resetAudioPlayer() {
        audioPlayer?.apply {
            stop()
            reset()
            release()
        }
        audioPlayer = null
        isAudioPlayerPrepared = false
    }

    private fun updateAudioVolumes() {
        if (isVideoPlayerPrepared) {
            videoPlayer?.setVolume(
                    if (!hasCustomAudio && isSoundOn) 1f else 0f,
                    if (!hasCustomAudio && isSoundOn) 1f else 0f
            )
        }
        if (isAudioPlayerPrepared) {
            audioPlayer?.setVolume(
                    if (hasCustomAudio && isSoundOn) 1f else 0f,
                    if (hasCustomAudio && isSoundOn) 1f else 0f
            )
        }
    }

    private fun syncAudioPlayback() {
        if (isReleased) return
        if (hasCustomAudio && isAudioPlayerPrepared && audioPlayer != null) {
            if (isVideoPlayerPrepared && videoPlayer?.isPlaying == true) {
                if (audioPlayer?.isPlaying != true) {
                    audioPlayer?.start()
                }
                syncAudioToVideo()
            } else {
                audioPlayer?.pause()
            }
        }
    }

    fun onPause() {
        if (isVideoPlayerPrepared) {
            videoPlayer?.pause()
        }
        if (isAudioPlayerPrepared) {
            audioPlayer?.pause()
        }
        stopProgressUpdates()
    }

    fun onResume() {
        if (!isReleased && isVideoPlayerPrepared) {
            videoPlayer?.start()
            Log.d("FilterRenderer", "Video started")
            syncAudioPlayback()
            startProgressUpdates()
        }
    }

    private fun syncAudioToVideo() {
        if (!hasCustomAudio || !isAudioPlayerPrepared || !isVideoPlayerPrepared || audioPlayer == null || videoPlayer == null) return
        try {
            videoPlayer?.let { video ->
                audioPlayer?.let { audio ->
                    if (video.isPlaying) {
                        val videoPosition = video.currentPosition
                        val audioDuration = audio.duration
                        val audioPosition = if (audioDuration > 0) {
                            videoPosition.coerceAtMost(audioDuration)
                        } else {
                            0
                        }
                        val currentAudioPosition = audio.currentPosition
                        if (kotlin.math.abs(currentAudioPosition - audioPosition) > 200) {
                            audio.seekTo(audioPosition)
                            audio.start()
                        }
                    }
                }
            }
        } catch (e: IllegalStateException) {
            Log.e("FilterRenderer", "Failed to sync audio: ${e.message}")
        }
    }

    @RequiresApi(Build.VERSION_CODES.FROYO)
    private fun createExternalTexture(): Int {
        val texture = IntArray(1)
        GLES20.glGenTextures(1, texture, 0)
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, texture[0])
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_LINEAR.toFloat())
        GLES20.glTexParameterf(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR.toFloat())
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_S, GLES20.GL_CLAMP_TO_EDGE)
        GLES20.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES20.GL_TEXTURE_WRAP_T, GLES20.GL_CLAMP_TO_EDGE)
        return texture[0]
    }

    private fun startProgressUpdates() {
        stopProgressUpdates()
        if (isReleased) return
        progressThread = Thread {
            try {
                while (isVideoPlayerPrepared && videoPlayer?.isPlaying == true && !Thread.currentThread().isInterrupted) {
                    val currentPosition = videoPlayer?.currentPosition?.toFloat() ?: 0f
                    (glSurfaceView as? FilteredVideoView)?.emitProgress(currentPosition / 1000f)
                    syncAudioToVideo()
                    Thread.sleep(100)
                }
            } catch (e: InterruptedException) {
                // Thread interrupted
            } catch (e: IllegalStateException) {
                Log.e("FilterRenderer", "Progress update failed: ${e.message}")
            }
        }.apply { start() }
    }

    private fun stopProgressUpdates() {
        progressThread?.interrupt()
        progressThread = null
    }

    private fun createDefaultVertexBuffer(): FloatBuffer {
        return ByteBuffer.allocateDirect(4 * 2 * 4)
                .order(ByteOrder.nativeOrder())
                .asFloatBuffer()
                .apply {
                    put(floatArrayOf(-1f, -1f, 1f, -1f, -1f, 1f, 1f, 1f))
                    position(0)
                }
    }

    private fun updateVertexBuffer(viewWidth: Int, viewHeight: Int) {
        if (videoWidth == 0 || videoHeight == 0) return

        val isPortrait = videoRotation == 90 || videoRotation == 270
        val videoAspect = if (isPortrait) {
            videoHeight.toFloat() / videoWidth.toFloat()
        } else {
            videoWidth.toFloat() / videoHeight.toFloat()
        }
        val viewAspect = viewWidth.toFloat() / viewHeight.toFloat()

        var left = -1f
        var right = 1f
        var bottom = -1f
        var top = 1f

        if (isPortrait) {
            val videoHeightRatio = 0.82f
            top = 0.90f
            bottom = -0.90f + (2 * 0.13f)
            val scaledHeightPixels = viewHeight * videoHeightRatio
            val scaledWidthPixels = scaledHeightPixels / videoAspect
            val widthRatio = scaledWidthPixels / viewWidth
            right = widthRatio
            left = -widthRatio
            val offset = (1f - widthRatio.coerceAtMost(1f))
            left += offset / 2
            right -= offset / 2
        } else {
            when (resizeMode) {
                "contain" -> {
                    if (videoAspect > viewAspect) {
                        val scaledHeight = viewWidth / videoAspect
                        val heightRatio = scaledHeight / viewHeight
                        top = heightRatio
                        bottom = -heightRatio
                    } else {
                        val scaledWidth = viewHeight * videoAspect
                        val widthRatio = scaledWidth / viewWidth
                        right = widthRatio
                        left = -widthRatio
                    }
                }
                "cover" -> {
                    if (videoAspect > viewAspect) {
                        val scaledHeight = viewWidth / videoAspect
                        val heightRatio = viewHeight / scaledHeight
                        top = heightRatio
                        bottom = -heightRatio
                    } else {
                        val scaledWidth = viewHeight * videoAspect
                        val widthRatio = viewWidth / scaledWidth
                        right = widthRatio
                        left = -widthRatio
                    }
                }
                "fill" -> {
                    left = -1f
                    right = 1f
                    bottom = -1f
                    top = 1f
                }
            }
        }

        val vertices = floatArrayOf(
                left, bottom,
                right, bottom,
                left, top,
                right, top
        )

        val texCoords = if (isPortrait) {
            when (videoRotation) {
                0 -> floatArrayOf(
                        0f, 1f,
                        1f, 1f,
                        0f, 0f,
                        1f, 0f
                )
                90 -> floatArrayOf(
                        1f, 1f,
                        1f, 0f,
                        0f, 1f,
                        0f, 0f
                )
                180 -> floatArrayOf(
                        1f, 0f,
                        0f, 0f,
                        1f, 1f,
                        0f, 1f
                )
                270 -> floatArrayOf(
                        0f, 0f,
                        0f, 1f,
                        1f, 0f,
                        1f, 1f
                )
                else -> floatArrayOf(
                        0f, 1f,
                        1f, 1f,
                        0f, 0f,
                        1f, 0f
                )
            }
        } else {
            floatArrayOf(
                    0f, 1f,
                    1f, 1f,
                    0f, 0f,
                    1f, 0f
            )
        }

        vertexBuffer.clear()
        vertexBuffer.put(vertices)
        vertexBuffer.position(0)

        texCoordBuffer.clear()
        texCoordBuffer.put(texCoords)
        texCoordBuffer.position(0)
    }
}