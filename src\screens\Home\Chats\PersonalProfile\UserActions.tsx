import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import BlockSVG from '../../../../assets/svgIcons/BlockSVG';
import ReportSVG from '../../../../assets/svgIcons/ReportSVG';
import CustomAlert from '../../../../component/Common/CustomAlert';
import { colors } from '../../../../theme/colors';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { useState } from 'react';
import { blockUser, reportUser, unBlockUser } from '../../../../utils/ApiService';
import { errorToast, showToast, successToast } from '../../../../utils/commonFunction';
import { ChatService } from '../../../../service/ChatService';
import { MembershipStatus } from '../../../../types/chats.types';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';

type UserActionsProps = {
  conversationInfo: ConversationInfo | null;
};

const UserActions: React.FC<UserActionsProps> = ({ conversationInfo }) => {
  const isBlockedUser =
    conversationInfo?.type === ConversationType.P2P && conversationInfo?.isBlocked;
  const [isBlocked, setIsBlocked] = useState<boolean>(isBlockedUser);
  const [showUnblockAlert, setShowUnblockAlert] = useState(false);
  const [showReportAlert, setShowReportAlert] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  const [reason, setReason] = useState('');

  const conversationId = conversationInfo?.id as string;

  const [loadingState, setLoadingState] = useState({
    isBlockUserLoading: false,
    isUnBlockUserLoading: false,
  });

  const handleBlock = async () => {
    try {
      setLoadingState({ ...loadingState, isBlockUserLoading: true });
      const res = await blockUser(conversationId);

      if (res?.body?.status === true) {
        const userData = ChatService.getUserData(conversationId);
        if (!userData) {
          await ChatService.getOrCreateUser(conversationId);
        }
        ChatService.onBlocked(conversationId);
        ChatService.onBlockedUser(conversationId);
        setIsBlocked(true);
        setShowUnblockAlert(false);
        successToast(`${conversationInfo?.displayPic} has been blocked successfully`);
      } else {
        errorToast(res?.body?.message || 'Failed to block user. Please try again.');
      }
    } catch (err) {
      errorToast('Failed to block user. Please try again.');
    } finally {
      setLoadingState({ ...loadingState, isBlockUserLoading: false });
    }
  };

  const handleUnBlock = async () => {
    try {
      setLoadingState({ ...loadingState, isUnBlockUserLoading: true });
      const res = await unBlockUser(conversationId);
      if (res?.body?.status === true) {
        ChatService.onUnBlocked(conversationId);
        ChatService.onUnBlockedUser(conversationId);
        setShowUnblockAlert(false);
        setIsBlocked(false);
        successToast(`${conversationInfo?.displayName} has been unblocked successfully`);
      } else {
        errorToast(res?.body?.message || 'Failed to unblock user. Please try again.');
      }
    } catch (error) {
      errorToast('Failed to unblock user. Please try again.');
    } finally {
      setLoadingState({ ...loadingState, isUnBlockUserLoading: false });
    }
  };

  const handleReportUser = async (reason: string) => {
    setReportLoading(true);
    try {
      const res = await reportUser(conversationId, reason);
      if (res?.status === true) {
        showToast(`${conversationInfo?.displayName} has been reported successfully`);
      }
    } catch (error) {
      console.error('Report user error:', error);
    } finally {
      setReportLoading(false);
      setShowReportAlert(false);
      setReason('');
    }
  };
  return (
    <View style={styles.section}>
      {!isBlocked ? (
        <>
          <TouchableOpacity style={styles.dangerRow} onPress={() => setShowUnblockAlert(true)}>
            <BlockSVG size={18} color={colors.thick_red} />
            <Text style={styles.dangerLabel}>Block</Text>
          </TouchableOpacity>
          <CustomAlert
            visible={showUnblockAlert}
            onCancel={() => setShowUnblockAlert(false)}
            onConfirm={handleBlock}
            title="Block User"
            message={`Are you sure you want to Block ${conversationInfo?.displayName}?`}
            confirmText="Block"
            cancelText="Cancel"
            loading={loadingState.isBlockUserLoading}
          />
        </>
      ) : (
        <>
          <TouchableOpacity style={styles.dangerRow} onPress={() => setShowUnblockAlert(true)}>
            <BlockSVG size={18} color={colors.thick_red} />
            <Text style={styles.dangerLabel}>Unblock</Text>
          </TouchableOpacity>
          <CustomAlert
            visible={showUnblockAlert}
            onCancel={() => setShowUnblockAlert(false)}
            onConfirm={handleUnBlock}
            title="Unblock User"
            message={`Are you sure you want to unblock ${conversationInfo?.displayName}?`}
            confirmText="Unblock"
            cancelText="Cancel"
            loading={loadingState.isUnBlockUserLoading}
          />
        </>
      )}
      <TouchableOpacity style={styles.dangerRow} onPress={() => setShowReportAlert(true)}>
        <ReportSVG size={18} color={colors.thick_red} />
        <Text style={styles.dangerLabel}>Report</Text>
      </TouchableOpacity>
      <CustomAlert
        visible={showReportAlert}
        onCancel={() => {
          setShowReportAlert(false);
          setReason('');
        }}
        onConfirm={() => handleReportUser(reason)}
        title="Report User"
        message={`Are you sure you want to Report ${conversationInfo?.displayName}?`}
        confirmText="Report"
        cancelText="Cancel"
        loading={reportLoading}
        showInput
        inputValue={reason}
        onChangeText={setReason}
      />
    </View>
  );
};

export default UserActions;

const styles = StyleSheet.create({
  section: {
    marginBottom: 10,
  },
  dangerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 10,
  },
  dangerLabel: {
    fontSize: 16,
    color: colors.thick_red,
    fontWeight: '400',
  },
});
