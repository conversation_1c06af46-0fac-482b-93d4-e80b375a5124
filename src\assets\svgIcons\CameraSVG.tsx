import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const CameraSVG: React.FC<SvgComponentProps & React.ComponentProps<typeof Svg>> = ({
    size = 20,
    color = "gray",
    ...props
}) => {
    const width = (size / 20) * 22;
    const height = size;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 22 20"
            fill="none"
            {...props}
        >
            <Path
                opacity={1}
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.592 19.75h4.815c3.38 0 5.071 0 6.286-.797a4.77 4.77 0 001.328-1.304c.812-1.192.812-2.852.812-6.172s0-4.98-.812-6.172a4.77 4.77 0 00-1.328-1.304c-.78-.512-1.757-.695-3.253-.76-.714 0-1.328-.531-1.468-1.218C14.762.992 13.84.25 12.769.25H9.23c-1.07 0-1.992.742-2.202 1.773-.14.687-.754 1.218-1.468 1.218-1.496.065-2.473.248-3.253.76A4.772 4.772 0 00.977 5.305C.166 6.498.166 8.158.166 11.477c0 3.32 0 4.98.811 6.172.352.516.803.96 1.329 1.304 1.214.797 2.905.797 6.286.797zm2.407-12.705c-2.493 0-4.514 1.985-4.514 4.432 0 2.448 2.021 4.432 4.514 4.432 2.493 0 4.514-1.984 4.514-4.432 0-2.447-2.02-4.432-4.514-4.432zm0 1.773c-1.495 0-2.708 1.19-2.708 2.66 0 1.468 1.213 2.658 2.708 2.658 1.496 0 2.709-1.19 2.709-2.659 0-1.468-1.213-2.659-2.709-2.659zm5.116-.886c0-.49.404-.887.903-.887h1.204c.498 0 .902.397.902.887s-.404.886-.902.886h-1.204a.895.895 0 01-.903-.886z"
                fill={color}
            />
        </Svg>
    );
};

export default CameraSVG;



