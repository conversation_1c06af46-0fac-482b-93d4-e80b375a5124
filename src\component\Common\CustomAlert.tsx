import React, { useState } from 'react';
import {
  Modal,
  Pressable,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  StyleProp,
  ActivityIndicator,
  TextInput,
} from 'react-native';

interface CustomAlertProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (reason?: string) => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  containerStyle?: StyleProp<ViewStyle>;
  modalStyle?: StyleProp<ViewStyle>;
  loading?: boolean;
  showInput?: boolean;
  inputPlaceholder?: string;
  inputValue?: string;
  onChangeText?: (text: string) => void;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  onCancel,
  onConfirm,
  containerStyle = {},
  modalStyle = {},
  title = 'Confirm Action',
  message = 'Are you sure you want to continue?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  loading = false,
  showInput = false,
  inputPlaceholder = 'Enter reason...',
  inputValue,
  onChangeText,
}) => {
  const handleCancel = () => {
    onCancel();
  };

  const handleConfirm = () => {
    onConfirm(showInput ? inputValue : undefined);
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <Pressable onPress={handleCancel} style={[styles.backdrop, containerStyle]}>
        <Pressable
          onPress={() => {}} // prevents tap propagation
          style={[styles.modalContainer, modalStyle]}
        >
          <Text style={styles.title}>{title}</Text>
          {showInput ? (
            <View>
              {!!message && <Text style={styles.message}>{message}</Text>}
              <TextInput
                style={styles.reasonInput}
                placeholder={inputPlaceholder}
                value={inputValue}
                multiline
                onChangeText={onChangeText}
              />
            </View>
          ) : (
            <Text style={styles.message}>{message}</Text>
          )}

          <View style={styles.buttonRow}>
            <TouchableOpacity
              onPress={handleCancel}
              style={[styles.button, styles.cancelButton]}
              disabled={loading}
            >
              <Text style={styles.cancelText}>{cancelText}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleConfirm}
              style={[styles.button, styles.confirmButton, loading && styles.disabledButton]}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#e11d48" />
              ) : (
                <Text style={styles.confirmText}>{confirmText}</Text>
              )}
            </TouchableOpacity>
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    elevation: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#111',
  },
  message: {
    fontSize: 16,
    marginBottom: 16,
    color: '#444',
  },
  reasonInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    borderRadius: 8,
    padding: 10,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16, // React Native 0.71+
  },
  button: {
    flex: 1,
    minHeight: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  confirmButton: {
    backgroundColor: '#fff5f5',
  },
  disabledButton: {
    opacity: 0.7,
  },
  cancelText: {
    fontSize: 16,
    color: '#777',
    textAlign: 'center',
  },
  confirmText: {
    fontSize: 16,
    color: '#e11d48',
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default CustomAlert;
