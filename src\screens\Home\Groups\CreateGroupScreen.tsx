import {
  Image,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  Switch,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import CommonView from '../../../component/CommonView';
import ButtonPurple from '../../../component/ButtonPurple';
import { commonFontStyle, hp, wp } from '../../../theme/fonts';
import Input from '../../../component/Input';
import { useTranslation } from 'react-i18next';
import ImageCropPicker from 'react-native-image-crop-picker';
import { colors } from '../../../theme/colors';
import { useContacts } from '../../../hooks/contacts/useContacts';
import SearchInput from '../../../component/SearchInput';
import { createGroupSpaceApi } from '../../../service/ChatSpacesService';
import { ChatService } from '../../../service/ChatService';
import { ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { resetAndNavigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import { ChatSpecificScreenParamsT } from '../Chats/ChatSpecificScreen';
import SingleTickSVG from '../../../assets/svgIcons/SingleTickSVG';
import { FeatherIcons, FontAwesome6Icons } from '../../../utils/vectorIcons';
import EditImageSVG from '../../../assets/svgIcons/EditImageSVG';
import { RouteProp, useRoute } from '@react-navigation/native';
import { defaultMemberPermissions } from './GroupPermissions';
import { defaultAdminPermissions } from './AdminPermissions';
import { useMe } from '../../../hooks/util/useMe';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Toast from 'react-native-toast-message';
import { CreateGroupSchema } from '../../../yup/formSchemas/CreateGroupSchema';
import UserAvatar from '../../../component/utils/UserAvatar';
import { IUser } from '../../../device-storage/realm/schemas/UserSchema';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';
import { MembershipStatus } from '../../../types/chats.types';
import SelectionSVG from '../../../assets/svgIcons/SelectionSVG';

type CreateGroupScreenParams = {
  CreateGroupScreen: {
    initialUsers?: string[];
  };
};
export interface PickedImage {
  name: string;
  type: string;
  uri: string;
}
export interface ICreateGroupForm {
  groupName: string;
  groupDescription: string;
  isPrivate?: boolean;
  imageUrl?: PickedImage | null;
  selectedContacts?: string[];
}

const CreateGroupScreen = () => {
  const route = useRoute<RouteProp<CreateGroupScreenParams, 'CreateGroupScreen'>>();
  const { initialUsers } = route.params;

  const { user: me } = useMe();

  const { t } = useTranslation();
  const { registeredContacts } = useContacts();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<ICreateGroupForm>({
    resolver: yupResolver(CreateGroupSchema, { context: { isCreate: true } }),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      isPrivate: false,
      imageUrl: null,
      selectedContacts: [],
    },
  });
  const sourceUrl = watch('imageUrl');
  const selectedContacts = watch('selectedContacts') ?? [];

  const [step, setStep] = useState<'select' | 'create'>('select');
  const [searchText, setSearchText] = useState('');

  const otherContacts = registeredContacts.filter((contact) => contact.id !== me?._id);

  const toggleSelect = (id: string) => {
    const updatedSet = new Set(selectedContacts);
    if (updatedSet.has(id)) {
      updatedSet.delete(id);
    } else {
      updatedSet.add(id);
    }
    setValue('selectedContacts', Array.from(updatedSet));
  };

  const onPressGallery = () => {
    ImageCropPicker.openPicker({
      cropping: true,
    }).then((image) => {
      const newImg: { uri: string; type: string; name: string } = {
        name: image.filename ?? image.path?.split('/')?.pop() ?? 'photo.jpg',
        type: image.mime,
        uri: image.path ?? image.sourceURL,
      };
      setValue('imageUrl', newImg);
    });
  };

  const filteredContacts = otherContacts.filter((contact) => {
    const search = searchText.toLowerCase();
    return (
      contact.name.toLowerCase().includes(search) ||
      contact.phoneNumber?.toLowerCase().includes(search)
    );
  });

  const onSubmit: SubmitHandler<ICreateGroupForm> = async (data: ICreateGroupForm) => {
    let displayPic: { uri: string; type: string; name: string } | undefined = undefined;
    if (data.imageUrl) {
      displayPic = {
        uri: data.imageUrl.uri,
        type: data.imageUrl.type,
        name: data.imageUrl.name,
      };
    }

    const selectedUserIds = Array.from(selectedContacts)
      .map((id) => {
        const contact = registeredContacts.find((c) => c.id === id);
        return contact?.id;
      })
      .filter((id): id is string => {
        const isValidMember = typeof id === 'string' && /^[a-f\d]{24}$/i.test(id);
        if (!isValidMember) {
          console.warn('⚠️ Invalid member ID filtered out:', id);
        }
        return isValidMember;
      });

    try {
      const response = await createGroupSpaceApi({
        name: data.groupName,
        type: ConversationType.GROUP,
        description: data.groupDescription || '',
        members: selectedUserIds,
        displayPic,
        isPrivate: data.isPrivate,
      });
      if (response && response.data) {
        const groupMemberOverrides = {
          member: defaultMemberPermissions,
          admin: defaultAdminPermissions,
        };

        const localChatSpace = ChatService.saveChatSpace(
          response.data?.chatSpace,
          MembershipStatus.OWNER,
          response.data?.systemMessage,
        );
        // ChatService.onIncomingMessage(response?.data?.systemMessage);
        // const userData: IChatScreenProps = {
        //   displayName: createdConversation.displayName,
        //   displayPic: createdConversation.displayPic,
        //   type: createdConversation.type,
        //   chatSpaceId: createdConversation.id,
        //   id: createdConversation.id || '',
        //   isActive: createdConversation.isActive,
        //   conversation: createdConversation,
        // };
        const chatScreenRouteParams: ChatSpecificScreenParamsT = {
          userData: {
            displayName: localChatSpace.name,
            displayPic: localChatSpace.displayPic,
            type: localChatSpace.type,
            chatSpaceId: localChatSpace.id,
            id: localChatSpace.id || '',
            conversation: {} as IConversation,
          },
          isFollowing: false,
          data: {
            convId: localChatSpace.id,
          },
        };
        resetAndNavigateTo(SCREENS.ChatSpecificScreen, chatScreenRouteParams, true); // true = reset stack
        // navigateTo(SCREENS.ChatSpecificScreen, {
        //   userData,
        // });
      }
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Failed to create group. Please try again.',
      });
    }
  };

  const renderContactItem = ({ item }: { item: IUser }) => {
    const isSelected = selectedContacts.filter((phone) => phone === item.id).length > 0;
    return (
      <TouchableOpacity style={styles.contactItem} onPress={() => toggleSelect(item.id!)}>
        <UserAvatar imgUrl={item.profilePic} width={50} />
        <View style={{ flex: 1 }}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.bio} numberOfLines={1}>
            {item.bio}
          </Text>
        </View>
        <View>
          {isSelected ? (
            <SelectionSVG size={24} isSelected={isSelected} />
          ) : (
            <View style={styles.checkboxCircle} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    if (initialUsers && initialUsers.length > 0) {
      setValue('selectedContacts', initialUsers);
    }
  }, [initialUsers, setValue]);

  return (
    <KeyboardAvoidingView behavior={'padding'} style={{ flex: 1 }}>
      {step === 'select' ? (
        <CommonView
          headerTitle={
            selectedContacts.length > 0 ? `${selectedContacts.length}` : t('Select Contacts')
          }
          renderRight={() =>
            selectedContacts.length > 0 ? (
              <TouchableOpacity
                onPress={() => {
                  if (selectedContacts.length === 0) return;
                  setStep('create');
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <SingleTickSVG size={18} color={colors.white} />
              </TouchableOpacity>
            ) : (
              ''
            )
          }
        >
          <View style={styles.searchView}>
            <SearchInput
              value={searchText}
              onChangeText={(text) => {
                setSearchText(text);
              }}
            />
          </View>
          <FlatList
            data={filteredContacts}
            keyExtractor={(item) => item.id}
            renderItem={renderContactItem}
            style={{ marginHorizontal: -10 }}
          />
        </CommonView>
      ) : (
        <CommonView
          headerTitle={t('New group')}
          isScrollable
          contentContainerStyle={styles.containerStyle}
        >
          <View style={styles.imageView}>
            {sourceUrl ? (
              <Image source={{ uri: sourceUrl.uri }} style={styles.sourceUrlStyle} />
            ) : (
              <View
                style={{
                  alignItems: 'center',
                  gap: 10,
                  borderRadius: hp(15 / 2),
                  height: '100%',
                  width: '100%',
                  justifyContent: 'center',
                }}
              >
                <FeatherIcons name="users" size={30} color={colors.gray_80} />
                <Text style={{ color: colors.gray_80, fontSize: 12 }}>Add group icon</Text>
              </View>
            )}

            <TouchableOpacity onPress={onPressGallery} style={styles.galleryIconView}>
              <EditImageSVG size={22} />
            </TouchableOpacity>
          </View>

          <Controller
            control={control}
            name="groupName"
            render={({ field: { onChange, value } }) => (
              <Input
                title={t('Group name')}
                value={value}
                placeHolder={t('Enter your group name')}
                onChangeText={onChange}
              />
            )}
          />
          {errors.groupName && <Text style={styles.errorText}>{errors.groupName.message}</Text>}

          <Controller
            control={control}
            name="groupDescription"
            render={({ field: { onChange, value } }) => (
              <Input
                title={t('Group description')}
                value={value}
                placeHolder={t('Describe what your group is about..')}
                onChangeText={onChange}
                multiline
                inputContainer={{ height: hp(15), alignItems: 'flex-start' }}
                textInputStyle={{ height: hp(15) }}
                textAlignVertical="top"
              />
            )}
          />
          {errors.groupDescription && (
            <Text style={styles.errorText}>{errors.groupDescription.message}</Text>
          )}

          <View style={styles.container}>
            <Text style={styles.label}>Group Private</Text>
            <Controller
              control={control}
              name="isPrivate"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                  trackColor={{ false: '#E9E9EA', true: colors.mainPurple }}
                  thumbColor={colors.white}
                />
              )}
            />
          </View>

          {selectedContacts.length > 0 && (
            <View style={{ marginTop: 10 }}>
              <Text style={styles.selectedLabel}>
                {t('Selected people')} ({selectedContacts.length})
              </Text>
              <ScrollView horizontal contentContainerStyle={styles.selectedList}>
                {Array.from(selectedContacts).map((id) => {
                  const contact = registeredContacts.find((c) => c.id === id);
                  if (!contact) return null;
                  return (
                    <View key={id} style={styles.selectedItem}>
                      <UserAvatar imgUrl={contact.profilePic} />
                      <TouchableOpacity onPress={() => toggleSelect(id)} style={styles.removeIcon}>
                        <FontAwesome6Icons name="xmark" size={15} color={colors.black_23} />
                      </TouchableOpacity>
                      <Text style={styles.selectedName} numberOfLines={1}>
                        {contact.name}
                      </Text>
                    </View>
                  );
                })}
              </ScrollView>
            </View>
          )}

          <ButtonPurple
            title={t('Create group')}
            onPress={handleSubmit(onSubmit)}
            isLoading={isSubmitting}
            disabled={isSubmitting || selectedContacts.length <= 0}
          />

          {selectedContacts.length <= 0 && (
            <Text
              style={{
                color: colors.red_ff4444,
                fontSize: 14,
                marginHorizontal: 'auto',
              }}
            >
              Select atleast 1 people to create group.
            </Text>
          )}
        </CommonView>
      )}
    </KeyboardAvoidingView>
  );
};

export default CreateGroupScreen;

const styles = StyleSheet.create({
  containerStyle: {
    paddingHorizontal: wp(2),
    gap: 20,
  },
  imageView: {
    backgroundColor: colors.purple_1,
    height: hp(15),
    width: hp(15),
    borderRadius: hp(15 / 2),
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: hp(3),
  },
  searchView: {
    paddingHorizontal: hp(0),
  },
  channelStyle: {
    height: hp(4),
    width: hp(4),
    resizeMode: 'contain',
  },
  galleryIconView: {
    height: hp(5),
    width: hp(5),
    borderRadius: hp(5),
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'absolute',
    bottom: -15,
  },
  galleryIcon: {
    height: hp(3),
    width: hp(3),
    resizeMode: 'contain',
    tintColor: colors.gray_86,
  },
  sourceUrlStyle: {
    height: '100%',
    width: '100%',
    borderRadius: hp(15 / 2),
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    gap: hp(1),
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  name: {
    fontFamily: 'SF Pro',
    fontWeight: '500',
    fontSize: 16,
    color: colors.black_23,
  },
  bio: {
    color: colors.gray_80,
    fontFamily: 'SF Pro',
    fontSize: 14,
  },
  checkboxCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray_80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkImage: {
    height: 20,
    width: 20,
    borderRadius: 12,
  },
  selectedLabel: {
    fontSize: 14,
    color: colors.gray_86,
    marginBottom: 5,
    marginLeft: wp(2),
  },

  selectedList: {
    flexDirection: 'row',
    gap: 15,
    paddingHorizontal: wp(2),
  },

  selectedItem: {
    alignItems: 'center',
    width: 60,
    position: 'relative',
    marginTop: 5,
  },

  selectedAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },

  selectedName: {
    fontSize: 12,
    color: colors.black_23,
    marginTop: 4,
    textAlign: 'center',
  },

  removeIcon: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.white,
    borderRadius: 10,
    width: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
  },

  removeIconImage: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
    tintColor: colors.black_23,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  errorText: {
    color: colors.red_ff4444,
    fontSize: 12,
    marginTop: -15,
  },
});
