import {
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  SafeAreaView,
  View,
  TextInput,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  ToastAndroid,
  Alert,
} from 'react-native';
import React, { useEffect, useState, useRef } from 'react';

import { AppStyles } from '../../theme/appStyles';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';

import ButtonPurple from '../../component/ButtonPurple';
import { SCREENS } from '../../navigation/screenNames';
import { styles as inputStyle } from '../../component/Input';
import { CountryPicker, CountryItem, countryCodes } from 'react-native-country-codes-picker';
import {
  errorToast,
  navigateTo, showToast,
  validateInternationalPhone,
  formatPhoneForDisplay,
} from '../../utils/commonFunction';

import { useTranslation } from 'react-i18next';
let vcm = require('validate-country-mobile');

import { LoginApi } from '../../utils/ApiService';
import { CSpinner } from '../../common';

type Props = {};

import DeviceInfo from 'react-native-device-info';
import { MaterialIcons } from '../../utils/vectorIcons';

const SignupScreen = (props: Props) => {
  const [mobile, setMobile] = useState('');
  const [inputLength, setInputLength] = useState(16);
  const [country, setCountry] = useState('+91');
  const [countryIso, setCountryIso] = useState('IN');
  const countryIsoRef = useRef('IN');
  // todo : it should get the default country code based on the current location
  // todo : refactor this component.
  const [countryPickerState, setCountryPickerState] = useState<CountryItem>(
    countryCodes.find((val) => val.code === 'IN')!,
  );

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [blocked, setBlocked] = useState(false);
  const [loader, setLoader] = useState(false);

  const { t } = useTranslation();

  useEffect(() => {
    getCountryLength('91');
  }, []);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const processPhoneInput = (
    input: string,
    maxDigits: number,
    dialCodeWithPlus: string,
  ): { formatted: string; digitCount: number } => {
    const digitsOnly = input.replace(/[^0-9]/g, '');
    const limited = maxDigits ? digitsOnly.slice(0, maxDigits) : digitsOnly;
    const callingCode = (dialCodeWithPlus || '').replace(/[^0-9]/g, '');
    const formatted = formatPhoneForDisplay(limited, callingCode);
    return { formatted, digitCount: limited.length };
  };

  useEffect(() => {
    countryIsoRef.current = countryIso;
  }, [countryIso]);

  const onSignup = async () => {
    const trimmedMobile = mobile?.trim();
    const rawDigits = trimmedMobile.replace(/[^0-9]/g, '');
    const uniqueId = await DeviceInfo.getUniqueId();

    if (!rawDigits || rawDigits.length === 0) {
      return showToast(t('Please enter mobile number'));
    }

    const validation = validateInternationalPhone(rawDigits, countryIso);
    if (!validation.isValid) {
      Keyboard.dismiss();
      return showToast(t('Please enter a valid mobile number'));
    }

    const phoneCode = country || '';
    if (!phoneCode) {
      return showToast(t('Invalid country code'));
    }

    const newPost = {
      phoneCode: validation.countryCallingCode,
      phoneNumber: validation.nationalNumber,
      deviceId: uniqueId,
    };
    setLoader(true);
    try {
      const res = await LoginApi(newPost);
      setLoader(false);
      if (res?.status && res?.status_code == 200) {
        setBlocked(false);
        navigateTo(SCREENS.MissCallScreen, { data: newPost, res: res });
      } else if (res?.statusCode == -6) {
        setBlocked(true);
        errorToast(t(res?.message || 'Login failed. Please try again.'));
      } else {
        errorToast(t(res?.message || 'Login failed. Please try again.'));
      }
    } catch (error) {
      errorToast(t('An error occurred. Please try again later.'));
    }
  };

  const getCountryLength = async (code: any) => {
    let resp = await vcm.getCountryInfo(code?.toString());
    if (resp) {
      setInputLength(resp?.maxlength);
    } else {
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {loader && <CSpinner size={''} />}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled">
          <View style={AppStyles.purpleMainContainer}>
            {/* Logo Section */}
            <View
              style={[
                AppStyles.flex,
                {
                  justifyContent: 'center',
                  top: isKeyboardVisible ? hp(-1) : hp(0.5),
                },
              ]}
            >
              <View style={{ justifyContent: 'center' }}>
                <Image source={IMAGES.introImage} style={AppStyles.chatLogo} />
              </View>
            </View>

            {/* Bottom White View */}
            <View style={[AppStyles.bottomWhiteView, AppStyles.flex]}>
              <View style={{ alignSelf: 'center', alignItems: 'center' }}>
                <Text style={{ fontWeight: '700', fontSize: 18 }}>{t('Get started with')}</Text>
                <Text style={{ fontWeight: '700', fontSize: 18 }}>{t('ChatBucket')}</Text>
              </View>

              {/* Country Picker */}
              <View style={{ gap: 5 }}>
                <Text style={inputStyle.title}>{t('Select country')}</Text>
                <TouchableOpacity
                  onPress={() => setIsModalVisible(true)}
                  activeOpacity={0.7}
                  style={{
                    height: 55,
                    marginBottom: hp(2),
                    borderWidth: 1,
                    borderColor: colors._DADADA_gray,
                    borderRadius: 15,
                    marginTop: 5,
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 15,
                    justifyContent: 'space-between',
                  }}
                >
                  <Text
                    style={{
                      color: 'black',
                      fontSize: 20,
                    }}
                  >
                    {countryPickerState.flag} {countryPickerState.name.en}
                  </Text>
                  <CountryPicker
                    show={isModalVisible}
                    style={{
                      modal: {
                        height: Platform.OS === 'ios' ? 550 : 400,
                        backgroundColor: 'white',
                      },
                      //Few devices text color is showing white below changes are for that
                      textInput: {
                        height: 60,
                        borderRadius: 0,
                        color: 'black',
                      },
                      countryButtonStyles: {
                        height: 60,
                        backgroundColor: '#FFF',
                      },
                      // Styles for search message [Text]
                      searchMessageText: {},
                      // Styles for search message container [View]
                      countryMessageContainer: {},
                      // Flag styles [Text]
                      flag: { color: 'black' },
                      // Dial code styles [Text]
                      dialCode: { color: 'black' },
                      // Country name styles [Text]
                      countryName: { color: 'black' },
                    }}
                    pickerButtonOnPress={(item) => {
                      setMobile('');
                      setCountryPickerState(item);
                      setCountry(item?.dial_code);
                      countryIsoRef.current = item?.code;
                      setCountryIso(item?.code);
                      setIsModalVisible(false);
                      getCountryLength(item?.code);
                    }}
                    onBackdropPress={() => {
                      setIsModalVisible(false);
                    }}
                    lang={''}
                  />
                  <MaterialIcons name="keyboard-arrow-down" size={30} color={colors.mainPurple} />
                </TouchableOpacity>

                {/* Mobile Input */}
                <View>
                  <Text style={inputStyle.title}>{t('Enter mobile number')}</Text>
                  <View
                    style={[
                      inputStyle.rowView,
                      {
                        marginBottom: hp(2),
                        borderColor: blocked ? '#E22403' : colors._DADADA_gray,
                      },
                    ]}
                  >
                    <Text style={styles.codeText}>{country}</Text>
                    <View style={styles.verLine} />
                    <TextInput
                      value={mobile}
                      onChangeText={(text) => {
                        const { formatted, digitCount } = processPhoneInput(
                          text,
                          inputLength,
                          country,
                        );
                        setMobile(formatted);
                        if (digitCount === inputLength) {
                          Keyboard.dismiss();
                        }
                      }}
                      style={inputStyle.inputStyle}
                      placeholder={t('Mobile number')}
                      keyboardType="phone-pad"
                      placeholderTextColor={colors._CCCCCC_gray}
                      returnKeyType="done"
                    />
                  </View>
                </View>
              </View>

              {/* Blocked Message */}
              {blocked && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderColor: '#E22403',
                    borderWidth: 1,
                    padding: 10,
                    borderRadius: 8,
                    backgroundColor: '#FFF9F8',
                  }}
                >
                  <Text
                    style={{
                      marginLeft: 10,
                      color: '#E22403',
                      flex: 1,
                    }}
                  >
                    {
                      'You are temporarily blocked for 24hrs for exceeding the maximum number of attempts.'
                    }
                  </Text>
                </View>
              )}

              {/* Verify Button */}
              <ButtonPurple
                extraStyle={{ marginVertical: 12 }}
                onPress={() => onSignup()}
                title={t('Get Verification Call')}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignupScreen;

const styles = StyleSheet.create({
  titleText: {
    ...commonFontStyle(700, 31, colors.white),
    textAlign: 'center',
  },
  des: {
    ...commonFontStyle(400, 16, colors.white),
    textAlign: 'center',
    marginBottom: hp(4),
  },
  input: {
    marginBottom: hp(2),
  },
  bottomText: {
    ...commonFontStyle(400, 14, colors.black_23),
  },
  loginTextView: {
    alignSelf: 'center',
    marginTop: hp(8),
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
  },
  tabText: {
    ...commonFontStyle(500, 14, colors.black_23),
  },
  selectedTaBText: {
    ...commonFontStyle(600, 14, colors.mainPurple),
  },
  tab: {
    paddingHorizontal: 20,
    marginBottom: hp(3),
  },
  selectedLine: {
    height: 3,
    width: 20,
    backgroundColor: colors.mainPurple,
    alignSelf: 'center',
    borderRadius: 20,
    marginVertical: 8,
  },
  rightArrow: {
    height: 15,
    width: 15,
    resizeMode: 'contain',
    tintColor: colors.mainPurple,
  },
  codeText: {
    ...commonFontStyle(500, 16, colors.gray_80),
  },
  verLine: {
    width: 1,
    marginHorizontal: 10,
    backgroundColor: 'rgba(0, 0, 0,0.1)',
    height: 30,
  },
});
