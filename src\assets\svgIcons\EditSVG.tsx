import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const EditSVG: React.FC<IconProps> = ({
    size = 17,
    color = "#232323",
    ...restProps
}) => {
    const height = (18 / 17) * size;

    return (
        <Svg
            width={size}
            height={height}
            viewBox="0 0 17 18"
            fill="none"
            {...restProps}
        >
            <Path
                d="M16.217 16.756H9.84a.75.75 0 010-1.5h6.377a.75.75 0 010 1.5z"
                fill={color}
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.54 7.7l1.228-1.634c.608-.81.864-1.808.721-2.811A3.764 3.764 0 0013.012.758a3.795 3.795 0 00-5.308.756L.952 10.509C-1.06 13.192.73 16.388.807 16.523a.75.75 0 00.483.357c.058.014.618.138 1.4.138 1.262 0 3.1-.323 4.326-1.957l5.369-7.152a.764.764 0 00.156-.208zM1.96 15.47c.781.096 2.751.163 3.856-1.31l4.996-6.656-3.664-2.752L2.15 11.41c-1.127 1.504-.507 3.345-.19 4.062zM8.048 3.554l3.665 2.75.855-1.138a2.294 2.294 0 00-.456-3.208 2.298 2.298 0 00-3.209.457l-.855 1.14z"
                fill={color}
            />
        </Svg>
    );
};

export default EditSVG;

