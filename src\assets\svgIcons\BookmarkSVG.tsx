import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
  size?: number;
  color?: string;
}

const BookmarkSVG: React.FC<SvgComponentProps> = ({
  size = 20,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 17) / 15} // maintain original aspect ratio (15:17)
      viewBox="0 0 15 17"
      fill="none"
      {...props}
    >
      <Path
        d="M12.702.95c.357.002.699.143.951.392.252.249.395.586.397.938v12.44c-.005.242-.077.476-.206.68-.13.204-.313.37-.53.479a1.361 1.361 0 01-1.381-.094h-.002L7.63 12.601l-.001-.001a.13.13 0 00-.161 0h-.001l-4.34 3.183a1.374 1.374 0 01-1.406.13 1.337 1.337 0 01-.56-.487 1.31 1.31 0 01-.21-.707V2.28a1.324 1.324 0 01.404-.946A1.357 1.357 0 012.32.95h10.382zm0 1.198v0H2.32a.138.138 0 00-.096.039.132.132 0 00-.04.093v12.44l.005.035a.125.125 0 00.015.033c.007.01.015.02.025.027l.03.02.008.004.032.015a.122.122 0 00.036.006.122.122 0 00.066-.021l4.34-3.196.09-.061a1.375 1.375 0 011.436 0l.09.06 4.3 3.182c.02.014.043.022.068.022a.123.123 0 00.068-.022l.007-.005a.126.126 0 00.075-.114V2.28a.133.133 0 00-.049-.104.136.136 0 00-.053-.027.139.139 0 00-.061-.002l-.01.002z"
        fill={color}
      />
    </Svg>
  );
};

export default BookmarkSVG;
