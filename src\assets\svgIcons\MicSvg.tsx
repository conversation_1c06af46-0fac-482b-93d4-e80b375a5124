import * as React from 'react';
import Svg, {Path   } from 'react-native-svg';


type MicSvgProps = {
    size?: number;
    color?: string;
}

function MicSvg({size = 18, color = "white",}: MicSvgProps) {
  return (
    <Svg
      width={size}
      height={23}
      viewBox="0 0 18 23"
      fill= {color}
   
    
      >
      <Path
        d="M9.253.434c-2.283 0-4.14 1.968-4.14 4.388v5.716c0 2.42 1.857 4.388 4.14 4.388 2.283 0 4.14-1.968 4.14-4.388V4.822c0-2.42-1.857-4.388-4.14-4.388zm3.008 10.104c0 1.758-1.35 3.188-3.008 3.188s-3.008-1.43-3.008-3.188V4.822c0-1.758 1.35-3.188 3.008-3.188 1.659 0 3.008 1.43 3.008 3.188v5.716z"
        fill={color}  
      />
      <Path
        d="M16.31 10.39a7.01 7.01 0 01-7.003 7.003 7.01 7.01 0 01-7.002-7.002H.971c0 2.218.87 4.31 2.448 5.888a8.278 8.278 0 005.221 2.422v3.472h1.335v-3.472a8.278 8.278 0 005.22-2.422 8.278 8.278 0 002.45-5.888H16.31z"
        fill={color}
      />
      <Path
        d="M1.237 1.578l16.019 17.163"
        stroke={color}
        strokeWidth={1.36842}
        strokeLinecap="round"
      />
    </Svg>
  );
}

export default MicSvg;
