import React, { useState } from 'react';
import CommonView from '../../../component/CommonView';
import { colors } from '../../../theme/colors';
import {
  Image,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import CustomImage from '../../../component/CustomImage';
import { IMAGES } from '../../../assets/Images';
import Api from '../../../utils/api';
import { RouteProp, useRoute } from '@react-navigation/native';
import ModalWrapper from '../../../component/ModalWrapper';
import { updateOwnershipTranfer } from '../../../api/Chatspace/chatspace.api';
import SingleTickSVG from '../../../assets/svgIcons/SingleTickSVG';

export type AdminPermissions = {
  canInviteUsersViaLink: boolean;
  canManageStories: {
    post: boolean;
    editOthers: boolean;
    deleteOthers: boolean;
  };
  canManageLiveStreams: boolean;
  canDeleteMessages: boolean;
  canPinMessage: boolean;
  canBanUsers: boolean;
  title: string;
  remainsAnonymous: boolean;
  canChangeChatSpaceInfo: boolean;
  canManageMessages: {
    post: boolean;
    editOthers: boolean;
    deleteOthers: boolean;
  };
  canAddNewAdmins: boolean;
};

export const defaultAdminPermissions: AdminPermissions = {
  canInviteUsersViaLink: true,
  canManageStories: {
    post: true,
    editOthers: true,
    deleteOthers: true,
  },
  canManageLiveStreams: true,
  canDeleteMessages: true,
  canPinMessage: true,
  canBanUsers: true,
  title: 'Admin',
  remainsAnonymous: true,
  canChangeChatSpaceInfo: true,
  canManageMessages: {
    post: true,
    editOthers: true,
    deleteOthers: true,
  },
  canAddNewAdmins: true
};

type AdminPermissionsParams = {
  AdminPermissions: {
    userDetails: any;
    userData: any;
  };
};

const AdminPermissions = () => {
  const route = useRoute<RouteProp<AdminPermissionsParams, 'AdminPermissions'>>();

  const user = route.params.userDetails;
  const selectedMember = route.params.userData;

  const chatSpaceId = user.id;
  const userIds = [selectedMember.userId];

  const [adminTitle, setAdminTitle] = useState('Admin');

  const [adminPermissions, setAdminPermissions] = useState<AdminPermissions>({
    canInviteUsersViaLink: true,
    canManageStories: {
      post: true,
      editOthers: true,
      deleteOthers: true,
    },
    canManageLiveStreams: true,
    canPinMessage: true,
    canDeleteMessages: true,
    canBanUsers: true,
    title: adminTitle,
    remainsAnonymous: true,
    canChangeChatSpaceInfo: true,
    canManageMessages: {
      post: true,
      editOthers: true,
      deleteOthers: true,
    },
    canAddNewAdmins: true,
  });

  // const [defaultMemberPermissions, setDefaultMemberPermissions] = useState<DefaultPermissions>(
  //     {
  //         canManageLiveStreams: true,
  //         canRemainAnonymous: true,
  //         canAddNewAdmins: true
  //     }
  // );

  const [openModal, setOpenModal] = useState<boolean>(false);

  const handleSave = async () => {
    try {
      const endpoint = `v1/chatspaces/${chatSpaceId}/adminPrivilege`;
      const payload = { userIds, ...adminPermissions };
      const res = await Api.patch(endpoint, payload);
    } catch (err) {
      console.error(err);
    }
  };

  const handleOwnershipTransfer = async () => {
    try {
      await updateOwnershipTranfer(chatSpaceId, selectedMember.userId);
      console.log('Ownership transferred successfully');
      setOpenModal(false);
    } catch (error) {
      console.error('Error transferring ownership:', error);
    }
  };

  const renderToggle = (label: string, value: boolean, onChange: (value: boolean) => void) => (
    <View style={styles.toggleRow}>
      <Text style={styles.label}>{label}</Text>
      <Switch
        value={value}
        onValueChange={onChange}
        trackColor={{ false: '#E9E9EA', true: colors.mainPurple }}
        thumbColor={colors.white}
      />
    </View>
  );

  return (
    <CommonView
      headerTitle="Admin Rights"
      renderRight={() => (
        <TouchableOpacity onPress={handleSave}>
          <SingleTickSVG size={18} color={colors.white} />
        </TouchableOpacity>
      )}
    >
      <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.avatarSection}>
          <Image
            source={
              selectedMember.user?.image ? { uri: selectedMember.user.image } : IMAGES.profile_image
            }
            style={styles.avatarImage}
          />
          <View style={styles.avatarTextContainer}>
            <Text style={styles.userName}>{selectedMember.user?.name}</Text>
            <Text style={styles.userHandle}>@{selectedMember?.user?.username}</Text>
          </View>

          {/* <View style={{margin: 16}}>
                        <Text style={styles.label}>Custom admin title</Text>
                        <TextInput
                            value={adminTitle}
                            onChangeText={setAdminTitle}
                            style={styles.input}                          
                        />
                    </View> */}
        </View>

        <View style={{ marginTop: 10 }}>
          <Text style={styles.label1}>Custom admin title</Text>
          <TextInput value={adminTitle} onChangeText={setAdminTitle} style={styles.input} />
        </View>

        <Text style={styles.sectionTitle}>Admins do</Text>

        {renderToggle('Invite isers via link', adminPermissions.canInviteUsersViaLink, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canSendMessage: val })),
        )}
        {renderToggle(
          'Manage stories',
          Object.values(adminPermissions.canManageStories).every(Boolean),
          (val) =>
            setAdminPermissions((prev) => ({
              ...prev,
              canManageStories: {
                post: val,
                editOthers: val,
                deleteOthers: val,
              },
            })),
        )}
        {renderToggle(
          'Manage messages',
          Object.values(adminPermissions.canManageMessages).every(Boolean),
          (val) =>
            setAdminPermissions((prev) => ({
              ...prev,
              canManageMessages: {
                post: val,
                editOthers: val,
                deleteOthers: val,
              },
            })),
        )}
        {renderToggle('Pin messages', adminPermissions.canPinMessage, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canPinMessage: val })),
        )}
        {renderToggle('Ban users', adminPermissions.canBanUsers, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canBanUsers: val })),
        )}
        {renderToggle('Delete messages', adminPermissions.canDeleteMessages, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canDeleteMessages: val })),
        )}
        {renderToggle('Edit group info', adminPermissions.canChangeChatSpaceInfo, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canChangeChatSpaceInfo: val })),
        )}
        {renderToggle('Manage live streams', adminPermissions.canManageLiveStreams, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canManageLiveStreams: val })),
        )}
        {renderToggle('Remain anonymous', adminPermissions.remainsAnonymous, (val) =>
          setAdminPermissions((prev) => ({ ...prev, remainsAnonymous: val })),
        )}
        {renderToggle('Add new admins', adminPermissions.canAddNewAdmins, (val) =>
          setAdminPermissions((prev) => ({ ...prev, canAddNewAdmins: val })),
        )}
        <TouchableOpacity
          style={{
            borderRadius: 15,
            backgroundColor: 'rgba(106, 77, 187, 0.1)',
            padding: 16,
            marginTop: 10,
          }}
          onPress={() => {
            setOpenModal(true);
          }}
        >
          <Text style={{ color: colors.mainPurple, textAlign: 'center' }}>Ownership transfer</Text>
        </TouchableOpacity>

        <ModalWrapper isVisible={openModal} onCloseModal={() => setOpenModal(false)}>
          <View>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                textAlign: 'left',
                marginBottom: 16,
                color: colors.black_23,
              }}
            >
              Transfer group ownership
            </Text>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '400',
                textAlign: 'left',
                marginBottom: 16,
                color: 'rgba(35, 35, 35, 1)',
              }}
            >
              This will transfer the full owner rights for {user.displayName} to @
              {selectedMember.user.username}
            </Text>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(243, 243, 243, 1)',
                  paddingVertical: 12,
                  borderRadius: 15,
                  marginRight: 10,
                }}
                onPress={() => setOpenModal(false)}
              >
                <Text style={{ textAlign: 'center', color: colors.black_23, fontWeight: '500' }}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(106, 77, 187, 1)',
                  paddingVertical: 12,
                  borderRadius: 15,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  handleOwnershipTransfer();
                }}
              >
                <Text style={{ color: colors.white, fontWeight: '500' }}>Change owner</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ModalWrapper>
      </ScrollView>
    </CommonView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  avatarSection: {
    alignItems: 'center',
    marginVertical: 16,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
  },
  userHandle: {
    fontSize: 14,
    color: colors.gray_80,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.gray_80,
    marginVertical: 12,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    flex: 1,
    color: colors.black_23,
  },
  kickText: {
    marginTop: 24,
    color: colors.thick_red,
    fontWeight: '400',
    fontSize: 16,
    textAlign: 'center',
  },
  avatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    backgroundColor: '#ccc', // fallback background
  },
  avatarTextContainer: {
    flexDirection: 'column',
  },
  label1: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.gray_80,
  },
  input: {
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 15,
    paddingVertical: 10,
    paddingHorizontal: 12,
    fontSize: 18,
    color: colors.black_23,
    fontWeight: '600',
  },
});

export default AdminPermissions;
