import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import LottieView from 'lottie-react-native';

const AppInfoScreen = () => {
  return (
    <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <SafeAreaView />
      <HeaderBackWithTitle title="App info" />
      <View style={styles.whiteContainer}>
        <LottieView
          source={require('../../../../assets/animatedLottiImages/logo.json')}
          autoPlay
          loop
          style={styles.lottieLogo}
        />
        <View style={styles.centeredContent}>
          <Text style={styles.title}>ChatBucket</Text>
          <Text style={styles.version}>Version 1.0.0</Text>
          <Text style={styles.about}>
            ChatBucket is a next-generation chatting application designed to enhance user
            communication through a seamless, secure, and feature-rich platform.
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  centeredContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  title: {
    fontSize: 24,
    color: colors.black_23,
    fontWeight: '600',
    marginBottom: 10,
  },
  lottieLogo: {
    width: 200,
    height: 200,
    marginTop: 100,
    // marginBottom: -300,
    alignSelf: 'center',
  },
  version: {
    fontSize: 16,
    color: colors.gray_80,
    marginBottom: 12,
    fontWeight: '600',
  },
  about: {
    fontSize: 15,
    color: colors.black_23,
    textAlign: 'center',
    lineHeight: 22,
    marginHorizontal: 10,
  },
});

export default AppInfoScreen;
