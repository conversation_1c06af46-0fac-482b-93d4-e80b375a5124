import * as React from "react";
import Svg, { Path, Circle, SvgProps } from "react-native-svg";
import { colors } from "../../theme/colors";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const TimerSVG: React.FC<IconProps> = ({
    size = 18,
    color = colors.black_23,
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 18 18"
            fill="none"
            {...restProps}
        >
            <Path
                d="M7.56 6.12v2.938c0 .732.593 1.326 1.326 1.326h4.28"
                stroke={color}
                strokeWidth={1.5}
                strokeLinecap="round"
            />
            <Circle cx={9} cy={9} r={8.25} stroke={color} strokeWidth={1.5} />
        </Svg>
    );
};

export default TimerSVG;

