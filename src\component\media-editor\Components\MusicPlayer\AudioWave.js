import React, { useEffect, useState } from "react";
import { View } from "react-native";
import { Canvas, Path, Skia, Paint } from "@shopify/react-native-skia";
import { useProgress } from "react-native-track-player";

const AudioWaveform = ({ width = 300, height = 80 }) => {
  const barWidth = 4;
  const gap = 6;
  const totalBars = Math.floor(width / (barWidth + gap));
  const { position, duration } = useProgress();
  const [waveformData, setWaveformData] = useState(generateWaveform());

  function generateWaveform() {
    return Array.from({ length: totalBars }, (_, i) => {
      const cycle = i % 5;
      return cycle === 0 ? 50 : cycle === 1 ? 35 : cycle === 2 ? 20 : cycle === 3 ? 30 : 45;
    });
  }

  useEffect(() => {
    if (duration > 0) {
      setWaveformData(generateWaveform());
    }
  }, [duration]);

  return (
    <View style={{ width, height, alignItems: "center", overflow: "hidden" ,zIndex:1,backgroundColor:'yellow',alignSelf:'center'}}>
      <Canvas style={{ width, height }}>
        {waveformData.map((barHeight, i) => {
          const x = i * (barWidth + gap);
          const isPlayed = (i / totalBars) < (position / duration);

          return (
            <Path
              key={i}
              path={Skia.Path.Make().moveTo(x, height / 2 - barHeight / 2).lineTo(x, height / 2 + barHeight / 2)}
              style="stroke"
              strokeWidth={barWidth}
            >
              <Paint color={isPlayed ? "white" : "red"} />
            </Path>
          );
        })}
      </Canvas>
    </View>
  );
};

export default AudioWaveform;
