import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import RenderUserIcon from '../../../component/RenderUserIcon';
import { AppStyles } from '../../../theme/appStyles';
import ButtonPurple from '../../../component/ButtonPurple';
import { useTranslation } from 'react-i18next';
import { commonFontStyle } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import { SCREENS } from '../../../navigation/screenNames';
import { useNavigation } from '@react-navigation/native';
import { dayPipe } from '../../../utils/commonFunction';
interface IProps {
  data?: any;
  type?: 'remove' | 'follow';
  onPress?: () => void;
  name?: string;
  dec?: string;
  image?: string;
  time?: string;
  followers?: number;
  buttonVisible?: boolean;
  showTime?: boolean;
  showDot?: boolean;
  onPressBtn?: () => void;
}

const ChannelsView = ({
  data,
  type,
  onPress,
  dec,
  name,
  image,
  followers,
  buttonVisible = true,
  showTime = false,
  time,
  showDot = false,
  onPressBtn,
}: IProps) => {
  const { t } = useTranslation();
  const navigation = useNavigation();

  return (
    <TouchableOpacity style={styles.container} disabled={!onPress} onPress={onPress}>
      <RenderUserIcon url={image} size={50} />
      <View style={{ ...AppStyles.flex, gap: 5 }}>
        <Text numberOfLines={1} style={styles.nameText}>
          {name ?? 'Anonymous'}
        </Text>
        {dec && (
          <Text numberOfLines={1} style={styles.followerText}>
            {dec}
          </Text>
        )}
      </View>
      <View style={{ alignItems: 'flex-end', gap: 10 }}>
        {showTime && <Text style={styles.timeText}>{dayPipe(time)}</Text>}
        {showDot && <View style={styles.dot} />}
      </View>
      {buttonVisible && (
        <ButtonPurple
          title={type == 'remove' ? t('Remove') : t('Follow')}
          extraStyle={styles.extraStyle}
          titleColor={colors.mainPurple}
          onPress={onPressBtn}
        />
      )}
    </TouchableOpacity>
  );
};

export default ChannelsView;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  nameText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  followerText: {
    ...commonFontStyle(400, 14, colors.gray_80),
  },
  extraStyle: {
    borderRadius: 30,
    height: 35,
    backgroundColor: colors.opacity_main_purple_15,
  },
  timeText: {
    ...commonFontStyle(400, 13, colors._B1B1B1_gray),
  },
  dot: {
    height: 8,
    width: 8,
    backgroundColor: colors.mainPurple,
    borderRadius: 8 / 2,
  },
});
