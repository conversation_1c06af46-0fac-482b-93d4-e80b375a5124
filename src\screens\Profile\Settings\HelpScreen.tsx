import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import ButtonPurple from '../../../component/ButtonPurple';
import { colors } from '../../../theme/colors';
import { hp } from '../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { IMAGES } from '../../../assets/Images';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { useTranslation } from 'react-i18next';
import FaqsSVG from '../../../assets/svgIcons/FaqsSVG';
import AppInfoSVG from '../../../assets/svgIcons/AppInfoSVG';
import ContactUsSVG from '../../../assets/svgIcons/ContactUsSVG';
import PrivatePolicySVG from '../../../assets/svgIcons/PrivatePolicySVG';
import RightArrowSVG from '../../../assets/svgIcons/RightArrowSVG';

const HelpScreen = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();

  return (
    <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <SafeAreaView>
        <HeaderBackWithTitle title={t('Help')} />
      </SafeAreaView>
      <View style={styles.whiteContainer}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <TouchableOpacity style={styles.row} onPress={() => navigation.navigate('FAQScreen')}>
            <FaqsSVG />
            <Text style={styles.rowText}>{t('FAQ')}</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.row}
            onPress={() => navigation.navigate('TermsPrivacyScreen')}
          >
            <PrivatePolicySVG />
            <Text style={styles.rowText}>{t('Terms & Privacy policy')}</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.row} onPress={() => navigation.navigate('AppInfoScreen')}>
            <AppInfoSVG />
            <Text style={styles.rowText}>{t('App info')}</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity>
          <View style={{ flex: 1 }} />

          <View style={styles.buttonContainer}>
            <ButtonPurple
              title={t('Contact us')}
              leftSVG={<ContactUsSVG />}
              onPress={() => navigation.navigate('ContactUsScreen')}
              extraStyle={{
                alignSelf: 'center',
                minWidth: 180,
                borderRadius: 100,
                marginBottom: hp(6),
              }}
            />
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: hp(6),
    paddingBottom: hp(2),
    paddingHorizontal: hp(2),
    backgroundColor: colors.mainPurple,
  },
  headerBack: {
    padding: 8,
    marginRight: 8,
  },
  headerBackText: {
    color: colors.white,
    fontSize: 22,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: colors.white,
    fontSize: 20,
    fontWeight: '600',
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_f3,
  },
  rowText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: colors.black_23,
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 40,
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
  },
});

export default HelpScreen;
