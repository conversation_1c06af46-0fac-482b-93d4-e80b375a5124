import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

type Props = {
  size?: number;
  color?: string;
};

const AllMediaSVG: React.FC<Props> = ({ size = 20, color = '#232323', ...props }) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Path
        d="M6.054 9.689c-.244 0-.49-.064-.712-.193a1.41 1.41 0 01-.713-1.235V5.488c0-.515.266-.977.713-1.234.446-.258.979-.258 1.425 0L9.168 5.64c.447.258.713.72.713 1.235 0 .515-.266.977-.713 1.234l-2.4 1.386a1.42 1.42 0 01-.714.194zm0-4.378a.175.175 0 00-.087.025.168.168 0 00-.088.152v2.773c0 .084.047.13.088.152.04.024.103.043.175 0l2.402-1.386a.167.167 0 00.087-.152.167.167 0 00-.087-.152L6.142 5.336a.175.175 0 00-.087-.025z"
        fill={color}
      />
      <Path
        d="M6.875 13.75A6.882 6.882 0 010 6.874 6.882 6.882 0 016.875 0a6.882 6.882 0 016.874 6.875 6.883 6.883 0 01-6.874 6.874zm0-12.5A5.631 5.631 0 001.25 6.875a5.631 5.631 0 005.625 5.624 5.631 5.631 0 005.624-5.624A5.631 5.631 0 006.875 1.25z"
        fill={color}
      />
      <Path
        d="M18.123 20h-8.75A1.877 1.877 0 017.5 18.123v-3.218a.625.625 0 011.25 0v3.218c0 .345.28.625.625.625h8.75c.344 0 .624-.28.624-.625v-8.75a.626.626 0 00-.625-.624h-3.218a.625.625 0 010-1.25h3.219c1.033 0 1.874.841 1.874 1.875v8.75a1.877 1.877 0 01-1.875 1.874z"
        fill={color}
      />
      <Path
        d="M15.082 20a.625.625 0 01-.526-.286l-2.994-4.638-2.66 4.128a.625.625 0 11-1.054-.672l2.664-4.134a1.24 1.24 0 011.05-.572c.43 0 .822.215 1.052.574l2.992 4.635a.625.625 0 01-.524.964z"
        fill={color}
      />
      <Path
        d="M18.124 19.998H15.08a.625.625 0 010-1.25h3.044a.625.625 0 010 1.25z"
        fill={color}
      />
      <Path
        d="M18.756 19.825a.624.624 0 01-.526-.287l-1.669-2.587-1.367 2.124a.625.625 0 11-1.052-.677l1.37-2.124a1.24 1.24 0 011.05-.573c.428 0 .821.215 1.051.575l1.667 2.585a.625.625 0 01-.524.963zM14.999 13.75c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25a1.251 1.251 0 010 2.5z"
        fill={color}
      />
    </Svg>
  );
};

export default AllMediaSVG;


