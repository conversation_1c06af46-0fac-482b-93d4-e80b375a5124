import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { View, FlatList, TouchableOpacity, Text, ActivityIndicator, Image, Alert, Platform, ToastAndroid, StyleSheet, AppState } from 'react-native';
import { RTCView, mediaDevices, MediaStream } from 'react-native-webrtc';
import { colors } from '../../theme/colors';
import { GeneratedSummaryState, useWebRTCStream, WebRTCStatus } from '../hooks/useWebrtchhook';
import { ScrollView } from 'react-native-gesture-handler';
import ViewShot, { captureRef } from 'react-native-view-shot';
import { Ionicons } from '../../utils/vectorIcons';
import LinearGradient from 'react-native-linear-gradient';
import { hexToRgba } from '../../theme/fonts';
import axios from 'axios';
import RNFS from 'react-native-fs'
import { useNetInfo } from '@react-native-community/netinfo';
import Sound from 'react-native-sound';
import RotateSVG from '../../assets/svgIcons/RotateSVG';
import { ImageContent } from '../Screens/BScanScreen';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useMe } from '../../hooks/util/useMe';
import { useIsFocused } from '@react-navigation/native';


type CameraProps = {
  mode: 'Photo' | 'Video';
  selectedModeOptionProp?: ModeOptionsType;
  handleSoundRef: (value: any) => void;
  handleModeOptions(option: ModeOptionsType, flipMode?: boolean): void;
  localStream: MediaStream | null;
  startStreaming: () => void;
  stopStreaming: () => void;
  status: WebRTCStatus;
  generatedSummaryState: GeneratedSummaryState;
  flipCamera?: () => void;
  handleImageContent?: (value: ImageContent) => void;
  flipMode: () => void;
  clearData: () => void;
  selectedLanguage: string | null,
  onCaptureModeChange?: (vlaue: CaptureMode) => void;
};

export type CaptureMode = "image" | "book_read_out" | 'live' | 'record' | 'currency_reader';

export type ModeOptionsType = {
  name: string;
  onClick?: () => void;
  id: CaptureMode;
};



export const InitialModeOptions: Record<'Photo' | 'Video', ModeOptionsType[]> = {
  Photo: [
    { name: 'Image', id: 'image' },
    { name: 'Book read out', id: 'book_read_out' },
    { name: 'Currency reader', id: 'currency_reader' },
  ],
  Video: [
    { name: 'Live', id: 'live' },
    // { name: 'Upload', id: 'upload' },

    { name: 'Record', id: 'record' },
    // { name: 'Currency reader', id: 'currency_reader' },
  ],
};

export type CameraRendererRef = {
  takePicture: () => Promise<string | null>;
  getCurrentImage: () => string | null;
  clearAllData: () => void;
};

const CameraRenderer = forwardRef<CameraRendererRef, CameraProps>(
  (
    {
      mode,
      selectedModeOptionProp,
      handleSoundRef,
      handleModeOptions,
      localStream,
      status,
      startStreaming,
      stopStreaming,
      generatedSummaryState,
      flipCamera = () => { },
      handleImageContent = () => { },
      flipMode,
      clearData,
      selectedLanguage = 'eng',
      onCaptureModeChange
    },
    ref,
  ) => {
    const viewShotRef = useRef<ViewShot>(null);
    const [selectedModeOption, setSelectedModeOption] = useState<ModeOptionsType>(
      selectedModeOptionProp || InitialModeOptions[mode][0],
    );
    const { user } = useMe()
    const [currImage, setCurrImage] = useState<string | null>(null);
    const [imageText, setImageText] = useState<string | null>('');
    const [CurrentAudioFile, setCurrentAudioFile] = useState<string | null>('')
    const [loading, setLoading] = useState<boolean>(false);
    const [captureMode, setCaptureMode] = useState<CaptureMode>(mode == 'Photo' ? 'image' : 'live')
    const { isConnected } = useNetInfo()
    const soundRef = useRef<Sound | null>(null);
    const validationSoundRef = useRef<Sound | null>(null);
    const cancelTokenRef = useRef<import("axios").CancelTokenSource | null>(null);
    const isFocused = useIsFocused();

    // const { flipCamera } =
    //   useWebRTCStream();

    useEffect(() => {
      setSelectedModeOption(selectedModeOptionProp || InitialModeOptions[mode][0]);
      setCaptureMode(mode == 'Video' ? 'live' : 'image');
    }, [mode]);

    console.log("--------------currImage", currImage);
    console.log("--------------imageText", imageText);

    const BASE_URL = "http://**********:5000";
    const BASE_URL2 = "http://**********:8181"
    const BASE_URL3 = "http://**********:8282";


    const finalURL = captureMode == 'image'
      ? 'http://**********:8181/api/summarize'
      : captureMode == 'book_read_out'
        ? 'http://**********:5000/api/ocr'
        : captureMode == 'currency_reader'
          ? 'http://**********:8282/api/currency_detect'
          : '';


    const uploadToOCR = async (imagePath?: string) => {
      console.log("imagePath---------------------", imagePath);
      setLoading(true);
      handleImageContent({ image: imagePath, text: '', mode: mode == 'Photo' ? 'image' : 'video' })

      // cancel old request if running
      if (cancelTokenRef.current) {
        cancelTokenRef.current?.cancel();
      }

      // create a new cancel token
      const CancelToken = axios.CancelToken;
      cancelTokenRef.current = CancelToken.source();

      try {
        const LANGUAGE = captureMode == 'image' ? selectedLanguage : 'eng';

        const formData = new FormData();
        formData.append("image", {
          uri: imagePath,
          type: "image/jpeg",
          name: "photo.jpg",
        });
        formData.append("lang", LANGUAGE);
        formData.append("rotation", "0");
        formData.append("session_id", user?._id);

        projectAudioFiles(require('../../assets/audios/waitResponse.mp3'));

        const res = await axios.post(finalURL, formData, {
          headers: { "Content-Type": "multipart/form-data" },
          cancelToken: cancelTokenRef.current.token,  // attach cancel token
        });
        console.log('----------res', JSON.stringify(res.data, null, 2));

        const data = res.data;
        setImageText(data?.text);

        if (data.audio_file) {
          const audioUrl = captureMode == 'book_read_out' ? `${BASE_URL + data.audio_file}` : captureMode == 'image' ? `${BASE_URL2 + data.audio_file}` : `${BASE_URL3 + data?.audio_file}`;
          playAudio(audioUrl, false);
          handleImageContent({ image: audioUrl, text: data?.text, mode: mode == 'Photo' ? 'image' : 'video' });
        }

      } catch (err: any) {
        if (axios.isCancel(err)) {
          console.log("❌ Request canceled:", err.message);
          setTimeout(() => {
            projectAudioFiles(require('../../assets/audios/requestCancelled.mp3'));
          }, 100);
        } else {
          console.log("❌ Upload failed:", err);
          setTimeout(() => {
            projectAudioFiles(require('../../assets/audios/serverBusy.mp3'));
          }, 100);
          handleImageContent({ image: imagePath, text: '', mode: mode == 'Photo' ? 'image' : 'video' })
          clearAllData();
          ToastAndroid.show('Something went wrong, please try again later.', ToastAndroid.SHORT);
        }
      } finally {
        // setLoading(false);
        console.log('finalli loading stoppeddddd---------')
        cancelTokenRef.current = null;
      }
    };


    const downloadAudio = async (audioUrl: string, isNotDownloaded: boolean = true): Promise<string | null> => {
      try {
        if (isNotDownloaded) return null;
        const localPath = `${RNFS.DownloadDirectoryPath}/downloaded_audio_${Date.now()}.wav`;
        console.log("----------------------localPath", localPath);
        const res = await RNFS.downloadFile({
          fromUrl: audioUrl,
          toFile: localPath,
        }).promise;
        console.log("---------------resdownload", res);

        if (res.statusCode === 200) {
          console.log("Saved audio locally:", localPath);
          return localPath;
        } else {
          console.log("Download failed:", res);
          return null;
        }
      } catch (err) {
        console.log("Download error:", err);
        return null;
      }
    };

    const projectAudioFiles = (path: string) => {
      try {
        console.log('------------------Loading audio:', path);

        if (validationSoundRef.current) {
          validationSoundRef.current.stop(() => {
            validationSoundRef.current?.release();
            validationSoundRef.current = null;
            console.log("------------------Previous sound stopped and released");
            loadAndPlayNewSound(path);
          });
        } else {
          console.log('---------noAudio');
          loadAndPlayNewSound(path);
        }
      } catch (error) {
        console.log('-------------Error in projectAudioFiles--------------------', error);
      }
    }



    const loadAndPlayNewSound = (path: string) => {
      console.log('----------loadAndPlayNewSound');
      try {
        validationSoundRef.current = new Sound(path, (error) => {
          if (error) {
            console.log('Failed to load sound', error);
            validationSoundRef.current = null;
            return;
          }

          console.log('Duration in seconds: ' + validationSoundRef.current?.getDuration());

          validationSoundRef.current?.play((success) => {
            if (success) {
              console.log('Successfully finished playing');
            } else {
              console.log('Playback failed due to decoding errors');
            }
            // Only release after playback completes
            validationSoundRef.current?.release();
            validationSoundRef.current = null;
          });
        });
      } catch (error) {
        console.log('-------------Error loading new sound--------------------', error);
      }
    }


    const playAudio = async (audioUrl: string, isLocal: boolean = true) => {
      console.log("audioUrl----------------", audioUrl)
      if (validationSoundRef.current) {
        validationSoundRef.current.stop(() => {
          validationSoundRef.current?.release();
          validationSoundRef.current = null;
          console.log("------------------Stopped validation audio");
        });
      }
      const downloadedLocalPath = await downloadAudio(audioUrl, isLocal);
      const localPath = isLocal ? audioUrl : downloadedLocalPath
      if (!localPath) {
        setLoading(false);
        ToastAndroid.show("Failed to download audio", ToastAndroid.SHORT);
        return;
      }
      setCurrentAudioFile(localPath);
      const sound = new Sound(localPath, undefined, (error) => {
        try {
          handleSoundRef(sound);
          soundRef.current = sound;
          setLoading(false)
          sound.play((success) => {
            if (success) {
              console.log('Playback finished successfully');
              soundRef.current?.stop(() => {
                soundRef.current?.release();
                soundRef.current = null;
              });
            } else {
              console.log('Playback failed');
            }
          });
        } catch (error) {
          setLoading(false)
          console.log("------------error Sound", error);
        }
        console.log('callbackerro----------------------', error)
      });
      console.log("----------------sound", sound);
    };


    const takePicture = async (): Promise<string | null> => {
      if (viewShotRef.current && localStream) {
        try {
          const uri = await captureRef(viewShotRef, {
            format: 'jpg',
            quality: 1,
            handleGLSurfaceViewOnAndroid: true,
          });
          console.log('📸 Captured image:', uri);
          setCurrImage(uri);
          if (!uri) {
            console.log("image not captured", uri)
          }
          return uri;
        } catch (e) {
          console.error('Failed to capture screenshot:', e);
          Alert.alert('Error', 'Failed to capture image');
          return null;
        }
      }
      return null;
    };

    // Expose methods to parent
    useImperativeHandle(ref, () => ({
      takePicture,
      getCurrentImage: () => currImage,
      clearAllData
    }));

    async function handleOptionClick(item: ModeOptionsType) {
      console.log('-------------handleoptions', item);
      if (mode === 'Photo' && (item.id === 'book_read_out' || item.id === 'currency_reader')) {
        handleModeOptions(item, true);
        return;
      }

      if (mode === 'Photo' && item.id === 'image') {
        if (!isConnected) {
          projectAudioFiles(require('../../assets/audios/yourOffline.mp3'));
          return null;
        }
        const path = await takePicture();
        console.log("---------------path", path);
        uploadToOCR(path as string);
        return;
      }

      setSelectedModeOption(item);

      switch (item.id) {
        case 'record':
          if (status === 'Streaming') {
            stopStreaming();
          } else {
            startStreaming();
          }
          break;

        default:
          break;
      }
    }


    const clearAllData = async () => {
      setLoading(false);
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel("User cancelled the request");
        cancelTokenRef.current = null;
      }
      const exists = await RNFS.exists(CurrentAudioFile as string);
      console.log('exists---------', exists)
      if (exists) {
        await RNFS.unlink(CurrentAudioFile as string);
        console.log("🗑️ Deleted file:", CurrentAudioFile);
      }
      clearData();
      setCurrImage(null);
      setImageText(null);
      soundRef.current?.stop(() => {
        soundRef.current?.release();
        soundRef.current = null;
      });
    }

    const clearSounds = () => {
      if (validationSoundRef.current || soundRef.current) {
        soundRef.current?.stop(() => {
          soundRef.current?.release();
          soundRef.current = null;
        });
        validationSoundRef.current?.stop(() => {
          validationSoundRef.current?.release();
          validationSoundRef.current = null;
        });
      }
    }



    useEffect(() => {
      const subscription = AppState.addEventListener("change", (nextState) => {
        if (nextState === "background" || nextState === "inactive") {
          clearSounds();
        }
      });
      return () => {
        subscription.remove();
        clearSounds();
        clearAllData()
      }
    }, [])


    return (
      <View style={{ flex: 1 }}>
        {/* Display captured image if available */}
        {currImage ? (
          <View style={{ flex: 1 }}>
            <Image
              source={{ uri: currImage }}
              style={{ flex: 1 }}
              resizeMode="cover"
            />

            {/* Overlay Loader */}
            {loading ? <View
              style={{
                ...StyleSheet.absoluteFillObject, // covers the whole parent
                backgroundColor: hexToRgba(colors.black_23, 0.2),
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <ActivityIndicator size="large" color={colors.white} />
              <Text style={{ fontSize: 16, fontWeight: '500', color: colors.white }}>Your description is getting ready..</Text>
            </View> : null}
          </View>
        ) : (
          /* Use ViewShot to wrap RTCView for capturing */
          <ViewShot
            ref={viewShotRef}
            options={{
              format: 'jpg',
              quality: 0.9,
              handleGLSurfaceViewOnAndroid: true
            }}
            style={{ flex: 1 }}
          >
            {localStream && (
              <RTCView
                streamURL={localStream.toURL()}
                objectFit="cover"
                style={{ flex: 1 }}
              />
            )}
          </ViewShot>
        )}

        {generatedSummaryState.summary != '' && (
          <SummaryRenderer summary={generatedSummaryState.summary} />
        )}

        {((imageText && imageText.trim().length > 0) || loading) && mode === 'Photo' ? (
          // {mode === 'Photo' ? (
          <SummaryRenderer summary={imageText} loading={loading} onClose={clearAllData} />
        ) : null}


        {!currImage && !imageText && !loading ? (
          <LinearGradient
            start={{ x: 1, y: 0 }}
            end={{ x: 1, y: 1 }}
            colors={['#fff', '#fff', '#fff']}
            style={{}}
          >
            <View style={{ flexDirection: 'column' }}>
              <FlatList
                data={InitialModeOptions[mode]}
                horizontal
                renderItem={({ item }) => (
                  <TouchableOpacity style={{ alignItems: 'center', marginTop: 13 }} onPress={() => {
                    setCaptureMode(item.id);
                    console.log("-------------------------", item.id);
                    onCaptureModeChange?.(item.id)
                  }}>
                    <Text style={{ fontSize: 15, fontWeight: '500', letterSpacing: 1, color: captureMode === item.id ? colors.mainPurple : colors.black_23 }}>{item.name}</Text>
                    <View style={{ height: 3, backgroundColor: captureMode === item.id ? colors.mainPurple : "transparent", width: '80%', borderTopLeftRadius: 10, borderTopRightRadius: 10, marginTop: 13 }} />
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.name}
                contentContainerStyle={{
                  flexDirection: 'row', justifyContent: 'space-evenly', flex: 1,
                  borderBottomWidth: 1, borderBottomColor: hexToRgba(colors.black, 0.15)
                }}
              />
            </View>
          </LinearGradient>
        ) : null}


        {!currImage && !imageText && !loading ? <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: colors.white, paddingHorizontal: 50, paddingVertical: 12 }}>
          <View style={{}}></View>
          <TouchableOpacity onPress={() => {
            console.log('-----------------------');
            handleOptionClick(selectedModeOption)
          }} style={{ backgroundColor: colors.white, height: 58, width: 58, borderRadius: 30, alignItems: 'center', justifyContent: 'center', elevation: 1 }}>
            <View style={{ backgroundColor: colors.mainPurple, height: 46, width: 46, borderRadius: 30, alignItems: 'center', justifyContent: 'center' }} />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => {
            console.log('--------------------clicke');
            flipCamera();
            // flipMode();
          }}>
            <RotateSVG size={24} color={colors.black_23} />
          </TouchableOpacity>
        </View> : null}

        {status === 'Streaming' && (
          <StreamingUI
            onEndRecording={() => {
              stopStreaming();
            }}
          />
        )}
        {status === 'Connecting' && <LoadingAction />}
      </View>
    );
  },
);

export default CameraRenderer;

type StreamingUIProps = {
  onEndRecording: () => void;
};
function StreamingUI({ onEndRecording }: StreamingUIProps) {
  return (
    <View
      style={{
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        backgroundColor: 'white',
        gap: 10,
      }}
    >
      <View></View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 10,
        }}
      >
        <View
          style={{
            height: 10,
            width: 10,
            borderRadius: 50,
            backgroundColor: 'red',
          }}
        ></View>
        <Text style={{ fontSize: 20, color: 'black' }}>Recording</Text>
      </View>
      <TouchableOpacity
        onPress={onEndRecording}
        style={{ backgroundColor: 'red', padding: 10, borderRadius: 50, paddingHorizontal: 20 }}
      >
        <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Stop</Text>
      </TouchableOpacity>
    </View>
  );
}

function LoadingAction() {
  return (
    <View
      style={{
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: 10,
        backgroundColor: 'white',
        gap: 10,
      }}
    >
      <ActivityIndicator size="large" color={colors.mainPurple} />
    </View>
  );
}

function SummaryRenderer({ summary, loading, onClose }: { summary: string | null, loading?: boolean, onClose?: () => void }) {
  return (
    <View
      style={{
        maxHeight: '35%',
        position: 'relative',
        backgroundColor: '#fafafa'
      }}
    >
      <TouchableOpacity onPress={onClose} style={{ width: 55, height: 55, borderRadius: 30, top: -15, backgroundColor: "#fafafa", justifyContent: 'center', alignItems: 'center', alignSelf: 'center' }}>
        <Ionicons name='close' size={25} color={colors.black_23} />
      </TouchableOpacity>
      {/* <View
        style={{
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 10,
          backgroundColor: 'red',
          height: 50,
          width: 50,
          position: 'absolute',
          top: -20,

          left: '50%',
          transform: [{ translateX: -25 }],
        }}
      ></View> */}
      <ScrollView showsVerticalScrollIndicator={false} style={{ marginTop: -15 }}>
        {!summary && loading ? <SkeletonPlaceholder highlightColor={'#d9d9d9'} backgroundColor={colors.white}>
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
          <View style={{ height: 8, marginHorizontal: 21, marginBottom: 10 }} />
        </SkeletonPlaceholder>
          :
          <Text
            style={{
              paddingHorizontal: 10,
              paddingBottom: 10,
              fontSize: 16,
              color: 'black',
            }}
          >
            {summary}
          </Text>
        }
      </ScrollView>
    </View>
  );
}
