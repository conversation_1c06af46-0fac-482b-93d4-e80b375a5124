import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TextInput,
  Alert,
} from 'react-native';

import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { _openAppSetting } from '../../../utils/locationHandler';
import { useCallContext } from '../../../Context/CallProvider';
import { IMAGES } from '../../../assets/Images';
import { Participant } from '../../../types/calls.types';
import { commonFontStyle } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import { useMe } from '../../../hooks/util/useMe';
import ContactAvatarSVG from '../../../assets/svgIcons/ContactAvatarSVG';
import SearchSVG from '../../../assets/svgIcons/SearchSVG';

// create a component

type CallMemberModalProps = {
  callMembersModal: boolean;
  setCallMembersModal: React.Dispatch<React.SetStateAction<boolean>>;
  searchCallMembers: (text: string) => void;
  participants: Participant[];
  searchText: string;
};

const CallMemberModal = ({
  callMembersModal,
  setCallMembersModal,
  searchCallMembers,
  participants,
  searchText,
}: CallMemberModalProps) => {
  const { callDetails, removeParticipantFromCall } = useCallContext();
  const { user } = useMe();

  const confirmAndRemoveParticipant = (participantId: string, participantName: string) => {
    Alert.alert(
      'Remove Participant',
      `Are you sure you want to remove ${participantName}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => removeParticipantFromCall(participantId),
          style: 'destructive',
        },
      ],
      { cancelable: true },
    );
  };
  return (
    <Modal
      transparent={true}
      visible={callMembersModal}
      onRequestClose={() => {
        setCallMembersModal(false);
      }}
    >
      <TouchableOpacity
        disabled={false}
        style={{
          flex: 1,
          justifyContent: 'flex-end',
        }}
        activeOpacity={0.6}
        onPress={() => {
          setCallMembersModal(false);
        }}
      >
        <LinearGradient colors={['#000000AA', '#000000AA']}>
          <View style={{ paddingHorizontal: 20 }}>
            <BlurView style={styles.blurContainer} blurType="dark" blurAmount={20} />
            <TouchableOpacity
              style={{ paddingVertical: 10 }}
              onPress={() => {
                setCallMembersModal(false);
              }}
            >
              <View
                style={{
                  width: 50,
                  height: 5,
                  alignSelf: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: 50,
                }}
              ></View>
            </TouchableOpacity>
            <Text style={[styles.title, {}]}>
              Call members ({callDetails?.participants?.length})
            </Text>
            <View
              style={{
                borderWidth: 0.5,
                borderBottomColor: '#aaa',
                marginVertical: 12,
              }}
            ></View>
            <View style={styles.searchContainer}>
              <SearchSVG size={16} color={colors.white} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search"
                placeholderTextColor="#c7c7c7"
                onChangeText={(text) => {
                  searchCallMembers(text);
                }}
                value={searchText}
              />
            </View>
            <FlatList
              data={participants}
              style={{ marginTop: 10, marginBottom: 20 }}
              renderItem={({ item }) => (
                <View style={styles.languageItem}>
                  {/* <Image source={IMAGES.userImage} style={{ width: 40, height: 40 }} /> */}
                  <ContactAvatarSVG size={30} />
                  <Text
                    style={[
                      {
                        flex: 1,
                        fontSize: 16,
                        color: '#fff',
                        marginLeft: 10,
                      },
                    ]}
                  >
                    {item.client?.name}
                  </Text>
                  {callDetails.initiatorId === user?._id && (
                    <>
                      <TouchableOpacity onPress={() => {}}>
                        <Image
                          source={IMAGES.mute_white}
                          style={{ width: 25, height: 25, marginRight: 20 }}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => {
                          confirmAndRemoveParticipant(item.participantId, item.client?.name);
                        }}
                      >
                        <Image
                          source={IMAGES.endcall}
                          style={{ width: 35, height: 35 }}
                          resizeMode="contain"
                        />
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              )}
              keyExtractor={(item) => item.participantId}
            />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  callBgIcon: {
    position: 'absolute',
    height: 215,
    resizeMode: 'stretch',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  iconStyle: {
    width: 40,
    height: 40,
  },
  iconStyle1: {
    width: 24,
    height: 24,
  },
  endcallStyle: {
    width: 50,
    height: 50,
  },
  name: {
    ...commonFontStyle(600, 20, colors.white),
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingVertical: 15,
  },
  buttonContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    right: 10,
  },

  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  video: {
    width: '100%',
    height: 200,
    backgroundColor: '#000',
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    color: '#fff',
    fontSize: 14,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  flag: {
    fontSize: 18,
    marginRight: 10,
  },
  languageName: {
    flex: 1,
    fontSize: 16,
    color: '#fff',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  box: {
    width: 100,
    height: 100,
    backgroundColor: 'salmon',
    borderRadius: 8,
    position: 'absolute',
    top: 100,
    left: 100,
    zIndex: 1000,
    elevation: 10,
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  centerContent: {
    flex: 1,
    width: '100%',
  },

  gridContainer: {
    flex: 1,
    width: '100%',
  },

  fullScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  twoParticipantsContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  halfScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  threeParticipantsContainer: {
    flex: 1,
    width: '100%',
  },
  firstRow: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  secondRow: {
    flex: 1,
    width: '100%',
  },
  halfWidthTile: {
    flex: 1,
    width: '50%',
    height: '100%',
  },
  fullWidthTile: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});

//make this component available to the app
export default CallMemberModal;
