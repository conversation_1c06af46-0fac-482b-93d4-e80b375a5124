import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TextInput,
} from 'react-native';

import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { _openAppSetting } from '../../../utils/locationHandler';
import { IMAGES } from '../../../assets/Images';

import { commonFontStyle } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import SearchSVG from '../../../assets/svgIcons/SearchSVG';

type LanguageModalProps = {
  languageModal: boolean;
  setLanguageModal: React.Dispatch<React.SetStateAction<boolean>>;
  filteredLanguages: (
    | {
        name: string;
        code: string;
        flag: null;
      }
    | {
        name: string;
        code: string;
        flag: string;
      }
  )[];
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  search: string;
  selectedLanguage: string;
  setSelectedLanguage: React.Dispatch<React.SetStateAction<string>>;
};

// create a component
const LanguageModal = ({
  languageModal,
  setLanguageModal,
  setSearch,
  search,
  selectedLanguage,
  setSelectedLanguage,
  filteredLanguages,
}: LanguageModalProps) => {
  return (
    <Modal
      transparent={true}
      visible={languageModal}
      onRequestClose={() => {
        setLanguageModal(false);
      }}
    >
      <TouchableOpacity
        disabled={false}
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          elevation: 10,
        }}
        activeOpacity={0.6}
        onPress={() => {
          setLanguageModal(false);
        }}
      >
        <LinearGradient colors={['#000000AA', '#000000AA']}>
          <View style={{ padding: 15 }}>
            <BlurView style={styles.blurContainer} blurType="dark" blurAmount={20} />
            <Text style={styles.title}>Select language</Text>
            <View
              style={{
                borderWidth: 0.5,
                borderBottomColor: '#aaa',
                marginVertical: 12,
              }}
            ></View>
            <View style={styles.searchContainer}>
              <SearchSVG size={16} color={colors.white} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search languages"
                placeholderTextColor="#c7c7c7"
                onChangeText={setSearch}
                value={search}
              />
            </View>
            <FlatList
              data={filteredLanguages}
              keyExtractor={(item) => item.code}
              style={{ marginTop: 10 }}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.languageItem}
                  onPress={() => setSelectedLanguage(item.code)}
                >
                  <Text style={styles.flag}>{item.flag}</Text>
                  <Text
                    style={[
                      styles.languageName,
                      {
                        right: item.name == 'Original language' ? 12 : 0,
                      },
                    ]}
                  >
                    {item.name}
                  </Text>
                  {selectedLanguage === item.code ? (
                    <Image source={IMAGES.selected_white} style={{ width: 20, height: 20 }} />
                  ) : (
                    <Image source={IMAGES.unselected_white} style={{ width: 20, height: 20 }} />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  callBgIcon: {
    position: 'absolute',
    height: 215,
    resizeMode: 'stretch',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  iconStyle: {
    width: 40,
    height: 40,
  },
  iconStyle1: {
    width: 24,
    height: 24,
  },
  endcallStyle: {
    width: 50,
    height: 50,
  },
  name: {
    ...commonFontStyle(600, 20, colors.white),
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingVertical: 15,
  },
  buttonContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    right: 10,
  },

  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  video: {
    width: '100%',
    height: 200,
    backgroundColor: '#000',
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    color: '#fff',
    fontSize: 14,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  flag: {
    fontSize: 18,
    marginRight: 10,
  },
  languageName: {
    flex: 1,
    fontSize: 16,
    color: '#fff',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  box: {
    width: 100,
    height: 100,
    backgroundColor: 'salmon',
    borderRadius: 8,
    position: 'absolute',
    top: 100,
    left: 100,
    zIndex: 1000,
    elevation: 10,
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  centerContent: {
    flex: 1,
    width: '100%',
  },

  gridContainer: {
    flex: 1,
    width: '100%',
  },

  fullScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  twoParticipantsContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  halfScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  threeParticipantsContainer: {
    flex: 1,
    width: '100%',
  },
  firstRow: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  secondRow: {
    flex: 1,
    width: '100%',
  },
  halfWidthTile: {
    flex: 1,
    width: '50%',
    height: '100%',
  },
  fullWidthTile: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});

//make this component available to the app
export default LanguageModal;
