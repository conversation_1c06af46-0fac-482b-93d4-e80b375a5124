
//@ts-nocheck
// import  fs from 'fs';

// ===== UNUSED KEYS BELOW =====

const unUsedImages = {

  logoChat: require('./Icons/logoChat.png'),
  passwordInput: require('./Icons/passwordInput.png'),
  otpInput: require('./Icons/otpInput.png'),
  successInput: require('./Icons/successInput.png'),
  usernameInput: require('./Icons/usernameInput.png'),
  callingInput: require('./Icons/callingInput.png'),
  imagePlaceholder: require('./Icons/imagePlaceholder.png'),
  storiesIcon: require('./Icons/storiesIcon.png'),
  liveIcon: require('./Icons/liveIcon.png'),
  location: require('./Icons/location.png'),
  forgetPassInput: require('./Icons/forgetPassInput.png'),
  resetPassInput: require('./Icons/resetPassInput.png'),
  addStory: require('./Icons/addStory.png'),
  Videocamera: require('./Icons/Videocamera.png'),
  Phone: require('./Icons/Phone.png'),
  chatInputPin: require('./Icons/chatInputPin.png'),
  chatInputAdd: require('./Icons/chatInputAdd.png'),
  microphone: require('./Icons/microphone.png'),
  blurView: require('./Icons/blurView.png'),
  emoji: require('./Icons/emoji.png'),
  clock: require('./Icons/clock.png'),
  checkRead: require('./Icons/checkRead.png'),
  checkRead2: require('./Icons/CheckRead2.png'),
  deleteMessage: require('./Icons/deleteMessage.png'),
  pinMessage: require('./Icons/pinMessage.png'),
  addReaction: require('./Icons/addReaction.png'),
  copyMessage: require('./Icons/copyMessage.png'),
  editMessage: require('./Icons/editMessage.png'),
  replyMessage: require('./Icons/replyMessage.png'),
  silentMessage: require('./Icons/silentMessage.png'),
  sendNow: require('./Icons/sendNow.png'),
  recording: require('./Icons/recording.png'),
  retake: require('./Icons/retake.png'),
  pauseBold: require('./Icons/pauseBold.png'),
  MicrophonePin: require('./Icons/MicrophonePin.png'),
  locationPin: require("./Icons/locationPin.png"),
  GalleryPin: require("./Icons/GalleryPin.png"),
  CameraPin: require("./Icons/CameraPin.png"),
  wallpaper: require("./Icons/wallpaper.png"),
  dropper: require("./Icons/dropper.png"),
  opacity: require("./Icons/opacity.png"),
  addEmailSetting: require("./Icons/addEmailSetting.png"),
  nameColorSetting: require("./Icons/nameColorSetting.png"),
  voiceSetting: require("./Icons/voiceSetting.png"),
  switch: require("./Icons/switch.png"),
  back_Icon: require("./Icons/back_Icon.png"),
  flash_of: require("./Icons/flash_of.png"),
  flash_on: require("./Icons/flash_on.png"),
  filter_pen: require("./Icons/filter_pen.png"),
  deleteWhite: require("./Icons/deleteWhite.png"),
  more_icon: require("./Icons/more_icon.png"),
  bg_story: require("./Icons/bg_story.png"),
  two_up_arrow: require("./Icons/two_up_arrow.png"),
  heart: require("./Icons/heart.png"),
  heart_fill: require("./Icons/heart_fill.png"),
  bg_1: require("./Icons/bg_1.png"),
  bg_2: require("./Icons/bg_2.png"),
  uTurn: require("./Icons/uTurn.png"),
  file_Icon: require("./Icons/file_Icon.png"),
  archive: require("./Icons/archive.png"),
  clear_chat: require("./Icons/clear_chat.png"),
  folder: require("./Icons/folder.png"),
  unRead: require("./Icons/unRead.png"),
  drafts: require("./Icons/drafts.png"),
  unArchived: require("./Icons/unArchived.png"),
  backup: require("./Icons/backup.png"),
  clear_all_chat: require("./Icons/clear_all_chat.png"),
  app_lock: require("./Icons/app_lock.png"),
  chatLock: require("./Icons/chatLock.png"),
  location_1: require("./Icons/location_1.png"),
  location_2: require("./Icons/location_2.png"),
  slider_thumb: require("./Icons/slider_thumb.png"),
  slider_thumb_2: require("./Icons/slider_thumb_2.png"),
  setting: require("./Icons/setting.png"),
  current_location: require("./Icons/current_location.png"),
  profile_bg: require("./Icons/profile_bg.png"),
  filter_icon: require("./Icons/filter_icon.png"),
  tick_mark: require("./Icons/tick_mark.png"),
  ic_video: require("./Icons/ic_video.png"),
  ic_comment: require("./Icons/ic_comment.png"),
  tick: require("./Icons/tick.png"),
  like: require("./Icons/like.png"),
  sound: require("./Icons/sound.png"),
  line: require("./Icons/line.png"),
  musicIcon: require("./Icons/musicIcon.png"),
  currentLocation: require("./Icons/currentLocation.png"),
  layout: require("./Icons/layout.png"),
  addImageCall: require("./Icons/Add.png"),
  languageSelected: require("./Icons/languageSelected.png"),
  languageUnSelected: require("./Icons/languageUnselected.png"),
  messageWhile: require("./Icons/message-while.png"),
  soundWhite: require("./Icons/sound_white.png"),
  soundBlack: require("./Icons/sound_black.png"),
  onCall: require("./Icons/onCall.png"),
  videoCallBlack: require("./Icons/VideoCallBlack.png"),
  Options: require("./Icons/Options.png"),
  backArrow: require("./Icons/backArrow.png"),
  crownBronze: require("./Icons/crown_bronze.png"),
  Subtract: require("./Icons/Subtract.png"),
  standard: require("./Icons/Standard.png"),
  gold: require("./Icons/Gold.png"),
  silver: require("./Icons/Silver.png"),
  record: require("./Icons/record.png"),
  changeBackground: require("./Icons/changeBackground.png"),
  Recording_black: require("./Icons/Recording_black.png"),
  SpeakerOn: require("./Icons/Speaker.png"),
  nature: require("./Icons/lonely-young-woman-looking-through-concrete-window.webp"),
}

//console.log only names of the files
const unusedFilenames = Object.values(unUsedImages).map((image) => String(image));
console.log(unusedFilenames);

const IMAGES = {
  introImage: require('./Icons/introImage.png'), // IMAGES.introImage. (no need to change)
  rightArrow: require('./Icons/rightArrow.png'), // IMAGES.rightArrow -done
  userInput: require('./Icons/userInput.png'), // IMAGES.userInput -- done
  bottomArrow: require('./Icons/bottomArrow.png'), // IMAGES.bottomArrow -done
  earphone: require('./Icons/earphone.png'), // IMAGES.earphone  -- EarPhoneSVG.tsx (In CallButtons.tsx)
  wrong: require('./Icons/wrong.png'), // IMAGES.wrong.  --(xmark icon in SetUsernameScreen.tsx)
  right: require('./Icons/right.png'), // IMAGES.right  --- (check icon in SetUsernameScreen.tsx & ProfileTab.tsx)
  updateImage: require('./Icons/updateImage.png'), // IMAGES.updateImage.( EditImageSVG.tsx)
  calender: require('./Icons/calender.png'), // IMAGES.calenders (used in SetProfileScreen.tsx)
  tabBG: require('./Icons/tabBG.png'), // IMAGES.tabBG. (no need to change)
  call: require('./Icons/call.png'), // IMAGES.call (CallTabSVG.tsx) -- done
  contacts: require('./Icons/contacts.png'), // IMAGES.contacts --- done
  homeTab: require('./Icons/homeTab.png'), // IMAGES.homeTab --- done
  chatIcon: require('./Icons/chatIcon.png'), // IMAGES.chatIcon -- done (chatSVG.tsx)
  userImage: require('./Icons/userImage.png'), // IMAGES.userImage (replaced with ContactAvatarSVG)
  moreMenu: require('./Icons/moreMenu.png'), // IMAGES.moreMenu.  (ThreeDots SVG)
  searchIcon: require('./Icons/searchIcon.png'), // IMAGES.searchIcon. (SearchSVG.tsx)
  deleteIcon: require('./Icons/deleteIcon.png'), // IMAGES.deleteIcon Done (DeleteSVG.tsx)
  Qr_Code: require('./Icons/Qr_Code.png'), //  ScannerSVG.tsx ---done
  view: require('./Icons/view.png'), // IMAGES.view. ion icon (used in Input.tsx)
  hide: require('./Icons/hide.png'), // IMAGES.hide ion icon (used in Input.tsx)
  check_single_tick: require('./Icons/check_single_tick.png'), // IMAGES.check_single_tick (SingleTickSVG.tsx)---done
  forward: require('./Icons/forward.png'), // IMAGES.forward --(return-up-forward-outline) --done
  sendButton: require('./Icons/sendButton.png'), // IMAGES.sendButton (sendSVG.tsx)---done
  trash: require('./Icons/trash.png'), // IMAGES.trash  (DeleteSVG.tsx) ---- (StoryEditModal.tsx)
  editSend: require('./Icons/editSend.png'), // IMAGES.editSend ---(only in storyeditmodal.tsx & ProfileScreen.tsx)
  checkPurple: require('./Icons/checkPurple.png'), // IMAGES.checkPurple (done pending in savepostscreen.tsx)
  play: require('./Icons/play.png'), // IMAGES.play (not used)
  pause: require('./Icons/pause.png'), // IMAGES.pause  (not used)
  UserPin: require('./Icons/UserPin.png'), // IMAGES.UserPin --done
  FilePin: require('./Icons/FilePin.png'), // IMAGES.FilePin  ---- done
  cropImage: require('./Icons/cropImage.png'), // IMAGES.cropImage (used in EditorIconList.tsx) no need 
  HDImage: require('./Icons/HDImage.png'), // IMAGES.HDImage (used in EditorIconList.tsx)no need
  TImage: require('./Icons/TImage.png'), // IMAGES.TImage (used in EditorIconList.tsx) no need
  closeImage: require('./Icons/closeImage.png'), // IMAGES.closeImage --- done
  drawImage: require('./Icons/drawImage.png'), // IMAGES.drawImage (used in EditorIconList.tsx) no need
  editImage: require('./Icons/editImage.png'), // IMAGES.editImage (used in EditorIconList.tsx) no need
  accountSetting: require('./Icons/accountSetting.png'), // IMAGES.accountSetting --- done
  chatSetting: require('./Icons/chatSetting.png'), // IMAGES.chatSetting ---done
  notificationSetting: require('./Icons/notificationSetting.png'), // IMAGES.notificationSetting ---- done
  languageSetting: require('./Icons/languageSetting.png'), // IMAGES.languageSetting ---done
  privacySetting: require('./Icons/privacySetting.png'), // IMAGES.privacySetting ----done
  favouriteSetting: require('./Icons/favouriteSetting.png'), // IMAGES.favouriteSetting --done
  inArrow: require('./Icons/inArrow.png'), // IMAGES.inArrow. --done
  imageWallpaperPick: require('./Icons/imageWallpaperPick.png'), // IMAGES.imageWallpaperPick --done
  capture: require('./Icons/capture.png'), // IMAGES.capture  (used in CaptureButton.tsx --unused) --remove image
  maskImage: require('./Icons/maskImage.png'), // IMAGES.maskImage (used in FilterModal.tsx --unused) --remove image
  play_icon: require('./Icons/play_icon.png'), // IMAGES.play_icon (used in FilterModal.tsx --unused) --remove image
  pause_icon: require('./Icons/pause_icon.png'), // IMAGES.pause_icon (used in FilterModal.tsx --unused) --remove image
  time: require('./Icons/time.png'), // IMAGES.time. ---(in storypostmodal.tsx & storyeditmodal.tsx)
  lock_profile: require('./Icons/lock_profile.png'), // IMAGES.lock_profile ---(only in storypostmodal.tsx)---done (remove)
  messageTimer: require('./Icons/messageTimer.png'), // IMAGES.messageTimer ----done
  menuIcon: require('./Icons/menuIcon.png'), // IMAGES.menuIcon ----done
  f_image: require('./Icons/f_image.png'), // IMAGES.f_image. (used in scan contact as bgimage)
  post_icon: require('./Icons/post_icon.png'), // IMAGES.post_icon  ---(only in storyeditmodal.tsx)---done (remove)
  copyCode: require('./Icons/copyCode.png'), // IMAGES.copyCode ---- done
  gallery_icon: require('./Icons/gallery_icon.png'), // IMAGES.gallery_icon ----done
  gallery_gray: require('./Icons/GalleryGray.png'), // IMAGES.gallery_gray ----(setprofilescreen.tsx)
  down_arrow: require('./Icons/down_arrow.png'), // IMAGES.down_arrow ----not used
  chat_lock: require('./Icons/chat_lock.png'), // IMAGES.chat_lock.  ----done 
  translate_msg: require('./Icons/translate_msg.png'), // IMAGES.translate_msg.  ----done
  pin: require('./Icons/pin.png'), // IMAGES.pin.  ---done
  menu_icon: require('./Icons/menu_icon.png'), // IMAGES.menu_icon. ----done
  profile_image: require('./Icons/profile_image.png'), // IMAGES.profile_image ---no need to change
  block: require('./Icons/block.png'), // IMAGES.block. ---done (except optionmodal.tsx)
  user_dp: require('./Icons/user_dp.png'), //  IMAGES.user_dp --done
  plus_icon: require('./Icons/plus_icon.png'), // IMAGES.plus_icon  ---done (except optionmodal.tsx)
  channel: require('./Icons/channel.png'), // IMAGES.channel. ----no need to change
  live_stream: require('./Icons/live_stream.png'), // IMAGES.live_stream (In EaringView.tsx &liveStreamModal.tsx)
  save: require('./Icons/save.png'), // IMAGES.save. ------done
  copy_link: require('./Icons/copy_link.png'), // IMAGES.copy_link -----done
  dollar_icon: require('./Icons/dollar_icon.png'), // IMAGES.dollar_icon --(in LiveScheduleScreen.tsx)
  share: require('./Icons/share.png'), // IMAGES.share. ---done
  ic_unSave: require('./Icons/ic_unSave.png'), // IMAGES.ic_unSave. (only used in savepostscreen)
  send: require('./Icons/send.png'), // IMAGES.send ----done
  group_icon: require('./Icons/group_icon.png'), // IMAGES.group_icon --no need to change.
  report: require('./Icons/report.png'), // IMAGES.report ---(OptionModal.tsx)
  videonew: require('./Icons/videonew.png'), // IMAGES.videonew ---(ongoingcall.tsx)
  mute: require('./Icons/mute.png'), // IMAGES.mute ---(In CallMembertile.tsx)
  mute_fill: require('./Icons/mute_fill.png'), // IMAGES.mute_fill  ---( In CallButtons)
  endcall: require('./Icons/endcall.png'), // IMAGES.endcall --CallButton.tsx & CallMemberModal.tsx
  chatList: require('./Icons/chatList.png'), // IMAGES.chatList ( In CallButtons)
  changeCamera: require('./Icons/changeCamera.png'), // IMAGES.changeCamera( In CallButtons)
  audio: require('./Icons/audio.png'), // IMAGES.audio ---done( In CallButtons)
  audio_selected: require('./Icons/audio_selected.png'), // IMAGES.audio_selected( In CallButtons)
  addUser: require('./Icons/addUser.png'), // IMAGES.addUser (ongoingcall.tsx)
  addCall: require('./Icons/addCall.png'), // IMAGES.addCall ( In CallButtons)
  more: require('./Icons/more.png'), // IMAGES.more  ---done 
  addVideo: require('./Icons/addVideo.png'), // IMAGES.addVideo ( In CallButtons)
  callBg: require('./Icons/callBg.png'), // IMAGES.callBg ---done (pending in GroupCallScreen.tsx)
  videocall: require('./Icons/videocall.png'), // IMAGES.videocall --done
  redArrow: require('./Icons/redArrow.png'), // IMAGES.redArrow --done
  downarrow: require('./Icons/downarrow.png'), // IMAGES.downarrow ----done
  callIcon: require('./Icons/callIcon.png'), // IMAGES.callIcon ---not used
  addNewCall: require('./Icons/addNewCall.png'), // IMAGES.addNewCall ---done
  image1: require('./Image/image1.png'), // IMAGES.image1 --not used
  image2: require('./Image/image2.png'), // IMAGES.image2 ---not used
  rainingTeddy: require('./Image/RainingTedday.png'), // IMAGES.rainingTeddy --- used in chatscreen(no need to change)
  cloudLock: require('./Image/CloudLock.png'), // IMAGES.CloudLock. ----no need to change
  warning: require('./Icons/warning.png'), // IMAGES.warning (in SetUsernameScreen.tsx)
  callVerify: require('./Icons/call_verify.png'), // IMAGES.callVerify  ----MissCallScreen.tsx
  whatsApp: require('./Icons/whatsapp.png'), // IMAGES.whatsApp ---In misscall and scancontact.tsx(no need to change)
  message: require('./Icons/message.png'), // IMAGES.message ----MissCallScreen.tsx
  posterImg: require('./Icons/posterimg.png'), // IMAGES.posterImg. ----MissCallScreen.tsx
  blocked: require('./Icons/blocked.png'), // IMAGES.blocked ----MissCallScreen.tsx
  unSelect: require('./Icons/unselect.png'), // IMAGES.unSelect ----NewCallScreen.tsx
  selected: require('./Icons/selected.png'), // IMAGES.selected ----NewCallScreen.tsx
  videoImageWhite: require('./Icons/videoImageWhite.png'), // IMAGES.videoImageWhite.  ----NewCallScreen.tsx
  callImageWhite: require('./Icons/callImageWhite.png'), // IMAGES.callImageWhite  ----NewCallScreen.tsx
  upload: require('./Icons/upload.png'), // IMAGES.upload ---ScheduleCallScreen.tsx
  translation: require('./Icons/translation.png'), // IMAGES.translation. ----OptionsModal.tsx
  shareScreen: require('./Icons/shareScreen.png'), // IMAGES.shareScreen ----OptionsModal.tsx
  queenSymbol: require('./Icons/queensymbol.png'), // IMAGES.queenSymbol ---no need to change
  addPeople: require('./Icons/AddPeople.png'), // IMAGES.addPeople  ----done
  unselected_white: require('./Icons/unselected_white.png'), // IMAGES.unselected_white(AddPeopleModal.tsx,LanguageModal.tsx)
  selected_white: require('./Icons/selected_white.png'), // IMAGES.selected_white (AddPeopleModal.tsx,LanguageModal.tsx)
  search_white: require('./Icons/search_white.png'), // IMAGES.search_white  ----done
  mute_white: require('./Icons/mute_white.png'), // IMAGES.mute_white (unmute)(CallButton.tsx,CallMembersModal.tsx,CallMembertile.tsx)

};