import React, { useEffect, useRef, useState, useCallback, memo } from 'react';
import { View, TouchableOpacity, Image, Animated, StyleSheet, ScrollView, Platform } from 'react-native';
import VidTrimmer from '../VTrim';
import RNFS from "react-native-fs";
import TrackPlayer, { Event, useProgress, RepeatMode } from 'react-native-track-player';
import { wp } from '../../utils/Constants/dimensionUtils';

// Memoized PlayerButton to prevent re-renders unless props change
const PlayerButton = memo(({ icon, disabled = false, onPress, style }: { icon: any, disabled: boolean, onPress: () => void, style: any }) => (
    <TouchableOpacity disabled={disabled} style={style} onPress={onPress}>
        <Image source={icon} style={styles.musicLogo} />
    </TouchableOpacity>
), (prevProps, nextProps) => {
    return prevProps.disabled === nextProps.disabled &&
        prevProps.icon === nextProps.icon &&
        prevProps.style === nextProps.style &&
        prevProps.onPress === nextProps.onPress;
});

const MusicPlayer = ({ audio, onClose, onDone, onStartPlay, videoDuration = 15, resetMusicPlayer, isImage }) => {
    const [localPath, setLocalPath] = useState(null);
    const [totalDuration, setTotalDuration] = useState(0);
    const [loading, setLoading] = useState(true);
    const [startTime, setStartTime] = useState(0);
    const [isTrimmed, setIsTrimmed] = useState(false);
    const [totalContentwidth, setTotalContentwidth] = useState(0);
    const { duration, position, buffered } = useProgress(200);
    const progressWidth = useRef(new Animated.Value(0)).current;
    const progressAnim = useRef(new Animated.Value(0)).current;
    const prevResetMusicPlayer = useRef(resetMusicPlayer);
    const scrollViewWidth = wp(47);

    const resetProgress = useCallback(() => progressWidth.setValue(0), []);
    const onPressedClose = useCallback(() => {
        setLocalPath(null);
        resetProgress();
        onClose();
    }, [onClose, resetProgress]);

    const onPressedDone = useCallback(() => {
        resetProgress();
        onDone(localPath, duration);
        stopMusic();
    }, [onDone, localPath, duration, resetProgress]);

    useEffect(() => {
        let animation: any;
        let interval;
        const width = wp(44.5);

        // Reset progress bar
        progressWidth.setValue(0);

        const startAnimation = () => {
            if (isImage) {
                playTrack(); // Replay audio only for images
            }
            progressWidth.setValue(0);
            animation = Animated.timing(progressWidth, {
                toValue: width,
                duration: videoDuration * 1040,
                useNativeDriver: false,
            });
            animation.start();
        };

        startAnimation();

        interval = setInterval(() => {
            startAnimation();
            if (isImage && localPath && !resetMusicPlayer) {
                playTrack();
            }
        }, videoDuration * 1040);

        return () => {
            animation?.stop();
            clearInterval(interval);
        };
    }, [videoDuration, isImage, localPath, resetMusicPlayer]);

    const playTrack = async () => {
        try {
            if (!localPath) {
                console.warn('No local path provided for audio playback');
                return;
            }

            const exists = await RNFS.exists(localPath);
            if (!exists) {
                console.error('File does not exist:', localPath);
                return;
            }

            await TrackPlayer.reset();
            const track = {
                url: Platform.OS === 'ios' ? localPath : `file://${localPath}`,
            };

            await TrackPlayer.add(track);

            if (totalDuration < videoDuration || isImage) {
                await TrackPlayer.setRepeatMode(RepeatMode.Track);
            } else {
                await TrackPlayer.setRepeatMode(RepeatMode.Off);
            }

            await TrackPlayer.play();
        } catch (error) {
            console.error('Error setting up audio playback:', error);
        }
    };

    const stopTrack = async () => {
        await TrackPlayer.reset();
        await TrackPlayer.stop();
    };

    useEffect(() => {
        let trackEndedListener = null;

        if (isImage) {
            if (resetMusicPlayer && prevResetMusicPlayer.current !== resetMusicPlayer) {
                stopTrack();
                setLocalPath(null);
            } else if (localPath && !resetMusicPlayer && prevResetMusicPlayer.current !== resetMusicPlayer) {
                playTrack();
            }
        } else {
            if (!resetMusicPlayer) {
                playTrack();
            } else {
                stopTrack();
            }
        }

        setLoading(false);

        trackEndedListener = TrackPlayer.addEventListener(Event.PlaybackQueueEnded, async () => {
            await TrackPlayer.setRepeatMode(RepeatMode.Track);
            if (!resetMusicPlayer) {
                await TrackPlayer.play();
            }
        });

        prevResetMusicPlayer.current = resetMusicPlayer;

        return () => {
            if (trackEndedListener) {
                trackEndedListener.remove();
            }
        };
    }, [localPath, resetMusicPlayer, onStartPlay, progressWidth, videoDuration]);

    const stopMusic = useCallback(async () => {
        await TrackPlayer?.reset();
        await TrackPlayer?.stop();
    }, []);

    const Wave1 = ({ color = 'gray' }) => (
        <View style={{ width: 2, height: 10, backgroundColor: color, marginRight: 4 }} />
    );
    const Wave2 = ({ noMargin = false }) => (
        <View style={{ width: 2, height: 20, backgroundColor: 'gray', marginRight: noMargin ? 0 : 4 }} />
    );

    const handleScrollEnd = useCallback(
        (event: any) => {
            if (isImage && totalDuration > videoDuration) {
                const offsetX = event.nativeEvent.contentOffset.x;
                const maxOffsetX = totalContentwidth - scrollViewWidth;
                const percentage = offsetX / maxOffsetX;
                let newStartTime = percentage * (totalDuration - videoDuration);

                newStartTime = Math.max(0, Math.min(newStartTime, totalDuration - videoDuration));
                setStartTime(newStartTime);
                setIsTrimmed(true);
            } else {
                setStartTime(0);
                setIsTrimmed(false);
            }
        },
        [totalContentwidth, totalDuration, videoDuration, isImage]
    );

    const handleContentSizeChange = useCallback((width) => setTotalContentwidth(width), []);

    const ConstView = () => (
        <View style={styles.constView}>
            <Wave1 />
            <Wave2 />
            <Wave1 />
            <Wave2 />
            <Wave1 />
            <Wave2 noMargin />
        </View>
    );

    let numWaves;
    const waveWidth = 2 + 4;
    if (isImage && totalDuration <= videoDuration) {
        const minWaves = 10;
        numWaves = Math.max(Math.ceil(scrollViewWidth / waveWidth), minWaves);
    } else {
        const pixelsPerSecond = 10;
        numWaves = Math.ceil(totalDuration * pixelsPerSecond / waveWidth);
    }

    return (
        <View style={styles.container}>
            <PlayerButton
                style={{ zIndex: 1 }}
                icon={require('../../Assets/Images/cross.png')}
                onPress={onPressedClose}
            />
            {!loading ? (
                <View style={[styles.trimParent, { flexDirection: 'row' }]}>
                    <ConstView />
                    <View style={styles.Player}>
                        <ScrollView
                            horizontal
                            bounces={false}
                            scrollEnabled={isImage ? totalDuration > videoDuration : totalDuration >= videoDuration}
                            scrollEventThrottle={16}
                            onMomentumScrollEnd={handleScrollEnd}
                            onContentSizeChange={handleContentSizeChange}
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={styles.scrollContainer}
                        >
                            {totalDuration > 0 &&
                                [...Array(numWaves)].map((_, index) =>
                                    index % 2 === 0 ? (
                                        <Wave1 key={index} color={index % 50 === 0 ? 'red' : 'gray'} />
                                    ) : (
                                        <Wave2 key={index} />
                                    )
                                )}
                        </ScrollView>
                        <Animated.View style={[styles.progressBar, { width: progressWidth }]} />
                    </View>
                    <ConstView />
                </View>
            ) : (
                <View style={styles.horizontalLoaderContainer}>
                    <Animated.View
                        style={[
                            styles.horizontalLoader,
                            {
                                transform: [
                                    {
                                        translateX: progressAnim.interpolate({
                                            inputRange: [0, 1],
                                            outputRange: [-wp(30), wp(30)],
                                        }),
                                    },
                                ],
                            },
                        ]}
                    />
                </View>
            )}
            <PlayerButton
                style={{ zIndex: 1 }}
                icon={require('../../Assets/Images/Tickicon.png')}
                onPress={onPressedDone}
            />
            <VidTrimmer
                forAudio={true}
                finalDuration={videoDuration}
                vaDuration={duration}
                audioUri={audio?.url}
                setAudioUri={setLocalPath}
                setADuration={setTotalDuration}
                audioStartTime={startTime}
            />
        </View>
    );
};

export default memo(MusicPlayer);

// Styles remain unchanged
const styles = StyleSheet.create({
    container: {
        width: '100%',
        backgroundColor: 'white',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderTopEndRadius: 20,
        borderTopStartRadius: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'absolute',
        bottom: 0,
        zIndex: 110000,
    },
    loaderContainer: {
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loader: {
        width: 30,
        height: 30,
    },
    musicLogo: {
        height: 30, width: 30
    },
    trimParent: {
        flex: 1,
        width: '80%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    scrollContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    trimView: {
        backgroundColor: '#F1F1F1',
        width: '70%',
        height: 50,
        borderRadius: 50,
        position: 'absolute',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: -10
    },
    constView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    loaderText: {
        height: 35,
        textAlign: 'center',
        textAlignVertical: 'center',
        color: '#000'
    },
    Player: {
        width: wp(47),
        height: 40,
        backgroundColor: '#F1F1F1',
        marginHorizontal: 4,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        borderWidth: 4,
        borderColor: '#FFF'
    },
    progressBar: {
        height: '100%',
        backgroundColor: "#6A4DBB",
        position: 'absolute',
        left: 0,
        zIndex: -1
    },
    horizontalLoaderContainer: {
        width: wp(32),
        height: 4,
        backgroundColor: '#E0E0E0',
        overflow: 'hidden',
        borderRadius: 2,
        marginVertical: 20,
        alignSelf: 'center',
    },
    horizontalLoader: {
        width: wp(22),
        height: '100%',
        backgroundColor: '#6A4DBB',
        borderRadius: 2,
    },
});