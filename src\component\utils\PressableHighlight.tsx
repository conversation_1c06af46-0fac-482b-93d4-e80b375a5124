import React from 'react';
import { View, ViewStyle, LayoutChangeEvent } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withDelay,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { colors } from '../../theme/colors';

interface EnhancedRippleProps {
  onPress?: () => void;
  children: React.ReactNode;
  containerStyle?: ViewStyle;
  rippleColor?: string;
  disabled?: boolean;
}

const PressableHighlight = (props: EnhancedRippleProps) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const centerX = useSharedValue(0);
  const centerY = useSharedValue(0);
  const containerWidth = useSharedValue(0);
  const containerHeight = useSharedValue(0);

  const onLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    containerWidth.value = width;
    containerHeight.value = height;
  };

  const animatedRippleStyle = useAnimatedStyle(() => {
    const maxRadius = Math.sqrt(
      Math.pow(containerWidth.value, 2) + Math.pow(containerHeight.value, 2),
    );

    return {
      position: 'absolute',
      top: centerY.value - maxRadius / 2,
      left: centerX.value - maxRadius / 2,
      width: maxRadius,
      height: maxRadius,
      borderRadius: maxRadius / 2,
      backgroundColor: props.rippleColor || colors.purple_1,
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  const tapGesture = Gesture.Tap()
    .enabled(!props.disabled)
    .onBegin((event) => {
      centerX.value = event.x;
      centerY.value = event.y;

      // Reset values
      scale.value = 0;
      opacity.value = 0;

      // Animate ripple expansion
      scale.value = withTiming(1, {
        duration: 300,
        easing: Easing.out(Easing.cubic),
      });

      // Fade in quickly, then fade out with delay
      opacity.value = withSequence(
        withTiming(1, { duration: 150 }), // stronger fade in
        withDelay(
          150,
          withTiming(0, {
            duration: 400,
            easing: Easing.out(Easing.cubic),
          }),
        ),
      );
    })
    .onEnd(() => {
      if (props.onPress) {
        runOnJS(props.onPress)();
      }
    });

  return (
    <GestureDetector gesture={tapGesture}>
      <View style={[{ overflow: 'hidden' }, props.containerStyle]} onLayout={onLayout}>
        {props.children}
        <Animated.View style={animatedRippleStyle} pointerEvents="none" />
      </View>
    </GestureDetector>
  );
};

export default PressableHighlight;
