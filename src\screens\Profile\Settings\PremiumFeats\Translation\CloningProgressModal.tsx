import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
// import Icon from 'react-native-vector-icons/MaterialIcons';
import { MaterialCommunityIcons } from '../../../../../utils/vectorIcons';
import ModalWrapper from '../../../../../component/ModalWrapper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { hp } from '../../../../../theme/fonts';
import { colors } from '../../../../../theme/colors';
import {
  EmbeddingDoc,
  EmbeddingStatus,
  getEmbeddingDocumentApi,
} from '../../../../../api/calls/calls.api';

const { width } = Dimensions.get('window');

interface Step {
  id: string;
  label: string;
  completed: boolean;
  current: boolean;
}

interface ProgressBarProps {
  progress: number;
}

interface StepItemProps {
  step: Step;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress }) => {
  const getWidth = () => {
    switch (progress) {
      case 0:
        return '0%';
      case 45:
        return '45%';
      case 80:
        return '80%';
      case 100:
        return '100%';
      default:
        return '0%'; // fallback
    }
  };

  return (
    <View style={styles.progressContainer}>
      <View style={styles.progressBackground}>
        <View style={[styles.progressFill, { width: getWidth() }]} />
      </View>
    </View>
  );
};

const StepItem: React.FC<StepItemProps> = ({ step }) => {
  return (
    <View
      style={[
        styles.stepContainer,
        { backgroundColor: step.completed ? colors.lightGreen : colors.gray_f3 },
      ]}
    >
      <View style={styles.stepIconContainer}>
        {step.completed ? (
          <MaterialCommunityIcons name="check" size={16} color="#4CAF50" style={styles.stepIcon} />
        ) : step.current ? (
          <View style={styles.currentStepIndicator} />
        ) : (
          <View style={styles.pendingStepIndicator} />
        )}
      </View>
      <Text
        style={[
          styles.stepText,
          step.completed && styles.completedStepText,
          step.current && styles.currentStepText,
          {
            color: step.completed ? 'green' : 'black',
          },
        ]}
      >
        {step.label}
      </Text>
    </View>
  );
};

type CloningProgressModalProps = {
  showModal: boolean;
  onCloseModal: () => void;
};

const initialSteps: Step[] = [
  {
    id: '1',
    label: 'Analyzing audio quality',
    completed: true,
    current: false,
  },
  {
    id: '2',
    label: 'Training AI model',
    completed: false,
    current: false,
  },
  {
    id: '3',
    label: 'Generating voice clone',
    completed: false,
    current: false,
  },
];

const CloningProgressModal: React.FC<CloningProgressModalProps> = ({ showModal, onCloseModal }) => {
  const [progress, setProgress] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [steps, setSteps] = useState(initialSteps);

  const [embeddingDocument, setEmbeddingDocument] = useState<EmbeddingDoc | null>(null);
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // write polling logic here for getting embedding document from backend
  const getEmbeddingDocument = async () => {
    try {
      const response = await getEmbeddingDocumentApi();
      if (!response.data) return;
      const embedDoc = response.data;
      if (embedDoc.status === EmbeddingStatus.COMPLETED) {
        setEmbeddingDocument(embedDoc);
        pollingTimerRef.current && clearInterval(pollingTimerRef.current);
        setSteps((pre) => pre.map((step) => ({ ...step, completed: true, current: false })));
      }
      if (embedDoc.status === EmbeddingStatus.IN_PROGRESS) {
        setProgress(80);
      }
      console.log(response);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    pollingTimerRef.current = setInterval(() => {
      getEmbeddingDocument();
    }, 5000);
    return () => {
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setProgress(45);
      setCurrentStepIndex(1);
      setSteps((prev) => {
        const newSteps = [...prev];
        newSteps[1].completed = true;
        newSteps[1].current = false;
        newSteps[2].current = true;
        return newSteps;
      });
    }, 2000);

    return () => clearTimeout(timeout);
  }, []);

  const handleOkayPress = () => {
    console.log('Okay button pressed');
    // Handle navigation or completion logic here
    onCloseModal();
  };

  return (
    <ModalWrapper
      isVisible={showModal}
      onCloseModal={() => {
        onCloseModal();
      }}
    >
      <View style={{ width: '100%' }}>
        <SafeAreaView style={Consnet_styles.container}>
          <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

          <View style={styles.content}>
            <Text style={styles.title}>Creating your voice clone</Text>
            <Text style={styles.subtitle}>Our AI is learning unique voice patterns...</Text>

            <View style={styles.progressSection}>
              <ProgressBar progress={progress} />
              <Text style={styles.progressText}>{progress}% Completed</Text>
            </View>

            <View style={styles.stepsContainer}>
              {steps.map((step, index) => (
                <StepItem
                  key={step.id}
                  step={{
                    ...step,
                    completed: index < currentStepIndex || index === 0,
                    current: index === currentStepIndex || (index === 1 && progress < 100),
                  }}
                />
              ))}
            </View>
          </View>
          <TouchableOpacity style={styles.okayButton} onPress={handleOkayPress} activeOpacity={0.8}>
            <Text style={styles.okayButtonText}>Okay</Text>
          </TouchableOpacity>
        </SafeAreaView>
      </View>
    </ModalWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 24,
    paddingVertical: 40,
    height: 500,
  },
  content: {},
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A1A1A',

    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'black',

    marginBottom: 48,
  },
  progressSection: {
    marginBottom: 48,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBackground: {
    height: 8,
    backgroundColor: '#E9ECEF',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#7C3AED',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    fontWeight: '500',
  },
  stepsContainer: {
    gap: 16,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',

    padding: 12,
    borderRadius: 10,
  },
  stepIconContainer: {
    width: 24,
    height: 24,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepIcon: {
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    width: 24,
    height: 24,
    textAlign: 'center',
    textAlignVertical: 'center',
    lineHeight: 24,
  },
  currentStepIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#7C3AED',
  },
  pendingStepIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#D1D5DB',
  },
  stepText: {
    fontSize: 16,

    flex: 1,
  },
  completedStepText: {
    fontWeight: '500',
  },
  currentStepText: {
    fontWeight: '500',
  },
  okayButton: {
    backgroundColor: '#7C3AED',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 24,
    shadowColor: '#7C3AED',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  okayButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CloningProgressModal;

const Consnet_styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 10,
  },
  header: {
    marginBottom: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 6,
    textAlign: 'left',
  },
  description: {
    fontSize: 15,
    color: '#6b7280',
    lineHeight: 24,
    textAlign: 'left',
  },

  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 5,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#1f2937',
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#1f2937',
    flex: 1,
  },
  infoBadge: {
    flexDirection: 'row',
    backgroundColor: colors.warmCream,
    borderRadius: 12,
    padding: 16,
    marginBottom: 40,
    alignItems: 'flex-start',
  },
  infoIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  infoIconText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6b7280',
  },
  infoBadgeText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    flex: 1,
  },
  actionButtons: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: 40,
    gap: 12,
  },
  skipButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: colors.gray_f3,
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6b7280',
  },
  proceedButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#8b5cf6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#8b5cf6',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    width: '48%',
  },
  proceedButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
