#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=14500, tid=10704
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\b4c54811f9342b977e0f40be9ce35415\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\b4c54811f9342b977e0f40be9ce35415\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-abc86f3f1613dd49146a117b4e74270a-sock

Host: Intel(R) Core(TM) i7-10610U CPU @ 1.80GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.5074)
Time: Tue Sep 16 21:16:57 2025 India Standard Time elapsed time: 295073.381916 seconds (3d 9h 57m 53s)

---------------  T H R E A D  ---------------

Current thread (0x000001a670866110):  JavaThread "Attach Listener" daemon [_thread_in_vm, id=10704, stack(0x0000004c38e00000,0x0000004c38f00000) (1024K)]

Stack: [0x0000004c38e00000,0x0000004c38f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc0347]
V  [jvm.dll+0x6d2cf9]
V  [jvm.dll+0x131116]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x8d9c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001a654d7e0d0, length=42, elements={
0x000001a6708ec320, 0x000001a67085f1f0, 0x000001a670862970, 0x000001a6708654c0,
0x000001a670866110, 0x000001a670867b20, 0x000001a67086cea0, 0x000001a6708742e0,
0x000001a679b70710, 0x000001a6709583a0, 0x000001a679e685e0, 0x000001a67e68f540,
0x000001a67e0308c0, 0x000001a67e6d45f0, 0x000001a67e6d5020, 0x000001a67e5776f0,
0x000001a67edaaaf0, 0x000001a67ed0c140, 0x000001a67ed0e8a0, 0x000001a67ed0d4f0,
0x000001a6527aebc0, 0x000001a6527acaf0, 0x000001a6527abdd0, 0x000001a6527af250,
0x000001a6527af8e0, 0x000001a6527ae530, 0x000001a6527ad180, 0x000001a6527ac460,
0x000001a6527aff70, 0x000001a6527adea0, 0x000001a6527b0600, 0x000001a6527ad810,
0x000001a6527b2040, 0x000001a6527b1320, 0x000001a6527b26d0, 0x000001a67f1dffe0,
0x000001a67f1e0670, 0x000001a67f1e1390, 0x000001a67f1df2c0, 0x000001a67ed0f5c0,
0x000001a653ff6000, 0x000001a653ff45c0
}

Java Threads: ( => current thread )
  0x000001a6708ec320 JavaThread "main"                              [_thread_blocked, id=11492, stack(0x0000004c38700000,0x0000004c38800000) (1024K)]
  0x000001a67085f1f0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=17864, stack(0x0000004c38b00000,0x0000004c38c00000) (1024K)]
  0x000001a670862970 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10844, stack(0x0000004c38c00000,0x0000004c38d00000) (1024K)]
  0x000001a6708654c0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=17144, stack(0x0000004c38d00000,0x0000004c38e00000) (1024K)]
=>0x000001a670866110 JavaThread "Attach Listener"            daemon [_thread_in_vm, id=10704, stack(0x0000004c38e00000,0x0000004c38f00000) (1024K)]
  0x000001a670867b20 JavaThread "Service Thread"             daemon [_thread_blocked, id=10636, stack(0x0000004c38f00000,0x0000004c39000000) (1024K)]
  0x000001a67086cea0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=8268, stack(0x0000004c39000000,0x0000004c39100000) (1024K)]
  0x000001a6708742e0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=14660, stack(0x0000004c39100000,0x0000004c39200000) (1024K)]
  0x000001a679b70710 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12856, stack(0x0000004c39200000,0x0000004c39300000) (1024K)]
  0x000001a6709583a0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=19992, stack(0x0000004c39300000,0x0000004c39400000) (1024K)]
  0x000001a679e685e0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=6592, stack(0x0000004c39400000,0x0000004c39500000) (1024K)]
  0x000001a67e68f540 JavaThread "Active Thread: Equinox Container: 0168ce9e-90c3-456e-b30b-a386cca779fc"        [_thread_blocked, id=8104, stack(0x0000004c39d00000,0x0000004c39e00000) (1024K)]
  0x000001a67e0308c0 JavaThread "Refresh Thread: Equinox Container: 0168ce9e-90c3-456e-b30b-a386cca779fc" daemon [_thread_blocked, id=11536, stack(0x0000004c39500000,0x0000004c39600000) (1024K)]
  0x000001a67e6d45f0 JavaThread "Framework Event Dispatcher: Equinox Container: 0168ce9e-90c3-456e-b30b-a386cca779fc" daemon [_thread_blocked, id=22196, stack(0x0000004c39e00000,0x0000004c39f00000) (1024K)]
  0x000001a67e6d5020 JavaThread "Start Level: Equinox Container: 0168ce9e-90c3-456e-b30b-a386cca779fc" daemon [_thread_blocked, id=16980, stack(0x0000004c39f00000,0x0000004c3a000000) (1024K)]
  0x000001a67e5776f0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=18684, stack(0x0000004c3a100000,0x0000004c3a200000) (1024K)]
  0x000001a67edaaaf0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=6412, stack(0x0000004c3a000000,0x0000004c3a100000) (1024K)]
  0x000001a67ed0c140 JavaThread "Worker-JM"                         [_thread_blocked, id=15612, stack(0x0000004c3a300000,0x0000004c3a400000) (1024K)]
  0x000001a67ed0e8a0 JavaThread "Java indexing"              daemon [_thread_blocked, id=5572, stack(0x0000004c3aa00000,0x0000004c3ab00000) (1024K)]
  0x000001a67ed0d4f0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=10880, stack(0x0000004c3ab00000,0x0000004c3ac00000) (1024K)]
  0x000001a6527aebc0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=2836, stack(0x0000004c3b000000,0x0000004c3b100000) (1024K)]
  0x000001a6527acaf0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=1436, stack(0x0000004c3b100000,0x0000004c3b200000) (1024K)]
  0x000001a6527abdd0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=19352, stack(0x0000004c3b200000,0x0000004c3b300000) (1024K)]
  0x000001a6527af250 JavaThread "Thread-5"                   daemon [_thread_in_native, id=8672, stack(0x0000004c3b300000,0x0000004c3b400000) (1024K)]
  0x000001a6527af8e0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=20760, stack(0x0000004c3b400000,0x0000004c3b500000) (1024K)]
  0x000001a6527ae530 JavaThread "Thread-7"                   daemon [_thread_in_native, id=14588, stack(0x0000004c3b500000,0x0000004c3b600000) (1024K)]
  0x000001a6527ad180 JavaThread "Thread-8"                   daemon [_thread_in_native, id=8492, stack(0x0000004c3b600000,0x0000004c3b700000) (1024K)]
  0x000001a6527ac460 JavaThread "Thread-9"                   daemon [_thread_in_native, id=18796, stack(0x0000004c3b700000,0x0000004c3b800000) (1024K)]
  0x000001a6527aff70 JavaThread "Thread-10"                  daemon [_thread_in_native, id=11452, stack(0x0000004c3b800000,0x0000004c3b900000) (1024K)]
  0x000001a6527adea0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=11120, stack(0x0000004c3b900000,0x0000004c3ba00000) (1024K)]
  0x000001a6527b0600 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=12480, stack(0x0000004c3a800000,0x0000004c3a900000) (1024K)]
  0x000001a6527ad810 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=13496, stack(0x0000004c3ba00000,0x0000004c3bb00000) (1024K)]
  0x000001a6527b2040 JavaThread "Timer-0"                           [_thread_blocked, id=20632, stack(0x0000004c3a600000,0x0000004c3a700000) (1024K)]
  0x000001a6527b1320 JavaThread "Timer-1"                           [_thread_blocked, id=19884, stack(0x0000004c3bc00000,0x0000004c3bd00000) (1024K)]
  0x000001a6527b26d0 JavaThread "Timer-2"                           [_thread_blocked, id=18452, stack(0x0000004c3bd00000,0x0000004c3be00000) (1024K)]
  0x000001a67f1dffe0 JavaThread "Timer-3"                           [_thread_blocked, id=8400, stack(0x0000004c3bb00000,0x0000004c3bc00000) (1024K)]
  0x000001a67f1e0670 JavaThread "Timer-4"                           [_thread_blocked, id=15680, stack(0x0000004c3be00000,0x0000004c3bf00000) (1024K)]
  0x000001a67f1e1390 JavaThread "Timer-5"                           [_thread_blocked, id=17624, stack(0x0000004c38600000,0x0000004c38700000) (1024K)]
  0x000001a67f1df2c0 JavaThread "Timer-6"                           [_thread_blocked, id=13104, stack(0x0000004c3a400000,0x0000004c3a500000) (1024K)]
  0x000001a67ed0f5c0 JavaThread "Timer-7"                           [_thread_blocked, id=9224, stack(0x0000004c3c400000,0x0000004c3c500000) (1024K)]
  0x000001a653ff6000 JavaThread "Worker-52"                         [_thread_blocked, id=10264, stack(0x0000004c38500000,0x0000004c38600000) (1024K)]
  0x000001a653ff45c0 JavaThread "Worker-53"                         [_thread_blocked, id=24332, stack(0x0000004c3a500000,0x0000004c3a600000) (1024K)]
Total: 42

Other Threads:
  0x000001a6709aea80 VMThread "VM Thread"                           [id=15920, stack(0x0000004c38a00000,0x0000004c38b00000) (1024K)]
  0x000001a67077ba00 WatcherThread "VM Periodic Task Thread"        [id=9136, stack(0x0000004c38900000,0x0000004c38a00000) (1024K)]
  0x000001a67090b790 WorkerThread "GC Thread#0"                     [id=19752, stack(0x0000004c38800000,0x0000004c38900000) (1024K)]
  0x000001a67e043ff0 WorkerThread "GC Thread#1"                     [id=13304, stack(0x0000004c39600000,0x0000004c39700000) (1024K)]
  0x000001a67e748420 WorkerThread "GC Thread#2"                     [id=18168, stack(0x0000004c39700000,0x0000004c39800000) (1024K)]
  0x000001a67e7487c0 WorkerThread "GC Thread#3"                     [id=12380, stack(0x0000004c39800000,0x0000004c39900000) (1024K)]
  0x000001a67e748b60 WorkerThread "GC Thread#4"                     [id=20548, stack(0x0000004c39900000,0x0000004c39a00000) (1024K)]
  0x000001a67e15c090 WorkerThread "GC Thread#5"                     [id=5960, stack(0x0000004c39a00000,0x0000004c39b00000) (1024K)]
  0x000001a67e15c430 WorkerThread "GC Thread#6"                     [id=18900, stack(0x0000004c39b00000,0x0000004c39c00000) (1024K)]
  0x000001a67e88c820 WorkerThread "GC Thread#7"                     [id=18536, stack(0x0000004c39c00000,0x0000004c39d00000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001a60f000000-0x000001a60fba0000-0x000001a60fba0000), size 12189696, SharedBaseAddress: 0x000001a60f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001a610000000-0x000001a650000000, reserved size: 1073741824
Narrow klass base: 0x000001a60f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 16153M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 9216K, used 7715K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 84% used [0x00000000d5580000,0x00000000d5ca8d98,0x00000000d5e00000)
  from space 512K, 75% used [0x00000000d6480000,0x00000000d64e0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843952K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382c288,0x00000000b3880000)
 Metaspace       used 80121K, committed 81792K, reserved 1179648K
  class space    used 9566K, committed 10368K, reserved 1048576K

Card table byte_map: [0x000001a670290000,0x000001a6706a0000] _byte_map_base: 0x000001a66fe90000

Marking Bits: (ParMarkBitMap*) 0x00007ffd5eefa340
 Begin Bits: [0x000001a673d90000, 0x000001a675d90000)
 End Bits:   [0x000001a675d90000, 0x000001a677d90000)

Polling page: 0x000001a66e6e0000

Metaspace:

Usage:
  Non-class:     68.90 MB used.
      Class:      9.34 MB used.
       Both:     78.24 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      69.75 MB ( 54%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      10.12 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      79.88 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  9.69 MB
       Class:  5.86 MB
        Both:  15.54 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 131.06 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 13.
num_arena_births: 1504.
num_arena_deaths: 24.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1278.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 38.
num_chunks_taken_from_freelist: 5005.
num_chunk_merges: 17.
num_chunk_splits: 3131.
num_chunks_enlarged: 1886.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=16571Kb max_used=16571Kb free=103428Kb
 bounds [0x000001a607ad0000, 0x000001a608b00000, 0x000001a60f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=32107Kb max_used=32107Kb free=87892Kb
 bounds [0x000001a600000000, 0x000001a601f60000, 0x000001a607530000]
CodeHeap 'non-nmethods': size=5760Kb used=1462Kb max_used=1557Kb free=4297Kb
 bounds [0x000001a607530000, 0x000001a6077a0000, 0x000001a607ad0000]
CodeCache: size=245760Kb, used=50140Kb, max_used=50235Kb, free=195617Kb
 total_blobs=14880, nmethods=14102, adapters=683, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 291315.631 Thread 0x000001a6708742e0 15746       4       org.eclipse.core.internal.resources.Workspace::beginOperation (80 bytes)
Event: 291315.776 Thread 0x000001a6708742e0 nmethod 15746 0x000001a608af4d90 code [0x000001a608af5100, 0x000001a608af7400]
Event: 294362.921 Thread 0x000001a679b70710 15747       3       org.eclipse.core.internal.events.NotificationManager::addListener (20 bytes)
Event: 294362.936 Thread 0x000001a679b70710 nmethod 15747 0x000001a601f59890 code [0x000001a601f59a80, 0x000001a601f59e00]
Event: 294588.288 Thread 0x000001a679b70710 15748       3       org.eclipse.core.internal.resources.Workspace::addResourceChangeListener (11 bytes)
Event: 294588.291 Thread 0x000001a679b70710 nmethod 15748 0x000001a601f5a010 code [0x000001a601f5a200, 0x000001a601f5a610]
Event: 294588.291 Thread 0x000001a679b70710 15749  s    3       org.eclipse.core.runtime.ListenerList::remove (125 bytes)
Event: 294588.296 Thread 0x000001a679b70710 nmethod 15749 0x000001a601f5a810 code [0x000001a601f5aa40, 0x000001a601f5b2c8]
Event: 294588.447 Thread 0x000001a679b70710 15750   !   3       com.google.gson.internal.Streams::parse (68 bytes)
Event: 294588.455 Thread 0x000001a679b70710 nmethod 15750 0x000001a601f5b510 code [0x000001a601f5b760, 0x000001a601f5be60]
Event: 294663.089 Thread 0x000001a679b70710 15751       3       org.eclipse.jdt.internal.compiler.util.HashtableOfObjectToInt::rehash (78 bytes)
Event: 294663.092 Thread 0x000001a679b70710 nmethod 15751 0x000001a601f5c210 code [0x000001a601f5c3e0, 0x000001a601f5c778]
Event: 294663.112 Thread 0x000001a679b70710 15752       1       java.lang.Thread::setThreadContainer (6 bytes)
Event: 294663.114 Thread 0x000001a679b70710 nmethod 15752 0x000001a608af8a10 code [0x000001a608af8ba0, 0x000001a608af8c80]
Event: 294663.114 Thread 0x000001a679b70710 15753       1       jdk.internal.vm.SharedThreadContainer::owner (2 bytes)
Event: 294663.114 Thread 0x000001a679b70710 nmethod 15753 0x000001a608af8d10 code [0x000001a608af8ea0, 0x000001a608af8f70]
Event: 294663.152 Thread 0x000001a6708742e0 15754       4       java.lang.invoke.MethodType::insertParameterTypes (121 bytes)
Event: 294663.305 Thread 0x000001a679b70710 15756       3       java.util.HashMap::internalWriteEntries (78 bytes)
Event: 294663.308 Thread 0x000001a679b70710 nmethod 15756 0x000001a601f5c990 code [0x000001a601f5cb60, 0x000001a601f5ced0]
Event: 294663.401 Thread 0x000001a6708742e0 nmethod 15754 0x000001a608af9410 code [0x000001a608af9960, 0x000001a608afce78]

GC Heap History (20 events):
Event: 290859.392 GC heap before
{Heap before GC invocations=3587 (full 4):
 PSYoungGen      total 9216K, used 8928K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 43% used [0x00000000d6480000,0x00000000d64b8000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843936K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b3828288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290859.394 GC heap after
{Heap after GC invocations=3587 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290864.384 GC heap before
{Heap before GC invocations=3588 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290864.386 GC heap after
{Heap after GC invocations=3588 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290870.046 GC heap before
{Heap before GC invocations=3589 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290870.049 GC heap after
{Heap after GC invocations=3589 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80093K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290880.324 GC heap before
{Heap before GC invocations=3590 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80100K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290880.326 GC heap after
{Heap after GC invocations=3590 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80100K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290914.054 GC heap before
{Heap before GC invocations=3591 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80101K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290914.058 GC heap after
{Heap after GC invocations=3591 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80101K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290944.856 GC heap before
{Heap before GC invocations=3592 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6400000,0x00000000d6440000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80102K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290944.860 GC heap after
{Heap after GC invocations=3592 (full 4):
 PSYoungGen      total 9216K, used 256K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80102K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290966.033 GC heap before
{Heap before GC invocations=3593 (full 4):
 PSYoungGen      total 9216K, used 8960K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 50% used [0x00000000d6480000,0x00000000d64c0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80102K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290966.037 GC heap after
{Heap after GC invocations=3593 (full 4):
 PSYoungGen      total 9216K, used 288K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 56% used [0x00000000d6400000,0x00000000d6448000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80102K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290997.466 GC heap before
{Heap before GC invocations=3594 (full 4):
 PSYoungGen      total 9216K, used 8992K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 56% used [0x00000000d6400000,0x00000000d6448000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80103K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 290997.469 GC heap after
{Heap after GC invocations=3594 (full 4):
 PSYoungGen      total 9216K, used 192K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 37% used [0x00000000d6480000,0x00000000d64b0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80103K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 291010.290 GC heap before
{Heap before GC invocations=3595 (full 4):
 PSYoungGen      total 9216K, used 8896K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000d5580000,0x00000000d5e00000,0x00000000d5e00000)
  from space 512K, 37% used [0x00000000d6480000,0x00000000d64b0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80104K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 291010.293 GC heap after
{Heap after GC invocations=3595 (full 4):
 PSYoungGen      total 9216K, used 288K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 56% used [0x00000000d6400000,0x00000000d6448000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80104K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 291314.420 GC heap before
{Heap before GC invocations=3596 (full 4):
 PSYoungGen      total 9216K, used 8984K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 99% used [0x00000000d5580000,0x00000000d5dfe100,0x00000000d5e00000)
  from space 512K, 56% used [0x00000000d6400000,0x00000000d6448000,0x00000000d6480000)
  to   space 512K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6500000)
 ParOldGen       total 844288K, used 843944K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382a288,0x00000000b3880000)
 Metaspace       used 80117K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}
Event: 291314.472 GC heap after
{Heap after GC invocations=3596 (full 4):
 PSYoungGen      total 9216K, used 384K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5e00000)
  from space 512K, 75% used [0x00000000d6480000,0x00000000d64e0000,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6480000)
 ParOldGen       total 844288K, used 843952K [0x0000000080000000, 0x00000000b3880000, 0x00000000d5580000)
  object space 844288K, 99% used [0x0000000080000000,0x00000000b382c288,0x00000000b3880000)
 Metaspace       used 80117K, committed 81728K, reserved 1179648K
  class space    used 9565K, committed 10368K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.048 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.235 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.306 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.320 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.324 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.330 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.372 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.566 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 5.913 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
Event: 21.822 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management.dll
Event: 21.839 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management_ext.dll
Event: 26.880 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-3599307\jna12596340775126950646.dll
Event: 28.530 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
Event: 29.020 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\extnet.dll
Event: 36.617 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll

Deoptimization events (20 events):
Event: 290804.614 Thread 0x000001a6527b0600 DEOPT PACKING pc=0x000001a608ab5bb4 sp=0x0000004c3a8ff0b0
Event: 290804.614 Thread 0x000001a6527b0600 DEOPT UNPACKING pc=0x000001a607583aa2 sp=0x0000004c3a8ff0a0 mode 2
Event: 290804.617 Thread 0x000001a6527b0600 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001a608ab5bb4 relative=0x0000000000000ff4
Event: 290804.617 Thread 0x000001a6527b0600 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001a608ab5bb4 method=org.eclipse.core.internal.localstore.FileSystemResourceManager.allResourcesFor(Ljava/net/URI;ZI)[Lorg/eclipse/core/resources/IResource; @ 188 c2
Event: 290804.617 Thread 0x000001a6527b0600 DEOPT PACKING pc=0x000001a608ab5bb4 sp=0x0000004c3a8feeb0
Event: 290804.617 Thread 0x000001a6527b0600 DEOPT UNPACKING pc=0x000001a607583aa2 sp=0x0000004c3a8feea0 mode 2
Event: 290805.353 Thread 0x000001a6527b0600 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001a608ab5bb4 relative=0x0000000000000ff4
Event: 290805.353 Thread 0x000001a6527b0600 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001a608ab5bb4 method=org.eclipse.core.internal.localstore.FileSystemResourceManager.allResourcesFor(Ljava/net/URI;ZI)[Lorg/eclipse/core/resources/IResource; @ 188 c2
Event: 290805.353 Thread 0x000001a6527b0600 DEOPT PACKING pc=0x000001a608ab5bb4 sp=0x0000004c3a8ff0b0
Event: 290805.353 Thread 0x000001a6527b0600 DEOPT UNPACKING pc=0x000001a607583aa2 sp=0x0000004c3a8ff0a0 mode 2
Event: 290828.456 Thread 0x000001a6527b0600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001a6089eb88c relative=0x000000000000024c
Event: 290828.456 Thread 0x000001a6527b0600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001a6089eb88c method=org.eclipse.core.internal.jobs.JobManager.lambda$8(Lorg/eclipse/core/internal/jobs/InternalJob;JLorg/eclipse/core/internal/jobs/InternalJob;)Ljava/lang/Boolean; @ 29 c2
Event: 290828.456 Thread 0x000001a6527b0600 DEOPT PACKING pc=0x000001a6089eb88c sp=0x0000004c3a8fec40
Event: 290828.456 Thread 0x000001a6527b0600 DEOPT UNPACKING pc=0x000001a607583aa2 sp=0x0000004c3a8feb58 mode 2
Event: 290935.437 Thread 0x000001a6527b0600 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001a608acb6d4 relative=0x0000000000000174
Event: 290935.437 Thread 0x000001a6527b0600 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001a608acb6d4 method=org.eclipse.jdt.internal.core.DeltaProcessor$2.visit(Lorg/eclipse/core/resources/IResourceDelta;)Z @ 55 c2
Event: 290935.437 Thread 0x000001a6527b0600 DEOPT PACKING pc=0x000001a608acb6d4 sp=0x0000004c3a8fe750
Event: 290935.437 Thread 0x000001a6527b0600 DEOPT UNPACKING pc=0x000001a607583aa2 sp=0x0000004c3a8fe6e0 mode 2
Event: 291010.755 Thread 0x000001a653ff6d20 DEOPT PACKING pc=0x000001a60140b396 sp=0x0000004c3a2ff290
Event: 291010.755 Thread 0x000001a653ff6d20 DEOPT UNPACKING pc=0x000001a607584242 sp=0x0000004c3a2fe790 mode 0

Classes loaded (20 events):
Event: 185.622 Loading class java/util/stream/Nodes$ToArrayTask$OfRef
Event: 185.622 Loading class java/util/stream/Nodes$ToArrayTask
Event: 185.623 Loading class java/util/stream/Nodes$ToArrayTask done
Event: 185.623 Loading class java/util/stream/Nodes$ToArrayTask$OfRef done
Event: 204.478 Loading class java/util/stream/DistinctOps
Event: 204.479 Loading class java/util/stream/DistinctOps done
Event: 204.479 Loading class java/util/stream/DistinctOps$1
Event: 204.479 Loading class java/util/stream/DistinctOps$1 done
Event: 204.483 Loading class java/util/stream/DistinctOps$1$2
Event: 204.484 Loading class java/util/stream/DistinctOps$1$2 done
Event: 211.127 Loading class java/io/SequenceInputStream
Event: 211.128 Loading class java/io/SequenceInputStream done
Event: 253.858 Loading class java/util/concurrent/CompletableFuture$UniApply
Event: 253.859 Loading class java/util/concurrent/CompletableFuture$UniCompletion
Event: 253.859 Loading class java/util/concurrent/CompletableFuture$UniCompletion done
Event: 253.859 Loading class java/util/concurrent/CompletableFuture$UniApply done
Event: 253.863 Loading class java/util/concurrent/CompletableFuture$UniAccept
Event: 253.864 Loading class java/util/concurrent/CompletableFuture$UniAccept done
Event: 253.864 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 253.864 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done

Classes unloaded (12 events):
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a5000 'java/lang/invoke/LambdaForm$MH+0x000001a6101a5000'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a4c00 'java/lang/invoke/LambdaForm$MH+0x000001a6101a4c00'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a4800 'java/lang/invoke/LambdaForm$MH+0x000001a6101a4800'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a4400 'java/lang/invoke/LambdaForm$MH+0x000001a6101a4400'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a4000 'java/lang/invoke/LambdaForm$BMH+0x000001a6101a4000'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a3c00 'java/lang/invoke/LambdaForm$DMH+0x000001a6101a3c00'
Event: 24.313 Thread 0x000001a6709aea80 Unloading class 0x000001a6101a2800 'java/lang/invoke/LambdaForm$DMH+0x000001a6101a2800'
Event: 27.344 Thread 0x000001a6709aea80 Unloading class 0x000001a6102e8000 'java/lang/invoke/LambdaForm$MH+0x000001a6102e8000'
Event: 27.344 Thread 0x000001a6709aea80 Unloading class 0x000001a6102e7800 'java/lang/invoke/LambdaForm$MH+0x000001a6102e7800'
Event: 27.344 Thread 0x000001a6709aea80 Unloading class 0x000001a6102e7000 'java/lang/invoke/LambdaForm$MH+0x000001a6102e7000'
Event: 27.344 Thread 0x000001a6709aea80 Unloading class 0x000001a6102e6000 'java/lang/invoke/LambdaForm$MH+0x000001a6102e6000'
Event: 27.344 Thread 0x000001a6709aea80 Unloading class 0x000001a6102e5800 'java/lang/invoke/LambdaForm$MH+0x000001a6102e5800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 294663.077 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a6c920}> (0x00000000d5a6c920) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.077 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a6d0b8}> (0x00000000d5a6d0b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.077 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a6e778}> (0x00000000d5a6e778) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.077 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a6ef10}> (0x00000000d5a6ef10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.078 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a70358}> (0x00000000d5a70358) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.078 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a70af0}> (0x00000000d5a70af0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.078 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a71f38}> (0x00000000d5a71f38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.078 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a726d0}> (0x00000000d5a726d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.079 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a73a58}> (0x00000000d5a73a58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.082 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a74438}> (0x00000000d5a74438) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.094 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a9c5c0}> (0x00000000d5a9c5c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.094 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a9cd58}> (0x00000000d5a9cd58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.094 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a9e1a0}> (0x00000000d5a9e1a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.094 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a9e938}> (0x00000000d5a9e938) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.095 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a9fcc0}> (0x00000000d5a9fcc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.095 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5aa06b8}> (0x00000000d5aa06b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.104 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5aa2d80}> (0x00000000d5aa2d80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.104 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5aa3518}> (0x00000000d5aa3518) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.105 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5aa4960}> (0x00000000d5aa4960) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 294663.105 Thread 0x000001a653ff45c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5aa50f8}> (0x00000000d5aa50f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 291314.322 Executing VM operation: Cleanup
Event: 291314.347 Executing VM operation: Cleanup done
Event: 291314.420 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 291314.472 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 294154.710 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 294154.712 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 294154.712 Executing VM operation: RendezvousGCThreads
Event: 294154.712 Executing VM operation: RendezvousGCThreads done
Event: 294395.255 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 294395.255 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 294395.255 Executing VM operation: RendezvousGCThreads
Event: 294395.255 Executing VM operation: RendezvousGCThreads done
Event: 294695.823 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 294695.823 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 294695.823 Executing VM operation: RendezvousGCThreads
Event: 294695.823 Executing VM operation: RendezvousGCThreads done
Event: 295056.601 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 295056.601 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 295056.601 Executing VM operation: RendezvousGCThreads
Event: 295056.601 Executing VM operation: RendezvousGCThreads done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600e87f10
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600e89510
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600e8b310
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600e8cc10
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eaa310
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ead490
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eaf010
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eaf490
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eb7810
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eb8610
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600eba890
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ebad10
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ebbe90
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ebe210
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ec4b10
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ec5810
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ee8890
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ef1810
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ef1b90
Event: 231.992 Thread 0x000001a6709aea80 flushing  nmethod 0x000001a600ef2890

Events (20 events):
Event: 291313.544 Thread 0x000001a653ff8760 Thread exited: 0x000001a653ff8760
Event: 291313.545 Thread 0x000001a653ff6d20 Thread exited: 0x000001a653ff6d20
Event: 291313.549 Thread 0x000001a653ff73b0 Thread exited: 0x000001a653ff73b0
Event: 291313.555 Thread 0x000001a653ff52e0 Thread exited: 0x000001a653ff52e0
Event: 294663.006 Thread 0x000001a653ff45c0 Thread added: 0x000001a653ff4c50
Event: 294663.033 Thread 0x000001a653ff4c50 Thread exited: 0x000001a653ff4c50
Event: 294663.109 Thread 0x000001a653ff45c0 Thread added: 0x000001a653ff6d20
Event: 294663.110 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff73b0
Event: 294663.111 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff7a40
Event: 294663.112 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff80d0
Event: 294663.112 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff4c50
Event: 294663.113 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff8760
Event: 294663.115 Thread 0x000001a653ff6d20 Thread added: 0x000001a653ff52e0
Event: 294663.158 Thread 0x000001a653ff52e0 Thread exited: 0x000001a653ff52e0
Event: 294663.158 Thread 0x000001a653ff8760 Thread exited: 0x000001a653ff8760
Event: 294663.158 Thread 0x000001a653ff7a40 Thread exited: 0x000001a653ff7a40
Event: 294663.158 Thread 0x000001a653ff73b0 Thread exited: 0x000001a653ff73b0
Event: 294663.165 Thread 0x000001a653ff6d20 Thread exited: 0x000001a653ff6d20
Event: 294663.165 Thread 0x000001a653ff80d0 Thread exited: 0x000001a653ff80d0
Event: 294663.166 Thread 0x000001a653ff4c50 Thread exited: 0x000001a653ff4c50


Dynamic libraries:
0x00007ff73cab0000 - 0x00007ff73cabe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffdc9920000 - 0x00007ffdc9b89000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffdc88a0000 - 0x00007ffdc8969000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffdc6cf0000 - 0x00007ffdc70e3000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffdc6ae0000 - 0x00007ffdc6c2b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffdad940000 - 0x00007ffdad958000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffdc8060000 - 0x00007ffdc8224000 	C:\WINDOWS\System32\USER32.dll
0x00007ffdc6ab0000 - 0x00007ffdc6ad7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffdc77b0000 - 0x00007ffdc77db000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffdc7390000 - 0x00007ffdc74bd000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffdc74c0000 - 0x00007ffdc7563000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffdad920000 - 0x00007ffdad93e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffdab600000 - 0x00007ffdab893000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.5074_none_3e0d6f78e32fd63f\COMCTL32.dll
0x00007ffdc94f0000 - 0x00007ffdc9599000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffdc87e0000 - 0x00007ffdc880f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffdbfe70000 - 0x00007ffdbfe7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffd5efe0000 - 0x00007ffd5f06d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffd5e240000 - 0x00007ffd5efd7000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffdc9600000 - 0x00007ffdc96b4000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffdc8230000 - 0x00007ffdc82d6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffdc8970000 - 0x00007ffdc8a88000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffdc82e0000 - 0x00007ffdc8354000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffdc6910000 - 0x00007ffdc696e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdb0850000 - 0x00007ffdb0885000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffdbfca0000 - 0x00007ffdbfcab000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdc68f0000 - 0x00007ffdc6904000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffdc5850000 - 0x00007ffdc586b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdad910000 - 0x00007ffdad91a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffdc4220000 - 0x00007ffdc4462000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffdc7ce0000 - 0x00007ffdc805b000 	C:\WINDOWS\System32\combase.dll
0x00007ffdc9410000 - 0x00007ffdc94e7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffda1530000 - 0x00007ffda156d000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffdc72f0000 - 0x00007ffdc7389000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd83600000 - 0x00007ffd8360f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffd5f630000 - 0x00007ffd5f64f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffdc8af0000 - 0x00007ffdc923d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffdc70f0000 - 0x00007ffdc725c000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffdc46d0000 - 0x00007ffdc4f29000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffdc96c0000 - 0x00007ffdc97b0000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffdc7c70000 - 0x00007ffdc7cd2000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffdc69d0000 - 0x00007ffdc69f9000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd5f610000 - 0x00007ffd5f628000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffdad900000 - 0x00007ffdad910000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffdbe4a0000 - 0x00007ffdbe5bf000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffdc5db0000 - 0x00007ffdc5e1b000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd5f5f0000 - 0x00007ffd5f606000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffdb0fc0000 - 0x00007ffdb0fd0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffd5ce90000 - 0x00007ffd5ced4000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854\eclipse_11916.dll
0x00007ffdc9270000 - 0x00007ffdc9408000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd99b80000 - 0x00007ffd99b8a000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management.dll
0x00007ffd8f5b0000 - 0x00007ffd8f5bb000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management_ext.dll
0x00007ffdc86b0000 - 0x00007ffdc86b8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffdc6180000 - 0x00007ffdc619b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffdc57b0000 - 0x00007ffdc57eb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffdc5e50000 - 0x00007ffdc5e7b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffdc69a0000 - 0x00007ffdc69c6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffdc5fe0000 - 0x00007ffdc5fec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdc52a0000 - 0x00007ffdc52d4000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffdc86a0000 - 0x00007ffdc86aa000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd99e50000 - 0x00007ffd99e99000 	C:\Users\<USER>\AppData\Local\Temp\jna-3599307\jna12596340775126950646.dll
0x00007ffdc0b00000 - 0x00007ffdc0b1e000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdc0ad0000 - 0x00007ffdc0af3000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffd8dce0000 - 0x00007ffd8dcee000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
0x00007ffdc7570000 - 0x00007ffdc76e7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffdc62b0000 - 0x00007ffdc62e0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffdc6260000 - 0x00007ffdc629f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffdc5340000 - 0x00007ffdc5466000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffdba9c0000 - 0x00007ffdba9cb000 	C:\Windows\System32\rasadhlp.dll
0x00007ffdc0d90000 - 0x00007ffdc0e16000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd8af20000 - 0x00007ffd8af29000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\extnet.dll
0x00007ffdc0160000 - 0x00007ffdc0187000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffd8d320000 - 0x00007ffd8d328000 	C:\WINDOWS\system32\wshunix.dll

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.5074_none_3e0d6f78e32fd63f;c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250801-0854;C:\Users\<USER>\AppData\Local\Temp\jna-3599307;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\b4c54811f9342b977e0f40be9ce35415\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.45.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\b4c54811f9342b977e0f40be9ce35415\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-abc86f3f1613dd49146a117b4e74270a-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.45.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\b4c54811f9342b977e0f40be9ce35415\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
PATH=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin;C:\ffmpeg-7.1.1-essentials_build\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin;
USERNAME=user
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.5074)
OS uptime: 4 days 10:56 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0x100, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 2304, Current Mhz: 1803, Mhz Limit: 1797

Memory: 4k page, system-wide physical 16153M (1356M free)
TotalPageFile size 34758M (AvailPageFile size 39M)
current process WorkingSet (physical memory assigned to process): 50M, peak: 1805M
current process commit charge ("private bytes"): 1205M, peak: 1743M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
