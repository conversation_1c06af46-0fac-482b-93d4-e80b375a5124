import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const AdminSettingSVG: React.FC<IconProps> = ({
  size = 18, // original height
  color = "#232323",
  ...restProps
}) => {
  const width = (size * 15) / 18;  

  return (
    <Svg
      width={width}
      height={size}
      viewBox="0 0 15 18"
      fill="none"
      {...restProps}
    >
      <Path
        d="M15 9.588V6.576l-1.82-.433a5.968 5.968 0 00-.293-.705l.981-1.594-2.13-2.13-1.594.981a5.978 5.978 0 00-.704-.292L9.006.582H5.994l-.434 1.82c-.24.083-.475.18-.704.293l-1.594-.981-2.13 2.13.981 1.594c-.112.229-.21.464-.292.705L0 6.575v3.012l1.82.434c.083.24.18.475.293.704l-.986 1.602 1.477 1.414a4.618 4.618 0 00-.237 1.463v2.21H12.7v-2.21c0-.483-.076-.96-.222-1.414l1.383-1.482-.974-1.583c.112-.229.21-.464.292-.704L15 9.588zm-3.3 6.827H3.367v-1.21c0-.693.197-1.369.571-1.953a3.607 3.607 0 013.052-1.67h1.087c1.241 0 2.382.624 3.052 1.67.373.584.571 1.26.571 1.953v1.21zM7.533 10.54a2.315 2.315 0 01-2.312-2.312 2.315 2.315 0 012.312-2.313 2.315 2.315 0 012.313 2.313 2.315 2.315 0 01-2.313 2.312zm5.077 1.644l-.582.623c-.019-.032-.037-.063-.057-.094a4.604 4.604 0 00-2.392-1.882 3.308 3.308 0 001.267-2.603 3.316 3.316 0 00-3.313-3.313 3.316 3.316 0 00-3.312 3.313c0 1.055.496 1.996 1.266 2.603a4.604 4.604 0 00-2.391 1.882c-.016.024-.03.049-.045.073l-.649-.622.865-1.405-.654-1.577L1 8.798V7.366l1.613-.384.654-1.577-.87-1.413L3.41 2.98l1.413.87L6.4 3.195l.384-1.613h1.432L8.6 3.195l1.577.654 1.413-.87 1.013 1.013-.87 1.413.654 1.577L14 7.366v1.432l-1.613.384-.654 1.577.877 1.425z"
        fill={color}
      />
    </Svg>
  );
};

export default AdminSettingSVG;
