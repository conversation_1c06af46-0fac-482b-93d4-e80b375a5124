import { ViewToken } from 'react-native';
import debounce from 'lodash.debounce';
import { useCallback, useRef } from 'react';
import Logger from '../Logger';
import { profileUpdatesCache } from '../inMemoryCache';
import { getUser } from '../../utils/userApiService';
import { ChatService } from '../../service/ChatService';

const useProfileViewability = () => {
  /**
   * Extracts the profile IDs from the given list of viewable items.
   * It will return a list of profile IDs, or null if the profile ID is not available.
   * This is used to pass the profile IDs to the debounce function that handles the
   * profile updates on the server when the user is viewing the profile.
   * @param viewableItems
   * @returns {string[] | null[]}
   */
  const getProfileIdList = (viewableItems: Array<ViewToken>, fieldName: string) => {
    return viewableItems.map((item) => (item.item[fieldName] as string) || null);
  };

  /**
   * Handles fetching and updating profile data for a conversation when it appears in the viewport.
   *
   * This function checks if the profile data for the given conversationId has already been updated
   * (using the `profileUpdatesCache`). If so, it exits early to avoid redundant network calls.
   * If the conversationId starts with 'cs', the implementation is skipped (reserved for future logic).
   * Otherwise, it fetches the user's latest profile data by calling `getUser`.
   * If a user is found, it caches the conversationId and updates the profile via `ChatService.handleProfileUpdate`.
   *
   * @async
   * @function handleProfileOnViewPort
   * @param {string} conversationId - The unique identifier for the conversation.
   * @returns {Promise<void>} A promise that resolves when the profile update handling completes, or exits early for invalid cases.
   */
  const handleProfileOnViewPort = async (profileId: string): Promise<void> => {
    Logger.info(`Handling profile ${profileId} on viewport`);
    if (profileUpdatesCache.has(profileId)) return;
    if (profileId.startsWith('cs')) {
      // TODO: Implement this
      return;
    }
    const user = await getUser(profileId);
    if (!user) return;
    profileUpdatesCache.add(profileId);
    ChatService.handleProfileUpdate(user);
  };

  const debouncedHandleProfilesOnViewPort = debounce(async (profileIds: (string | null)[]) => {
    // Process profiles in parallel
    await Promise.allSettled(
      profileIds.map((profileId) => {
        if (!profileId) return;
        handleProfileOnViewPort(profileId);
      }),
    );
  }, 1000);

  /**
   * Creates a callback function for handling viewable items changed
   */
  const createViewableItemsHandler = useCallback(
    (fieldName: string) => {
      return ({ viewableItems }: { viewableItems: Array<ViewToken> }) => {
        Logger.log(`Viewable items changed for ${fieldName}` + viewableItems);

        const profileIds = getProfileIdList(viewableItems, fieldName);
        if (profileIds.length > 0) {
          debouncedHandleProfilesOnViewPort(profileIds);
        }
      };
    },
    [debouncedHandleProfilesOnViewPort],
  );

  return {
    createViewableItemsHandler,
  };
};

export default useProfileViewability;
