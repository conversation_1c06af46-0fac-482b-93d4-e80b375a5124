import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { colors } from '../../theme/colors';
import { commonFontStyle, hp } from '../../theme/fonts';
import { Dropdown } from 'react-native-element-dropdown';
import DatePicker from 'react-native-date-picker';
type Props = {
  scheduleDate?: any;
  setScheduleDate: (milliSeconds: number) => void;
};

const CustomDatePicker = ({ scheduleDate, setScheduleDate }: Props) => {
  const [date, setDate] = useState(
    scheduleDate ? new Date(scheduleDate) : moment().add(1, 'minute').toDate(),
  );

  const [futureTime, setFutureTime] = useState(moment().add(1, 'minute').toDate());

  const [open, setOpen] = useState(false);

  useEffect(() => {
    setScheduleDate(date.getTime());
  }, [date]);

  return (
    <View>
      <View style={styles.rowView}>
        <DatePicker
          date={date}
          mode="date"
          theme="auto"
          modal
          onConfirm={(date: any) => {
            setOpen(false);
            setDate(date);
          }}
          onCancel={() => {
            setOpen(false);
          }}
          open={open}
          minimumDate={new Date()}
        />
        <TouchableOpacity onPress={() => setOpen(true)} style={styles.boxView}>
          <Dropdown
            style={{ flex: 1 }}
            placeholderStyle={styles.text}
            selectedTextStyle={styles.text}
            data={[{ title: 'test' }]}
            maxHeight={170}
            labelField="title"
            valueField="title"
            value={moment(date).format('DD, MMMM')}
            placeholder={moment(date).format('DD, MMMM')}
            disable
            iconColor={colors.black_23}
            onChange={(item) => {}}
            renderItem={(item) => {
              return (
                <Text style={[styles.text, { marginVertical: 5, marginHorizontal: 5 }]}>
                  {item.title}
                </Text>
              );
            }}
            autoScroll={false}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setOpen(true)} style={{ ...styles.boxView, flex: 0.7 }}>
          <Dropdown
            style={{ flex: 1 }}
            placeholderStyle={styles.text}
            selectedTextStyle={styles.text}
            data={[{ title: 'test' }]}
            maxHeight={170}
            labelField="title"
            valueField="title"
            value={moment(date).format('YYYY')}
            placeholder={moment(date).format('YYYY')}
            disable
            iconColor={colors.black_23}
            onChange={(item) => {}}
            renderItem={(item) => {
              return (
                <Text style={[styles.text, { marginVertical: 5, marginHorizontal: 5 }]}>
                  {item.title}
                </Text>
              );
            }}
            autoScroll={false}
          />
        </TouchableOpacity>
      </View>

      <DatePicker
        date={date}
        mode="time"
        onDateChange={setDate}
        minimumDate={futureTime} // This enforces the minimum time
        style={{ alignSelf: 'center', marginVertical: 20 }}
        theme="auto"
      />
    </View>
  );
};

export default CustomDatePicker;

const styles = StyleSheet.create({
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: hp(2),
  },
  boxView: {
    borderWidth: 1,
    borderRadius: 15,
    borderColor: colors._DADADA_gray,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 55,
    // marginTop: hp(3),
    paddingHorizontal: hp(2.5),
    justifyContent: 'space-between',
    zIndex: 100,
  },
  bottomArrow: {
    tintColor: colors.black_23,
    height: 15,
    width: 15,
    resizeMode: 'contain',
  },
  text: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
});
