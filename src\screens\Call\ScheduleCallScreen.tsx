import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Pressable,
  TextInput,
} from 'react-native';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import { styles as inputStyle } from '../../component/Input';

import { useNavigation } from '@react-navigation/native';
import { FontAwesome6Icons } from '../../utils/vectorIcons';

const ScheduleCallScreen = ({}) => {
  const { t } = useTranslation();
  const navigation = useNavigation();

  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(moment(new Date()).format('hh:mm A'));

  const [mobile, setMobile] = useState('');
  const [open, setopen] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');

  return (
    <View style={{ flex: 1, backgroundColor: '#FFFFFF' }}>
      <View
        style={{
          backgroundColor: '#6a4dbb',
          paddingVertical: 10,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <View style={{ alignItems: 'center', flexDirection: 'row', marginLeft: 15 }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            //   style={headerStyle.headerBackView}
          >
            <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{ color: '#FFFFFF', marginLeft: 10, fontSize: 15 }}>
            Schedule audio call
          </Text>
        </View>
      </View>
      <View style={{ marginHorizontal: 15, marginTop: 15 }}>
        <Text style={{ marginTop: 10, fontWeight: '500', top: 12 }}>Set time</Text>
        <View style={styles.rowView}>
          <DatePicker
            date={date}
            mode={datePickerMode == 'date' ? 'date' : 'time'}
            theme="auto"
            modal
            onConfirm={(date: Date) => {
              console.log(' date ', date);
              datePickerMode == 'date' ? setDate(date) : setTime(moment(date).format('hh:mm A'));
              setopen(false);
            }}
            onCancel={() => {
              setopen(false);
            }}
            open={open}
            minimumDate={new Date()}
            // maximumDate={new Date(moment().format('YYYY-MM-DD'))}
          />
          <TouchableOpacity
            onPress={() => {
              setopen(true), setDatePickerMode('date');
            }}
            style={styles.boxView}
          >
            <Dropdown
              style={{ flex: 1 }}
              placeholderStyle={styles.text}
              selectedTextStyle={styles.text}
              data={[{ title: 'test' }]}
              maxHeight={170}
              labelField="title"
              valueField="title"
              value={moment(date).format('DD, MMMM')}
              placeholder={moment(date).format('DD, MMMM')}
              disable
              iconColor={colors.black_23}
              onChange={(item) => {}}
              autoScroll={false}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setopen(true)} style={styles.boxView}>
            <Dropdown
              style={{ flex: 1 }}
              placeholderStyle={styles.text}
              selectedTextStyle={styles.text}
              data={[
                { title: '2025' },
                { title: '2026' },
                { title: '2027' },
                { title: '2028' },
                { title: '2029' },
                { title: '2030' },
              ]}
              // maxHeight={170}
              labelField="title"
              valueField="title"
              value={moment(date).format('YYYY')}
              placeholder={moment(date).format('YYYY')}
              mode={'default'}
              disable
              iconColor={colors.black_23}
              onChange={(item) => {}}
              renderItem={(item) => {
                return (
                  <Text style={[styles.text, { marginVertical: 5, marginHorizontal: 5 }]}>
                    {item.title}
                  </Text>
                );
              }}
              autoScroll={false}
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={{
            paddingHorizontal: 50,
            width: '50%',
            alignSelf: 'center',
            padding: 14,
            borderWidth: 1,
            marginBottom: 10,
            borderColor: '#DDD',
            borderRadius: 12,
          }}
          onPress={() => {
            setopen(true), setDatePickerMode('time');
          }}
        >
          <Text style={{}}>{time}</Text>
        </TouchableOpacity>
        <View style={{ marginTop: 10 }}>
          <Text style={{ fontWeight: '500' }}>Added people(10)</Text>
          <Text style={{ marginTop: 15 }}>Enter call agenda(Optional)</Text>
          {/* <View style={styles.verLine} /> */}

          <View style={[inputStyle.rowView, { marginBottom: hp(2) }]}>
            <TextInput
              value={mobile}
              onChangeText={setMobile}
              style={{ flex: 1, height: 45 }}
              placeholder={t('Enter what this call is reagarding..')}
              keyboardType={'phone-pad'}
              placeholderTextColor={colors._CCCCCC_gray}
              // maxLength={inputLength}
            />
          </View>
          <Text style={{ marginTop: 5 }}>Upload banner(Optional)</Text>

          <View style={[inputStyle.rowView, { marginBottom: hp(2) }]}>
            <TextInput
              value={mobile}
              onChangeText={setMobile}
              style={{
                flex: 1,
                height: 45,
              }}
              placeholder={t('Formats :jpg,png')}
              keyboardType={'phone-pad'}
              placeholderTextColor={colors._CCCCCC_gray}
              // maxLength={inputLength}
            />
            <TouchableOpacity
              style={{
                backgroundColor: '#eee',
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 15,
              }}
            >
              <Image style={{ width: 20, height: 20 }} source={IMAGES.upload} />
              <Text style={{ paddingVertical: 9, marginLeft: 5 }}>Upload</Text>
            </TouchableOpacity>
          </View>
          <Pressable
            onPress={() => {}}
            style={{
              height: 40,
              backgroundColor: '#6a4dbb',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 10,
              marginTop: 10,
            }}
          >
            <Text style={{ color: '#FFF' }}>Schedule</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  rowView: {
    flexDirection: 'row',
    gap: hp(1.5),
    // backgroundColor:'red'
  },
  boxView: {
    borderWidth: 1,
    borderRadius: 15,
    borderColor: colors._DADADA_gray,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 55,
    marginVertical: hp(3),
    paddingHorizontal: hp(2.5),
    justifyContent: 'space-between',
  },
  bottomArrow: {
    tintColor: colors.black_23,
    height: 15,
    width: 15,
    resizeMode: 'contain',
  },
  text: {
    ...commonFontStyle(400, 16, colors.black_23),
  },

  video: {
    width: '100%',
    height: 200, // You can adjust the size of the video based on your requirements
    backgroundColor: '#000', // Add background color to simulate video placeholder
  },
  verLine: {
    width: 1,
    marginHorizontal: 10,
    backgroundColor: 'rgba(0, 0, 0,0.1)',
    height: 30,
  },
});

export default ScheduleCallScreen;
