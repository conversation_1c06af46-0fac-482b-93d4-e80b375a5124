import Realm from 'realm';
import { CallType, CallOrigin, CallOriginType } from '../../../types/calls.types';
import { realmSchemaNames } from './schemaNames';
import { IContact } from './ContactSchema';
import { CallUser } from '../../../api/calls/calls.api';
import { UserSchema } from './UserSchema';

export type CallHistoryItem = {
  callStatus: CallHistoryStatus;
  callType: CallType;
  callId: string;
  scheduledAt: Date | null;
  createdAt: Date;
  participants: string[]; // user IDs
  invitedUserIds: string[];

  initiator: string; // user ID
  origin?: CallOrigin;
  isOngoingCall?: boolean;
  syncPending?: boolean;
  originType?: CallOriginType;
  initiatorJson?: string;
  participantsJson?: string;
  invitedUserIdsJson?: string;
  originJson?: string;
};

export type CallHistoryStatus =
  | 'outgoing'
  | 'missed'
  | 'incoming'
  | 'rejected'
  | 'ignored'
  | 'answered';

export class CallSchema extends Realm.Object<CallSchema> implements CallHistoryItem {
  callStatus!: CallHistoryStatus;
  callType!: CallType;
  callId!: string;
  scheduledAt!: Date | null;
  createdAt!: Date;
  participants!: string[];
  invitedUserIds!: string[];

  initiator!: string;
  isOngoingCall?: boolean;
  originJson?: string;
  syncPending?: boolean;
  originType?: CallOriginType;
  initiatorJson?: string;
  participantsJson?: string;
  invitedUserIdsJson?: string;

  get origin(): CallOrigin | undefined {
    if (!this.originJson) return undefined;
    try {
      return JSON.parse(this.originJson) as CallOrigin;
    } catch {
      return undefined;
    }
  }

  set origin(value: CallOrigin | undefined) {
    if (value) {
      this.originJson = JSON.stringify(value);
    } else {
      this.originJson = undefined;
    }
  }

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.call,
    primaryKey: 'callId',
    properties: {
      callStatus: 'string',
      callType: 'string',
      callId: 'string',
      scheduledAt: 'date?',
      createdAt: 'date',
      participants: 'string[]',
      invitedUserIds: 'string[]',
      initiator: 'string',
      isOngoingCall: 'bool?',
      originJson: 'string?',
      syncPending: 'bool?',
      originType: 'string?',
      initiatorJson: 'string?',
      participantsJson: 'string?',
      invitedUserIdsJson: 'string?',
    },
  };
}

type CallVariant = 'groupCall' | 'p2pCall' | 'adhocCall';
export interface CallHistoryItemNormalized extends CallHistoryItem {
  initiatorContact?: UserSchema;
  initiatorDetails: CallUser | null;
  participantDetails: CallUser[] | null;
  invitedUserIdsDetails: CallUser[] | null;
  callVariant: CallVariant;
}
