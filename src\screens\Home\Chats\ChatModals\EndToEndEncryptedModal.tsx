import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { IMAGES } from '../../../../assets/Images';
import { androidVersion, hp } from '../../../../theme/fonts';
import ModalWrapper from '../../../../component/ModalWrapper';
import { Image } from 'react-native';
import { colors } from '../../../../theme/colors';
import ChatDotSVG from '../../../../assets/svgIcons/ChatDotSVG';
import CallSVG from '../../../../assets/svgIcons/CallSVG';
import FolderSVG from '../../../../assets/svgIcons/FolderSVG';
import ButtonPurple from '../../../../component/ButtonPurple';
import StatusSVG from '../../../../assets/svgIcons/StatusSVG';
import LocationSVG from '../../../../assets/svgIcons/LocationSVG';

type IProps = {
  isVisible: boolean;
  onClose: () => void;
};

const EndToEndEncryptedModal = ({ isVisible, onClose }: IProps) => {
  const isAndroid15 = Number(androidVersion) > 14;

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={() => onClose()}>
      <View style={{ paddingHorizontal: hp(3) }}>
        <Image
          source={IMAGES.cloudLock}
          style={{ height: 55, resizeMode: 'contain', alignSelf: 'center', marginTop: 8 }}
        />

        <Text
          style={{
            fontSize: 20,
            color: colors.black_23,
            fontWeight: '600',
            marginTop: 15,
            alignSelf: 'center',
            textAlign: 'center',
          }}
        >
          Your activity is encrypted
        </Text>
        <Text
          style={{
            fontSize: 16,
            color: colors.black_23,
            fontWeight: '400',
            alignSelf: 'center',
            textAlign: 'center',
            marginBottom: 35,
            marginTop: 5,
          }}
        >
          Your activity on ChatBucket is completely{'\n'}
          end-to-end encrypted. You and the person{'\n'}
          you choose has the access to your{'\n'}conversations. Including
        </Text>

        <View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 18 }}>
            <ChatDotSVG color={colors.black_23} size={19} />
            <Text style={styles.bottomText}>Text and voice messages</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 18 }}>
            <CallSVG color={colors.black_23} size={18} />
            <Text style={styles.bottomText}>Audio and video calls</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 18 }}>
            <FolderSVG color={colors.black_23} size={20} />
            <Text style={styles.bottomText}>Photos, videos and documents</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 18 }}>
            <LocationSVG color={colors.black_23} size={20} />
            <Text style={styles.bottomText}>Location sharing</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 18 }}>
            <StatusSVG color={colors.black_23} size={17} />
            <Text style={styles.bottomText}>Status updates</Text>
          </View>
        </View>

        <ButtonPurple
          extraStyle={{ marginBottom: isAndroid15 ? hp(2) : hp(4), marginTop: hp(2) }}
          onPress={() => {
            console.log('modal close clicked');
            onClose();
          }}
          textStyle={{ fontSize: 16, fontWeight: '600' }}
          title={'Okay'}
        />
      </View>
    </ModalWrapper>
  );
};

export default EndToEndEncryptedModal;

const styles = StyleSheet.create({
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
});
