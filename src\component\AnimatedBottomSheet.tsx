import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  PanResponder,
} from 'react-native';
import Modal from 'react-native-modal';

const AnimatedBottomSheet = () => {
  const { height } = Dimensions.get('window');
  const [isModalVisible, setModalVisible] = useState(false);
  const modalHeight = height * 0.7;

  const translateY = useRef(new Animated.Value(modalHeight)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dy) > 10; // Start pan only for vertical gestures
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > modalHeight / 3) {
          Animated.spring(translateY, {
            toValue: modalHeight,
            useNativeDriver: true,
          }).start(() => {
            setModalVisible(false);
          });
        } else {
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  const openModal = () => {
    setModalVisible(true);
    translateY.setValue(modalHeight);
    Animated.spring(translateY, {
      toValue: 0,
      useNativeDriver: true,
    }).start();
  };

  const closeModal = () => {
    Animated.spring(translateY, {
      toValue: modalHeight,
      useNativeDriver: true,
    }).start(() => setModalVisible(false));
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={openModal}>
        <Text>Open Bottom Sheet</Text>
      </TouchableOpacity>

      <Modal
        isVisible={isModalVisible}
        onBackdropPress={closeModal}
        onBackButtonPress={closeModal}
        style={{ justifyContent: 'flex-end', margin: 0 }}
        useNativeDriver
        hideModalContentWhileAnimating
      >
        <Animated.View
          style={[styles.modalContent, { height: modalHeight, transform: [{ translateY }] }]}
          {...panResponder.panHandlers}
        >
          {/* Handle / Arrow */}
          <View style={styles.handleContainer}>
            <Text style={styles.arrow}>⬇️⬆️</Text>
          </View>

          {/* Content */}
          {Array.from({ length: 10 }).map((_, i) => (
            <Text style={{ color: 'black' }} key={i}>
              Your bottom sheet content goes here
            </Text>
          ))}
        </Animated.View>
      </Modal>
    </View>
  );
};

export default AnimatedBottomSheet;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  handleContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },
  arrow: {
    fontSize: 20,
  },
});
