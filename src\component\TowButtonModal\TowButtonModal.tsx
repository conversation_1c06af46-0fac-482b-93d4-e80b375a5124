import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import ButtonPurple from '../ButtonPurple';
import { commonFontStyle } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import Modal from 'react-native-modal';
import ThreeDotsSVG from '../../assets/svgIcons/ThreeDotsSVG';

interface Props {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
  onPressMore?: () => void;
  title?: string;
  buttonLeftTitle?: string;
  buttonRightTitle?: string;
  onPressLeft?: () => void;
  onPressRight?: () => void;
}

const TowButtonModal = ({
  onPressMore,
  title,
  buttonLeftTitle = '',
  buttonRightTitle = '',
  isVisible,
  setIsVisible,
  onPressLeft,
  onPressRight,
}: Props) => {
  const [_isVisible, _setIsVisible] = useState(false);

  useEffect(() => {
    _setIsVisible(isVisible);
  }, [isVisible]);

  const onClose = () => {
    _setIsVisible(false);
    setIsVisible(!_isVisible);
  };

  return (
    <Modal
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      isVisible={isVisible}
      style={styles.modalContainer}
      animationIn={'slideInUp'}
      animationOut={'slideOutDown'}
    >
      <View style={styles.container}>
        <View style={styles.row}>
          <View style={{ ...styles.row, flex: 1 }}>
            <Text style={styles.newStoryText}>{title}</Text>
          </View>
          <TouchableOpacity onPress={onPressMore}>
            <ThreeDotsSVG color={colors.white} />
          </TouchableOpacity>
        </View>
        <View style={{ ...styles.row, top: 20 }}>
          <ButtonPurple
            type="gray"
            title={buttonLeftTitle}
            extraStyle={{ flex: 1 }}
            onPress={onPressLeft}
          />
          <ButtonPurple title={buttonRightTitle} extraStyle={{ flex: 1 }} onPress={onPressRight} />
        </View>
      </View>
    </Modal>
  );
};

export default TowButtonModal;

const styles = StyleSheet.create({
  modalContainer: {
    margin: 0,
    flex: 1,
    justifyContent: 'flex-end',
  },
  container: {
    padding: 20,
    backgroundColor: colors.white,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingBottom: 40,
    gap: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  newStoryText: {
    ...commonFontStyle(600, 16, colors.black),
  },

  sharingText: {
    ...commonFontStyle(400, 16, colors.black),
    backgroundColor: '#F3F3F3',
    padding: 5,
    borderRadius: 10,
    overflow: 'hidden',
    paddingHorizontal: 10,
  },

  moreMenuIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
});
