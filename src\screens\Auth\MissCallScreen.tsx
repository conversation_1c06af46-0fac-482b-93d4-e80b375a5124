import {
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  SafeAreaView,
  View,
  Clipboard,
  ActivityIndicator,
  NativeModules,
  DeviceEventEmitter,
  Alert,
  Platform,
  PermissionsAndroid,
  Pressable,
  Keyboard,
} from 'react-native';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { AppStyles } from '../../theme/appStyles';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp, SCREEN_WIDTH } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import Input from '../../component/Input';
import ButtonPurple from '../../component/ButtonPurple';
import { SCREENS } from '../../navigation/screenNames';
import { useTranslation } from 'react-i18next';

const { SendSmsModule, IncomingCallModule, SimInfo } = NativeModules;

import {
  emailCheck,
  errorToast,
  successToast,
  mobileNumberCheck,
  navigateTo,
} from '../../utils/commonFunction';
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';

import { OtpInput, OtpInputRef } from 'react-native-otp-entry';

const CELL_COUNT = 4;
import { AxiosError } from 'axios';
import { setAuthorization } from '../../utils/apiGlobal';
import { setAsyncToken, setStreamToken } from '../../utils/asyncStorage';
import DeviceInfo from 'react-native-device-info';
import { LoginApi, VerifyMisedCall } from '../../utils/ApiService';
import { CSpinner } from '../../common';
import { connectToSocketServer, getSocket } from '../../socket-client/socket';
import { navigationRef } from '../../navigation/RootContainer';
import { useMe } from '../../hooks/util/useMe';
import { Client } from '../../lib/Client';
import { handleVerificationResponse } from './authUtils';

type MyRouteParams = {
  MyScreen: {
    data: {
      phoneNumber: string;
      phoneCode: string;
      deviceId: string;
    };
  };
};

type MyScreenRouteProp = RouteProp<MyRouteParams, 'MyScreen'>;

const MissCallScreen = () => {
  const { params } = useRoute();
  const route = useRoute<MyScreenRouteProp>();
  const { t } = useTranslation();
  const [timer, setTimer] = useState(5);
  const { updateUser } = useMe();
  interface CallParams {
    data?: any;
    res?: {
      data?: any;
    };
  }

  let callRequestDetails = (params as CallParams)?.data;
  let paramsData = (params as CallParams)?.res?.data;
  const [callFrom, setCallFrom] = useState('');
  const [value, setValue] = useState('');

  const [loginVia, setLoginVia] = useState('call');
  const [next, setNext] = useState('');

  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);

  const [isVerify, setIsVerify] = useState(false);

  const otpInputRef = useRef<OtpInputRef>(null);
  // const {fetchUser} = useContext(UserContext);
  useMe();
  useEffect(() => {
    if (paramsData?.statusCode == 200) {
      setLoginVia('message');
    } else if (paramsData?.statusCode == 202) {
      setLoginVia('call');
      setCallFrom(paramsData?.cli_prefix);
    } else if (paramsData?.statusCode == 206) {
      setLoginVia('whatsapp');
    }
    if (paramsData?.nextStepCode == 202) {
      setNext('Request call again');
    } else if (paramsData?.nextStepCode == 200) {
      setNext('Request SMS');
    } else if (paramsData?.nextStepCode == 206) {
      setNext('Request whatsapp');
    }

    return () => {
      if (otpInputRef.current) otpInputRef.current.clear();
      setValue('');
    };
  }, []);

  const enableListener = async () => {
    const callListener = DeviceEventEmitter.addListener('IncomingCall', async (number) => {
      try {
        IncomingCallModule.stopListening();
        const newPost = {
          tsid: paramsData?.tsid,
          otp: number.toString().slice(-4),
        };

        if (number) {
          const res = await VerifyMisedCall(newPost);
          console.log(' res login auto call verification ', res);
          await handleVerificationResponse(res, updateUser);
        }
      } catch (error) {
        console.error('Error during call verification:', error);
        setLoading(false);
      }
    });

    // Stop listening automatically after 30 seconds to prevent memory leaks
    const timeout = setTimeout(() => {
      console.log('Stopping call listener after timeout...');
      IncomingCallModule.stopListening();
      callListener.remove(); // Remove listener properly
    }, 30000);

    // Cleanup function for safety
    return () => {
      clearTimeout(timeout);
      callListener.remove();
      IncomingCallModule.stopListening();
    };
  };

  useEffect(() => {
    requestPermissions()
      .then((granted) => {
        console.log('permisson granted  = = = = =', granted);
        if (granted) {
          IncomingCallModule.startListening();
          enableListener();
          console.log('Permissions granted');
        } else {
          Alert.alert('Permissions Denied', 'App needs permissions to detect incoming calls.');
        }
      })
      .catch((err) => console.error(err));

    return () => {
      IncomingCallModule.stopListening();
    };
  }, []);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
          PermissionsAndroid.PERMISSIONS.READ_CALL_LOG,
        ]);
        console.log('call permissions granted ', granted);
        return (
          granted[PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE] &&
          granted[PermissionsAndroid.PERMISSIONS.READ_CALL_LOG] ===
            PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (err) {
        console.log('err permission ', err);
        console.warn(err);
        return false;
      }
    }
    return true;
  };
  const handleResponse202 = (responsecode: any, nextresponsecode: any) => {
    switch (nextresponsecode) {
      case 202:
        // setCallFrom(responsecode?.callfrom);
        setNext('Request call back');
        // setTimeout(() => {}, 15000);
        break;
      case 206:
        setNext('Request WhatsApp SMS');
        break;
      case 200:
        setNext('Request SMS');
        break;
      case 210:
        setNext('You have only one attempt left');
        break;
      case -6:
        setNext('You are blocked');
        setTimeout(() => setLoginVia('blocked'), 45000);
        break;
      default:
        break;
    }
  };

  const handleResponse206 = (responsecode: any, nextresponsecode: any) => {
    switch (nextresponsecode) {
      case 206:
        setNext('Request WhatsApp SMS');
        break;
      case 200:
        setNext('Request SMS');
        break;
      case 202:
        setNext('Request call back');
        break;
      case -6:
        setNext('You are blocked');
        setTimeout(() => setLoginVia('blocked'), 46000);
        break;
      default:
        break;
    }
  };

  const handleResponse200 = (responsecode: any, nextresponsecode: any) => {
    switch (nextresponsecode) {
      case 200:
        setNext('Request SMS');
        break;
      case 202:
        setNext('Request call back');
        break;
      case 206:
        setNext('Request WhatsApp');
        break;
      case 210:
        setNext('You have only one attempt left');
        break;
      case -6:
        setNext('You are blocked');
        setTimeout(() => handleBlockedAccount(responsecode?.message), 45000);
        break;
      default:
        break;
    }
  };

  const handleBlockedAccount = (message: any) => {
    setTimeout(() => {
      setNext('');
      navigateTo('');
    }, 15000);
  };

  const onSignup = async () => {
    const uniqueId = await DeviceInfo.getUniqueId();
    if (callRequestDetails?.phoneCode.trim().length == 0) {
      errorToast(t('Please enter a valid mobile number'));
    } else if (!mobileNumberCheck(callRequestDetails?.phoneNumber.trim())) {
      errorToast(t('Please enter a valid mobile number'));
    } else {
      const newPost = {
        phoneCode: callRequestDetails?.phoneCode,
        phoneNumber: callRequestDetails?.phoneNumber.trim(),
        deviceId: uniqueId,
      };

      setLoading(true);
      try {
        const res = await LoginApi(newPost);
        console.log(' login resp = = = =', res);
        if (res.status) {
          setLoading(false);
          if (res?.data?.statusCode == 202) {
            enableListener();
            setCallFrom(res?.data?.cli_prefix);
          }
          handleResponse(res?.data?.statusCode, res?.data?.nextStepCode);
        } else if (res?.statusCode == -6) {
          setLoading(false);
        } else {
          setLoading(false);
          errorToast(t(res?.message || 'Login failed. Please try again.'));
          navigateTo(SCREENS.SignupScreen);
        }
      } catch (error) {
        setLoading(false);
        errorToast(t(getAxiosErrorMessage(error)));
      } finally {
        otpInputRef.current && otpInputRef.current.clear();
        setValue('');
      }
    }
  };
  const getAxiosErrorMessage = (err: unknown): string => {
    const error = err as AxiosError<{ message?: string }>;
    return error?.response?.data?.message || 'Login failed. Please try again.';
  };

  const handleResponse = (responsecode: any, nextresponsecode: any) => {
    switch (responsecode) {
      case 202:
        setLoginVia('call');
        setTimer(5);
        handleResponse202(responsecode, nextresponsecode);
        break;
      case 206:
        setLoginVia('whatsapp');
        setTimer(5);
        handleResponse206(responsecode, nextresponsecode);
        break;
      case 200:
        setLoginVia('message');
        setTimer(5);
        handleResponse200(responsecode, nextresponsecode);
        break;
      case -6:
        setLoginVia('blocked');
        break;
      default:
        break;
    }
  };

  const verifyOTP = async () => {
    setIsVerify(true);
    const newPost = {
      tsid: paramsData.tsid,
      otp: value,
    };
    try {
      const res = await VerifyMisedCall(newPost);
      console.log('auto call res = = = =  = ', res);
      await handleVerificationResponse(res, updateUser);
    } catch (error) {
      console.error('Error during call verification:', error);
      setLoading(false);
    } finally {
      setIsVerify(false);
    }
  };

  const renderSpinner = () => {
    if (loader) {
      return <CSpinner size={''} />;
    }
  };

  const numberText = (
    <Text style={[styles.textBlack1, { marginTop: 0, fontWeight: '700' }]}>
      {'+' + route.params?.data?.phoneCode + route.params?.data?.phoneNumber}
    </Text>
  );

  useEffect(() => {
    const expectedLength = loginVia === 'whatsapp' ? 6 : 4;
    if (value.length === expectedLength) {
      Keyboard.dismiss(); // Dismiss the keyboard
      // You can also trigger submit/validation
    }
  }, [value, loginVia]);

  return (
    <View style={AppStyles.purpleMainContainer}>
      {renderSpinner()}
      <View style={{ padding: 50, backgroundColor: colors.mainPurple }}></View>
      <View style={AppStyles.bottomWhiteViewCall}>
        <View style={[AppStyles.chatLogo2, { elevation: 8 }]}>
          <Image
            source={
              loginVia == 'whatsapp'
                ? IMAGES.whatsApp
                : loginVia == 'call'
                ? IMAGES.callVerify
                : loginVia == 'message'
                ? IMAGES.message
                : loginVia == 'blocked'
                ? IMAGES.blocked
                : ''
            }
            style={{
              width: 50,
              height: 50,
              resizeMode: 'contain',
            }}
          />
        </View>
        {loginVia == 'call' ? (
          <SafeAreaView>
            <Text style={styles.textBlackNormal}>
              {t(
                `Calling... +${callFrom} XXXXXX${'\n\n'} You'll receive a missed call for the ${'\n'}provided number`,
              )}
              , {numberText}
            </Text>
            <Text style={styles.textBlack1}>{t('No need to pick up the call')}</Text>
            <Text style={styles.desBlack}>{t('We’ll verify the number automatically')}</Text>
            <View
              style={{
                borderBottomWidth: 1,
                borderBottomColor: '#ddd',
                marginTop: 20,
              }}
            ></View>

            {loading ? (
              <View
                style={{
                  alignSelf: 'center',
                  marginVertical: hp(2.5),
                  alignItems: 'center',
                }}
              >
                <ActivityIndicator animating={loading} size="large" color="#0000ff" />
              </View>
            ) : (
              <TouchableOpacity onPress={() => {}} style={styles.loginTextView}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={styles.bottomText}>{t('Didn’t receive call?')}</Text>
                  {timer > 0 && (
                    <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                      <Text>
                        {''} in{'  '}
                      </Text>
                      <CountdownCircleTimer
                        isPlaying
                        duration={timer}
                        colors={['#004777', '#F7B801', '#A30000', '#A30000']}
                        colorsTime={[7, 5, 2, 0]}
                        size={28}
                        strokeWidth={2}
                        onComplete={() => {
                          setTimer(0);
                        }}
                      >
                        {({ remainingTime }) => <Text>{remainingTime}</Text>}
                      </CountdownCircleTimer>
                      <Text>
                        {'  '}
                        Sec.
                      </Text>
                    </View>
                  )}
                </View>

                {timer <= 0 && (
                  <Pressable
                    onPress={() => {
                      Keyboard.dismiss();
                      onSignup();
                    }}
                    style={{ marginLeft: 5, bottom: 1.4 }}
                  >
                    {
                      <Text
                        style={{
                          color: next === 'You are blocked' ? '#E03D3D' : '#6A4DBB',
                          fontWeight: '600',
                        }}
                      >
                        {next}
                      </Text>
                    }
                  </Pressable>
                )}
              </TouchableOpacity>
            )}
          </SafeAreaView>
        ) : loginVia == 'message' || loginVia == 'whatsapp' ? (
          <SafeAreaView>
            <Text style={styles.textBlack}>
              {t(loginVia == 'whatsapp' ? 'Whatsapp OTP' : 'Enter OTP')}
            </Text>
            <Text style={[styles.desBlack, { marginTop: 5 }]}>
              {t('OTP has been sent to ') +
                '+' +
                callRequestDetails?.phoneCode +
                callRequestDetails?.phoneNumber}
            </Text>
            <OtpInput
              ref={otpInputRef}
              numberOfDigits={loginVia == 'whatsapp' ? 6 : 4}
              focusColor="#6A4DBB"
              focusStickBlinkingDuration={500}
              onTextChange={(text) => {
              const filtered = text.replace(/[^0-9]/g, '');
                if (filtered !== text) {
                  otpInputRef.current?.setValue(filtered);
                }
                setValue(filtered);
                if (filtered?.length == (loginVia === 'whatsapp' ? 6 : 4)) {
                  Keyboard.dismiss();
                }
              }}
              onFilled={(text) => {}}
              textInputProps={{
                accessibilityLabel: 'One-Time Password',
                keyboardType: 'numeric',
                maxLength: loginVia === 'whatsapp' ? 6 : 4,
                onKeyPress: (e) => {
                  const key = e.nativeEvent.key;
                  if (!/^[0-9]$/.test(key) && key !== 'Backspace') {
                    e.preventDefault?.();
                  }
                },
              }}
              theme={{
                containerStyle: {
                  width: loginVia === 'whatsapp' ? '90%' : '60%',
                  alignSelf: 'center',
                  // marginVertical: 15,
                  marginTop: 30,
                },
                pinCodeContainerStyle: { borderRadius: 6, height: 45, width: 45 },
                pinCodeTextStyle: { fontSize: 17, color: 'black' },
                focusStickStyle: { height: 20 },
              }}
            />
            <ButtonPurple
              isLoading={isVerify}
              disabled={
                loginVia === 'whatsapp'
                  ? value?.length === 6
                    ? false
                    : true
                  : value?.length === 4
                  ? false
                  : true
              }
              extraStyle={{
                width: 200,
                alignSelf: 'center',
                marginTop: 30,
                height: 45,
                // backgroundColor:
                //   loginVia === 'whatsapp'
                //     ? value?.length === 6
                //       ? colors.mainPurple
                //       : colors.mainPurple
                //     : value?.length === 4
                //     ? colors.mainPurple
                //     : colors.mainPurple,
              }}
              onPress={() => {
                verifyOTP();
              }}
              title={'Submit'}
              textStyle={{
                color: colors.white,
                fontWeight: 'bold',
              }}
            />
            {loading ? (
              <View
                style={{
                  alignSelf: 'center',
                  marginVertical: hp(2.5),
                  alignItems: 'center',
                }}
              >
                <ActivityIndicator animating={loading} size="large" color="#0000ff" />
              </View>
            ) : (
              <TouchableOpacity onPress={() => {}} style={styles.loginTextView}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={styles.bottomText}>{t('Didn’t receive OTP?')}</Text>
                  {timer > 0 && (
                    <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                      <Text>
                        {''} in{'  '}
                      </Text>
                      <CountdownCircleTimer
                        isPlaying
                        duration={timer}
                        colors={['#004777', '#F7B801', '#A30000', '#A30000']}
                        colorsTime={[7, 5, 2, 0]}
                        size={28}
                        strokeWidth={2}
                        onComplete={() => {
                          setTimer(0);
                        }}
                      >
                        {({ remainingTime }) => <Text>{remainingTime}</Text>}
                      </CountdownCircleTimer>
                      <Text>
                        {'  '}
                        Sec.
                      </Text>
                    </View>
                  )}
                </View>

                {timer <= 0 && (
                  <Pressable
                    onPress={() => {
                      Keyboard.dismiss();
                      setValue('');
                      onSignup();
                    }}
                    style={{ marginLeft: 5, bottom: 1.4 }}
                  >
                    {<Text style={{ color: '#6A4DBB', fontWeight: '600' }}>{next}</Text>}
                  </Pressable>
                )}
              </TouchableOpacity>
            )}
          </SafeAreaView>
        ) : loginVia == 'blocked' ? (
          <SafeAreaView>
            <Text style={[styles.textBlack, { color: '#E03D3D' }]}>{t('You are blocked')}</Text>

            <ButtonPurple
              extraStyle={{
                width: 200,
                alignSelf: 'center',
                marginVertical: 20,
                height: 45,
                backgroundColor: colors.mainPurple,
              }}
              onPress={() => {
                navigateTo(SCREENS.SignupScreen);
              }}
              title={'Okay'}
            />
          </SafeAreaView>
        ) : null}
      </View>
      <Image
        source={IMAGES.posterImg}
        style={{
          width: 300,
          height: 200,
          resizeMode: 'contain',
          alignSelf: 'center',
          alignItems: 'flex-end',
          top: 25,
        }}
      />
    </View>
  );
};

export default MissCallScreen;

const styles = StyleSheet.create({
  titleText: {
    ...commonFontStyle(700, 31, colors.white),
    textAlign: 'center',
  },
  des: {
    ...commonFontStyle(400, 16, colors.white),
    textAlign: 'center',
    marginBottom: hp(4),
  },
  bottomText: {
    ...commonFontStyle(400, 14, colors.black_23),
  },
  loginTextView: {
    alignSelf: 'center',
    marginVertical: hp(2.5),
    alignItems: 'center',
  },
  textBlack: {
    ...commonFontStyle(500, 17, colors.black_23),
    textAlign: 'center',
    fontWeight: '700',
  },
  textBlackNormal: {
    ...commonFontStyle(500, 16, colors.black_23),
    textAlign: 'center',
    fontWeight: '500',
  },
  textBlack1: {
    ...commonFontStyle(500, 16, colors.black_23),
    textAlign: 'center',
    marginTop: 10,
  },
  textBlack2: {
    ...commonFontStyle(500, 15, colors.black_23),
    textAlign: 'center',
    marginTop: 10,
  },
  desBlack: {
    ...commonFontStyle(400, 14, colors.black_23),
    textAlign: 'center',
  },
  cell: {
    width: 42,
    height: 42,
    lineHeight: 42,
    fontSize: 22,
    borderWidth: 2,
    borderColor: '#00000030',
    textAlign: 'center',
    marginHorizontal: 3,
    borderRadius: 5,
  },
  codeFieldRoot: {
    marginVertical: hp(2),
    marginHorizontal: hp(2),
  },
  textStyle: {
    ...commonFontStyle(500, 25, colors.mainPurple),
  },
  focusCell: {
    borderColor: colors.mainPurple,
  },
});
