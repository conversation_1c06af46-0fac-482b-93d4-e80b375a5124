import * as React from "react";
import Svg, { Circle, Path } from "react-native-svg";

function DownloadSVG({ size = 36, color = "#fff", backgroundColor = "#6A4DBB" }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <Circle cx={18} cy={18} r={18} fill={backgroundColor} />
            <Path
                d="M17.486 22.878a.74.74 0 00.053.048l.025.019.032.023.032.02.03.017.032.016.032.014c.01.005.021.008.032.012.012.004.023.009.035.012.01.004.022.006.033.009l.036.009.038.005.032.005a.716.716 0 00.144 0c.01 0 .021-.003.032-.005l.038-.005.037-.01c.01-.002.021-.004.032-.008.012-.003.024-.008.035-.012.011-.004.022-.007.032-.012l.032-.014.033-.016.03-.018.03-.019c.012-.007.022-.015.032-.023.009-.006.018-.012.026-.02a.714.714 0 00.05-.045c.002 0 .003-.002.003-.002l3.394-3.394a.727.727 0 00-1.028-1.029l-2.153 2.153v-9.88a.727.727 0 10-1.454 0v9.88l-2.153-2.152a.727.727 0 10-1.028 1.028l3.394 3.394zM25.273 24.546H10.727a.727.727 0 100 1.454h14.546a.727.727 0 100-1.454z"
                fill={color}
            />
        </Svg>
    );
}

export default DownloadSVG;

