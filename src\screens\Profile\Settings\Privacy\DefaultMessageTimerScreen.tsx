import React from 'react';
import { View, SafeAreaView, StyleSheet, Text } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import RightSelectionRow from '../../components/RightSelectionRow';
import { useMe } from '../../../../hooks/util/useMe';
import { DefaultMessageTimerEnum } from '../../../../device-storage/realm/schemas/UserPrefSchema';

const DefaultMessageTimerScreen = () => {
  const { userPreferencesState } = useMe();
  const { userPreferences, updatePreferences } = userPreferencesState;
  const defaultMessageTimer =
    userPreferences?.privacy?.defaultMessageTimer || DefaultMessageTimerEnum.OFF;

  const options = [
    { key: DefaultMessageTimerEnum.OFF, label: 'Off' },
    { key: DefaultMessageTimerEnum.HOURS_24, label: '24 hours' },
    { key: DefaultMessageTimerEnum.DAYS_7, label: '7 days' },
    { key: DefaultMessageTimerEnum.DAYS_90, label: '90 days' },
  ];

  const handleSelect = async (key: string) => {
    const newPrivacy = {
      ...userPreferences?.privacy,
      defaultMessageTimer: key as DefaultMessageTimerEnum,
    };

    console.log('[DEBUG] Updating defaultMessageTimer:', newPrivacy);
    await updatePreferences('privacy', {
      defaultMessageTimer: key as DefaultMessageTimerEnum,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Default message timer" />
      <View style={styles.whiteContainer}>
        <View style={{ marginTop: 24 }}>
          <Text style={styles.descriptionText}>
            All chat histories will be cleared automatically after the set period
          </Text>
          {options.map((option) => (
            <RightSelectionRow
              key={option.key}
              label={option.label}
              selected={defaultMessageTimer === option.key}
              onPress={() => handleSelect(option.key)}
            />
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: colors.gray_80,
    textAlign: 'left',
    paddingHorizontal: 16,
    lineHeight: 20,
  },
});

export default DefaultMessageTimerScreen;
