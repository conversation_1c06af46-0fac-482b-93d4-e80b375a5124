import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const SoundSVG: React.FC<IconProps> = ({
  size = 16,
  color = '#232323',
  ...restProps
}) => {
  return (
    <Svg
      width={size}
      height={(size * 14) / 16}
      viewBox="0 0 16 14"
      fill="none"
      {...restProps}>
      <Path
        d="M9 0h-.184c-.31 0-.608.094-.86.271L3.341 3.5H1.5C.673 3.5 0 4.173 0 5v4c0 .827.673 1.5 1.5 1.5h1.842l4.613 3.229c.253.177.55.271.86.271H9c.827 0 1.5-.673 1.5-1.5v-11C10.5.673 9.827 0 9 0zM3 9.5H1.5A.5.5 0 011 9V5a.5.5 0 01.5-.5H3v5zm6.5 3a.5.5 0 01-.5.5h-.184a.499.499 0 01-.288-.09L4 9.74V4.26l4.529-3.17A.497.497 0 018.816 1H9a.5.5 0 01.5.5v11zm6.354-4.354a.5.5 0 11-.707.707L14 7.708l-1.146 1.147a.498.498 0 01-.707 0 .5.5 0 010-.707L13.293 7l-1.146-1.146a.5.5 0 11.707-.708L14 6.293l1.146-1.147a.5.5 0 11.707.708L14.707 7l1.146 1.146z"
        fill={color}
      />
    </Svg>
  );
};

export default SoundSVG;
