// import { StyleSheet, Text, View } from "react-native";
// import React, { useEffect } from "react";
// import ReactNativeModal from "react-native-modal";
// import ColorPicker, {
//   Panel2,
//   BrightnessSlider,
//   OpacitySlider,
//   InputWidget,
//   colorKit,
//   returnedResults,
// } from "reanimated-color-picker";
// import { colors } from "../theme/colors";
// import { commonFontStyle, hp, SCREEN_WIDTH } from "../theme/fonts";
// import { useSharedValue } from "react-native-reanimated";
// import ButtonPurple from "./ButtonPurple";
// import { useTranslation } from "react-i18next";

// type Props = {
//   visible: boolean;
//   onClose: () => void;
//   selectedColor: any;
//   setselectedColor: (text: any) => void;
// };

// const ColorPickerModal = ({
//   visible,
//   onClose,
//   selectedColor,
//   setselectedColor,
// }: Props) => {
//   const initialColor = selectedColor
//     ? selectedColor
//     : colorKit.randomRgbColor().hex();

//   const COLOR = useSharedValue(initialColor);

//   const onColorSelect = (color: returnedResults) => {
//     COLOR.value = color.hex;
//   };
//   const { t } = useTranslation();

//   return (
//     <ReactNativeModal
//       isVisible={visible}
//       backdropOpacity={0}
//       style={{
//         justifyContent: "flex-end",
//         margin: 0,
//         backgroundColor: "transparent",
//       }}
//       onBackButtonPress={() => onClose()}
//       onBackdropPress={() => onClose()}
//     >
//       <View style={styles.picker}>
//         <View style={styles.pickerContainer}>
//           <ColorPicker
//             value={COLOR.value}
//             sliderThickness={12}
//             thumbSize={14}
//             // thumbShape="rect"
//             onChange={onColorSelect}
//           >
//             <Panel2 style={styles.panelStyle} thumbShape="ring" />
//             <BrightnessSlider style={styles.sliderStyle} />
//             <OpacitySlider style={styles.sliderStyle} />
//             <View style={styles.previewTxtContainer}>
//               <InputWidget
//                 inputStyle={{
//                   paddingVertical: 2,
//                   borderColor: "#E5E8E8",
//                   marginLeft: 5,
//                   ...commonFontStyle(400, 16, colors.black_23),
//                 }}
//                 inputProps={{ editable: false }}
//                 iconColor={colors.black_23}
//                 formats={["HEX", "RGB"]}
//               />
//             </View>
//           </ColorPicker>
//           <ButtonPurple
//             extraStyle={styles.btn}
//             onPress={() => {
//               onClose();
//               setselectedColor(COLOR.value);
//             }}
//             title={t("Select Color")}
//           />
//         </View>
//       </View>
//     </ReactNativeModal>
//   );
// };

// export default ColorPickerModal;

// const styles = StyleSheet.create({
//   pickerContainer: {
//     // position: "absolute",
//     // zIndex: 1,
//     backgroundColor: colors.white,
//   },
//   panelStyle: {
//     height: 100,
//     borderRadius: 8,
//   },
//   picker: {
//     backgroundColor: colors.white,
//     width: SCREEN_WIDTH,
//     alignSelf: "center",
//     padding: hp(2),
//     borderRadius: 12,

//     shadowColor: "#000",
//     shadowOffset: {
//       width: 0,
//       height: 2,
//     },
//     shadowOpacity: 0.25,
//     shadowRadius: 3.84,

//     elevation: 10,
//   },
//   sliderStyle: {
//     marginTop: 10,
//     borderRadius: 50,
//   },
//   previewTxtContainer: {
//     marginTop: 10,
//   },
// });
