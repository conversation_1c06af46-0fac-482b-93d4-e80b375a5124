import axios from 'axios';

export const translateText = async (text: any, lan: string) => {
  const apiKey =
    '3cy6BvyDt7nyNeBGwnM4Sd9efgfMLBWPxSpl5KXLE7u132CbKM0iJQQJ99AJACGhslBXJ3w3AAAbACOGvsCw';
  const region = 'centralindia';
  const endpoint = `https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&to=${lan}`;

  try {
    const response: any = await axios.post(endpoint, [{ Text: text }], {
      headers: {
        'Ocp-Apim-Subscription-Key': apiKey,
        'Ocp-Apim-Subscription-Region': region,
        'Content-Type': 'application/json',
      },
    });

    // Extract and return translated text
    return response.data[0].translations[0].text;
  } catch (error: any) {
    // console.error(
    //   "Translation API Error:",
    //   error.response ? error.response.data : error.message
    // );
    return null;
  }
};
