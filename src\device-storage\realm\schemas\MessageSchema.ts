// realm/schemas.ts
import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';
import { translateText } from '../../../service/TranslateServices';
import { UserSchema } from './UserSchema';

// Type-safe enums
export enum MessageStatus {
  PENDING = 'pending', // pending timer
  UPLOADING = 'uploading', // pending timer
  SCHEDULED = 'scheduled',
  SENT = 'sent', // single tick
  DELIVERED = 'delivered', // double tick
  SEEN = 'seen', // double tick with color change
  FAILED = 'failed', // failed to send
}

export enum ConversationType {
  P2P = 'p2p',
  GROUP = 'group',
  CHANNEL = 'channel',
}

export enum ChannelType {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  LOCATION = 'location',
  CONTACT = 'contact',
  VOICE = 'voice',
  EVENT = 'event',
}

export enum ScheduleUpdateType {
  SEND_NOW = 'sendNow',
  DELETE = 'delete',
  RESCHEDULE = 'reschedule',
  EDIT = 'edit',
}

export enum MessageEventType {
  //related to chat spaces
  MEMBER_ADDED = 'member_added',
  MEMBER_REMOVED = 'member_removed',
  USER_LEFT = 'user_left',
  JOIN_REQUEST_APPROVED = 'join_request_approved',
  USER_JOINED = 'user_joined',
  CHAT_SPACE_CREATED = 'chat_space_created',
  CHAT_SPACE_DELETED = 'chat_space_deleted',
  CHAT_SPACE_INFO_UPDATED = 'chat_space_info_updated',
  MEMBER_PRIVILEGES_UPDATED = 'member_privileges_updated',
  ADMIN_PRIVILEGES_UPDATED = 'admin_privileges_updated',
  ROLE_UPDATED = 'role_updated',
  CHAT_REACTION_ADDED = 'chat_reaction_added',
  CHAT_REACTION_REMOVED = 'chat_reaction_removed',
  OWNERSHIP_TRANSFERRED = 'ownership_transferred',
  GROUP_ADDITION_REQUEST = 'group_addition_request',
  GROUP_ADDITION_REQUEST_ACCEPTED = 'group_addition_request_accept',
  REACTION_ADDED = 'reaction_added',
  REACTION_REMOVED = 'reaction_removed',

  //related to P2P
  BLOCKED = 'blocked',
  UNBLOCKED = 'unblocked',

  // related to calls
  GROUP_CALL = 'group_call',
}

// Embedded object: Location
export class LocationMessageSchema extends Realm.Object<LocationMessageSchema> {
  latitude!: number;
  longitude!: number;
  type!: string;
  expiresAt!: number;
  isStopped!: boolean;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.locationMessage,
    embedded: true,
    properties: {
      latitude: 'double',
      longitude: 'double',
      type: 'string?',
      expiresAt: 'int?',
      isStopped: 'bool?',
    },
  };
}

// Embedded object: Contact
export class ContactMessageSchema extends Realm.Object<ContactMessageSchema> {
  name!: string;
  phoneNumber!: string;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.contactMessage,
    embedded: true,
    properties: {
      name: 'string',
      phoneNumber: 'string',
      userId: 'string?',
    },
  };
}

export class Reaction extends Realm.Object<Reaction> {
  _id!: string;
  messageId!: string;
  userId!: string;
  emoji!: string;
  createdAt!: number;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.reaction,
    primaryKey: '_id',
    properties: {
      _id: 'string',
      messageId: { type: 'string', indexed: true },
      userId: { type: 'string', indexed: true },
      emoji: 'string',
      createdAt: 'int',
    },
  };
}

export interface IReaction {
  id: string;
  messageId: string;
  emoji: string;
  userId: string;
  createdAt: number;
}

export interface IContactMessage {
  name: string;
  phoneNumber: string;
  userId?: string;
}

// Message object
export class MessageSchema extends Realm.Object<MessageSchema> implements IMessage {
  globalId?: string;
  receiverId!: string;
  senderId!: string;
  sender!: UserSchema;
  localId!: string;
  conversationType!: ConversationType;
  conversationId!: string;
  status!: MessageStatus;
  messageType!: MessageType;
  text?: string;
  translatedText?: string | undefined;
  mediaUrl?: string;
  fileName?: string;
  fileSize?: number;
  videoThumbnail?: string;
  location?: LocationMessageSchema;
  contact?: ContactMessageSchema;
  replyToMessageId?: string;
  deliveredAt?: number;
  seenAt?: number;
  scheduledAt?: number;
  isPinned?: boolean;
  pinnedAt?: number;
  pinnedBy?: string;
  pinnedUntil?: number;
  isSeenByMe?: boolean = false;
  isSilent?: boolean;
  createdAt!: number;
  updatedAt!: number;
  unPinTime?: number;
  disappearAfter?: number;
  isTextEdited?: boolean;
  eventType?: MessageEventType;
  eventPayload?: string;
  targetUserIds?: string[];
  mentionedIds?: string[];
  isSaved?: boolean;
  isCleared?: boolean;
  localPath?: string;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.message,
    primaryKey: 'localId',
    properties: {
      receiverId: 'string',
      senderId: 'string',
      sender: `${realmSchemaNames.user}`,
      localId: 'string',
      globalId: 'string?',
      conversationType: 'string',
      conversationId: 'string',
      status: 'string',
      messageType: 'string',
      text: 'string?',
      translatedText: 'string?',
      mediaUrl: 'string?',
      fileName: 'string?',
      fileSize: 'int?',
      videoThumbnail: 'string?',
      location: `${realmSchemaNames.locationMessage}?`,
      contact: `${realmSchemaNames.contactMessage}?`,
      replyToMessageId: 'string?',
      deliveredAt: 'int?',
      seenAt: 'int?',
      scheduledAt: 'int?',
      isPinned: { type: 'bool', default: false },
      pinnedAt: 'int?',
      pinnedBy: 'string?',
      pinnedUntil: 'int?',
      isSilent: { type: 'bool', default: false },
      isSeenByMe: { type: 'bool', default: false },
      isTextEdited: 'bool?',
      createdAt: 'int',
      updatedAt: 'int',
      unPinTime: 'int?',
      disappearAfter: 'int?',
      eventType: 'string?',
      eventPayload: 'string?',
      targetUserIds: 'string[]',
      mentionedIds: 'string[]',
      isSaved: { type: 'bool', default: false },
      isCleared: { type: 'bool', default: false },
      localPath: 'string?',
    },
  };
}

// Message interface for type safety
export interface IMessage {
  globalId?: string; // Server-assigned ID for message synchronization across devices
  receiverId: string; // Identifies the user or group receiving the message
  senderId: string; // Identifies the user sending the message
  sender: UserSchema; // The user object of the sender
  localId: string; // Unique local message ID used as primary key
  conversationType: ConversationType; // Specifies the type of conversation (P2P, group, or channel)
  conversationId: string; // Links the message to its conversation
  status: MessageStatus; // Tracks message state (pending, sent, delivered, seen, scheduled)
  messageType: MessageType; // Defines message format (text, image, etc.)
  text?: string; // Contains the text content of the message, if applicable
  mediaUrl?: string; // URL for media resources, if the message includes media
  fileName?: string;
  fileSize?: number;
  videoThumbnail?: string;
  location?: ILocationMessage; // Stores location data, if the message includes a location
  contact?: IContactMessage; // Stores contact data, if the message shares a contact
  replyToMessageId?: string; // ID of the message being replied to, if applicable
  deliveredAt?: number; // Timestamp when the message was delivered
  seenAt?: number; // Timestamp when the message was seen by the receiver
  isPinned?: boolean; // Indicates if the message is pinned in the conversation
  pinnedAt?: number; // Timestamp when the message was pinned
  pinnedBy?: string; // User ID who pinned the message
  pinnedUntil?: number; // Timestamp when the message pin expires
  isSilent?: boolean; // Indicates if the message was sent without notification
  isSeenByMe?: boolean; // Tracks if the sender's message was seen by the receiver
  createdAt: number; // Timestamp when the message was created
  updatedAt: number; // Timestamp when the message was last updated
  scheduledAt?: number; // Timestamp for when the message is scheduled to send, if applicable
  unPinTime?: number; // Timestamp for when the message is scheduled to unpin, if applicable
  translatedText?: string; // Optional field for translated text, if applicable
  disappearAfter?: number;
  eventType?: MessageEventType;
  eventPayload?: string;
  isTextEdited?: boolean;
  targetUserIds?: string[];
  mentionedIds?: string[];
  isSaved?: boolean;
  isCleared?: boolean;
  localPath?: string;
}

export interface ILocationMessage {
  latitude: number;
  longitude: number;
  type?: string;
  expiresAt?: number;
  isStopped?: boolean;
}
