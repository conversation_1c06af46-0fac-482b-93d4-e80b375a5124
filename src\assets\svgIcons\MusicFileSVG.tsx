import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"
import { colors } from "../../theme/colors";

interface Props extends SvgProps {
  size?: number
  color?: string
}

const MusicFileSVG: React.FC<Props> = ({
  size = 22,
  color = colors.mainPurple,
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 26) / 22} // Maintains aspect ratio based on width
      viewBox="0 0 22 26"
      fill="none"
      {...props}
    >
      <Path
        d="M20.947 6.108h-4.904a1.027 1.027 0 01-1.033-1.033V.17c.215.099.404.233.566.395l4.984 4.967c.162.17.297.36.387.575z"
        fill={color}
      />
      <Path
        d="M16.043 7.455a2.382 2.382 0 01-2.38-2.38V0H2.75C1.678 0 .81.87.81 1.942v21.264c0 1.073.869 1.942 1.941 1.942h16.437c1.072 0 1.941-.87 1.941-1.942V7.455h-5.085zm-1.111 5.313a4.525 4.525 0 01-1.896-.724v6.59a3.48 3.48 0 01-3.476 3.475 3.48 3.48 0 01-3.476-3.476 3.48 3.48 0 013.476-3.476c.685 0 1.32.206 1.858.549V9.953a.81.81 0 011.528-.372c.176.34.588.968 1.375 1.337.262.123.543.207.837.248a.81.81 0 01.688.912.801.801 0 01-.914.69z"
        fill={color}
      />
      <Path
        d="M9.558 20.492a1.86 1.86 0 100-3.719 1.86 1.86 0 000 3.719z"
        fill={color}
      />
    </Svg>
  );
};

export default MusicFileSVG


