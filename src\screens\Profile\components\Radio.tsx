import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../../theme/colors';
import CheckSVG from '../../../assets/svgIcons/CheckSVG';

interface RadioProps {
  selected: boolean;
  size?: number;
  color?: string;
  type?: 'radio' | 'tick';
}

const Radio: React.FC<RadioProps> = ({
  selected,
  size = 20,
  color = colors._7A5DCB_purple,
  type = 'radio',
}) => {
  if (type === 'tick') {
    return (
      <View
        style={[
          styles.tickOuter,
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: selected ? color : colors.gray_80,
            backgroundColor: selected ? color : 'transparent',
          },
        ]}
      >
        {selected && <CheckSVG color={color} size={size * 1} />}
      </View>
    );
  }

  return (
    <View
      style={[
        styles.radioOuter,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderColor: color,
        },
      ]}
    >
      {selected ? (
        <View
          style={[
            styles.radioInner,
            {
              width: size * 0.5,
              height: size * 0.5,
              borderRadius: (size * 0.5) / 2,
              backgroundColor: color,
            },
          ]}
        />
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  radioOuter: {
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },
  tickOuter: {
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },
});

export default Radio;
