import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, Animated, Image, StyleSheet } from 'react-native';

import { colors } from '../theme/colors';
import CallSVG from '../assets/svgIcons/CallSVG';
import EndCallSvg from '../assets/svgIcons/EndCallSvg';
import { useCallContext } from '../Context/CallProvider';
import { zIndex } from '../utils/Filters';
import { hp } from '../theme/fonts';
import { IMAGES } from '../assets/Images';
import UserAvatar from './Common/UserAvatar';
import { useMe } from '../hooks/util/useMe';
import { CallDetails } from '../types/calls.types';

const CallOverlay = () => {
  const {
    endCall,
    callDetails,
    answerCall,
    setShowFullScreenCall,
    showFullScreenCall,
    rejectCall,
  } = useCallContext();

  const { user } = useMe();

  const slideAnim = useRef(new Animated.Value(-100)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const processText = () => {
    if (callDetails.state === 'outgoing') {
      return getOutgoingCallText(callDetails);
    } else if (callDetails.state === 'incoming') {
      return getIncomingCallText(callDetails);
    } else if (callDetails.state === 'ongoing') {
      return getOngoingCallText(callDetails, user?._id || '');
    }
  };

  const processRenderImage = () => {
    const origin = callDetails.origin;
    if (origin?.type === 'groupConversation') {
      return origin.conversation?.displayPic || '';
    }
    const isInitiator = callDetails.initiatorId === user?._id;
    return isInitiator
      ? callDetails.recipentList[0]?.image || ''
      : callDetails.initiatorDetails?.image || '';
  };

  if (callDetails.state === 'idle') {
    return;
  }

  if (showFullScreenCall) {
    return;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <View style={styles.contentWrapper}>
        <View style={styles.avatarContainer}>
          <UserAvatar url={processRenderImage()} />
        </View>
        <TouchableOpacity
          style={styles.callInfoWrapper}
          onPress={() => setShowFullScreenCall(true)}
        >
          {callDetails.state === 'ongoing' && (
            <>
              <Text style={styles.callerName} numberOfLines={1} ellipsizeMode="tail">
                {processText()}
              </Text>
              <View style={styles.ongoingInfo}>
                <CallSVG size={10} />
                <OngoingCallTimer startedAt={callDetails.startedAt || new Date()} />
              </View>
            </>
          )}
          {callDetails.state === 'incoming' && (
            <>
              <Text style={styles.callerName} numberOfLines={1} ellipsizeMode="tail">
                {processText()}
              </Text>
              <View style={styles.ongoingInfo}>
                <CallSVG size={15} />
                <Text style={styles.incomingCallText}>{` Incoming ${callDetails.type} Call`}</Text>
              </View>
            </>
          )}
          {callDetails.state === 'outgoing' && (
            <>
              <Text style={styles.callerName} numberOfLines={1} ellipsizeMode="tail">
                {processText()}
              </Text>
              <View style={styles.ongoingInfo}>
                <CallSVG size={15} />
                <Text style={styles.incomingCallText}>{` Outgoing ${callDetails.type} Call`}</Text>
              </View>
            </>
          )}
        </TouchableOpacity>

        <View style={styles.buttonsWrapper}>
          {callDetails.state === 'incoming' && (
            <IncomingCallButtons
              rejectCall={rejectCall}
              answerCall={answerCall}
              callDetails={callDetails}
            />
          )}
          {callDetails.state === 'ongoing' && <OnGoingCallButtons endCall={endCall} />}
          {callDetails.state === 'outgoing' && <OutgoingCalButtons endCall={endCall} />}
        </View>
      </View>
    </Animated.View>
  );
};

interface OngoingCallTimerProps {
  startedAt: Date;
}

const OngoingCallTimer = ({ startedAt }: OngoingCallTimerProps) => {
  const diff = Date.now() - startedAt.getTime();
  const initMins = Math.floor(diff / 60000);
  const initSecs = Math.floor((diff % 60000) / 1000);

  const [minutes, setMinutes] = useState(initMins);
  const [seconds, setSeconds] = useState(initSecs);

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds((prev) => {
        if (prev + 1 === 60) {
          setMinutes((m) => m + 1);
          return 0;
        }
        return prev + 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatted = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

  return <Text style={styles.timerText}>{formatted}</Text>;
};

interface IncomingCallButtonsProps {
  rejectCall: () => void;
  answerCall: (params: { roomId: string }) => void;
  callDetails: {
    roomId: string;
    [key: string]: any; // For any additional properties that might be present
  };
}

const IncomingCallButtons = ({ rejectCall, answerCall, callDetails }: IncomingCallButtonsProps) => (
  <View style={styles.incomingButtonWrapper}>
    <TouchableOpacity style={[styles.buttonCircle, styles.endButton]} onPress={rejectCall}>
      <EndCallSvg size={25} />
    </TouchableOpacity>
    <TouchableOpacity
      style={[styles.buttonCircle, styles.answerButton]}
      onPress={() => answerCall({ roomId: callDetails.roomId })}
    >
      <Image source={IMAGES.addCall} style={styles.answerCallImage} />
    </TouchableOpacity>
  </View>
);

interface OnGoingCallButtonsProps {
  endCall: () => void;
}

const OnGoingCallButtons = ({ endCall }: OnGoingCallButtonsProps) => (
  <TouchableOpacity style={[styles.buttonCircle, styles.endButton]} onPress={endCall}>
    <EndCallSvg size={20} />
  </TouchableOpacity>
);

interface OutgoingCallButtonsProps {
  endCall: () => void;
}

const OutgoingCalButtons = ({ endCall }: OutgoingCallButtonsProps) => (
  <TouchableOpacity style={[styles.buttonCircle, styles.endButton]} onPress={endCall}>
    <EndCallSvg size={20} />
  </TouchableOpacity>
);

export default CallOverlay;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: hp(8),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.ligthPurple,
    padding: 20,
    paddingVertical: 20,
    zIndex: zIndex.level_10,
  },
  contentWrapper: {
    gap: 5,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  callInfoWrapper: {
    flex: 1,
    paddingHorizontal: 5,
    gap: 5,
  },
  callerName: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  ongoingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  buttonsWrapper: {
    height: 'auto',
    flexDirection: 'row',
    gap: 10,
    paddingHorizontal: 10,
  },
  buttonCircle: {
    padding: 10,
    borderRadius: 50,
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  endButton: {
    backgroundColor: colors.red_ff4444,
  },
  answerButton: {
    backgroundColor: 'green',
  },
  incomingButtonWrapper: {
    flexDirection: 'row',
    gap: 10,
  },
  avatarContainer: {
    height: 50,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  incomingCallText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  timerText: {
    color: 'white',
    fontWeight: '400',
  },
  answerCallImage: {
    width: 50,
    height: 50,
  },
});

function getOutgoingCallText(callDetails: CallDetails) {
  if (callDetails.origin && callDetails.origin.type === 'groupConversation') {
    return callDetails.origin.conversation.displayName;
  } else if (
    !callDetails.isGroupCall &&
    callDetails.recipentList &&
    callDetails.recipentList.length > 0
  ) {
    const recipent = callDetails.recipentList[0] || callDetails.recipentList[0];
    return recipent?.username || recipent?.name;
  }
  return `Group Call`;
}

function getIncomingCallText(callDetails: CallDetails) {
  let text = '';
  if (callDetails.origin && callDetails.origin.type === 'groupConversation') {
    text = callDetails.origin.conversation.displayName;
  } else if (
    !callDetails.isGroupCall &&
    callDetails.recipentList &&
    callDetails.recipentList.length > 0
  ) {
    text =
      callDetails.initiatorDetails?.name || callDetails.initiatorDetails?.username || 'Unknown';
  }
  if (text === '') {
    text = 'Group Call';
  }
  return text;
}
function getOngoingCallText(callDetails: CallDetails, userId: string) {
  // if it is a group call. render group text
  // if it is a ad_hoc call./
  // if initiator === curr user. construct text from recipentList
  // if initiator !== curr user. construct text from initiatorDetails and recipentList skipping curr user, just show 1 user. show max 2 users add +users at the end. get the users from the participants.

  let text = '';
  if (callDetails.origin && callDetails.origin.type === 'groupConversation') {
    text = callDetails.origin.conversation.displayName;
  } else {
    const allUserTextInCallIdMap = callDetails.participants?.map((participant) => {
      return {
        userId: participant.userId,
        name: participant.client.name ?? participant.client.username,
      };
    });

    const filteredUserTextInCallIdMap = allUserTextInCallIdMap.filter(
      (participant) =>
        participant.userId !== userId || participant.userId !== callDetails.initiatorId,
    );

    // include atmost 2 names.after that if we have >= 3 add + n-2
    if (filteredUserTextInCallIdMap.length > 2) {
      text =
        filteredUserTextInCallIdMap.map((participant) => participant.name).join(', ') +
        ` + ${filteredUserTextInCallIdMap.length - 2}`;
    } else {
      text = filteredUserTextInCallIdMap.map((participant) => participant.name).join(', ');
    }
  }
  return text;
}
