import { Platform, Dimensions } from 'react-native';

const { winW, winH } = Dimensions.get('window');

const AndroidStyles = {
  blue: { backgroundColor: '#010066' },
  skyblue: { backgroundColor: '#004ADD' },
  orange: { backgroundColor: '#FF6600' },
  cblue: { color: '#010066' },
  // Common usage classes
  jStart: { justifyContent: 'flex-start' },
  jCenter: { justifyContent: 'center' },
  jEnd: { justifyContent: 'flex-end' },
  aitStart: { alignItems: 'flex-start' },
  aitCenter: { alignItems: 'center' },
  aitEnd: { alignItems: 'flex-end' },
  aslStretch: { alignSelf: 'stretch' },
  aslCenter: { alignSelf: 'center' },
  aslStart: { alignSelf: 'flex-start' },
  aslEnd: { alignSelf: 'flex-end' },
  row: { flexDirection: 'row' },
  jSpaceBet: { justifyContent: 'space-between' },
  jSpaceArd: { justifyContent: 'space-around' },
  txtAlignCen: { textAlign: 'center' },
  txtAlignLt: { textAlign: 'left' },
  txtAlignRt: { textAlign: 'right' },
  alignCenter: { alignItems: 'center', justifyContent: 'center' },
  negZIndex: { position: 'relative', zIndex: -1 },
  hitSlop: { top: 10, bottom: 10, left: 10, right: 10 },

  // Margins
  m3: { margin: 3 },
  m5: { margin: 5 },
  m8: { margin: 8 },
  m10: { margin: 10 },
  m15: { margin: 15 },
  m20: { margin: 20 },
  mLt5: { marginLeft: 5 },
  mLt10: { marginLeft: 10 },
  mLt15: { marginLeft: 15 },
  mLt20: { marginLeft: 20 },
  mLt30: { marginLeft: 30 },
  mLt70minus: { marginLeft: -70 },
  mTop10minus: { marginTop: -10 },
  mTop30minus: { marginTop: -30 },
  mRt5: { marginRight: 5 },
  mRt10: { marginRight: 10 },
  mRt15: { marginRight: 15 },
  mRt20: { marginRight: 20 },
  mRt30: { marginRight: 30 },
  mRt40: { marginRight: 40 },
  mRt50: { marginRight: 50 },
  mRt60: { marginRight: 60 },
  mTop3: { marginTop: 3 },
  mTop5: { marginTop: 5 },
  mTop10: { marginTop: 10 },
  mTop15: { marginTop: 15 },
  mTop20: { marginTop: 20 },
  mTop30: { marginTop: 30 },
  mTop40: { marginTop: 40 },
  mTop70: { marginTop: 70 },
  mTop110: { marginTop: 110 },
  mBtm5: { marginBottom: 5 },
  mBtm10: { marginBottom: 10 },
  mBtm15: { marginBottom: 15 },
  mBtm20: { marginBottom: 20 },
  mBtm30: { marginBottom: 30 },
  mBtm50: { marginBottom: 50 },
  mBtm70: { marginBottom: 70 },
  mBtm90: { marginBottom: 90 },
  marH5: { marginHorizontal: 5 },
  marH10: { marginHorizontal: 10 },
  marH15: { marginHorizontal: 15 },
  marH20: { marginHorizontal: 20 },
  marH30: { marginHorizontal: 30 },
  marH40: { marginHorizontal: 40 },
  marH75: { marginHorizontal: 75 },
  marH67: { marginHorizontal: 67 },
  marV5: { marginVertical: 5 },
  marV10: { marginVertical: 10 },
  marV15: { marginVertical: 15 },
  marV20: { marginVertical: 20 },
  marV30: { marginVertical: 30 },
  marV40: { marginVertical: 40 },
  marV50: { marginVertical: 50 },

  //paddings
  p3: { padding: 3 },
  p5: { padding: 5 },
  p8: { padding: 8 },
  p10: { padding: 10 },
  p15: { padding: 15 },
  p20: { padding: 20 },
  pLt5: { paddingLeft: 5 },
  pLt10: { paddingLeft: 10 },
  pLt15: { paddingLeft: 15 },
  pLt20: { paddingLeft: 20 },
  pRt5: { paddingRight: 5 },
  pRt10: { paddingRight: 10 },
  pRt15: { paddingRight: 15 },
  pRt20: { paddingRight: 20 },
  pTop3: { paddingTop: 3 },
  pTop5: { paddingTop: 5 },
  pTop10: { paddingTop: 10 },
  pTop15: { paddingTop: 15 },
  pTop20: { paddingTop: 20 },
  pTop30: { paddingTop: 30 },
  pTop40: { paddingTop: 40 },
  pBtm5: { paddingBottom: 5 },
  pBtm10: { paddingBottom: 10 },
  pBtm15: { paddingBottom: 15 },
  pBtm20: { paddingBottom: 20 },
  pBtm30: { paddingBottom: 30 },
  pBtm50: { paddingBottom: 50 },
  pBtm70: { paddingBottom: 70 },
  padH5: { paddingHorizontal: 5 },
  padH10: { paddingHorizontal: 10 },
  padH15: { paddingHorizontal: 15 },
  padH20: { paddingHorizontal: 20 },
  padH30: { paddingHorizontal: 30 },
  padH40: { paddingHorizontal: 40 },
  padH50: { paddingHorizontal: 50 },
  padH75: { paddingHorizontal: 75 },
  padH67: { paddingHorizontal: 67 },
  padV5: { paddingVertical: 5 },
  padV10: { paddingVertical: 10 },
  padV15: { paddingVertical: 15 },
  padV20: { paddingVertical: 20 },
  padV30: { paddingVertical: 30 },
  padV40: { paddingVertical: 40 },
  padV50: { paddingVertical: 50 },
  // Text colors used
  cFFF: { color: '#FFF' },
  cBlk: { color: '#000' },
  cAsh: { color: '#A8A8A8' },
  cPink: { color: '#D60062' },
  cRed: { color: '#F01861' },
  cPurple: { color: '#A13293' },
  cYellow: { color: '#FAA330' },
  cBlue: { color: '#1E32FA' },
  //background colors
  bgBlk: { backgroundColor: '#000' },
  bgEEE: { backgroundColor: '#EEE' },
  bgFFF: { backgroundColor: '#FFFFFF' },
  bgAsh: { backgroundColor: '#A8A8A8' },
  bgPink: { backgroundColor: '#A13293' },
  bgRed: { backgroundColor: '#F01861' },
  bgPurple: { backgroundColor: '#801C3E' },
  bgYellow: { backgroundColor: '#FAA330' },
  bgBlue: { backgroundColor: '#1E32FA' },
  // Flex
  flex1: { flex: 1 },
  flexWrap: { flexWrap: 'wrap' },

  //fontSizes
  f10: { fontSize: 10 },
  f11: { fontSize: 11 },
  f12: { fontSize: 12 },
  f13: { fontSize: 13 },
  f15: { fontSize: 15 },
  f16: { fontSize: 16 },
  f17: { fontSize: 17 },
  f18: { fontSize: 18 },
  f20: { fontSize: 20 },
  f30: { fontSize: 30 },
  f40: { fontSize: 40 },
  f25: { fontSize: 25 },
  f50: { fontSize: 50 },

  //fontWeight
  fWbold: { fontWeight: 'bold' },
  fItalic: { fontStyle: 'italic' },

  backgroundImg: { flex: 1, width: null, height: null },

  //border radius
  brdRad3: { borderRadius: 3 },
  brdRad5: { borderRadius: 5 },
  brdRad8: { borderRadius: 8 },
  brdRad10: { borderRadius: 10 },
  brdRad15: { borderRadius: 15 },
  brdRad20: { borderRadius: 20 },
  brdRad30: { borderRadius: 30 },
  brdRad40: { borderRadius: 40 },

  //Images
  img10: { width: 10, height: 10, resizeMode: 'contain' },
  img15: { width: 15, height: 15, resizeMode: 'contain' },
  img20: { width: 20, height: 20, resizeMode: 'contain' },
  img25: { width: 25, height: 25, resizeMode: 'contain' },
  img30: { width: 30, height: 30, resizeMode: 'contain' },
  img40: { width: 40, height: 40, resizeMode: 'contain' },
  img80: { width: 80, height: 80, resizeMode: 'contain' },
  img140: { width: 140, height: 140, resizeMode: 'contain' },

  shadow: { elevation: 8 },

  // Default common elements Styles

  cButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 5,
  },

  radioStyle: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 20,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioActiveStyle: { backgroundColor: '#8B3363', width: 12, height: 12, borderRadius: 12 },
  checkboxStyle: {
    borderWidth: 1,
    borderColor: '#999',
    borderRadius: 4,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActiveStyle: { backgroundColor: '#8B3363', width: 12, height: 12, borderRadius: 12 },
  cText: { backgroundColor: 'transparent', fontSize: 14, color: '#666' },
  inputStyle: { height: 50, marginVertical: 5 },
  // cText: { backgroundColor: 'transparent', fontSize: 14, color: '#666',fontFamily:"Lato-Regular" },
  // inputStyle: { height: 50, marginVertical: 5,fontFamily:"Lato-Regular" },

  // Common Styles

  mDTop: { marginTop: 10 },
};

// IOS STYLES
const IosStyles = {
  // Common usage classes
  jStart: { justifyContent: 'flex-start' },
  jCenter: { justifyContent: 'center' },
  jEnd: { justifyContent: 'flex-end' },
  aitStart: { alignItems: 'flex-start' },
  aitCenter: { alignItems: 'center' },
  aitEnd: { alignItems: 'flex-end' },
  aslStretch: { alignSelf: 'stretch' },
  aslCenter: { alignSelf: 'center' },
  aslStart: { alignSelf: 'flex-start' },
  aslEnd: { alignSelf: 'flex-end' },
  row: { flexDirection: 'row' },
  jSpaceBet: { justifyContent: 'space-between' },
  jSpaceArd: { justifyContent: 'space-around' },
  txtAlignCen: { textAlign: 'center' },
  txtAlignLt: { textAlign: 'left' },
  txtAlignRt: { textAlign: 'right' },
  alignCenter: { alignItems: 'center', justifyContent: 'center' },
  negZIndex: { position: 'relative', zIndex: -1 },
  hitSlop: { top: 10, bottom: 10, left: 10, right: 10 },

  // Margins
  m3: { margin: 3 },
  m5: { margin: 5 },
  m8: { margin: 8 },
  m10: { margin: 10 },
  m15: { margin: 15 },
  m20: { margin: 20 },
  mLt5: { marginLeft: 5 },
  mLt10: { marginLeft: 10 },
  mLt15: { marginLeft: 15 },
  mLt20: { marginLeft: 20 },
  mLt30: { marginLeft: 30 },
  mLt70minus: { marginLeft: -70 },
  mTop10minus: { marginTop: -10 },
  mTop30minus: { marginTop: -30 },
  mRt5: { marginRight: 5 },
  mRt10: { marginRight: 10 },
  mRt15: { marginRight: 15 },
  mRt20: { marginRight: 20 },
  mRt30: { marginRight: 30 },
  mRt40: { marginRight: 40 },
  mRt50: { marginRight: 50 },
  mRt60: { marginRight: 60 },
  mTop3: { marginTop: 3 },
  mTop5: { marginTop: 5 },
  mTop10: { marginTop: 10 },
  mTop15: { marginTop: 15 },
  mTop20: { marginTop: 20 },
  mTop30: { marginTop: 30 },
  mTop40: { marginTop: 40 },
  mTop70: { marginTop: 70 },
  mTop110: { marginTop: 110 },
  mBtm5: { marginBottom: 5 },
  mBtm10: { marginBottom: 10 },
  mBtm15: { marginBottom: 15 },
  mBtm20: { marginBottom: 20 },
  mBtm30: { marginBottom: 30 },
  mBtm50: { marginBottom: 50 },
  mBtm70: { marginBottom: 70 },
  mBtm90: { marginBottom: 90 },
  marH5: { marginHorizontal: 5 },
  marH10: { marginHorizontal: 10 },
  marH15: { marginHorizontal: 15 },
  marH20: { marginHorizontal: 20 },
  marH30: { marginHorizontal: 30 },
  marH40: { marginHorizontal: 40 },
  marH75: { marginHorizontal: 75 },
  marH67: { marginHorizontal: 67 },
  marV5: { marginVertical: 5 },
  marV10: { marginVertical: 10 },
  marV15: { marginVertical: 15 },
  marV20: { marginVertical: 20 },
  marV30: { marginVertical: 30 },
  marV40: { marginVertical: 40 },
  marV50: { marginVertical: 50 },

  //paddings
  p3: { padding: 3 },
  p5: { padding: 5 },
  p8: { padding: 8 },
  p10: { padding: 10 },
  p15: { padding: 15 },
  p20: { padding: 20 },
  pLt5: { paddingLeft: 5 },
  pLt10: { paddingLeft: 10 },
  pLt15: { paddingLeft: 15 },
  pLt20: { paddingLeft: 20 },
  pRt5: { paddingRight: 5 },
  pRt10: { paddingRight: 10 },
  pRt15: { paddingRight: 15 },
  pRt20: { paddingRight: 20 },
  pTop3: { paddingTop: 3 },
  pTop5: { paddingTop: 5 },
  pTop10: { paddingTop: 10 },
  pTop15: { paddingTop: 15 },
  pTop20: { paddingTop: 20 },
  pTop30: { paddingTop: 30 },
  pTop40: { paddingTop: 40 },
  pBtm5: { paddingBottom: 5 },
  pBtm10: { paddingBottom: 10 },
  pBtm15: { paddingBottom: 15 },
  pBtm20: { paddingBottom: 20 },
  pBtm30: { paddingBottom: 30 },
  pBtm50: { paddingBottom: 50 },
  pBtm70: { paddingBottom: 70 },
  padH5: { paddingHorizontal: 5 },
  padH10: { paddingHorizontal: 10 },
  padH15: { paddingHorizontal: 15 },
  padH20: { paddingHorizontal: 20 },
  padH30: { paddingHorizontal: 30 },
  padH40: { paddingHorizontal: 40 },
  padH50: { paddingHorizontal: 50 },
  padH75: { paddingHorizontal: 75 },
  padH67: { paddingHorizontal: 67 },
  padV5: { paddingVertical: 5 },
  padV10: { paddingVertical: 10 },
  padV15: { paddingVertical: 15 },
  padV20: { paddingVertical: 20 },
  padV30: { paddingVertical: 30 },
  padV40: { paddingVertical: 40 },
  padV50: { paddingVertical: 50 },
  // Text colors used
  cFFF: { color: '#FFF' },
  cBlk: { color: '#000' },
  cAsh: { color: '#A8A8A8' },
  cPink: { color: '#D60062' },
  cYellow: { color: '#FAA330' },
  cRed: { color: '#F01861' },
  cPurple: { color: '#A13293' },
  cBlue: { color: '#1E32FA' },

  //background colors
  bgBlk: { backgroundColor: '#000' },
  bgEEE: { backgroundColor: '#EEE' },
  bgFFF: { backgroundColor: '#FFFFFF' },
  bgAsh: { backgroundColor: '#A8A8A8' },
  bgPink: { backgroundColor: '#A13293' },
  bgRed: { backgroundColor: '#F01861' },
  bgPurple: { backgroundColor: '#801C3E' },
  bgYellow: { backgroundColor: '#FAA330' },
  bgBlue: { backgroundColor: '#1E32FA' },

  // Flex
  flex1: { flex: 1 },
  flexWrap: { flexWrap: 'wrap' },

  //fontSizes
  f10: { fontSize: 10 },
  f11: { fontSize: 11 },
  f12: { fontSize: 12 },
  f13: { fontSize: 13 },
  f15: { fontSize: 15 },
  f16: { fontSize: 16 },
  f17: { fontSize: 17 },
  f18: { fontSize: 18 },
  f20: { fontSize: 20 },
  f30: { fontSize: 30 },
  f40: { fontSize: 40 },
  f25: { fontSize: 25 },
  f50: { fontSize: 50 },

  //fontWeight
  fWbold: { fontWeight: 'bold' },
  fItalic: { fontStyle: 'italic' },

  backgroundImg: { flex: 1, width: null, height: null },

  //border radius
  brdRad3: { borderRadius: 3 },
  brdRad5: { borderRadius: 5 },
  brdRad8: { borderRadius: 8 },
  brdRad10: { borderRadius: 10 },
  brdRad15: { borderRadius: 15 },
  brdRad20: { borderRadius: 20 },
  brdRad30: { borderRadius: 30 },
  brdRad40: { borderRadius: 40 },

  //Images
  img10: { width: 10, height: 10, resizeMode: 'contain' },
  img15: { width: 15, height: 15, resizeMode: 'contain' },
  img20: { width: 20, height: 20, resizeMode: 'contain' },
  img25: { width: 25, height: 25, resizeMode: 'contain' },
  img30: { width: 30, height: 30, resizeMode: 'contain' },
  img40: { width: 40, height: 40, resizeMode: 'contain' },
  img80: { width: 80, height: 80, resizeMode: 'contain' },
  img140: { width: 140, height: 140, resizeMode: 'contain' },

  shadow: {
    shadowOpacity: 0.3,
    shadowRadius: 3,
    shadowOffset: {
      height: 0,
      width: 0,
    },
  },

  // Default common elements Styles

  cButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  radioStyle: {
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 20,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioActiveStyle: { backgroundColor: '#8B3363', width: 12, height: 12, borderRadius: 12 },
  checkboxStyle: {
    borderWidth: 1,
    borderColor: '#999',
    borderRadius: 4,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActiveStyle: { backgroundColor: '#8B3363', width: 12, height: 12, borderRadius: 12 },
  cText: { backgroundColor: 'transparent', fontSize: 14, color: '#666' },
  inputStyle: { height: 50, marginVertical: 5 },
  // cText: { backgroundColor: 'transparent', fontSize: 14, color: '#666',fontFamily:"Lato-Regular" },
  // inputStyle: { height: 50, marginVertical: 5,fontFamily:"Lato-Regular" },

  // Common Styles
  mDTop: { marginTop: 30 },
};
const Styles = Platform.OS === 'ios' ? IosStyles : AndroidStyles;

export { Styles };
