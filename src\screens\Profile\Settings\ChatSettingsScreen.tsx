import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { colors } from '../../../theme/colors';
import { hp } from '../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { SCREENS } from '../../../navigation/screenNames';
import RightNavigationRow from '../components/RightNavigationRow';
import StreamToggleOption from '../../Home/Channels/components/StreamToggle';
import GallerySVG from '../../../assets/svgIcons/GallerySVG';
import ArchivedSVG from '../../../assets/svgIcons/ArchivedSVG';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import TranslateSVG from '../../../assets/svgIcons/TranslateSVG';
// import RedoSVG from '../../../assets/svgIcons/RedoSVG';
// import BackupSVG from '../../../assets/svgIcons/BackupSVG';
import ClearChatsSVG from '../../../assets/svgIcons/ClearChatsSVG';
import DownloadSVG from '../../../assets/svgIcons/DownloadSVG';
import { ChatService } from '../../../service/ChatService';
import { useMe } from '../../../hooks/util/useMe';
import CustomAlert from '../../../component/Common/CustomAlert';
import { successToast, errorToast } from '../../../utils/commonFunction';

const ChatSettingsScreen = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();
  const saveToGallery = userPreferencesState.userPreferences?.chats?.saveToGallery ?? false;
  const [showClearAlert, setShowClearAlert] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [showArchiveAlert, setShowArchiveAlert] = useState(false);

  const handleArchiveExistingChats = () => {
    setShowArchiveAlert(true);
  };

  const handleConfirmArchiveExistingChats = () => {
    ChatService.toggleArchiveAllChats(true);
    setShowArchiveAlert(false);
    successToast('All existing chats have been archived successfully');
  };

  const handleSaveToGalleryToggle = () => {
    const newValue = !saveToGallery;
    userPreferencesState.updatePreferences('chats', { saveToGallery: newValue });
  };

  const handleClearAllChats = () => {
    setShowClearAlert(true);
  };

  const handleConfirmClearAllChats = () => {
    try {
      ChatService.clearAllChats();
      setShowClearAlert(false);
      successToast('All chats have been cleared successfully');
    } catch (error) {
      errorToast('Failed to clear all chats. Please try again.');
    }
  };

  const handleDeleteChats = () => {
    setShowDeleteAlert(true);
  };

  const handleConfirmDeleteChats = () => {
    try {
      ChatService.deleteAllConversations();
      setShowDeleteAlert(false);
      successToast('All chats have been deleted successfully');
    } catch (error) {
      errorToast('Failed to delete all chats. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Chat Settings" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.sectionContainer}>
          <RightNavigationRow
            text="Chat wallpaper"
            screenName={SCREENS.ChatWallpaperScreen}
            icon={<GallerySVG size={20} color={colors.black_23} />}
          />

          <RightNavigationRow
            text="Translate language"
            screenName={SCREENS.TranslateLanguageScreen}
            icon={<TranslateSVG size={20} color={colors.black_23} />}
          />

          <StreamToggleOption
            label="Save to gallery"
            value={saveToGallery}
            onToggle={handleSaveToGalleryToggle}
            icon={<DownloadSVG size={35} backgroundColor={colors.white} color={colors.black_23} />}
            switchStyle={styles.switchStyle}
          />

          <RightNavigationRow
            text="Archive existing chats"
            onPress={handleArchiveExistingChats}
            icon={<ArchivedSVG size={20} color={colors.black_23} />}
            showRightArrow={false}
          />

          {/* <RightNavigationRow
            text="Chat backup"
            screenName={SCREENS.ChatBackupScreen}
            icon={<RedoSVG size={20} color={colors.black_23} />}
          /> */}

          {/* <RightNavigationRow
            text="Storage and data"
            screenName={SCREENS.StorageAndDataScreen}
            icon={<BackupSVG size={20} color={colors.black_23} />}
          /> */}

          <RightNavigationRow
            text="Delete chats"
            onPress={handleDeleteChats}
            icon={<DeleteSVG size={20} color={colors.black_23} />}
            showRightArrow={false}
          />

          <RightNavigationRow
            text="Clear all chats"
            onPress={handleClearAllChats}
            icon={<ClearChatsSVG size={20} color={colors.black_23} />}
            showRightArrow={false}
          />
        </View>
      </View>

      <CustomAlert
        visible={showClearAlert}
        onCancel={() => setShowClearAlert(false)}
        onConfirm={handleConfirmClearAllChats}
        title="Clear All Chats"
        message="This will delete all messages from all conversations. The conversations will remain but all messages will be permanently deleted. This action cannot be undone."
        confirmText="Clear All"
        cancelText="Cancel"
      />

      <CustomAlert
        visible={showDeleteAlert}
        onCancel={() => setShowDeleteAlert(false)}
        onConfirm={handleConfirmDeleteChats}
        title="Delete All Chats"
        message="This will permanently delete all conversations and their messages. This action cannot be undone and all chat history will be lost."
        confirmText="Delete All"
        cancelText="Cancel"
      />

      <CustomAlert
        visible={showArchiveAlert}
        onCancel={() => setShowArchiveAlert(false)}
        onConfirm={handleConfirmArchiveExistingChats}
        title="Archive Existing Chats"
        message="This will move all your current chats to the archived section. You can unarchive them anytime from the archived section."
        confirmText="Archive All"
        cancelText="Cancel"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  sectionContainer: {
    paddingHorizontal: hp(2),
    paddingVertical: hp(1),
  },
  switchStyle: {
    marginRight: -6,
  },
});

export default ChatSettingsScreen;
