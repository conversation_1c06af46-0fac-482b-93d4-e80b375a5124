import * as yup from 'yup';
import { ICreateGroupForm } from '../../screens/Home/Groups/CreateGroupScreen';
import { ChatConstants } from '../../constants/appConstants';

export const CreateGroupSchema: yup.ObjectSchema<ICreateGroupForm> = yup.object({
  groupName: yup
    .string()
    .trim()
    .required('Group name is required')
    .max(
      ChatConstants.inputLength.groupName,
      `Group name must be less than ${ChatConstants.inputLength.groupName} characters`,
    ),
  groupDescription: yup
    .string()
    .trim()
    .required('Group description is required')
    .max(
      ChatConstants.inputLength.groupDescription,
      `Group description length should be less than ${ChatConstants.inputLength.groupDescription} characters`,
    ),
  isPrivate: yup.boolean(),
  imageUrl: yup
    .object({
      name: yup.string().required(),
      type: yup.string().required(),
      uri: yup.string().required(),
    })
    .nullable()
    .optional(),
  selectedContacts: yup
    .array()
    .of(yup.string().required())
    .when('$isCreate', {
      is: true,
      then: (schema) => schema.min(1, 'Please select at least one contact.').required(),
      otherwise: (schema) => schema.optional(),
    }),
});
