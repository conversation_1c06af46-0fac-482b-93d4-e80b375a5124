import {
  Image,
  InteractionManager,
  Keyboard,
  KeyboardAvoidingView,
  Pressable,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  ToastAndroid,
  TouchableWithoutFeedback,
  View,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { colors } from '../../../theme/colors';
import { RouteProp, useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import { NavigationRouteParams } from '../../../navigation/navigationParams';
import Video from 'react-native-video';
import { commonFontStyle, hexToRgba, SCREEN_WIDTH } from '../../../theme/fonts';
import BackArrowSVG from '../../../assets/svgIcons/BackArrowSVG';
import { TouchableOpacity } from 'react-native';
import ForwardArrow from '../../../assets/svgIcons/ForwardArrow';
import ThreeDotsSVG from '../../../assets/svgIcons/ThreeDotsSVG';
import PinSVG from '../../../assets/svgIcons/PinSVG';
import ReplySVG from '../../../assets/svgIcons/ReplySVG';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { ConversationType, MessageType } from '../../../device-storage/realm/schemas/MessageSchema';
import { Menu } from 'react-native-material-menu';
import StarSVG from '../../../assets/svgIcons/StarSVG';
import MuteSVG from '../../../assets/svgIcons/MuteSVG';
import ShareIconSVG from '../../../assets/svgIcons/ShareIconSVG';
import EditSVG from '../../../assets/svgIcons/EditSVG';
import RefreshSVG from '../../../assets/svgIcons/RefreshSVG';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import { t } from 'i18next';
import ShareSVG from '../../../assets/svgIcons/ShareSVG';
import SendSVG from '../../../assets/svgIcons/SendSVG';
import { useKeyboard } from '../../../utils/useKeyboard';
import BookmarkSVG from '../../../assets/svgIcons/BookmarkSVG';
import SettingsSVG from '../../../assets/svgIcons/SettingsSVG';
import AllMediaSVG from '../../../assets/svgIcons/AllMediaSVG';
import ElementRenderer from '../../../component/media-editor/Components/CombineElement';
import PaintBrush from '../../../component/media-editor/Components/PaintBrush';
import {
  navigateTo,
  successToast,
  errorToast,
  handleMediaFileSharing,
} from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import PinModal from '../../../component/PersonalChat/PinModal';
import { ChatService } from '../../../service/ChatService';
import UnpinSVG from '../../../assets/svgIcons/UnpinSVG';
import UnpinSVG2 from '../../../assets/svgIcons/UnpinSVG2';
import { ComposedMessage } from '../../../types/index.types';
import { useMe } from '../../../hooks/util/useMe';
import { saveToGallery } from '../../../utils/saveToGallery';
import { StackNavigationProp } from '@react-navigation/stack';
import { generateLocalMessageId } from '../../../device-storage/realm/lib';
import RNFS from 'react-native-fs';
import { createThumbnail } from 'react-native-create-thumbnail';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';

interface IProps {}

type NavigationProps = StackNavigationProp<NavigationRouteParams, 'MediaPreviewScreen'>;

const itemArray = [
  // { id: 1, title: 'Share', image: <ShareSVG size={17} color={colors.black_23} />, type: 'SHARE' },
  {
    id: 2,
    title: 'Save to gallery',
    image: <BookmarkSVG size={15} color={colors.black_23} />,
    type: 'GALLERY',
  },
  // {
  //   id: 3,
  //   title: 'Edit',
  //   image: <EditSVG size={15} color={colors.black_23} />,
  //   type: 'EDIT',
  // },
  // {
  //   id: 4,
  //   title: 'All media',
  //   image: <AllMediaSVG size={16} color={colors.black_23} />,
  //   type: 'ALLMEDIA',
  // },
  // {
  //   id: 5,
  //   title: 'Set as',
  //   image: <SettingsSVG size={15} color={colors.black_23} />,
  //   type: 'SETAS',
  // },
  // {
  //   id: 6,
  //   title: 'Rotate',
  //   image: <RefreshSVG size={14} color={colors.black_23} />,
  //   type: 'ROTATE',
  // },
  // {
  //   id: 7,
  //   title: 'Delete',
  //   image: <DeleteSVG size={15} color={colors.black_23} />,
  //   type: 'DELETE',
  // },
];

const MediaPreviewScreen = ({}: IProps) => {
  const route = useRoute<RouteProp<NavigationRouteParams, 'MediaPreviewScreen'>>();
  const { msgData, otherUserData, isFromCameraScreen, handleSendMessage } = route.params;
  const [currentMsgData, setCurrentMsgData] = useState(msgData);

  const { keyboardHeight } = useKeyboard();

  // In your component:
  const navigation = useNavigation<NavigationProps>();
  useFocusEffect(
    useCallback(() => {
      // Always log the data when screen is focused
      console.log('MediaPreviewScreen - FOCUS - Current data:', {
        msgData: route.params.msgData,
        additionalImages: route.params.msgData?.additionalImages,
        texts: route.params.msgData?.texts,
        stickers: route.params.msgData?.stickers,
        matrix: route.params.msgData?.matrix,
        path: route.params.msgData?.path,
        drawingPaths: route.params.msgData?.drawingPaths,
        isFromCameraScreen: route.params.isFromCameraScreen,
      });

      // Update msgData if it was changed by the editor
      if (route.params.msgData !== currentMsgData) {
        setCurrentMsgData(route.params.msgData);
      }
    }, [route.params.msgData]),
  );
  // const navigation = useNavigation();
  const { user: me, userPreferencesState } = useMe();

  const [play, setPlay] = useState<boolean>(true);
  const [fullScreen, setFullScreen] = useState<boolean>(false);
  const [replyMsg, setReplyMsg] = useState<string>('');
  const [isPinMessage, setIsPinMessage] = useState<boolean>(msgData?.isPinned as boolean);
  const [pinModal, setPinModal] = useState<boolean>(false);
  const childRef = useRef<any>(null);

  // refs
  const videoRef = useRef<any>(null);
  const menuRef = useRef<any>(null); // Create a ref for the Menu component

  const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm'];

  const extension = msgData?.path?.split('/')?.pop()?.split('.')?.pop()?.toLowerCase();
  const isVideoMsg = videoExtensions.includes(extension);
  const safePath = `${RNFS.CachesDirectoryPath}/video_${Date.now()}.mp4`;

  const initialComposedMessage: ComposedMessage = {
    senderId: me?._id || '',
    receiverId: otherUserData?.id,
    isSilent: false,
    scheduledAt: undefined,
    conversationType:
      otherUserData.type === 'group'
        ? ConversationType.GROUP
        : otherUserData.type === 'channel'
        ? ConversationType.CHANNEL
        : ConversationType.P2P,
    messageType: MessageType.TEXT,
    text: replyMsg?.trim(),
    replyToMessageId: !isFromCameraScreen ? msgData?.globalId : '',
  };

  const insets = useSafeAreaInsets();

  const autoSaveToGallery = async () => {
    try {
      const saveToGalleryEnabled = userPreferencesState.userPreferences?.chats?.saveToGallery;
      if (saveToGalleryEnabled && msgData?.mediaUrl) {
        const fileName = msgData.fileName || `${Date.now()}_${msgData.messageType}`;
        const mediaType = msgData.messageType === 'video' ? 'video' : 'image';

        await saveToGallery(msgData.mediaUrl, fileName, mediaType, {
          showToast: false,
        });
      }
    } catch (error) {
      console.error('Auto-save to gallery failed:', error);
    }
  };

  async function hasAndroidPermission() {
    if (Number(Platform.Version) >= 33) {
      const imagesPermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
      );
      const videoPermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
      );
      return (
        imagesPermission === PermissionsAndroid.RESULTS.GRANTED &&
        videoPermission === PermissionsAndroid.RESULTS.GRANTED
      );
    } else {
      const extPermission = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      );
      return extPermission === PermissionsAndroid.RESULTS.GRANTED;
    }
  }

  // Auto-save to gallery if preference is enabled
  useEffect(() => {
    if (isFromCameraScreen) {
      hasAndroidPermission();
    }
    if (userPreferencesState.userPreferences?.chats?.saveToGallery) {
      autoSaveToGallery();
    }
  }, [msgData?.mediaUrl, userPreferencesState.userPreferences?.chats?.saveToGallery]);

  const onShow = () => {
    setTimeout(() => {
      menuRef.current?.show(); // Open the menu
    }, 300);
  };

  const handleSaveToGallery = async () => {
    if (!msgData?.mediaUrl) {
      errorToast('No media URL found');
      return;
    }

    const fileName = msgData.fileName || `${Date.now()}_${msgData.messageType}`;
    const mediaType = msgData.messageType === 'video' ? 'video' : 'image';

    await saveToGallery(msgData.mediaUrl, fileName, mediaType, {
      showToast: true,
    });
  };

  const onPress = (type: string) => {
    if (type === 'GALLERY') {
      handleSaveToGallery();
      menuRef.current?.hide();
    } else if (type == 'forward') {
      navigateTo(SCREENS.ForwardMessagesScreen, {
        messages: msgData,
      });
    }
  };

  const onPinMessage = (value?: any) => {
    setPinModal(false);

    let hours = 24;
    switch (value) {
      case '1_HOUR':
        hours = 1;
        break;
      case '24_HOURS':
        hours = 24;
        break;
      case '7_DAYS':
        hours = 168; // 7 * 24 hours
        break;
      case '30_DAYS':
        hours = 720; // 30 * 24 hours
        break;
    }
    try {
      ChatService.pinMessage(msgData?.localId as string, otherUserData.id, hours);
      ToastAndroid.show('This message is pinned', ToastAndroid.SHORT);
      setIsPinMessage(true);
    } catch (error) {
      console.log('pin message error');
      ToastAndroid.show('Not pinned', ToastAndroid.SHORT);
    }
  };

  const onSendMessage = async () => {
    try {
      Keyboard.dismiss();
      await ChatService.sendMessage(initialComposedMessage);
      // Dismiss the keyboard
      // Wait until all UI interactions (like keyboard animating out) are complete
      InteractionManager.runAfterInteractions(() => {
        navigation.goBack();
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const onEditPress = () => {
    navigateTo('MediaEditor', {
      initialMedia: msgData,
      handleSendMessage: handleSendMessage,
      otherUserData: otherUserData,
      isFromMediaPreview: true,
      originalMsgData: currentMsgData,
    });
  };

  const onImageSend = async () => {
    try {
      if (!handleSendMessage) return;
      const storagePermissions = await hasAndroidPermission();
      if (!storagePermissions) return;
      const videoPath = await CameraRoll.saveAsset(`file://${msgData?.path}`, {
        type: 'auto',
      });

      const messageToSend: ComposedMessage = {
        ...initialComposedMessage,
        messageType: isVideoMsg ? MessageType.VIDEO : MessageType.IMAGE,
        mediaUrl: isVideoMsg ? videoPath?.node?.image?.uri : `file://${msgData?.path}`,
        fileName: isVideoMsg
          ? (videoPath?.node?.image?.filename as string)
          : generateLocalMessageId(),
        fileSize: isVideoMsg ? Number(videoPath?.node?.image?.fileSize) : 0,
        text: replyMsg?.trim(),
        isMedia: true,
      };

      // const sentData2 = await ChatService.sendMessage(messageToSend, true);
      const sentData = await handleSendMessage(messageToSend);

      const formData = new FormData();
      formData.append('file', {
        uri: isVideoMsg ? videoPath?.node?.image?.uri : `file://${msgData?.path}`,
        name: `media.${extension}`,
        type: isVideoMsg ? `video/${extension}` : `image/${extension}`,
      });

      handleMediaFileSharing(formData, sentData, isVideoMsg);

      setTimeout(() => {
        navigation.goBack();
      }, 200);
    } catch (error) {
      console.error('Error sending media:', error);
      errorToast('Failed to send media');
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.black }}>
      <StatusBar barStyle={'default'} backgroundColor={colors.black} />
      <KeyboardAvoidingView style={{ flex: 1, marginBottom: keyboardHeight }}>
        <View style={{ flex: 1 }}>
          {!fullScreen && (
            <View
              style={{
                position: 'absolute',
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: hexToRgba(colors.black, 0.3),
                paddingHorizontal: 21,
                paddingVertical: 8,
                zIndex: 10,
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: 'center',
                  flexDirection: 'row',
                }}
              >
                <TouchableOpacity
                  onPress={() => {
                    navigation.goBack();
                  }}
                >
                  <BackArrowSVG color={colors.white} size={20} />
                </TouchableOpacity>

                <Image
                  source={
                    otherUserData?.displayPic
                      ? { uri: otherUserData?.displayPic }
                      : require('../../../assets/Image/beautiful.png')
                  }
                  style={styles.userImage}
                />
              </View>

              {!isFromCameraScreen && (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 30 }}>
                  <TouchableOpacity
                    style={{}}
                    onPress={() => {
                      navigateTo(SCREENS.ForwardMessagesScreen, {
                        messages: [msgData],
                      });
                    }}
                  >
                    <ForwardArrow color={colors.white} size={19} />
                  </TouchableOpacity>
                  {!(
                    otherUserData?.type === 'channel' &&
                    otherUserData?.conversation?.role === 'member'
                  ) && (
                    <TouchableOpacity
                      style={{}}
                      onPress={() => {
                        if (isPinMessage) {
                          ChatService.unpinMessage(
                            msgData?.localId as string,
                            msgData?.receiverId as string,
                          );
                          setIsPinMessage(false);
                          ToastAndroid.show('Message unpinned', ToastAndroid.SHORT);
                        } else {
                          setPinModal(true);
                        }
                      }}
                    >
                      {isPinMessage ? (
                        <UnpinSVG2 size={26} color={colors.white} />
                      ) : (
                        <PinSVG color={colors.white} size={20} />
                      )}
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity style={{}} onPress={() => {}}>
                    <StarSVG color={colors.white} size={20} />
                  </TouchableOpacity>

                  <Menu
                    ref={menuRef}
                    style={{
                      borderRadius: 8,
                    }}
                    animationDuration={100}
                    anchor={
                      <TouchableOpacity
                        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                        style={{}}
                        onPress={() => {
                          onShow();
                        }}
                      >
                        <ThreeDotsSVG color={colors.white} size={20} />
                      </TouchableOpacity>
                    }
                    onRequestClose={() => {
                      menuRef.current?.hide();
                    }}
                  >
                    <View
                      style={{
                        paddingVertical: 10,
                        paddingHorizontal: 10,
                      }}
                    >
                      {itemArray.map(({ type: itemType, id, title, image }, index) => (
                        <TouchableOpacity
                          onPress={() => onPress(itemType)}
                          key={id}
                          style={styles.menuItemStyle}
                        >
                          {image}
                          <Text style={styles.menuItemText}>{title}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </Menu>
                </View>
              )}
            </View>
          )}

          {/* <TouchableWithoutFeedback onPress={() => setFullScreen((prev) => !prev)}> */}
          {/* <Pressable
            style={{ flex: 1 }}
            onPress={() => {
              console.log('User tapped on video');
              // You can toggle full screen here or show/hide controls etc.
              setFullScreen((prev) => !prev);
            }}
          > */}
          <View style={{ flex: 1 }}>
            {msgData?.messageType == MessageType.VIDEO || (isVideoMsg && isFromCameraScreen) ? (
              <Video
                ref={videoRef}
                source={{
                  uri: isFromCameraScreen
                    ? `file://${msgData?.path}`
                    : currentMsgData?.mediaUrl || currentMsgData?.uri,
                }}
                style={{ flex: 1 }}
                resizeMode="contain"
                controlsStyles={{
                  hideNext: true,
                  hidePrevious: true,
                }}
                playWhenInactive
                controls={true}
              />
            ) : (
              <View style={{ flex: 1 }}>
                <Image
                  source={{
                    uri: isFromCameraScreen
                      ? `file://${msgData?.path}`
                      : currentMsgData?.mediaUrl || currentMsgData?.uri,
                  }}
                  style={{ flex: 1, width: 'auto' }}
                  resizeMode="contain"
                />

                {/* Render stickers, texts and additional images */}
                {currentMsgData && (
                  <>
                    <ElementRenderer
                      stickers={currentMsgData?.stickers || []}
                      images={currentMsgData?.additionalImages || []}
                      texts={currentMsgData?.texts || []}
                      itemDragging={() => false}
                      isDeletable={() => false}
                      deleteItem={() => {}}
                      onTextPress={() => {}}
                      activeTextId={null}
                    />
                    {Array.isArray(currentMsgData?.drawingPaths) &&
                      currentMsgData?.drawingPaths.length > 0 && (
                        <PaintBrush
                          ref={childRef}
                          visible={false}
                          paintData={currentMsgData.drawingPaths}
                          strokeWidth={10}
                          eraserSize={10}
                          activeStoryIdx={currentMsgData?.id}
                          onDrawStateChange={() => {}}
                        />
                      )}
                  </>
                )}
              </View>
            )}
          </View>
          {/* </Pressable> */}
          {/* </TouchableWithoutFeedback> */}

          {/* {!fullScreen && ( */}
          {isFromCameraScreen ? (
            <View
              style={{
                flexShrink: 1,
                paddingHorizontal: 21,
                width: SCREEN_WIDTH,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginVertical: Number(StatusBar.currentHeight) / 4,
              }}
            >
              <TouchableOpacity
                onPress={onEditPress}
                style={{
                  backgroundColor: hexToRgba(colors.white, 0.2),
                  borderRadius: 30,
                  paddingHorizontal: 15,
                  paddingVertical: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <EditSVG size={24} color={colors.white} />
                <Text
                  style={{
                    color: colors.white,
                    fontSize: 12,
                    marginLeft: 8,
                    fontWeight: '500',
                  }}
                >
                  Edit
                </Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={onImageSend}>
                <SendSVG
                  size={60}
                  color={colors.white}
                  showBackground={true}
                  backgroundColor={hexToRgba(colors.white, 0.2)}
                />
              </TouchableOpacity>
            </View>
          ) : otherUserData?.type === 'channel' &&
            otherUserData?.conversation?.role === 'member' ? null : (
            <View
              style={{
                backgroundColor: hexToRgba(colors.black, 0.3),
                paddingHorizontal: 21,
                paddingVertical: 13,
                width: SCREEN_WIDTH,
              }}
            >
              {/* Text Input Row */}
              <View
                style={{
                  backgroundColor: hexToRgba(colors.white, 0.09),
                  height: 50,
                  borderRadius: 15,
                  paddingLeft: 19,
                  paddingRight: 13,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <TextInput
                  value={replyMsg}
                  onChangeText={setReplyMsg}
                  placeholder="Write your reply.."
                  placeholderTextColor={hexToRgba(colors.white, 0.5)}
                  style={styles.input}
                />

                <TouchableOpacity
                  onPress={() => {
                    Keyboard.dismiss();
                    onSendMessage();
                  }}
                  disabled={replyMsg.trim().length == 0}
                  hitSlop={{ left: 20, right: 20 }}
                >
                  <SendSVG
                    size={47}
                    color={colors.white}
                    style={{ marginLeft: -10 }}
                    showBackground={false}
                    backgroundColor={'red'}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
          {/* )} */}
        </View>

        <PinModal
          isVisible={pinModal}
          onCloseModal={() => setPinModal(false)}
          onConfirmPin={onPinMessage}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default MediaPreviewScreen;

const styles = StyleSheet.create({
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginLeft: 18,
    marginRight: 15,
  },
  input: {
    flex: 1,
    color: colors.white,
    fontSize: 14,
    fontWeight: '400',
  },
  menuMainStyle: {
    marginTop: 20,
    marginHorizontal: -75,
    borderRadius: 10,
    padding: 5,
  },
  menuStyle: {
    gap: 20,
    padding: 10,
    height: 'auto',
  },
  menuItemStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    paddingVertical: 10,
    paddingHorizontal: 8,
  },
  menuItemText: {
    color: colors.black_23,
    fontSize: 16,
    fontWeight: '400',
  },
});
