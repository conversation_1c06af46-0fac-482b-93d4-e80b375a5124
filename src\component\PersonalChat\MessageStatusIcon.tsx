import React from 'react';
import { Text } from 'react-native';
import { MessageStatus } from '../../device-storage/realm/schemas/MessageSchema';
import TimerSVG from '../../assets/svgIcons/TimerSVG';
import SingleTickSVG from '../../assets/svgIcons/SingleTickSVG';
import DoubleTicksSVG from '../../assets/svgIcons/DoubleTicksSVG';
import { colors } from '../../theme/colors';

interface Props {
  status: MessageStatus;
}

const MessageStatusIcon: React.FC<Props> = ({ status }) => {
  const getStatusIcon = () => {
    switch (status) {
      case MessageStatus.PENDING:
        return <TimerSVG size={12} />;
      case MessageStatus.UPLOADING:
        return <TimerSVG size={12} />;
      case MessageStatus.SENT:
        return <SingleTickSVG size={11} />;
      case MessageStatus.DELIVERED:
        return <DoubleTicksSVG color={colors._7A6A90_purple} />;
      case MessageStatus.SEEN:
        return (
          <DoubleTicksSVG
            color={
              'red'
              // colors.mainPurple
            }
          />
        );
      case MessageStatus.SCHEDULED:
        return <TimerSVG color={colors._7A6A90_purple} />;
      default:
        return '';
    }
  };

  return <Text>{getStatusIcon()}</Text>;
};

export default MessageStatusIcon;
