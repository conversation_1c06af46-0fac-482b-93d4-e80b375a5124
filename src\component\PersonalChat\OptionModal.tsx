import { StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import React from 'react';
import ModalWrapper from '../ModalWrapper';
import { useTranslation } from 'react-i18next';
import { colors } from '../../theme/colors';
import { commonFontStyle } from '../../theme/fonts';
import { IMAGES } from '../../assets/Images';

interface IProps {
  isVisible: boolean;
  onClose: (value: boolean) => void;
  onPressSaveContact: () => void;
  onPressBlock: () => void;
  onPressReport: () => void;
}

const OptionModal = ({
  isVisible,
  onClose,
  onPressSaveContact,
  onPressBlock,
  onPressReport,
}: IProps) => {
  const { t } = useTranslation();

  const onCloseModal = () => {
    onClose(false);
  };

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
      <Text style={styles.title}>{t('Options')}</Text>

      <TouchableOpacity onPress={onPressSaveContact} style={styles.row}>
        <Image style={styles.imageIcon} source={IMAGES.plus_icon} tintColor={colors.black} />
        <Text style={styles.title2}>{t('Save contact')}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onPressBlock} style={styles.row}>
        <Image style={styles.imageIcon} source={IMAGES.block} tintColor={colors._DB1515_red} />
        <Text style={{ ...styles.title2, color: colors._DB1515_red }}>{t('Block')}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onPressReport} style={styles.row}>
        <Image style={styles.imageIcon} source={IMAGES.report} />
        <Text style={{ ...styles.title2, color: colors._DB1515_red }}>{t('Report')}</Text>
      </TouchableOpacity>
    </ModalWrapper>
  );
};

export default OptionModal;

const styles = StyleSheet.create({
  title: {
    ...commonFontStyle(600, 16, colors.gray_80),
    paddingBottom: 20,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    // paddingLeft: hp(3),
    paddingVertical: 10,
  },
  imageIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  title2: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
});
