import { StyleSheet, Text, View, Linking, Platform, TouchableOpacity } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import MessageStatusIcon from '../../../component/PersonalChat/MessageStatusIcon';
import { dayPipe } from '../../../utils/commonFunction';
import MapView, { Marker } from 'react-native-maps';
import LiveLocationSVG from '../../../assets/svgIcons/LiveLocationSVG';
import ButtonPurple from '../../../component/ButtonPurple';
import Geolocation from '@react-native-community/geolocation';
import useSocket from '../../../socket-client/useSocket';
import { ChatService } from '../../../service/ChatService';
import { IMessage } from '../../../device-storage/realm/schemas/MessageSchema';
import UnpinSVG from '../../../assets/svgIcons/UnpinSVG';

type OpenMapArgs = {
  lat: string | number;
  lng: string | number;
  label: string;
};

interface IProps {
  msgData: IMessage;
  onPress: () => void;
  isMyData: boolean;
}

const LocationCard = ({ msgData, onPress = () => {}, isMyData = false }: IProps) => {
  const [sharingMessage, setSharingMessage] = useState<string>('');
  const [liveTimer, setLiveTimer] = useState<NodeJS.Timeout | null>(null);
  const [shareLive, setShareLive] = useState<boolean>(false);
  const [sharingTime, setSharingTime] = useState<string>('15 minutes');
  const [otherUserCoords, setOtherUserCoords] = useState<any>({});
  const { socket } = useSocket();

  const pinned = Number(msgData?.pinnedUntil) > Date.now();

  const openGoogleMaps = (latitude: number, longitude: number) => {
    const scheme = Platform.select({
      ios: 'maps:0,0?q=',
      android: 'geo:0,0?q=',
    });
    const latLng = `${latitude},${longitude}`;
    const label = 'Custom Label'; // Optional label
    const url: string | undefined = Platform.select({
      ios: `${scheme}@${latLng}`,
      android: `${scheme}${latLng}`,
    });

    console.log('hellow world -------------- ');
    Linking.openURL(url as string).catch((err) => {
      // If native maps app fails to open, try web URL
      console.log('catch block of linking url');
      const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      // Linking.openURL(webUrl);
    });
  };

  const stopLiveLocationSharing = () => {
    if (liveTimer) {
      clearInterval(liveTimer);
      setLiveTimer(null);
      setShareLive(false);
    }
  };

  return (
    <View
      style={[
        styles.fileContainer,
        {
          flex: 1,
          alignSelf: isMyData ? 'flex-end' : 'flex-start',
          backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
          borderTopLeftRadius: isMyData ? 16 : 1,
          borderBottomRightRadius: isMyData ? 1 : 16,
          minWidth: hp(35),
        },
      ]}
    >
      {pinned && (
        <View
          style={{
            position: 'absolute',
            top: '50%',
            [isMyData ? 'left' : 'right']: -35,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              ChatService.unpinMessage(msgData.localId, msgData.receiverId);
            }}
            style={styles.pinIconButton}
          >
            <UnpinSVG size={12} color={colors.mainPurple} />
          </TouchableOpacity>
        </View>
      )}

      <View
        style={{
          // paddingHorizontal: 10,
          // paddingVertical: 5,
          borderRadius: 16,
          backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
          flexDirection: 'row',
          overflow: 'hidden',
          alignItems: 'center',
          flexGrow: 1,
          gap: 8,
          height: 135, // <-- fixed height here
        }}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          onPress={() => {
            openGoogleMaps(
              msgData?.location?.latitude as number,
              msgData?.location?.longitude as number,
            );
          }}
          activeOpacity={1}
        >
          <MapView
            provider={'google'}
            scrollEnabled={false}
            zoomEnabled={false}
            rotateEnabled={false}
            pitchEnabled={false}
            moveOnMarkerPress={false}
            style={{ height: '100%', width: '100%' }}
            mapType="terrain"
            loadingEnabled={true}
            loadingIndicatorColor={colors.mainPurple}
            loadingBackgroundColor={colors.gray_f3}
            initialRegion={{
              latitude: msgData?.location?.latitude as number,
              longitude: msgData?.location?.longitude as number,
              latitudeDelta: 0.004,
              longitudeDelta: 0.004,
            }}
          >
            <Marker
              coordinate={{
                latitude: msgData?.location?.latitude as number,
                longitude: msgData?.location?.longitude as number,
              }}
              anchor={{ x: 1, y: 1 }}
            />
          </MapView>
        </TouchableOpacity>

        {msgData?.isLiveSharing && isMyData ? (
          <ButtonPurple
            title="Stop sharing"
            extraStyle={{ backgroundColor: colors._E3DEF4_gray }}
            onPress={() => {
              stopLiveLocationSharing();
              // Send to backend: user stopped live location
              // e.g., socket.emit('stopLiveLocation', msgData.messageId);
            }}
          />
        ) : null}
      </View>

      {Number(msgData?.text?.length) > 0 ? (
        <Text style={{ color: colors.black_23, fontSize: 14, paddingVertical: 5 }}>
          {msgData?.text}
        </Text>
      ) : null}

      {/* Right side: time + tick */}
      <View
        style={{
          flexDirection: isMyData ? 'row' : 'row-reverse',
          alignItems: 'center',
          justifyContent: msgData?.isLiveSharing ? 'space-between' : 'flex-end',
          marginTop: 6,
        }}
      >
        {msgData?.liveCreated ? (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <LiveLocationSVG size={18} color={colors.black_23} style={{ marginRight: 5 }} />
            <Text
              style={{
                color: colors.black,
                fontSize: 12,
              }}
            >
              {`Live untill ${dayPipe(msgData?.createdAt, 'time')}`}
            </Text>
          </View>
        ) : null}

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={{
              color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
              fontSize: 12,
              marginRight: 4,
            }}
          >
            {dayPipe(msgData?.createdAt, 'time')}
          </Text>

          {isMyData && <MessageStatusIcon status={msgData?.status} />}
        </View>
      </View>

      {msgData?.liveCreated ? (
        <ButtonPurple title="Stop sharing" extraStyle={{ backgroundColor: colors._E3DEF4_gray }} />
      ) : null}
    </View>
  );
};

export default LocationCard;

const styles = StyleSheet.create({
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 16,
    // marginBottom: 22,
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  pinIconButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
