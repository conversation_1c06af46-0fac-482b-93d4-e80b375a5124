import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgIconProps {
  size?: number;   // height will be 'size', width will scale proportionally
  color?: string;
}

const EditIconSVG: React.FC<SvgIconProps> = ({
  size = 20,
  color = "#232323",
  ...props
}) => {
  const width = (size * 19) / 20; // keep aspect ratio 19:20
  const height = size;

  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 19 20"
      fill="none"
      {...props}
    >
      <Path
        d="M18.22 3.894l-1.638-1.745a2.306 2.306 0 00-3.257-.103l-2.927 2.756-8.641 8.107A3.829 3.829 0 00.54 15.606L.46 18.204a.751.751 0 00.725.773l2.598.081a3.86 3.86 0 002.768-1.044l8.641-8.107 2.927-2.756c.448-.42.706-.99.725-1.604a2.283 2.283 0 00-.623-1.646v-.007zM5.52 16.917a2.346 2.346 0 01-1.691.644l-1.85-.058.059-1.85c.02-.629.285-1.22.748-1.647l8.093-7.592 2.741 2.919-8.093 7.6-.007-.016zM17.096 6.054l-2.379 2.241-2.741-2.919 2.379-2.24a.805.805 0 011.138.035l1.639 1.745a.811.811 0 01-.036 1.138z"
        fill={color}
      />
    </Svg>
  );
};

export default EditIconSVG;
