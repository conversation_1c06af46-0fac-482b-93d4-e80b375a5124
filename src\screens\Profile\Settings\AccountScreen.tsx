import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import { colors } from '../../../theme/colors';
import { hp } from '../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { IMAGES } from '../../../assets/Images';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { SCREENS } from '../../../navigation/screenNames';
import ContactUsSVG from '../../../assets/svgIcons/ContactUsSVG';
import SecuritySettingSVG from '../../../assets/svgIcons/securitySettingSVG';
import ProfileColorSVG from '../../../assets/svgIcons/ProfileColorSVG';
import { useMe } from '../../../hooks/util/useMe';
import { CommonActions } from '@react-navigation/native';
import { navigationRef } from '../../../navigation/RootContainer';
import { navigateTo } from '../../../utils/commonFunction';
import { UserPreferencesService } from '../../../service/UserService';
import RightArrowSVG from '../../../assets/svgIcons/RightArrowSVG';

const AccountScreen = () => {
  const navigation = useNavigation();
  const { user } = useMe();
  const email = user?.email;

  return (
    <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <SafeAreaView>
        <HeaderBackWithTitle title={'Account Settings'} onBack={() => navigation.goBack()} />
      </SafeAreaView>
      <View style={styles.whiteContainer}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          {/* <TouchableOpacity
            style={styles.row}
          onPress={() => {
              navigateTo(SCREENS.EmailScreen, { email });
            }}
          >
            <ContactUsSVG color="#000000" />
            <Text style={styles.rowText}>{email ? 'Change Email' : 'Add Email'}</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity> */}

          <TouchableOpacity
            style={styles.row}
            onPress={() => navigateTo(SCREENS.NameProfileColoursScreen, { type: 'name' })} //
          >
            <ProfileColorSVG />
            <Text style={styles.rowText}>Name & profile colors</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity>

          {/* <TouchableOpacity style={styles.row} onPress={() => navigateTo(SCREENS.SecurityScreen)}>
            <SecuritySettingSVG />
            <Text style={styles.rowText}>Security</Text>
            <RightArrowSVG color={colors.black_23} width={12} height={12} />
          </TouchableOpacity>
            <Image source={IMAGES.inArrow} style={styles.arrowIcon} />
          </TouchableOpacity> */}
          <View style={{ flex: 1 }} />
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
  },
  rowText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: colors.black_23,
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginLeft: 8,
  },
});

export default AccountScreen;
