// src/realm/listeners.ts
import { getRealm } from './realm';

export const attachWriteListeners = () => {
  console.log('Attaching realm write listeners');
  try {
    const realm = getRealm();
    console.log('Realm write listeners attached for all non-embedded schemas');
    const schemas = realm.schema;
    console.log('Schemas:', schemas);
    schemas.forEach((s) => {
      if (s.embedded) return; // skip embedded objects

      const collection = realm.objects(s.name);
      collection.addListener((objects, changes) => {
        // Log inserts
        changes.insertions.forEach((index) =>
          console.log(`[Realm][${s.name}] Inserted:`, objects[index]),
        );
        // Log modifications
        changes.newModifications.forEach((index) =>
          console.log(`[Realm][${s.name}] Modified:`, objects[index]),
        );
        // Log deletions
        if (changes.deletions.length > 0) {
          console.log(`[Realm][${s.name}] Deleted ${changes.deletions.length} object(s)`);
        }
      });
    });

    console.log('Realm write listeners attached for all non-embedded schemas');
  } catch (er) {
    console.error(er);
  }
};
