import React from "react"
import Svg, { Path } from "react-native-svg"

function CallSVG({ size = 22, color = "#fff" }) {

    return (
        <Svg
            width={size}
            height={(size * 21) / 22} // maintains original aspect ratio
            viewBox="0 0 22 21"
            fill="none"
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M3.211 1.213C4.53-.098 6.701.135 7.806 1.61l1.366 1.825c.898 1.2.819 2.878-.248 3.938l-.258.257a.707.707 0 00-.009.332c.069.442.437 1.378 1.985 2.917 1.547 1.538 2.49 1.907 2.938 1.975.203.031.309.002.342-.01l.442-.439c.948-.942 2.405-1.119 3.579-.48l2.07 1.125c1.772.963 2.22 3.372.767 4.816l-1.539 1.53c-.484.482-1.136.884-1.932.959-1.96.182-6.53-.051-11.331-4.825C1.495 11.073.635 7.186.526 5.27l.811-.046-.811.046c-.055-.968.403-1.787.985-2.366l1.7-1.69zm3.294 1.371c-.55-.733-1.572-.791-2.148-.219l-1.7 1.691c-.358.356-.53.747-.509 1.122.087 1.522.782 5.03 4.976 9.2 4.4 4.374 8.463 4.505 10.034 4.358.32-.03.64-.196.938-.492l1.539-1.53c.625-.622.487-1.755-.399-2.237l-2.07-1.125c-.571-.31-1.24-.208-1.657.206l-.493.49-.573-.576.572.578-.002.001-.003.004-.007.006-.016.015a1.031 1.031 0 01-.148.115 1.665 1.665 0 01-.346.175c-.293.11-.68.168-1.16.095-.939-.145-2.183-.785-3.837-2.43-1.653-1.644-2.3-2.882-2.445-3.82-.074-.479-.015-.866.096-1.16a1.655 1.655 0 01.258-.453l.035-.039.015-.016.006-.007.004-.003.001-.002c.001 0 .002-.001.575.575l-.573-.576.31-.31c.465-.46.53-1.227.093-1.811L6.505 2.584z"
                fill={color}
            />
        </Svg>
    )

}

export default CallSVG
