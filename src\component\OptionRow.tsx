import React from 'react';
import { TouchableOpacity, Text, StyleProp, TextStyle } from 'react-native';

interface OptionRowProps {
  icon: React.ComponentType<any>;
  text: string;
  textStyle?: StyleProp<TextStyle>;
  onPress: () => void;
}

const OptionRow: React.FC<OptionRowProps> = ({ icon: Icon, text, textStyle, onPress }) => (
  <TouchableOpacity
    style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 14,
      paddingHorizontal: 8,
    }}
    onPress={onPress}
  >
    <Icon style={{ marginRight: 16 }} />
    <Text style={[{ fontSize: 16, color: '#222' }, textStyle]}>{text}</Text>
  </TouchableOpacity>
);

export default OptionRow;
