// import { Image, StyleSheet, Text, TouchableOpacity, View, ScrollView } from "react-native";
// import React, { useEffect, useState } from "react";
// import ModalWrapper from "../ModalWrapper";
// import { commonFontStyle, hp } from "../../theme/fonts";
// import { colors } from "../../theme/colors";
// // import RNDateTimePicker from "@react-native-community/datetimepicker";
// import DateTimePicker from '@react-native-community/datetimepicker';
// // import { Picker, DatePicker } from 'react-native-wheel-pick';
// // import WheelPicker from 'react-native-wheel-picker';
// // import { Picker, DatePicker } from 'react-native-wheel-pick';
// // import WheelPickerExpo from 'react-native-wheel-picker-expo';
// import moment from "moment";
// import { IMAGES } from "../../assets/Images";
// import DatePicker from "react-native-date-picker";
// import { Dropdown } from "react-native-element-dropdown";
// import ButtonPurple from "../ButtonPurple";
// import { TransWithoutContext, useTranslation } from "react-i18next";
// // import { Picker } from '@react-native-picker/picker';

// type Props = {
//   isVisible: boolean;
//   onCloseModal: () => void;
//   setscheduleDate: (text: any) => void;
//   onClickSchedule: () => void;
//   scheduleDate?: any;
//   title?: any;
// };

// const ScheduleMessageModal = ({
//   isVisible,
//   onCloseModal,
//   setscheduleDate,
//   onClickSchedule,
//   scheduleDate,
//   title = "Schedule message",
// }: Props) => {
//   // const [date, setDate] = useState(
//   //   scheduleDate ? new Date(scheduleDate) : moment().add(1, "minute").toDate()
//   // );
//   const [date, setDate] = useState(new Date())
//   const [time, setTime] = useState(moment(new Date()).format('hh:mm A'))
//   const [hour, setHour] = useState('00');
//   const [minute, setMinute] = useState('00');
//   // console.log("date", date);
//   const [futureTime, setFutureTime] = useState(
//     moment().add(1, "minute").toDate()
//   );

//   const [open, setopen] = useState(false);
//   const [datePickerMode, setDatePickerMode] = useState('date');
//   // const [selectedHour, setSelectedHour] = useState(hours[0]);
//   // const [selectedMinute, setSelectedMinute] = useState(minutes[0]);
//   const { t } = useTranslation();

//   useEffect(() => {
//     setscheduleDate(date);
//   }, [date]);

//   return (
//     <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
//       <View>
//         <Text style={styles.title}>{t(title)}</Text>
//         <View style={styles.rowView}>
//           <DatePicker
//             date={date}
//             mode = {datePickerMode == "date" ?  "date" : "time"}
//             theme="auto"
//             modal
//             onConfirm={(date : Date) => {
//               console.log(" date ", date)
//               datePickerMode == "date" ? setDate(date) : setTime(moment(date).format('hh:mm A'))
//               setopen(false);
//             }}
//             onCancel={() => {
//               setopen(false);
//             }}
//             open={open}
//             minimumDate={new Date}
//             // maximumDate={new Date(moment().format('YYYY-MM-DD'))}
//           />
//           <TouchableOpacity
//             onPress={() => {setopen(true) , setDatePickerMode("date")}}
//             style={styles.boxView}
//           >
//             <Dropdown
//               style={{ flex: 1 }}
//               placeholderStyle={styles.text}
//               selectedTextStyle={styles.text}
//               data={[{ title: "test" }]}
//               maxHeight={170}
//               labelField="title"
//               valueField="title"
//               value={moment(date).format("DD, MMMM")}
//               placeholder={moment(date).format("DD, MMMM")}
//               disable
//               iconColor={colors.black_23}
//               onChange={(item) => {}}
//               // renderItem={(item) => {
//               //   return (
//               //     <Text
//               //       style={[
//               //         styles.text,
//               //         { marginVertical: 5, marginHorizontal: 5 },
//               //       ]}
//               //     >
//               //       {item.title}
//               //     </Text>
//               //   );
//               // }}
//               autoScroll={false}
//             />
//           </TouchableOpacity>
//           <TouchableOpacity
//             onPress={() => setopen(true)}
//             style={styles.boxView}
//           >
//             <Dropdown
//               style={{ flex: 1 }}
//               placeholderStyle={styles.text}
//               selectedTextStyle={styles.text}
//               data={[{ title: "2025" }, { title: "2026" },{ title: "2027" },{ title: "2028" },{ title: "2029" },{ title: "2030" }]}
//               // maxHeight={170}
//               labelField="title"
//               valueField="title"
//               value={moment(date).format("YYYY")}
//               placeholder={moment(date).format("YYYY")}
//               mode={'default'}
//               disable
//               iconColor={colors.black_23}
//               onChange={(item) => {}}
//               renderItem={(item) => {
//                 return (
//                   <Text
//                     style={[
//                       styles.text,
//                       { marginVertical: 5, marginHorizontal: 5 },
//                     ]}
//                   >
//                     {item.title}
//                   </Text>
//                 );
//               }}
//               autoScroll={false}
//             />
//           </TouchableOpacity>
//         </View>

//          <TouchableOpacity style={{paddingHorizontal:50, width:'50%', alignSelf:'center', padding:14, borderWidth:1, marginBottom:10, borderColor:'#DDD', borderRadius:12}} onPress={()=>{setopen(true) , setDatePickerMode('time')}}>
//           <Text style={{}}>{time}</Text>
//          </TouchableOpacity>
//          <View>

//          <View style={styles.pickerContainer}>

//       </View>
//       {/* <Text>Selected Time: {hour}:{minute}</Text> */}

//           {/* <Picker
//             textSize={20}

//             selectTextColor='green'
//             isShowSelectBackground={false} // Default is true
//             selectBackgroundColor='#8080801A' // support HEXA color Style (#rrggbbaa)
//             // (Please always set 'aa' value for transparent)
//             isShowSelectLine={false} // Default is true
//             selectLineColor='black'
//             selectLineSize={6} // Default is 4
//           /> */}
//     </View>

//         {/* <DatePicker
//            modal
//           date={date}
//           mode="time"
//           open={true}
//           // onDateChange={setDate}
//           // minimumDate={futureTime} // This enforces the minimum time
//           style={{ alignSelf: "center", marginVertical: 20 }}
//           // theme="auto"
//         /> */}
//         <ButtonPurple title={t("Schedule")} onPress={onClickSchedule} />
//       </View>
//     </ModalWrapper>
//   );
// };

// export default ScheduleMessageModal;

// const styles = StyleSheet.create({
//   title: {
//     ...commonFontStyle(600, 16, colors.black_23),
//   },
//   rowView: {
//     flexDirection: "row",
//     gap: hp(2),
//   },
//   boxView: {
//     borderWidth: 1,
//     borderRadius: 15,
//     borderColor: colors._DADADA_gray,
//     flex: 1,
//     flexDirection: "row",
//     alignItems: "center",
//     height: 55,
//     marginVertical: hp(3),
//     paddingHorizontal: hp(2.5),
//     justifyContent: "space-between"
//   },
//   bottomArrow: {
//     tintColor: colors.black_23,
//     height: 15,
//     width: 15,
//     resizeMode: "contain",
//   },
//   text: {
//     ...commonFontStyle(400, 16, colors.black_23),
//   },
//   container: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' },
//   // title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
//   toggleButton: {
//     backgroundColor: '#2ecc71',
//     padding: 10,
//     borderRadius: 5,
//     marginBottom: 20,
//   },
//   toggleButtonText: {
//     fontSize: 18,
//     color: '#fff',
//     fontWeight: 'bold',
//   },
//   pickerContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   picker: {
//     width: 100,
//     height: 200,
//   },
//   colon: {
//     fontSize: 40,
//     marginHorizontal: 10,
//     fontWeight: 'bold',
//     color: '#333',
//   },
//   selectedTime: {
//     marginTop: 20,
//     fontSize: 20,
//     color: '#333',
//   },
//   confirmButton: {
//     backgroundColor: '#3498db',
//     padding: 12,
//     borderRadius: 5,
//     marginTop: 20,
//   },
//   confirmButtonText: {
//     fontSize: 18,
//     color: '#fff',
//     fontWeight: 'bold',
//   },
// });
