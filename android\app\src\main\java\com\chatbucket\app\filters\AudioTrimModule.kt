//package com.chatbucket.audiotrim
//
//import android.Manifest
//import android.content.pm.PackageManager
//import android.media.*
//import androidx.core.app.ActivityCompat
//import com.facebook.react.bridge.*
//import com.facebook.react.modules.core.DeviceEventManagerModule
//import java.io.File
//import java.io.FileOutputStream
//import java.io.IOException
//import java.net.HttpURLConnection
//import java.net.URL
//import java.nio.ByteBuffer
//import java.util.*
//
//class AudioTrimModule(private val reactContext: ReactApplicationContext) :
//        ReactContextBaseJavaModule(reactContext) {
//
//    override fun getName(): String = "AudioTrimModule"
//
//    private fun sendEvent(eventName: String, params: WritableMap? = null) {
//        reactContext
//                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
//                .emit(eventName, params)
//    }
//
//    @ReactMethod
//    fun getAudioDuration(filePath: String, promise: Promise) {
//        try {
//            if (filePath.isEmpty()) {
//                promise.reject("INVALID_PATH", "File path is empty")
//                return
//            }
//            val retriever = MediaMetadataRetriever()
//            try {
//                retriever.setDataSource(filePath)
//                val durationStr =
//                        retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
//                val durationMs = durationStr?.toLongOrNull() ?: 0L
//                val durationSec = durationMs / 1000.0
//                println("Audio duration for $filePath: $durationSec seconds")
//                promise.resolve(durationSec)
//            } finally {
//                retriever.release()
//            }
//        } catch (e: Exception) {
//            promise.reject("ERROR_GET_DURATION", "Failed to get audio duration: ${e.localizedMessage}", e)
//        }
//    }
//
//    @ReactMethod
//    fun trimAudio(inputPath: String, startMs: Double, endMs: Double, promise: Promise) {
//        // Validate input time range
//        if (startMs < 0 || endMs <= startMs) {
//            promise.reject("INVALID_TIME", "Invalid start or end time.")
//            return
//        }
//
////        // Check for necessary permissions
////        val hasReadPermission = ActivityCompat.checkSelfPermission(
////                reactContext, Manifest.permission.READ_EXTERNAL_STORAGE
////        ) == PackageManager.PERMISSION_GRANTED
////        val hasWritePermission = ActivityCompat.checkSelfPermission(
////                reactContext, Manifest.permission.WRITE_EXTERNAL_STORAGE
////        ) == PackageManager.PERMISSION_GRANTED
////
////        // If permissions are not granted, reject with error
////        if (!hasReadPermission || !hasWritePermission) {
////            promise.reject("PERMISSION_DENIED", "Storage permissions are required.")
////            return
////        }
//
//        // Handle input file: Either local file or URL download
//        val inputFile: File = try {
//            if (inputPath.startsWith("http://") || inputPath.startsWith("https://")) {
//                downloadFile(inputPath)  // Add downloadFile() implementation
//            } else {
//                File(inputPath)
//            }
//        } catch (e: Exception) {
//            promise.reject("DOWNLOAD_ERROR", "Failed to download input: ${e.message}")
//            return
//        }
//
//        // Check if input file exists and is not empty
//        if (!inputFile.exists() || inputFile.length() == 0L) {
//            promise.reject("INPUT_FILE_ERROR", "Input file does not exist or is empty.")
//            return
//        }
//
//
//        promise.resolve(outputFile.absolutePath)
//
//        // Create a temporary output file in cache
//        val outputFile = //        // Validate the file format (must be AAC or M4A)
////        val retriever = MediaMetadataRetriever()
////        try {
////            retriever.setDataSource(inputFile.absolutePath)
////            val mime = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)
////            if (mime != "audio/mp4a-latm") {  // Only AAC (audio/mp4a-latm) is supported
////                promise.reject("CODEC_ERROR", "Only AAC (M4A) format is supported. Detected: $mime")
////                return
////            }
////        } finally {
////            retriever.release()
////        }File(reactContext.cacheDir, "trimmed_${UUID.randomUUID()}.m4a")
//
////        try {
////            // Trim the audio using MediaMuxer (Assuming trimAudioUsingMediaMuxer() is implemented)
////            val trimmed = trimAudioUsingMediaMuxer(
////                    inputPath = inputFile.absolutePath,
////                    outputPath = outputFile.absolutePath,
////                    startMs = (startMs * 1000).toLong(),
////                    endMs = (endMs * 1000).toLong()
////            )
////
////            // If trimming fails, reject with an error
////            if (!trimmed) {
////                promise.reject("TRIMMING_FAILED", "Audio trimming failed.")
////            } else {
////                promise.resolve(outputFile.absolutePath)  // Resolve with the trimmed file path
////            }
////        } catch (e: Exception) {
////            promise.reject("TRIMMING_ERROR", "Trimming failed: ${e.message}", e)
////        } finally {
////            // If input was a URL, delete the downloaded file after processing
////            if (inputPath.startsWith("http")) inputFile.delete()
////        }
//    }
//    private fun downloadFile(fileUrl: String): File {
//        val url = URL(fileUrl)
//        val connection = url.openConnection() as HttpURLConnection
//        connection.connect()
//        if (connection.responseCode != HttpURLConnection.HTTP_OK) {
//            throw IOException("HTTP error code: ${connection.responseCode}")
//        }
//
//        val inputStream = connection.inputStream
//        val outputFile = File.createTempFile("downloaded_", ".tmp", reactContext.cacheDir)
//        val outputStream = FileOutputStream(outputFile)
//
//        val buffer = ByteArray(4096)
//        var bytesRead: Int
//        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
//            outputStream.write(buffer, 0, bytesRead)
//        }
//
//        outputStream.flush()
//        outputStream.close()
//        inputStream.close()
//        connection.disconnect()
//
//        return outputFile
//    }
//}
//
//fun trimAudioUsingMediaMuxer(
//        inputPath: String,
//        outputPath: String,
//        startMs: Long,
//        endMs: Long
//): Boolean {
//    val extractor = MediaExtractor()
//    val muxer: MediaMuxer
//    try {
//        extractor.setDataSource(inputPath)
//        val trackCount = extractor.trackCount
//
//        var audioTrackIndex = -1
//        for (i in 0 until trackCount) {
//            val format = extractor.getTrackFormat(i)
//            val mime = format.getString(MediaFormat.KEY_MIME)
//            if (mime?.startsWith("audio/") == true) {
//                audioTrackIndex = i
//                break
//            }
//        }
//
//        if (audioTrackIndex == -1) return false
//
//        extractor.selectTrack(audioTrackIndex)
//        val format = extractor.getTrackFormat(audioTrackIndex)
//
//        muxer = MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
//        val dstIndex = muxer.addTrack(format)
//
//        val maxBufferSize = if (format.containsKey(MediaFormat.KEY_MAX_INPUT_SIZE)) {
//            format.getInteger(MediaFormat.KEY_MAX_INPUT_SIZE)
//        } else 1 * 1024 * 1024
//
//        val buffer = ByteBuffer.allocate(maxBufferSize)
//        val bufferInfo = MediaCodec.BufferInfo()
//
//        extractor.seekTo(startMs * 1000, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)
//        muxer.start()
//
//        while (true) {
//            bufferInfo.offset = 0
//            bufferInfo.size = extractor.readSampleData(buffer, 0)
//            if (bufferInfo.size < 0 || extractor.sampleTime > endMs * 1000) break
//
//            bufferInfo.presentationTimeUs = extractor.sampleTime
//            bufferInfo.flags = extractor.sampleFlags
//            muxer.writeSampleData(dstIndex, buffer, bufferInfo)
//            extractor.advance()
//        }
//
//        muxer.stop()
//        muxer.release()
//        extractor.release()
//
//        return true
//    } catch (e: Exception) {
//        e.printStackTrace()
//        return false
//    }
//}



package com.chatbucket.filters

import android.media.*
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import java.nio.ByteBuffer
import java.util.*

class AudioTrimModule(private val reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String = "AudioTrimModule"

    private fun sendEvent(eventName: String, params: WritableMap? = null) {
        reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, params)
    }

    @ReactMethod
    fun getAudioDuration(filePath: String, promise: Promise) {
        if (filePath.isEmpty()) {
            promise.reject("INVALID_PATH", "File path is empty")
            return
        }
        try {
            val retriever = MediaMetadataRetriever().apply {
                setDataSource(filePath)
            }
            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val durationSec = (durationStr?.toLongOrNull() ?: 0L) / 1000.0
            promise.resolve(durationSec)
        } catch (e: Exception) {
            promise.reject("ERROR_GET_DURATION", "Failed to get audio duration: ${e.localizedMessage}", e)
        }
    }

    @ReactMethod
    fun trimAudio(inputPath: String, startMs: Double, endMs: Double, promise: Promise) {
        if (startMs < 0 || endMs <= startMs) {
            promise.reject("INVALID_TIME", "Invalid start or end time.")
            return
        }

        // Handle input file: Either local file or URL download
        val inputFile: File = try {
            if (inputPath.startsWith("http://") || inputPath.startsWith("https://")) {
                downloadFile(inputPath)
            } else {
                File(inputPath)
            }
        } catch (e: Exception) {
            promise.reject("DOWNLOAD_ERROR", "Failed to download input: ${e.message}")
            return
        }

        // Check if input file exists and is not empty
        if (!inputFile.exists() || inputFile.length() == 0L) {
            promise.reject("INPUT_FILE_ERROR", "Input file does not exist or is empty.")
            return
        }

        // Validate audio format (must be AAC or  )
        val mime = MediaMetadataRetriever().apply {
            setDataSource(inputFile.absolutePath)
        }.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)

//        println('mime ==', mime)
        println("mime is now for $mime")

//        if (mime != "audio/mp4a-latm") {  // Only AAC (audio/mp4a-latm) is supported
//            promise.reject("CODEC_ERROR", "Only AAC (M4A) format is supported. Detected: $mime")
//            return
//        }

        val outputFile = File(reactContext.cacheDir, "trimmed_${UUID.randomUUID()}.m4a")

        // Trim the audio
        try {
//            val trimmed = trimAudioUsingMediaMuxer(inputFile.absolutePath, outputFile.absolutePath, (startMs * 1000).toLong(), (endMs * 1000).toLong())
            val trimmed: Boolean = if (inputPath.lowercase().endsWith(".mp3")) {
                trimMp3ToMp3(inputFile.absolutePath, outputFile.absolutePath, (startMs * 1000).toLong(), (endMs * 1000).toLong())
            } else {
                trimAudioUsingMediaMuxer(inputFile.absolutePath, outputFile.absolutePath, (startMs * 1000).toLong(), (endMs * 1000).toLong())
            }
            println("trimmed == $trimmed")
            if (!trimmed) {
                promise.reject("TRIMMING_FAILED", "Audio trimming failed.")
            } else {
                promise.resolve(outputFile.absolutePath)
            }
        } catch (e: Exception) {
            promise.reject("TRIMMING_ERROR", "Trimming failed: ${e.message}", e)
        } finally {
            // If input was a URL, delete the downloaded file after processing
            if (inputPath.startsWith("http")) inputFile.delete()
        }
    }

    private fun downloadFile(fileUrl: String): File {
        val connection = (URL(fileUrl).openConnection() as HttpURLConnection).apply {
            connect()
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("HTTP error code: $responseCode")
            }
        }

        val inputStream = connection.inputStream
        val outputFile = File.createTempFile("downloaded_", ".tmp", reactContext.cacheDir)
        val outputStream = FileOutputStream(outputFile)

        val buffer = ByteArray(4096)
        var bytesRead: Int
        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
            outputStream.write(buffer, 0, bytesRead)
        }

        outputStream.flush()
        outputStream.close()
        inputStream.close()
        connection.disconnect()

        return outputFile
    }
}

fun trimMp3ToMp3(inputPath: String, outputPath: String, startMs: Long, endMs: Long): Boolean {
    var extractor: MediaExtractor? = null
    var outputStream: FileOutputStream? = null;
    try {
        extractor = MediaExtractor().apply {
            setDataSource(inputPath)
        }

        var audioTrackIndex = -1
        var audioFormat: MediaFormat? = null
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            if (format.getString(MediaFormat.KEY_MIME)?.startsWith("audio/mpeg") == true) {
                audioTrackIndex = i;
                audioFormat = format;
                break
            }
        }

        if (audioTrackIndex == -1) {
            println("No MP3 audio track found in $inputPath")
            return false
        }

        extractor.selectTrack(audioTrackIndex)

        outputStream = FileOutputStream(outputPath)

        val bufferSize = 1024 * 1024 // 1MB buffer
        val buffer = ByteBuffer.allocate(bufferSize)

        extractor?.seekTo(startMs * 1000, MediaExtractor.SEEK_TO_CLOSEST_SYNC)
        var currentPresentationTimeUs = 0L

        while (true) {
            val sampleSize = extractor.readSampleData(buffer, 0)

            if (sampleSize < 0) {
                break // End of stream
            }

            val presentationTimeUs = extractor.sampleTime
            if (presentationTimeUs < startMs * 1000) {
                extractor.advance()
                continue
            }
            if (presentationTimeUs > endMs * 1000) {
                break
            }

            val byteArray = ByteArray(sampleSize)
            buffer.get(byteArray)
            outputStream.write(byteArray)
            buffer.clear()
            extractor.advance()
            currentPresentationTimeUs = presentationTimeUs
        }

        return true

    } catch (e: Exception) {
        e.printStackTrace()
        return false
    } finally {
        extractor?.release()
        outputStream?.close()
    }
}

fun trimAudioUsingMediaMuxer(inputPath: String, outputPath: String, startMs: Long, endMs: Long): Boolean {
    val extractor = MediaExtractor()
    val muxer: MediaMuxer
    try {
        extractor.setDataSource(inputPath)

        var audioTrackIndex = -1
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            if (format.getString(MediaFormat.KEY_MIME)?.startsWith("audio/") == true) {
                audioTrackIndex = i
                break
            }
        }

        if (audioTrackIndex == -1) return false

        extractor.selectTrack(audioTrackIndex)
        val format = extractor.getTrackFormat(audioTrackIndex)
        muxer = MediaMuxer(outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
        val dstIndex = muxer.addTrack(format)

        val maxBufferSize = format.getInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1 * 1024 * 1024)
        val buffer = ByteBuffer.allocate(maxBufferSize)
        val bufferInfo = MediaCodec.BufferInfo()

        extractor?.seekTo(startMs * 1000, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)
        muxer.start()

        while (true) {
            bufferInfo.offset = 0
            bufferInfo.size = extractor.readSampleData(buffer, 0)
            if (bufferInfo.size < 0 || extractor.sampleTime > endMs * 1000) break

            bufferInfo.presentationTimeUs = extractor.sampleTime
            bufferInfo.flags = extractor.sampleFlags
            muxer.writeSampleData(dstIndex, buffer, bufferInfo)
            extractor.advance()
        }

        muxer.stop()
        muxer.release()
        extractor.release()

        return true
    } catch (e: Exception) {
        e.printStackTrace()
        return false
    }
}

