import { getLocales } from 'react-native-localize';
import parsePhoneNumberFromString, { CountryCode } from 'libphonenumber-js';

// Get the default country from the device locale
export const getDefaultCountry = () => {
  const locales = getLocales();
  if (Array.isArray(locales)) {
    return locales[0].countryCode;
  }
  return 'IN';
};

/**
 * Formats a raw phone number to the E.164 national format based on the default country.
 *
 * @param rawPhoneNumber - The phone number string to be formatted.
 * @returns The formatted phone number in national format if valid; otherwise, null.
 */

export const formatToE164 = (rawPhoneNumber: string) => {
  const defaultCountry = getDefaultCountry() as CountryCode;
  try {
    const phoneNumber = parsePhoneNumberFromString(rawPhoneNumber.trim(), defaultCountry);
    // Return the formatted phone number without the "+" sign
    if (phoneNumber && phoneNumber.number.length > 5) {
      const num = phoneNumber.number.replace(/^\+/, '');
      if (phoneNumber.number.includes('00000')) {
      }
      return num;
    }
    return null;
  } catch {
    return null;
  }
};

export const toTimestampMillis = (date: Date | string | undefined) => {
  if (!date) {
    return undefined;
  }
  if (typeof date === 'string') {
    return new Date(date).getTime();
  }
  return date.getTime();
};

export const getRandomColor = () => {
  const majorColors = [
    '#FF0000', // Red
    '#00FF00', // Green (Lime)
    '#0000FF', // Blue
    '#FFFF00', // Yellow
    '#FF00FF', // Magenta/Fuchsia
    '#00FFFF', // Cyan/Aqua
    '#FFA500', // Orange
    '#800080', // Purple
    '#FFC0CB', // Pink
    '#A52A2A', // Brown
    '#808080', // Gray
    '#000000', // Black
    '#008000', // Dark Green
    '#800000', // Maroon
    '#000080', // Navy
    '#808000', // Olive
    '#008080', // Teal
  ];

  return majorColors[Math.floor(Math.random() * majorColors.length)];
};

export const getDateFromString = (dateString: string): Date | undefined => {
  if (!dateString) return undefined;
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? undefined : date;
};

export const getDateInMillies = (dateString: string): number | undefined => {
  const date = getDateFromString(dateString);
  return date ? date.getTime() : undefined;
};
