import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { colors } from '../../../theme/colors';
import Radio from './Radio';

interface RightSelectionRowProps {
  label: string;
  subtitle?: string;
  selected: boolean;
  onPress: () => void;
}

const RightSelectionRow: React.FC<RightSelectionRowProps> = ({
  label,
  subtitle,
  selected,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.row} onPress={onPress} activeOpacity={1}>
      <View style={{ flex: 1 }}>
        <Text style={styles.label}>{label}</Text>
        {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}
      </View>
      <Radio selected={selected} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 20,
    backgroundColor: colors.white,
  },
  label: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  subtitle: {
    fontSize: 13,
    color: colors.gray_80,
    marginTop: 2,
  },
});

export default RightSelectionRow;
