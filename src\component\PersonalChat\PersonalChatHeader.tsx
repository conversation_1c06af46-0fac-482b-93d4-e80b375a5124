import {
  StyleSheet,

} from 'react-native';

import { colors } from '../../theme/colors';
import { commonFontStyle, hp } from '../../theme/fonts';


export const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // gap: 15,
  },
  selectedView: {
    backgroundColor: colors._7155C3_purple,
    paddingVertical: 10,
    paddingHorizontal: 25,
    borderRadius: 50,
  },
  tabIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  tabText: {
    ...commonFontStyle(500, 16, colors.white),
  },
  headerMainView: {
    paddingLeft: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // paddingVertical: hp(1.5),
    height: hp(8),
  },
  moreMenu: {
    padding: hp(2),
  },
  moreMenuStyle: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  headerBackView: {
    height: 18 + hp(2),
    width: 18 + hp(4),
    alignItems: 'center',
    justifyContent: 'center',

  },
  headerBackIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
});
