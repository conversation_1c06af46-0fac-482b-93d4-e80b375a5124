import { ConversationType } from '../device-storage/realm/schemas/MessageSchema';
import { ChatService } from './ChatService';

export const defaultMemberPermissions = {
  canSendText: true,
  canSendPhotos: true,
  canSendVideos: true,
  canSendFiles: true,
  canSendMusic: true,
  canSendVoiceMessages: true,
  canSendContact: true,
  canSendLocation: true,
  canPinMessage: true,
  canEditGroupInfo: true,
  canAddMember: true,
};

export const defaultAdminPermissions = {
  canInviteUsersViaLink: true,
  canManageStories: true,
  canManageLiveStreams: true,
  canDeleteMessages: true,
  canPinMessage: true,
  canBanUsers: true,
  title: '',
  remainsAnonymous: true,
  canChangeChatSpaceInfo: true,
  canManageMessages: true,
  canAddNewAdmins: true,
};

export const ChatSpacePermissions = (conversation: any) => {
  const chatSpaceId = conversation.id;

  const liveConversation = ChatService.getLiveConversation(chatSpaceId);

  if (
    !liveConversation ||
    liveConversation.type === ConversationType.P2P ||
    liveConversation.type === ConversationType.CHANNEL
  ) {
    return defaultMemberPermissions;
  }

  if (liveConversation.type === ConversationType.GROUP) {
    const userRole = liveConversation.role;

    if (userRole === 'owner' || userRole === 'admin') {
      return defaultMemberPermissions;
    }

    if (!liveConversation.groupMemberOverrides) {
      return defaultMemberPermissions;
    }

    try {
      const overrides =
        typeof liveConversation.groupMemberOverrides === 'string'
          ? JSON.parse(liveConversation.groupMemberOverrides)
          : liveConversation.groupMemberOverrides;

      let memberPerms = overrides?.member;

      if (typeof memberPerms === 'string') {
        memberPerms = JSON.parse(memberPerms);
      }

      if (!memberPerms) {
        return defaultMemberPermissions;
      }

      return {
        canSendText: memberPerms.canSendMessage,
        canSendPhotos: memberPerms.canSendMedia?.photos,
        canSendVideos: memberPerms.canSendMedia?.videos,
        canSendFiles: memberPerms.canSendMedia?.files,
        canSendMusic: memberPerms.canSendMedia?.music,
        canSendVoiceMessages: memberPerms.canSendMedia?.voiceMessages,
        canSendContact: memberPerms.canSendMessage,
        canSendLocation: memberPerms.canSendMessage,
        canPinMessage: memberPerms.canPinMessage,
        canEditGroupInfo: memberPerms.canChangeChatInfo,
        canAddMember: memberPerms.canAddUsers,
      };
    } catch (error) {
      return defaultMemberPermissions;
    }
  }

  return defaultMemberPermissions;
};

export const ChatSpaceAdminPermissions = (conversation: any) => {
  const chatSpaceId = conversation.id;
  const liveConversation = ChatService.getLiveConversation(chatSpaceId);
  if (
    !liveConversation ||
    liveConversation.type === ConversationType.P2P ||
    liveConversation.type === ConversationType.CHANNEL
  ) {
    return defaultAdminPermissions;
  }

  if (liveConversation.type === ConversationType.GROUP) {
    const userRole = liveConversation.role;

    
    if (userRole === 'owner') {
      return defaultAdminPermissions;
    }

    if (!liveConversation.groupMemberOverrides) {
      return defaultAdminPermissions;
    }


    try {
      const overrides =
        typeof liveConversation.groupMemberOverrides === 'string'
          ? JSON.parse(liveConversation.groupMemberOverrides)
          : liveConversation.groupMemberOverrides;

      let adminPerms = overrides?.admin;

      if (typeof adminPerms === 'string') {
        adminPerms = JSON.parse(adminPerms);
      }
      if (!adminPerms) {
        return defaultAdminPermissions;
      }
      return {
        canInviteUsersViaLink: adminPerms.canInviteUsersViaLink,
        canManageStories: adminPerms.canManageStories,
        canManageLiveStreams: adminPerms.canManageLiveStreams,
        canDeleteMessages: adminPerms.canDeleteMessages,
        canPinMessage: adminPerms.canPinMessage,
        canBanUsers: adminPerms.canBanUsers,
        title: adminPerms.title,
        remainsAnonymous: adminPerms.remainsAnonymous,
        canChangeChatSpaceInfo: adminPerms.canChangeChatSpaceInfo,
        canManageMessages: adminPerms.canManageMessages,
        canAddNewAdmins: adminPerms.canAddNewAdmins,
      };
    } catch (error) {
      return defaultAdminPermissions;
    }
  }
};
