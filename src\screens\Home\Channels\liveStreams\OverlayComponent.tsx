import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import HeartOutlineSvg from '../../../../assets/svgIcons/HeartOutlineSvg';
import ShareOutlineSvg from '../../../../assets/svgIcons/ShareOutlineSvg';
import StickerOutlineSvg from '../../../../assets/svgIcons/StickerOutlineSvg';
import { useKeyboard } from '../../../../utils/useKeyboard';
import EyeOutlineSvg from '../../../../assets/svgIcons/EyeOutlineSvg';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { colors } from '../../../../theme/colors';

import RNVideo from 'react-native-video';
import LiveStreamOverlay from './LiveStreamOverlay';
import { Comment, CommentInput, CommentsList } from './CommentContainer';
import ActionButtons from './ActionButtons';

import StreamInfo from './StreamInfo';
import { ILivestreamSession } from '../../../../hooks/channels/useLivestreamSpace';
import { ChatSpace } from '../../../../types/socketPayload.type';

type OverlayComponentProps = {
  bottomTranslateY: Animated.Value;
  bottomOpacity: Animated.Value;
  mockComments: Comment[];
  handleLike: () => void;
  handleShare: () => void;
  // handleStopLive: () => void;
  handleSendComment: (comment: string) => void;
  liveStreamSpace: ILivestreamSession;
  handleStopStream: () => void;
};
function OverlayComponent({
  bottomTranslateY,
  bottomOpacity,
  mockComments,
  handleLike,
  handleShare,
  liveStreamSpace,
  handleSendComment,
  handleStopStream,
}: // handleStopLive,
OverlayComponentProps) {
  return (
    <Animated.View
      style={{
        height: '50%',
        // backgroundColor: 'red',
        transform: [{ translateY: bottomTranslateY }],
        opacity: bottomOpacity,
      }}
    >
      <LiveStreamOverlay>
        <View style={{ ...styles.content }}>
          {liveStreamSpace.livestream?.allowChat && (
            <CommentsList
              comments={mockComments}
              isLoading={liveStreamSpace.isPrevChatLoading}
              liveStreamChatController={liveStreamSpace.liveStreamChatController}
            />
          )}

          <ActionButtons
            isLiked={liveStreamSpace.streamDetails.isLiked}
            likes={liveStreamSpace.streamDetails.likeCount}
            shares={liveStreamSpace.streamDetails.shareCount}
            onLike={handleLike}
            onShare={handleShare}
          />
        </View>

        {liveStreamSpace.livestream?.allowChat && <CommentInput onSend={handleSendComment} />}

        <StreamInfo
          chatspace={liveStreamSpace.chatspaceinfo}
          streamInfo={liveStreamSpace.streamDetails}
          onStopLive={handleStopStream}
        />
      </LiveStreamOverlay>
    </Animated.View>
  );
}

export default OverlayComponent;

const styles = StyleSheet.create({
  content: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
});
