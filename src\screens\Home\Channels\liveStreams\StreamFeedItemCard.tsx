import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '../../../../utils/vectorIcons';
import { colors } from '../../../../theme/colors';
import { StreamData } from '../Screens/LiveStreamFeedsScreen';

import { joinStream } from '../../../../hooks/channels/useLiveStreamController';
const { width } = Dimensions.get('window');

type StreamCardProp = {
  item: StreamData;
};

const StreamCard: React.FC<StreamCardProp> = ({ item }) => {
  async function onPressHandler() {
    try {
      await joinStream({ chatSpaceId: item.chatspaceId });
    } catch (err) {
      console.log(err);
    }
  }

  return (
    <TouchableOpacity key={item._id} style={styles.streamCard} onPress={onPressHandler}>
      <Image source={{ uri: item.thumbnail }} style={styles.streamImage} />

      <View style={styles.earningsContainer}>
        <View style={styles.earningsBackground}>
          <MaterialCommunityIcons name="star-circle-outline" size={20} color="white" />
          <Text style={styles.earningsText}>{item.targetAmount}</Text>
        </View>
      </View>

      <View style={styles.streamInfo}>
        <Image source={{ uri: item.chatspace.displayPic }} style={styles.avatar} />
        <View style={styles.streamDetails}>
          <Text style={styles.streamName}>{item.chatspace.name}</Text>
          <View style={styles.viewersContainer}>
            <Ionicons name="eye" size={14} color="#888" />
            <Text style={styles.viewersText}>{item.views}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default StreamCard;

const styles = StyleSheet.create({
  streamCard: {
    width: (width - 32) / 2,
    marginHorizontal: 8,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  streamImage: {
    width: '100%',
    height: 250,
    resizeMode: 'cover',
    position: 'relative',
  },
  earningsContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  earningsBackground: {
    flexDirection: 'row',
    gap: 5,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
  },
  earningsText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '600',
  },
  streamInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  streamDetails: {
    flex: 1,
  },
  streamName: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    marginBottom: 2,
  },
  viewersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewersText: {
    fontSize: 12,
    color: 'white',
    marginLeft: 4,
  },
});
