declare module '@cbucket/react-native-livestream' {
  import { ComponentType, RefObject } from 'react';
  import { ViewStyle } from 'react-native';

  type Resolution = {
    width: number;
    height: number;
  };

  type PredefinedResolution = '240p' | '360p' | '480p' | '720p' | '1080p';

  export type ApiVideoLiveStreamProps = {
    style: ViewStyle;
    camera?: 'front' | 'back';
    video: {
      fps: number;
      resolution: Resolution | PredefinedResolution;
      bitrate: number;
      gopDuration: number;
    };
    audio: {
      sampleRate: 44100;
      isStereo: true;
      bitrate: number;
    };
    isMuted?: boolean;
    enablePinchedZoom?: boolean;
    onConnectionSuccess?: () => void;
    onConnectionFailed?: (code: string) => void;
    onDisconnect?: () => void;
    ref: RefObject<ApiVideoLiveStreamMethods | null>;
  };

  export type ApiVideoLiveStreamMethods = {
    startStreaming: (streamKey: string, url?: string) => void;
    stopStreaming: () => void;
    setZoomRatio: (zoomRatio: number) => void;
  };

  export const ApiVideoLiveStreamView: ComponentType<ApiVideoLiveStreamProps> &
    ApiVideoLiveStreamMethods;
}
