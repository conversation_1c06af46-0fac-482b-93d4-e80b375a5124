// import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import React from 'react';
// import { IMAGES } from '../../../assets/Images';
// import { colors } from '../../../theme/colors';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';

// interface Props {
//   onPress: (item: any) => void;
//   type?: 'VIDEO' | 'IMAGE';
// }
// const EditorIconList = ({ onPress, type }: Props) => {
//   const insets = useSafeAreaInsets();

//   const list = [
//     {
//       id: 1,
//       image: IMAGES.drawImage,
//       title: 'Stickers',
//     },
//     {
//       id: 2,
//       image: IMAGES.TImage,
//       title: 'Text',
//     },
//     {
//       id: 3,
//       image: IMAGES.editImage,
//       title: 'pen',
//     },
//     {
//       id: 4,
//       image: IMAGES.HDImage,
//       title: 'hd',
//     },
//     {
//       id: 5,
//       image: IMAGES.cropImage,
//       title: 'crop',
//     },
//   ];

//   return (
//     <View style={{ ...styles.container, top: insets.top }}>
//       {list.map((item) => (
//         <TouchableOpacity
//           onPress={() => {
//             onPress(item);
//           }}
//           style={styles.iconContainer}
//           key={item.id}
//         >
//           <Image source={item.image} style={styles.iconStyle} resizeMode="contain" />
//         </TouchableOpacity>
//       ))}
//     </View>
//   );
// };

// export default EditorIconList;

// const styles = StyleSheet.create({
//   container: {
//     position: 'absolute',
//     zIndex: 1,
//     right: 20,
//     gap: 10,
//     marginTop: 20,
//   },
//   iconContainer: {
//     zIndex: 1,
//     height: 50,
//     width: 50,
//     alignItems: 'center',
//     justifyContent: 'center',
//     borderRadius: 50,
//     backgroundColor: colors.back_opacity_20,
//   },

//   iconStyle: {
//     height: 20,
//     width: 20,
//     tintColor: colors.white,
//   },
// });
