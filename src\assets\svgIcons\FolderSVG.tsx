import React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"

interface SvgComponentProps extends SvgProps {
    size?: number
    fillColor?: string
}

const FolderSVG: React.FC<SvgComponentProps> = ({
    size = 20,
    fillColor = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={(size * 16) / 20} // maintain aspect ratio
            viewBox="0 0 20 16"
            fill="none"
            {...restProps}
        >
            <Path
                d="M6.871.85c.516 0 1.001.2 1.366.562l.822.813.072.064c.176.141.396.22.624.22h5.225c1.072 0 1.94.863 1.94 1.928V5.45h.286c.623 0 1.194.288 1.564.788.369.5.475 1.126.293 1.715v.001l-1.836 5.84a1.939 1.939 0 01-1.853 1.355H2.809a1.944 1.944 0 01-1.43-.618l-.15-.178a1.926 1.926 0 01-.378-1.21V2.782C.85 1.713 1.72.85 2.788.85h4.083zm-2.26 5.555a.975.975 0 00-.878.536l-.058.148-1.828 5.814v.001a.986.986 0 00.15.886l.076.092a1 1 0 00.732.314H15.37c.433 0 .81-.273.938-.683l1.836-5.84.029-.113a.942.942 0 00-.179-.757v-.001a.971.971 0 00-.792-.397H4.612zM2.784 1.804c-.538 0-.98.44-.98.977v.15H1.8v6.93l.964-3.058v-.001l.054-.148a1.939 1.939 0 011.798-1.203h11.352V4.437a.982.982 0 00-.984-.974h-5.23c-.45 0-.879-.153-1.224-.435L8.388 2.9l-.82-.811-.074-.065a.999.999 0 00-.623-.22H2.784z"
                fill={fillColor}
                stroke={fillColor}
                strokeWidth={0.3}
            />
        </Svg>
    )
}

export default FolderSVG
