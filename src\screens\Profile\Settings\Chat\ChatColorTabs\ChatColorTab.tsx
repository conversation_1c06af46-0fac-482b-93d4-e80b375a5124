import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '../../../../../theme/colors';

interface ChatColorTabProps {
  onColorSelect: (color: string) => void;
  selectedColor: string;
}

const ChatColorTab: React.FC<ChatColorTabProps> = ({ onColorSelect, selectedColor }) => {
  const colorRows = [colors.nameTabColors.slice(0, 6), colors.nameTabColors.slice(12, 18)];

  return (
    <View style={styles.container}>
      <View style={styles.colorGridContainer}>
        {colorRows.map((row, rowIndex) => (
          <View key={`row-${rowIndex}`} style={styles.colorRow}>
            {row.map((color, index) => (
              <TouchableOpacity
                key={`color-${rowIndex}-${index}`}
                activeOpacity={0.7}
                onPress={() => onColorSelect(color)}
              >
                <View
                  style={[
                    styles.colorOuterCircle,
                    selectedColor === color && { borderColor: color, borderWidth: 2 },
                  ]}
                >
                  <View style={[styles.colorInnerCircle, { backgroundColor: color }]} />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 14,
  },
  colorGridContainer: {
    alignItems: 'center',
  },
  colorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  colorOuterCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorInnerCircle: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 2,
    borderColor: 'white',
  },
});

export default ChatColorTab;
