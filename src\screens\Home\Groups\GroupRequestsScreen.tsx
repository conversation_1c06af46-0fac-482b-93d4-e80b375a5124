// Create new file: src/screens/Home/Groups/GroupRequestsScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  getPendingJoinRequests,
  approveJoinRequest,
  rejectJoinRequest,
  approveAllJoinRequests,
  PendingJoinRequestPaginatedResponse,
  PendingJoinRequest, // Import the interface
} from '../../../api/Chatspace/chatspace.api';
import { successToast, errorToast } from '../../../utils/commonFunction';
import { colors } from '../../../theme/colors';
import { IMAGES } from '../../../assets/Images'; // For placeholder image
import { IChatScreenProps } from '../Chats/ChatSpecificScreen';
import CommonView from '../../../component/CommonView';
import { ChatService } from '../../../service/ChatService';
import { IUserData } from '../../../device-storage/realm/schemas/UserSchema';

type GroupRequestsScreenParams = {
  GroupRequestsScreen: {
    userData: IChatScreenProps;
  };
};

const GroupRequestsScreen = () => {
  const route = useRoute<RouteProp<GroupRequestsScreenParams, 'GroupRequestsScreen'>>();
  const userData = route.params.userData;
  const chatSpaceId = userData.chatSpaceId;

  const [requests, setRequests] = useState<PendingJoinRequest[]>([]);
  const [loading, setLoading] = useState(true);

  const liveRequestData = ChatService.getLiveRequest(chatSpaceId)
  console.log('liveUserData', liveRequestData);

  const userRequestData = ChatService.getUserData(liveRequestData?.requesterId ?? '');
  console.log("userRequestData", userRequestData)


  // const fetchRequests = useCallback(async () => {
  //   setLoading(true);
  //   const res = await getPendingJoinRequests(chatSpaceId);
  //   setRequests(res?.data || []);
  //   setLoading(false);
  // }, [chatSpaceId]);

  // useEffect(() => {
  //   fetchRequests();
  // }, [fetchRequests]);

  const handleAction = async (action: 'approve' | 'reject', requestId: string) => {
    const response = await (action === 'approve'
      ? approveJoinRequest(requestId, chatSpaceId)
      : rejectJoinRequest(requestId));

    if (response?.status) {
      successToast(`Request ${action}d.`);
    //   setRequests((prev) => prev.filter((req) => req._id !== requestId));
    } else {
      errorToast('Action failed. Please try again.');
    }
  };

  // ... handleApproveAll / handleRejectAll functions ...

  const renderRequestItem = ({ item }: { item: IUserData }) => (
    <View style={styles.requestItem}>
      <Image
        source={
          userRequestData?.displayPic ? { uri: userRequestData?.displayPic } : IMAGES.profile_image
        }
        style={styles.avatar}
      />
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{userRequestData?.displayName}</Text>
        <Text style={styles.userHandle}>@{userRequestData?.userName}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.approveBtn]}
          //   onPress={() => handleAction('approve', item._id)}
        >
          <Text style={styles.buttonText}>Approve</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.rejectBtn]}
          //   onPress={() => handleAction('reject', item._id)}
        >
          <Text style={styles.buttonText}>Reject</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <CommonView headerTitle="Group Requests">
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.approveBtn]}
          //   onPress={() => handleAction('approve', item._id)}
        >
          <Text style={styles.buttonText}>Approve All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.rejectBtn]}
          //   onPress={() => handleAction('reject', item._id)}
        >
          <Text style={styles.buttonText}>Reject All</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.divider} />
      <FlatList
        data={userRequestData ? [userRequestData] : []}
        keyExtractor={(item) => item.id}
        renderItem={renderRequestItem}
        ListEmptyComponent={<Text style={styles.emptyText}>No pending requests.</Text>}
      />
    </CommonView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: 'white' },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  avatar: { width: 50, height: 50, borderRadius: 25, marginRight: 15 },
  userInfo: { flex: 1 },
  userName: { fontWeight: '400', fontSize: 16, color: colors.black_23 },
  userHandle: { color: 'gray' },
  buttonContainer: { flexDirection: 'row', justifyContent: 'center', marginTop: 10 },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginHorizontal: 5,
  },
  approveBtn: { backgroundColor: colors.mainPurple },
  rejectBtn: { backgroundColor: colors.red_ff4444 },
  buttonText: { color: 'white', fontWeight: 'bold' },
  emptyText: { textAlign: 'center', marginTop: 50, color: 'gray' },
  divider: {
    height: 1,
    backgroundColor: colors.black_23,
    marginVertical: 8,
  },
});

export default GroupRequestsScreen;
