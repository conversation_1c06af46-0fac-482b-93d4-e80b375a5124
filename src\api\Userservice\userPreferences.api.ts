import Api from '../../utils/api';
import {
  IAccountPreferences,
  IChatPreferences,
  IPrivacyPreferences,
  INotificationPreferences,
  IFavoritePreferences,
  IAppLanguage,
} from '../../device-storage/realm/schemas/UserPrefSchema';

const userPrefRoutes = {
  getUserPreferences: 'v1/userpreferences',
  updateAccountPreferences: 'v1/userpreferences/account',
  updateChatPreferences: 'v1/userpreferences/chat',
  updatePrivacyPreferences: 'v1/userpreferences/privacy',
  updateNotificationPreferences: 'v1/userpreferences/notifications',
  updateFavoriteChatSpaces: 'v1/userpreferences/favorites/chat-spaces',
  updateFavoriteContacts: 'v1/userpreferences/favorites/contacts',
  updateAppLanguage: 'v1/userpreferences/app-language',
};

// Main User Preferences API
export const getUserPreferences = async (): Promise<any> => {
  try {
    const response = await Api.get(userPrefRoutes.getUserPreferences);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return Promise.reject(error);
  }
};

// Account Preferences API
export const updateAccountPreferences = async (
  payload: Partial<IAccountPreferences>,
): Promise<IAccountPreferences> => {
  try {
    const response = await Api.put(userPrefRoutes.updateAccountPreferences, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating account preferences:', error);
    return Promise.reject(error);
  }
};

// Chat Preferences API
export const updateChatPreferences = async (
  payload: Partial<IChatPreferences>,
): Promise<IChatPreferences> => {
  try {
    const response = await Api.put(userPrefRoutes.updateChatPreferences, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating chat preferences:', error);
    return Promise.reject(error);
  }
};

// Privacy Preferences API
export const updatePrivacyPreferences = async (
  payload: Partial<IPrivacyPreferences>,
): Promise<IPrivacyPreferences> => {
  try {
    const response = await Api.put(userPrefRoutes.updatePrivacyPreferences, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating privacy preferences:', error);
    return Promise.reject(error);
  }
};

// Notification Preferences API
export const updateNotificationPreferences = async (
  payload: Partial<INotificationPreferences>,
): Promise<INotificationPreferences> => {
  try {
    const response = await Api.put(userPrefRoutes.updateNotificationPreferences, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return Promise.reject(error);
  }
};

// Favorite Chat Spaces API
export const updateFavoriteChatSpaces = async (
  chatSpaceIds: string[],
): Promise<IFavoritePreferences> => {
  try {
    const response = await Api.post(userPrefRoutes.updateFavoriteChatSpaces, {
      chatSpaces: chatSpaceIds,
    });
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error adding favorite chat space:', error);
    return Promise.reject(error);
  }
};

// Favorite Contacts API
export const updateFavoriteContacts = async (payload: {
  contacts: string[];
}): Promise<IFavoritePreferences> => {
  try {
    const response = await Api.put(userPrefRoutes.updateFavoriteContacts, payload);
    if (response?.body?.status) {
      return response.body.data;
    }
    return Promise.reject(response.body);
  } catch (error) {
    console.error('Error updating favorite contacts:', error);
    return Promise.reject(error);
  }
};
