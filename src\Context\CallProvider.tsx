import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { useMediasoup } from './RtcProvider';
import {
  answerCallRes,
  ICallsContext,
  incomingCallData,
  Participant,
  StartCallParams,
  StartCallRes,
  removeParticipantResponse,
  RecipientDetail,
  userIsOnOtherCallType,
  CallDetails,
  IgnoreCallPayload,
  CallTranslationContext,
} from '../types/calls.types';

import useCallSocketListeners from '../hooks/calls/useCallSocketListeners';
import useCallNotifications from '../hooks/calls/useCallNotifications';
import { Socket } from 'socket.io-client';
import { useHandleReconnection } from '../hooks/calls/useHandleReconnection';
import {
  clearPersistedCallDetails,
  getCallPermissions,
  updateCallHistory,
  verifyPersistedCallStatus,
  writeForegroundCallEndInfo,
  writeIncomingHistory,
  writeOutgoingHistory,
} from '../calls/calls.lib';
import { persistCallData } from '../calls/calls.lib';
import useSocket from '../socket-client/useSocket';
import useCallState from '../hooks/calls/useCallState';
import { callSocketEvents } from '../socket-client/socketEvents';
import { Alert, ToastAndroid } from 'react-native';
import { navigationRef } from '../navigation/RootContainer';
import { SCREENS } from '../navigation/screenNames';
import { generateUUID, nameCheck, navigateTo } from '../utils/commonFunction';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { requestCameraPermission, requestMicrophonePermission } from '../utils/usePermission';
import { useMe } from '../hooks/util/useMe';
import { config } from '../appConfig';
import { getAppLanguage } from '../utils/asyncStorage';
import { CallDirection, useInCallManager } from '../hooks/calls/useInCallManager';
import { getChatSpaceMembers } from '../service/ChatSpacesService';

import { useNetworkEvents } from '../hooks/netinfo/useOnline';
import { useNetInfoCtx } from './NetInfoProvider';

import { useContacts } from '../hooks/contacts/useContacts';
import useUsers from '../device-storage/realm/hooks/useUsers';

const CallsContext = createContext<ICallsContext | undefined>(undefined);

function CallProvider({ children }: { children: React.ReactNode }) {
  const { user: userData } = useMe();

  const [showFullScreenCall, setShowFullScreenCall] = useState(false);

  const { socket } = useSocket();
  const {
    currentCallDetails,
    callDetailsRef,
    setCurrentCallDetails,
    resetCallDetails,
    updateCallDetails,
    getCallDetails,
    getParticipants,
  } = useCallState();

  const { createFilteredContactsQuery } = useUsers();
  const { getContactByUserId } = useContacts();

  const { isConnected: isInternetAvailable } = useNetInfoCtx();

  const { mediaConsumers, mediaDevice, mediaProducers, removeDevice } = useMediasoup();
  useCallSocketListeners({
    socket,
    onIncomingCall, // handle incoming call
    handleUserAnswered, // when user answered this will be emitted
    handleCallTerminated, // when the call is closed.(due to any reason)
    handleUserLeftCall, // when a user left from the group call
    onSwitchToVideo, // when a user in the call sent an intent to turn the audio call to video
    handleToggleAudio, // when someone muted or unmuted their audio
    handleToggleVideo, // when someone is toggled their video
    haddleNewProducer, // when a new participant started producing their stream
    onCallRejected, // when someone rejected the call;
    onSwitchToVideoAccepted, // when your intent to switch to video call is accepted by atleast one user;
    onSwitchToVideoRejected, // when your intent to switch to video call is rejected by atleast one user;
    onParticipantRemovedFromCall, // this will be emitted when a user is removed from the call by admin;
    onYouAreRemovedFromCall, // emitted when you have been removed from the call;
    onUserIsOnOtherCall,

    onCallIgnored, //when any recipient ignored the call;
    onTerminateTranslation, // translation
  });

  const {
    callNotifications,
    closeNotifications,

    startIncomingCallTimer,
    timerRefs,
    resetAutoEndTimer,
    startAutoEndTimer,
    showCallNotification,
    hideCallNotification,
    startKeepCallAliveTimer,
    resetKeepCallAliveTimer,
  } = useCallNotifications();

  const inCallController = useInCallManager();

  const startCall = useCallback(
    async (params: StartCallParams) => {
      if (!isInternetAvailable) {
        ToastAndroid.show('No internet connection', ToastAndroid.SHORT);
        return;
      }

      if (!userData) return;
      const hasPermissions = await getCallPermissions(params);
      if (!hasPermissions) {
        ToastAndroid.show('Permissions denied to start call', ToastAndroid.SHORT);
        return;
      }
      setShowFullScreenCall((prev) => true);
      const { recipients, callType, origin } = params;
      if (origin && origin.type === 'groupConversation') {
        const groupMembers = await getChatSpaceMembers(origin.conversation.id || '');
        groupMembers?.members.forEach((member: any) => {
          if (member.userId !== userData._id) {
            recipients.push({
              id: member.userId || '',
              name: member.user.name || '',

              profilePic: member.user.image || '',
              username: member.user.username || '',
            });
          }
        });
      }
      const recipentList: RecipientDetail[] = recipients.map((item) => {
        return {
          _id: item.id || '',
          name: item.name || '',
          image: item.profilePic || '',
          username: item.username || '',
          email: '',
        };
      });

      const clientCallId = generateUUID(30);
      // pre initialising the call state for the UI.
      setCurrentCallDetails((prev) => ({
        ...prev,
        state: 'outgoing',
        recipentList: recipentList,
        origin: origin,
        clientCallId,
        type: callType,
      }));
      inCallController.startCallSession(callType, CallDirection.OUTGOING);
      const payload = {
        callerId: userData._id,
        recipients: recipients.map((r) => r.id),
        call_type: callType,
        origin: origin,
        clientCallId: clientCallId,
      };

      socket.emit(callSocketEvents.START_CALL, payload, async (data: StartCallRes) => {
        if (data.error) {
          ToastAndroid.show(data.error, ToastAndroid.SHORT);
          resetCallDetails();
          inCallController.stopCallSession();
          setShowFullScreenCall((prev) => false);
          return;
        }
        const { callId, roomId, initiatorId, recipentList, rtpCapabilities } = data;
        if (origin && origin.type === 'groupConversation') {
        }

        await mediaDevice.initializeDevice(roomId, rtpCapabilities);
        await mediaProducers.createProducerTransport(roomId, callType);
        await mediaConsumers.createConsumerTransport(roomId);

        // for reconnection
        persistCallData({ roomId, startedAt: new Date() });

        setCurrentCallDetails((prev) => ({
          ...prev,
          callId,
          roomId,
          recipients,
          initiatorId: userData._id,
          initialCallType: callType,
          isGroupCall: recipients.length > 1,
          participants: [],
          state: 'outgoing',
          startedAt: new Date(),
          type: callType,
          recipentList,
        }));
        startKeepCallAliveTimer(sendCallHeartBeat);
        startAutoEndTimer(() => {
          endCall();
        }, 30 * 1000);
        writeOutgoingHistory({ ...data, origin: origin });
      });
    },
    [
      userData,
      socket,
      mediaDevice,
      mediaProducers,
      mediaConsumers,
      persistCallData,
      setCurrentCallDetails,
      startAutoEndTimer,
      endCall,
    ],
  );

  /**
   * Ends the call
   */
  async function endCall(context?: 'outgoing' | 'ongoing') {
    try {
      const callDetails = getCallDetails();
      if (!callDetails || !userData) {
        return;
      }
      setShowFullScreenCall(false);
      inCallController.stopRingback();
      if (callDetails.state === 'idle') {
        return;
      }

      if (callDetails.initiatorId === userData._id) {
        socket.emit(callSocketEvents.END_CALL, {
          callId: callDetails.callId,
          clientId: userData._id,
          roomId: callDetails.roomId,
        });
        writeForegroundCallEndInfo(callDetails.callId, 'outgoing');
      } else if (callDetails.roomId && callDetails.roomId) {
        socket.emit(callSocketEvents.LEAVE_CALL, {
          callId: callDetails.callId,
          clientId: userData._id,
          roomId: callDetails.roomId,
        });
        writeForegroundCallEndInfo(callDetails.callId, 'answered');
      }

      if (context === 'outgoing') {
        socket.emit(callSocketEvents.END_OUTGOING_CALL, {
          clientCallId: callDetails.clientCallId,
        });
        writeForegroundCallEndInfo(callDetails.callId, 'outgoing');
      }

      if (mediaProducers.producers.isScreenSharing) {
        stopScreenSharing();
      }
      inCallController.stopCallSession();

      mediaProducers.stopProducing();
      removeDevice();
      resetCallDetails();
      resetAutoEndTimer();
      clearPersistedCallDetails();
      closeNotifications();
      resetKeepCallAliveTimer();
      goBackFromMainCallScreen(setShowFullScreenCall);
    } catch (err) {
      console.error(err);
    }
  }

  async function cancelCall() {
    const callDetails = callDetailsRef.current;
    if (!callDetails || !userData) {
      return;
    }
    socket.emit(callSocketEvents.CANCEL_CALL, {
      callId: callDetails.callId,
      clientId: userData._id,
      roomId: callDetails.roomId,
    });
    updateCallHistory(callDetails.callId, {
      callStatus: 'outgoing',
      isOngoingCall: false,
    });
    clearPersistedCallDetails();
  }

  function answerCall({ roomId }: { roomId: string }) {
    inCallController.stopRingtone();
    if (!isInternetAvailable) {
      ToastAndroid.show('No internet connection', ToastAndroid.SHORT);
      return;
    }
    setShowFullScreenCall(true);
    socket.emit(callSocketEvents.ANSWER_CALL, { roomId: roomId }, async (data: answerCallRes) => {
      if (!userData) {
        throw new Error('user not found, cannot make call ');
      }
      const { roomId, producers, rtpCapabilites, callType, callId, initialPartcipants } = data;
      inCallController.startCallSession(callType, CallDirection.INCOMING);
      const device = await mediaDevice.initializeDevice(roomId, rtpCapabilites);
      await mediaProducers.createProducerTransport(roomId, callType);
      await mediaConsumers.createConsumerTransport(roomId);
      socket.emit(callSocketEvents.NOTIFY_OWNER, {
        roomId,
        callId,
        clientId: userData._id,
      });
      const newPartcipants = initialPartcipants.map((item) => {
        const newParticipant: Participant = {
          userId: item.userId,
          participantId: item.participantId,
          audioConsumer: null,
          videoConsumer: null,
          client: item.client,
          isScreenSharing: item.isScreenSharing,
        };
        return newParticipant;
      });
      mediaConsumers.setRemoteVideoData((prev) => {
        return [...prev, ...newPartcipants];
      });

      if (producers && producers.length > 0) {
        producers.forEach(async (producer: any) => {
          if (producer.kind === 'audio' && config.audioTranslationEnabled) {
            initiateTranslation(roomId, producer.id, producer.producerClientId);
          }
          const consumer = await mediaConsumers.consume(
            producer.id,
            roomId,
            producer.producerClientId,
            device,
            producer.isScreenSharer ? producer.isScreenSharer : false,
          );
        });
      }
      persistCallData({ roomId: roomId, startedAt: new Date() });
      setCurrentCallDetails((prev) => {
        return {
          ...prev,
          state: 'ongoing',
          initiatorId: data.initiatorId,
          roomId,
          startedAt: new Date(),
        };
      });
      //write to realm db
      updateCallHistory(currentCallDetails.callId, { isOngoingCall: true });
    });
  }
  function rejectCall() {
    const callDetails = callDetailsRef.current;
    if (!callDetails || !userData) {
      return;
    }
    inCallController.stopRingtone();
    socket.emit(callSocketEvents.REJECT_CALL, {
      callId: callDetails.callId,
      clientId: userData._id,
      roomId: callDetails.roomId,
    });

    resetCallDetails();
    clearPersistedCallDetails();
    goBackFromMainCallScreen(setShowFullScreenCall);
    writeForegroundCallEndInfo(callDetails.callId, 'rejected');
  }

  async function onCallRejected(data: { userId: string }) {
    try {
      const { userId } = data;
      // end the call if the call is not group call

      const callDetails = getCallDetails();
      if (!callDetails) return;
      inCallController.stopRingback();
      inCallController.stopCallSession();
      const rejectedRecipient =
        callDetails.recipients.find(({ userId: _id }) => _id === userId)?.name ?? userId;
      const [contact] = createFilteredContactsQuery().searchIds([userId]).get();

      // const contact = await getContactByUserId(userId);
      if (callDetails.state === 'outgoing' && callDetails.recipients.length <= 1) {
        writeForegroundCallEndInfo(callDetails.callId, 'outgoing');
        clearPersistedCallDetails();
        resetCallDetails();
        resetAutoEndTimer();
        resetKeepCallAliveTimer();
        goBackFromMainCallScreen(setShowFullScreenCall);
        ToastAndroid.show(
          `${contact?.name || rejectedRecipient} rejected the call`,
          ToastAndroid.SHORT,
        );
        await mediaProducers.stopProducing();
        return;
      }
      ToastAndroid.show(
        `${contact?.name || rejectedRecipient} rejected the call`,
        ToastAndroid.SHORT,
      );

      updateCallOnAParticipantLeftsCall(userId);
    } catch (err) {
      console.log(err, '>>>>>>>>>>>>.. error at onCallRejected');
    }
  }

  function muteCall() {}

  // 🎥 Video Upgrade Flow:

  async function upgrageToVideoCall() {
    const callDetails = getCallDetails();
    if (!callDetails || callDetails.state !== 'ongoing') {
      return;
    }
    await mediaProducers.produceVideo({});
  }
  async function switchTovidoResponse(response: 'accept' | 'reject') {
    const callDetails = getCallDetails();
    if (response === 'accept') {
      socket.emit(callSocketEvents.SWITCH_TO_VIDEO_ACCEPTED, {
        roomId: callDetails?.roomId,
      });
      updateCallDetails({ type: 'video' });
      await mediaProducers.produceVideo({});
    } else
      socket.emit(callSocketEvents.SWITCH_TO_VIDEO_REJECTED, {
        roomId: callDetails?.roomId,
      });

    hideCallNotification('showSwitchToVideoPopup');
  }

  async function onSwitchToVideo(data: any) {
    timerRefs.current.showSwitchToVideoTimer != null &&
      clearTimeout(timerRefs.current.showSwitchToVideoTimer);

    showCallNotification('showSwitchToVideoPopup');

    timerRefs.current.showSwitchToVideoTimer = setTimeout(() => {
      hideCallNotification('showSwitchToVideoPopup');
    }, 10 * 1000);
  }

  async function requestSwitchToVideo() {
    const callDetails = getCallDetails();
    if (!callDetails || callDetails.state != 'ongoing' || callDetails.type === 'video') {
      return;
    }
    // const producer = await produceVideo({});
    socket.emit(
      callSocketEvents.SWITCH_TO_VIDEO,
      {
        roomId: callDetails.roomId,
        // producerId: producer?.id,
      },
      async (data: any) => {
        if (data.error) {
          //toast.error(data.error);
          return;
        }
        setCurrentCallDetails((prev) => {
          return { ...prev, type: 'video' };
        });
      },
    );
  }

  async function onSwitchToVideoAccepted(data: any) {
    updateCallDetails({ type: 'video' });
    await mediaProducers.produceVideo({});
  }
  async function onSwitchToVideoRejected(data: any) {
    Alert.alert('switch to video rejected');
  }

  // 🧑‍🤝‍🧑 Participant Handling:
  function inviteUsers(contacts: any[]) {
    const callDetails = getCallDetails();
    if (callDetails?.state !== 'ongoing' || !userData) {
      return;
    }

    socket.emit(
      callSocketEvents.INVITE_USERS,
      {
        invitedUserIds: contacts.map((item) => item.userId),
        roomId: callDetails.roomId,
        userId: userData._id,
      },
      (data: any) => {
        console.log(data, 'invite users resp');
      },
    );
  }

  async function handleUserAnswered() {
    try {
      const currentCallDetails = callDetailsRef.current;
      if (!currentCallDetails) return;
      if (currentCallDetails.state != 'outgoing') {
        return;
      }
      setCurrentCallDetails((prev) => {
        return {
          ...prev,
          state: 'ongoing',
          startedAt: new Date(),
        };
      });
      resetAutoEndTimer();
      inCallController.stopRingback();
    } catch (err) {
      throw new Error(JSON.stringify(err));
    }
  }

  async function handleCallTerminated(data: { callId: string; roomId: string; message?: string }) {
    const { message = '' } = data;
    const callDetails = callDetailsRef.current;
    if (!callDetails || callDetails.state == 'idle') {
      throw new Error('tried to end the call without the currentCal');
    }
    if (callDetails.roomId !== data.roomId) {
      ToastAndroid.show(`call with roomId ${data.roomId} terminated`, ToastAndroid.SHORT);
      return;
    }
    // toast(`Call Terminated by ${data.callId}`);
    if (message) {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    }
    inCallController.stopRingback();
    inCallController.stopCallSession();

    mediaProducers.stopProducing();
    writeForegroundCallEndInfo(
      callDetails.callId,
      callDetails.initiatorId === userData?._id ? 'outgoing' : 'answered',
    );
    removeDevice();
    resetCallDetails();
    clearPersistedCallDetails();
    hideCallNotification('showReconnectCallPopup');
    resetKeepCallAliveTimer();
    goBackFromMainCallScreen(setShowFullScreenCall);
  }
  async function handleUserLeftCall(data: {
    callId: string;
    participantId: string;
    roomId: string;
    isSharingScreen?: boolean;
    userId: string;
  }) {
    mediaConsumers.removeRemoteVideo({
      participantId: data.participantId,
      isSharingScreen: data.isSharingScreen ? data.isSharingScreen : false,
      userId: data.userId,
    });
  }

  async function initiateScreenShare() {
    const callDetails = getCallDetails();
    if (!callDetails) {
      return;
    }
    if (callDetails.isScreenSharing === true) {
      return;
    }
    await requestCameraPermission();
    socket.emit(
      callSocketEvents.REQUEST_SCREEN_SHARE,
      { roomId: callDetails.roomId },
      async (data: { status: boolean; error?: string }) => {
        if (data.status === false || data.error) {
          console.log('screen sharing request expired', data);
          ToastAndroid.show(data.error || "Can't share screen", ToastAndroid.SHORT);
          return;
        }
        mediaProducers.hasScreenSharingRequest.current = true;
        const time_out_id = setTimeout(async () => {
          if (mediaProducers.hasScreenSharingRequest.current) {
            ToastAndroid.show('Screen sharing request expired', ToastAndroid.SHORT);
          }
          socket.emit(callSocketEvents.REMOVE_SCREEN_SHARE_REQUEST, { roomId: callDetails.roomId });

          mediaProducers.hasScreenSharingRequest.current = false;
          await mediaProducers.stopScreenMedia();
        }, 1000 * 60 * 1); // 1 min sync with backend
        const screenMediaResp = await mediaProducers.initiateScreenMedia();
        if (screenMediaResp.status === false || screenMediaResp.displayMediaTrack === null) {
          socket.emit(callSocketEvents.REMOVE_SCREEN_SHARE_REQUEST, { roomId: callDetails.roomId });
          mediaProducers.hasScreenSharingRequest.current = false;
          const error = screenMediaResp.error;
          let errText = 'Failed to initiate screen media';
          if (error && error.message && error.message === 'NotAllowedError') {
            errText = 'You cancelled screen sharing';
          }
          ToastAndroid.show(errText, ToastAndroid.SHORT);
          return;
        }
        if (!mediaProducers.hasScreenSharingRequest.current) {
          clearTimeout(time_out_id);
          console.log('screen sharing request expired');
          ToastAndroid.show('Screen sharing request expired', ToastAndroid.SHORT);
          return;
        }

        clearTimeout(time_out_id);
        socket.emit(
          callSocketEvents.SHARE_SCREEN,
          { roomId: callDetails.roomId },
          async (data: { error: string; screenShareParticipant: Participant }) => {
            if (data.error || !screenMediaResp.displayMediaTrack) {
              ToastAndroid.show(data?.error || 'Failed to share screen', ToastAndroid.SHORT);
              return;
            }

            await mediaProducers.produceScreenShare(screenMediaResp.displayMediaTrack);
          },
        );
      },
    );
  }

  async function stopScreenSharing() {
    return new Promise((resolve, reject) => {
      const callDetails = getCallDetails();
      socket.emit(
        callSocketEvents.STOP_SCREENSHARING,
        { roomId: callDetails?.roomId },
        async (data: any) => {
          if (data.error) {
            ToastAndroid.show(data.error, ToastAndroid.SHORT);
            return reject(data.err);
          }
          await mediaProducers.stopScreenMedia();
          return resolve({ status: true });
        },
      );
    });
  }

  // 🛎️ Incoming & Admin:

  function ignoreCall() {
    const currentCallDetails = callDetailsRef.current;
    if (currentCallDetails?.state != 'incoming') {
      return;
    }
    socket.emit(callSocketEvents.CALL_IGNORED, {
      callId: currentCallDetails.callId,
      roomId: currentCallDetails.roomId,
    });
    writeForegroundCallEndInfo(currentCallDetails.callId, 'ignored');
    resetCallDetails();
    goBackFromMainCallScreen(setShowFullScreenCall);
    inCallController.stopRingback();
    inCallController.stopCallSession();
  }

  function onIncomingCall(data: incomingCallData) {
    console.log(data, '>>>>>>>... onIncomingCall');
    setShowFullScreenCall((prev) => false);
    setCurrentCallDetails((prev) => {
      const {
        callType = prev.type,
        recipents = prev.recipients,
        callId = prev.callId,
        initialCallType = prev.initialCallType,
        isGroupCall = prev.isGroupCall,
        roomId = prev.roomId,
        recipentList = prev.recipentList,
        origin = prev.origin,
        initiatorDetails,
        initiatorId,
      } = data;
      const updateObj: CallDetails = {
        ...prev,
        state: 'incoming',
        startedAt: new Date(),
        type: callType,
        recipients: recipents,
        callId: callId,
        initialCallType: initialCallType,
        isGroupCall: isGroupCall,
        roomId: roomId,
        recipentList: recipentList,
        origin: origin,
        initiatorDetails: initiatorDetails,
        initiatorId: initiatorId,
      };
      callDetailsRef.current = updateObj;
      return updateObj;
    });
    inCallController.startRingtone('_DEFAULT_');
    startIncomingCallTimer(ignoreCall);
    writeIncomingHistory(data);

    // navigateTo(SCREENS.MainCallScreen, {});
  }

  //🔊 Media/AV Controls:
  function handleToggleAudio(data: any) {
    const { roomId, callId, clientId, audioEnabled } = data;
    const callDetails = callDetailsRef.current;
    if (!callDetails || callDetails.roomId != roomId || callDetails.callId != callId) {
      return;
    }
    mediaConsumers.updateRemoteVideoData(clientId, {
      isAudioPaused: !audioEnabled,
    });
  }
  type ToggleVideoPayload = {
    callId: string;
    clientId: string;
    muted: boolean;
    roomId: string;
    videoEnabled: boolean;
    participantId: string;
  };
  function handleToggleVideo(data: ToggleVideoPayload) {
    const { roomId, callId, clientId, videoEnabled, participantId } = data;
    const callDetails = callDetailsRef.current;
    if (!callDetails || callDetails.roomId != roomId || callDetails.callId != callId) {
      return;
    }
    mediaConsumers.updateRemoteVideoData(participantId, {
      isVideoPaused: !videoEnabled,
    });
  }

  async function toggleAudioTrack() {
    const callDetails = callDetailsRef.current;
    if (!callDetails || !userData) return;
    let isEnabled = false;
    const audioProducer = mediaProducers.producers?.audioProducer;

    if (!audioProducer) return;

    if (!audioProducer.paused) {
      audioProducer.pause();
      mediaProducers.setProducers((prev) => {
        return !prev ? prev : { ...prev, isAudioPaused: true };
      });
    } else {
      audioProducer.resume();
      mediaProducers.setProducers((prev) => {
        return !prev ? prev : { ...prev, isAudioPaused: false };
      });
      isEnabled = true;
    }

    socket.emit(callSocketEvents.TOGGLE_AUDIO, {
      muted: audioProducer.paused,
      clientId: userData._id,
      roomId: callDetails.roomId,
      callId: callDetails.callId,
      audioEnabled: isEnabled,
    });
  }
  async function toggleVideoTrack() {
    const callDetails = callDetailsRef.current;
    if (!callDetails || !userData) return;

    const videoProducer = mediaProducers.producers?.videoProducer;
    let isEnabled = false;
    if (!videoProducer) return;

    if (!videoProducer.paused) {
      videoProducer.pause();
      mediaProducers.setProducers((prev) => {
        return !prev ? prev : { ...prev, isVideoPaused: true };
      });
    } else {
      videoProducer.resume();
      mediaProducers.setProducers((prev) => {
        return !prev ? prev : { ...prev, isVideoPaused: false };
      });
      isEnabled = true;
    }

    socket.emit(callSocketEvents.TOGGLE_VIDEO, {
      muted: videoProducer.paused,
      clientId: userData._id,
      roomId: callDetails.roomId,
      callId: callDetails.callId,
      videoEnabled: isEnabled,
    });
  }

  async function muteParticipantMedia(type: 'audio' | 'video', participentId: string) {
    const participants = getParticipants();
    const participant = participants?.find((p) => p.participantId === participentId);
    if (!participant) return;

    if (type === 'audio' && participant.audioConsumer) {
      participant.audioConsumer?.pause();
    }
    if (type === 'video' && participant.videoConsumer) {
      participant.videoConsumer.pause();
    }
  }
  async function unmuteParticipantMedia(type: 'audio' | 'video', participentId: string) {
    const participants = getParticipants();
    const participant = participants?.find((p) => p.participantId === participentId);
    if (!participant) return;

    if (type === 'audio' && participant.audioConsumer) {
      participant.audioConsumer.resume();
    }
    if (type === 'video' && participant.videoConsumer) {
      participant.videoConsumer.resume();
    }
  }

  // 📡 Producer Event:
  async function haddleNewProducer(data: any) {
    try {
      const { producerId, kind, roomId, callType, rtpCapabilites, clientId, isScreenSharing } =
        data;

      console.log('haddle new producer', producerId, clientId, isScreenSharing);

      if (kind === 'audio' && config.audioTranslationEnabled) {
        initiateTranslation(roomId, producerId, clientId);
      }
      const device = mediaDevice.getDevice();
      let prevDevice = device;
      if (!device) {
        prevDevice = await mediaDevice.initializeDevice(roomId, rtpCapabilites);
      }
      mediaConsumers.consume(producerId, roomId, clientId, prevDevice!, isScreenSharing);
    } catch (err) {
      throw new Error('error when handling new producer');
    }
  }

  async function initiateTranslation(roomId: string, producerId: string, clientId: string) {
    if (!config.audioTranslationEnabled) return;
    const targetLang = await getAppLanguage();
    socket.emit(
      callSocketEvents.INITIATE_TRANSLATION,
      { roomId, producerId, targetLang },
      async (data: any) => {
        const device = mediaDevice.getDevice();
        let prevDevice = device;
        if (!device) {
          prevDevice = await mediaDevice.initializeDevice(roomId, data.rtpCapabilites);
        }
        mediaConsumers.consume(
          data.producerId,
          roomId,
          clientId,
          prevDevice!,
          false,
          true,
          producerId,
        );
      },
    );
  }

  async function onTerminateTranslation(callTranslationContext: CallTranslationContext) {
    console.log('onTranslationStopped', callTranslationContext);
    const callDetails = getCallDetails();
    if (!callDetails || callDetails.state !== 'ongoing') return;
    const participant = callDetails?.participants?.find(
      (p) => p.userId === callTranslationContext.callContext.speaker,
    );
    if (!participant) {
      console.warn('Terminating translation failed, participant not found');
      return;
    }
    participant.translatedAudioConsumer?.close();
    participant.translatedAudioTrack?.stop();
    console.info('Terminated translation');
    if (!participant?.audioProducerId) {
      console.info('Reconnecting translation failed, audio producer not found');
      return;
    }
    initiateTranslation(callDetails.roomId, participant.audioProducerId, participant.participantId);
  }

  async function handleReconnection(socket: Socket) {
    const data = await verifyPersistedCallStatus(socket);
    if (data.status) {
      showCallNotification('showReconnectCallPopup');
    }
  }

  function removeParticipantFromCall(participantId: string) {
    const callDetails = getCallDetails();
    if (callDetails?.state !== 'ongoing') {
      console.error('Cannot remove participant: Invalid call state');
      return;
    }
    socket.emit(
      callSocketEvents.REMOVE_PARTICIPANT_FROM_CALL,
      {
        roomId: callDetails.roomId,
        participantId,
      },
      participantRemovedFromCallOwnerHandler,
    );
  }

  async function onYouAreRemovedFromCall(data: { roomId: string }) {
    ToastAndroid.show('You have been removed from the call by the host.', ToastAndroid.SHORT);

    mediaProducers.stopProducing();
    removeDevice();
    resetCallDetails();
    clearPersistedCallDetails();
    goBackFromMainCallScreen(setShowFullScreenCall);
  }
  //this will be handling for all remaining participants
  async function onParticipantRemovedFromCall(data: {
    participantId: string;
    userIdOfParticipant: string;
    callId: string;
  }) {
    const callDetails = getCallDetails();

    if (!callDetails || callDetails.state === 'idle') {
      throw new Error('Tried to remove participant without an active call');
    }
    const client = callDetails?.participants.filter(
      ({ userId }) => userId === data.userIdOfParticipant,
    )[0].client;

    ToastAndroid.show(`${client.name} has been removed by host`, ToastAndroid.SHORT);
    updateCallOnAParticipantLeftsCall(data.userIdOfParticipant as string);
  }

  async function participantRemovedFromCallOwnerHandler(data: removeParticipantResponse) {
    if (!data?.status) {
      ToastAndroid.show(data.response, ToastAndroid.SHORT);
      return;
    }

    if (data.response === 'Call Ended') {
      mediaProducers.stopProducing();
      removeDevice();
      resetCallDetails();
      clearPersistedCallDetails();
      resetKeepCallAliveTimer();
      goBackFromMainCallScreen(setShowFullScreenCall);
      return;
    }

    ToastAndroid.show(data.response, ToastAndroid.SHORT);
    updateCallOnAParticipantLeftsCall(data.userIdOfParticipant as string);
  }

  function updateCallOnAParticipantLeftsCall(userIdOfParticipant: string) {
    const callDetails = getCallDetails();
    if (!callDetails) {
      console.error('Error:: while updating call, call not found');
      return;
    }
    const { participants, recipentList, recipients, initiatorId } = callDetails;
    const userIdSet = new Set();
    const updatedCallDetails = {
      participants: participants?.filter(({ userId }) => {
        userIdSet.add(userId);
        return userId !== userIdOfParticipant;
      }),
      recipentList: recipentList?.filter(({ _id }) => {
        userIdSet.add(_id);
        return _id !== userIdOfParticipant;
      }),
      recipients: recipients?.filter(({ userId: _id }) => {
        userIdSet.add(_id);
        return _id !== userIdOfParticipant;
      }),
      isGroupCall: [...userIdSet.add(initiatorId)].length >= 3,
    };
    updateCallDetails(updatedCallDetails);
  }

  function onUserIsOnOtherCall(data: userIsOnOtherCallType) {
    const { onCallRecipients, isGroupCall, areAllBusy } = data;
    const callDetails = getCallDetails();
    if (!callDetails) {
      console.error('Error:: while updating call, call not found');
      return;
    }

    if (isGroupCall && areAllBusy) {
      ToastAndroid.show('All recipients are on other call', ToastAndroid.SHORT);
      return;
    }

    function formatUserList(users: string[]) {
      if (users.length === 1) return users[0];
      if (users.length === 2) return `${users[0]} and ${users[1]}`;
      return `${users.slice(0, -1).join(', ')} and ${users[users.length - 1]}`;
    }

    const message = `${formatUserList(onCallRecipients.map(({ name }) => name))} ${
      onCallRecipients.length > 1 ? 'are' : 'is'
    } on another call`;
    ToastAndroid.show(message, ToastAndroid.SHORT);
  }

  async function onCallIgnored(payload: IgnoreCallPayload) {
    const callDetails = getCallDetails();
    const { roomId, userId } = payload;
    if (!callDetails || callDetails.roomId !== roomId) {
      console.error('Error::call not found');
      return;
    }
    inCallController.stopRingback();
    inCallController.stopCallSession();

    const rejectedRecipient =
      callDetails.recipients.find(({ userId: _id }) => _id === userId)?.name ?? userId;
    if (callDetails.recipients.length <= 1) {
      updateCallHistory(callDetails.callId, {
        callStatus: 'outgoing',
        isOngoingCall: false,
      });
      clearPersistedCallDetails();
      resetCallDetails();
      resetAutoEndTimer();
      resetKeepCallAliveTimer();
      endCall();
      goBackFromMainCallScreen(setShowFullScreenCall);
      ToastAndroid.show(`${rejectedRecipient} ignored the call`, ToastAndroid.SHORT);
      await mediaProducers.stopProducing();
      return;
    }
    updateCallOnAParticipantLeftsCall(userId);
  }

  function sendCallHeartBeat() {
    const callDetails = getCallDetails();
    if (!callDetails || callDetails.state !== 'ongoing') {
      return;
    }
    const { roomId } = callDetails;
    socket.emit(
      callSocketEvents.KEEP_CALL_ALIVE,
      {
        roomId,
      },
      (data: any) => {},
    );
  }
  // comment if we work on designs
  useEffect(() => {
    if (mediaConsumers.remoteVideoData)
      setCurrentCallDetails((prev) => {
        return {
          ...prev,
          participants: mediaConsumers.remoteVideoData,
        };
      });
  }, [mediaConsumers.remoteVideoData]);

  useEffect(() => {
    inCallController.onProximityChange((data: any) => {
      console.log(data, 'proximity change');
    });
    inCallController.onWiredHeadsetChange((data: any) => {
      console.log(data, 'wired headset change');
    });
    inCallController.onAudioFocusChange((data: any) => {
      console.log(data, 'audio focus change');
    });
    inCallController.onNoisyAudio((data: any) => {
      console.log(data, 'noisy audio');
    });
  }, []);

  const contextValues: ICallsContext = {
    callDetails: currentCallDetails,
    getCallDetails,
    startCall,
    rejectCall,
    muteCall,
    answerCall,
    inviteUsers,
    upgrageToVideoCall,
    endCall,
    showCallNotification,
    callNotifications,
    hideCallNotification,
    requestSwitchToVideo,
    toggleAudioTrack,
    toggleVideoTrack,
    muteParticipantMedia,
    unmuteParticipantMedia,

    stopScreenSharing,
    cancelCall,

    switchTovidoResponse,
    producers: mediaProducers.producers,
    resetCallDetails,

    removeParticipantFromCall,

    inCallController,
    updateCallDetails,
    initiateScreenShare,
    showFullScreenCall,
    setShowFullScreenCall,
  };

  return <CallsContext.Provider value={contextValues}>{children}</CallsContext.Provider>;
}

export const useCallContext = () => {
  const context = useContext(CallsContext);
  if (!context) {
    throw new Error('Error while accessing the CallContext');
  }
  return context;
};

export default CallProvider;

// for Ui demo
// useEffect(() => {
//   setInterval(async () => {
//     const mediaStream = await navigator.mediaDevices.getUserMedia({
//       audio: true,
//       video: true,
//     });
//     const audioTrack = mediaStream.getAudioTracks()[0];
//     const videoTrack = mediaStream.getVideoTracks()[0];

//     setCurrentCallDetails((prev) => {
//       if (prev.participants.length === 10) {
//         return prev;
//       }
//       return {
//         ...prev,
//         participants: [
//           ...prev.participants,
//           {
//             id: "user-123",
//             clientId: Math.random().toString(36).substring(2, 15),
//             producerId: "producer-xyz",
//             audioTrack: audioTrack,
//              videoTrack: videoTrack,
//             audioConsumer: null,
//             videoConsumer: null,

//             client: {
//               id: "1",
//               name: Math.random().toString(36).substring(2, 15),
//               username: Math.random().toString(36).substring(2, 15),
//               image: imgUrl,
//             },
//           },
//         ],
//       };
//     });
//   }, 5000);
// }, []);

/* 
endcall
*/

function goBackFromMainCallScreen(
  setShowFullScreenCall: React.Dispatch<React.SetStateAction<boolean>>,
) {
  setShowFullScreenCall(false);
}
