import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import ModalWrapper from '../../../component/ModalWrapper';
import { MaterialCommunityIcons } from '../../../utils/vectorIcons';
import { SafeAreaView, StatusBar } from 'react-native';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';

const PremiumFeaturesScreen = () => {
  const [showVoiceSampleConsent, setShowVoiceSampleConsent] = useState(false);
  const premiumFeatures = [
    {
      id: 1,
      name: 'Language Translation',
      onPress: () => {
        setShowVoiceSampleConsent(true);
      },
    },
  ];

  const renderFeatureCard = ({ item }: { item: (typeof premiumFeatures)[0] }) => (
    <TouchableOpacity style={styles.card} onPress={item.onPress}>
      <Text style={styles.cardText}>✨ {item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Premium Features</Text>

      <FlatList
        data={premiumFeatures}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderFeatureCard}
        contentContainerStyle={styles.listContent}
      />
      <VoiceConsentModal
        showModal={showVoiceSampleConsent}
        setShowModal={setShowVoiceSampleConsent}
      />
    </View>
  );
};

export default PremiumFeaturesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white || '#fff',
    paddingHorizontal: 20,
    paddingTop: hp(4),
  },
  heading: {
    ...commonFontStyle(700, 22, colors.black_23 || '#222'),
    marginBottom: hp(2),
  },
  listContent: {
    paddingBottom: 40,
  },
  card: {
    backgroundColor: colors.gray_f3 || '#f3f3f3',
    padding: 15,
    borderRadius: 16,
    marginBottom: hp(2),
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  cardText: {
    ...commonFontStyle(500, 16, colors.black_23 || '#222'),
  },
});

type VoiceConsentModalProps = {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

function VoiceConsentModal({ showModal, setShowModal }: VoiceConsentModalProps) {
  return (
    <ModalWrapper
      isVisible={showModal}
      onCloseModal={() => {
        setShowModal(false);
      }}
    >
      <View style={{ width: '100%' }}>
        <SafeAreaView style={Consnet_styles.container}>
          <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

          {/* Header Section */}
          <View style={Consnet_styles.header}>
            <Text style={Consnet_styles.title}>Share us your voice</Text>
            <Text style={Consnet_styles.description}>
              We need a quick voice sample from you to{'\n'}
              personalize the voice translation experience.
            </Text>
          </View>

          {/* Feature Highlights Section */}
          <View style={{}}>
            <Text style={Consnet_styles.featuresTitle}>Feature highlights:</Text>

            <FeatureItem text="1000+ languages supported" />
            <FeatureItem text="Realistic voice clone" />
            <FeatureItem text="Ultra premium quality" />
          </View>

          {/* Info Badge */}
          <InfoBadge />

          {/* Action Buttons */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginTop: hp(3),
            }}
          >
            <TouchableOpacity
              style={Consnet_styles.skipButton}
              onPress={() => {
                setShowModal(false);
              }}
            >
              <Text style={Consnet_styles.skipButtonText}>I'll Do it later</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={Consnet_styles.proceedButton}
              onPress={() => {
                setShowModal(false);
                navigateTo(SCREENS.VoiceSampleScreen);
              }}
            >
              <Text style={Consnet_styles.proceedButtonText}> Proceed </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    </ModalWrapper>
  );
}

// Feature Item Component
const FeatureItem = ({ text }: { text: string }) => (
  <View style={Consnet_styles.featureItem}>
    <MaterialCommunityIcons name="star-four-points" size={15} color={'black'} />
    <Text style={Consnet_styles.featureText}>{text}</Text>
  </View>
);

// Info Badge Component
const InfoBadge = () => (
  <View style={Consnet_styles.infoBadge}>
    <View style={Consnet_styles.infoIcon}>
      <Text style={Consnet_styles.infoIconText}>i</Text>
    </View>
    <Text style={Consnet_styles.infoBadgeText}>
      Be sure to be in a noise free location for better accuracy of translation
    </Text>
  </View>
);

// Main Voice Recording UI Component

const Consnet_styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 10,
  },
  header: {
    marginBottom: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 6,
    textAlign: 'left',
  },
  description: {
    fontSize: 15,
    color: '#6b7280',
    lineHeight: 24,
    textAlign: 'left',
  },

  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 5,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#1f2937',
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#1f2937',
    flex: 1,
  },
  infoBadge: {
    flexDirection: 'row',
    backgroundColor: colors.warmCream,
    borderRadius: 12,
    padding: 16,
    marginBottom: 40,
    alignItems: 'flex-start',
  },
  infoIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  infoIconText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6b7280',
  },
  infoBadgeText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    flex: 1,
  },
  actionButtons: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: 40,
    gap: 12,
  },
  skipButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: colors.gray_f3,
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6b7280',
  },
  proceedButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#8b5cf6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#8b5cf6',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    width: '48%',
  },
  proceedButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
