import React from 'react';
import { StyleSheet, TextInput } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, runOnJS } from 'react-native-reanimated';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import { useData } from '../utils/Context';

interface CustomTextInputProps {
  setInputText: (text: string) => void;
  id: any;
  text: string;
  color: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: 'normal' | 'bold' | '500';
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number, value: boolean) => boolean;
  deleteItem: () => void;
  yPos: number;
  xPos: number;
  isEditable: boolean;
}

const CustomTextInput: React.FC<CustomTextInputProps> = ({
  setInputText,
  id,
  text,
  color,
  fontSize,
  fontFamily,
  fontWeight,
  itemDragging,
  isDeletable,
  deleteItem,
  yPos,
  xPos,
  isEditable,
}) => {
  const { setMemorizedPosition } = useData();

  const translateX = useSharedValue(xPos || 0);
  const translateY = useSharedValue(yPos || 0);
  const context = useSharedValue({ x: 0, y: 0 });

  const returnStatus = (value: boolean) => {
    itemDragging(value);
  };

  const onDeleteArea = (x: number, y: number) => {
    if (y > 310 && y < 360) {
      return isDeletable(id, true);
    } else {
      return isDeletable(id, false);
    }
  };

  const panGesture = Gesture.Pan()
    .minDistance(10)
    .onStart(() => {
      runOnJS(returnStatus)(true);
      context.value = { x: translateX.value, y: translateY.value };
    })
    .onUpdate((event) => {
      translateX.value = context.value.x + event.translationX;
      translateY.value = context.value.y + event.translationY;
      runOnJS(onDeleteArea)(translateX.value, translateY.value);
    })
    .onEnd(() => {
      runOnJS(returnStatus)(false);
      const finalX = translateX.value;
      const finalY = translateY.value;
      const isInDeleteArea = (finalY > 200 && finalX > -114 && finalX < 114) || finalY > 352;
      if (isInDeleteArea) {
        runOnJS(deleteItem)();
      } else {
        runOnJS(setMemorizedPosition)({ type: 'input', id, x: finalX, y: finalY });
      }
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }, { translateY: translateY.value }],
  }));

  return (
    <GestureDetector gesture={panGesture}>
      {/* <Animated.View style={[styles.textInputMainContainer, animatedStyle]}>
        <TextInput
          value={text}
          onChangeText={setInputText}
          style={{
            fontSize: fontSize || 24,
            fontWeight: fontWeight || '600',
            color: color,
            fontFamily: fontFamily,
          }}
          editable={isEditable}
          selectTextOnFocus={isEditable}
        />
      </Animated.View> */}
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  textInputMainContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    paddingHorizontal: '1%',
    paddingVertical: '1%',
  },
});

export default CustomTextInput;
