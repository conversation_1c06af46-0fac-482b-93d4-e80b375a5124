import {
  View,
  Text,
  Alert,
  Image,
  Platform,
  AppState,
  StatusBar,
  TextInput,
  StyleSheet,
  NativeModules,
  TouchableOpacity,
  ActivityIndicator,
  NativeEventEmitter,
  TouchableWithoutFeedback,
} from 'react-native';
import React, { useRef, useMemo, useState, useEffect, useCallback } from 'react';
import { launchImageLibrary } from 'react-native-image-picker';
import { BlurView } from '@react-native-community/blur';
import { useRoute, useNavigation } from '@react-navigation/native';
import Footer from '../Components/Footer';
// import SearchBar from '../Components/SearchBar';
import CustomModal from '../Components/Modal';
import Animated, { withTiming, useSharedValue, useAnimatedStyle } from 'react-native-reanimated';
// import RNFS from 'react-native-fs';
import { hp, wp } from '../utils/Constants/dimensionUtils';
import { Filter<PERSON>an<PERSON>, Header, TextColorPicker, Deletebtn } from '../Components';
import { ArrStickers, arrMusic } from '../utils/Data';
import StickerList from '../Components/StickerHandler/StickerList';
// import Music from '../Components/Tab/Music';
import AfterCapture from '../Components/AfterCapture';
import CanvasEditor from '../Components/CanvasEditor';
import { useData, DataProvider } from '../utils/Context';
import ImagesBundle from '../utils/Constants/Imges';
// import DocumentPicker from '@react-native-documents/picker';
import TrackPlayer, {
  State,
  // RepeatMode,
  // Capability,
  // AppKilledPlaybackBehavior,
} from 'react-native-track-player';
import PaintBrush from '../Components/PaintBrush';
import { updatePosition } from '../utils/StateData';
// import MusicPlayer from '../Components/MusicPlayer/MusicPlayer';
import { useTimeData, TimeProvider } from '../utils/Context/TimeContext';
import { updateTime } from '../utils/StateData/TimeStore';
import { generateVideoThumbnail as generateAndroidVideoThumbnail } from '../Components/FilteredVideoView';
import { showEditor } from 'react-native-video-trim';
import { navigateTo } from '../../../utils/commonFunction';
import { navigationRef } from '../../../navigation/RootContainer';
const { AudioTrimmer } = NativeModules;

const CameraScreenContent: React.FC<any> = ({ handleSendMessage, otherUserData }) => {
  const route = useRoute<any>();
  const navigation = useNavigation();
  const initialMedia = (route?.params as any)?.initialMedia;
  const isFromMediaPreview = (route?.params as any)?.isFromMediaPreview;
  // Prefer values from route.params; fall back to props if provided directly
  const routeHandleSendMessage = (route?.params as any)?.handleSendMessage;
  const routeOtherUserData = (route?.params as any)?.otherUserData;
  const effectiveHandleSendMessage = handleSendMessage || routeHandleSendMessage;
  const effectiveOtherUserData = otherUserData || routeOtherUserData;
  const videoRef = useRef<any>(null);
  const [images, setImages] = useState<any[]>([]);
  const [OverlapExtra, setOverlapExtra] = useState<any>({});
  const [captured, setCaptured] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isPainton, setIsPaintOn] = useState(false);
  const [isPropDragging, setIsPropDragging] = useState(false);
  const [isDeleteable, setIsDeleteable] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('Filters');
  const [selectedFilterIndex, setSelectedFilterIndex] = useState<number | undefined>(undefined);
  // const [selectedMusicIndex, setSelectedMusicIndex] = useState(null);
  const [activeInput, setActiveInput] = useState(false);
  const [tempText, setTempText] = useState<string | null>(null);
  const [activeColor, setActiveColor] = useState(false);
  const [activeColorInput, setActiveColorInput] = useState<any>(null);
  const [activeStoryIdx, setActiveStoryIdx] = useState<any>(null);
  const toggleModal = useCallback(() => setIsModalVisible((prev) => !prev), []);
  const [paintBrsClr, setPaintBrsClr] = useState('#000000');
  const [strokeWidth, setStrokeWidth] = useState(10);
  const [resetMusicPlayer, setResetMusicPlayer] = useState(0);
  const [previousPlayingPosition, setPreviousPlayingPosition] = useState(0);
  const [isVideoTrimmingStarted, setIsVideoTrimmingStarted] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [isVideoProcessing, setIsVideoProcessing] = useState(false);
  const [activeTool, setActiveTool] = useState<any>(null);
  // const [selectedAudio, setSelectedAudio] = useState(null);
  // const [tempSelectedAudio, setTempSelectedAudio] = useState(null);
  // const [videoCurrentTime, setVideoCurrentTime] = useState(0);
  const { memorizedPosition } = useData();
  const childRef = useRef<any>(null);
  const canvasRef = useRef<any>(null);
  const appState = useRef(AppState.currentState);

  const activePaintData = () => {
    const foundItem = images.find((itm: any) => itm.id === activeStoryIdx);
    return foundItem && foundItem.path ? foundItem.path : [];
  };

  const {
    trimStartTime,
    trimEndTime,
    tempUri,
    endTime,
    setTrimStartTime,
    setTrimEndTime,
    setStartTime,
    setEndTime,
    setTempUri,
  } = useTimeData();

  const paintTools = [
    { id: 'paint', icon: ImagesBundle.Paint },
    { id: 'erase', icon: ImagesBundle.Erase },
  ];
  const ActionTools = [
    { id: 'undo', icon: ImagesBundle.Undo },
    { id: 'redo', icon: ImagesBundle.Redo },
  ];

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      appState.current = nextAppState;
    });
    return () => subscription.remove();
  }, []);

  useEffect(() => {
    if (tempUri !== null && activeStoryIdx !== null) {
      updateTime(setImages, setOverlapExtra, activeStoryIdx, trimStartTime, trimEndTime, tempUri);
    }
  }, [trimStartTime, trimEndTime, tempUri, activeStoryIdx]);

  // Initialize editor with external media passed via navigation
  useEffect(() => {
    if (!initialMedia) return;
    const normalize = (asset: any) => {
      const id = `${Date.now()}-${Math.random()}`;
      if (asset?.mime?.startsWith?.('video') || asset?.type?.startsWith?.('video')) {
        const rawDuration = asset.duration || 0;
        const duration = rawDuration > 1000 ? Math.round(rawDuration / 1000) : rawDuration;
        return {
          id,
          fileName: asset.filename || asset.fileName,
          fileSize: asset.size || asset.fileSize,
          isMp3PlayerActive: false,
          height: asset.height,
          type: asset.mime || asset.type,
          videoDuration: duration,
          uri: asset.path || asset.uri,
          appliedFilter: 'default',
          thumbnailUri: asset.videoThumbnail || asset.thumbnailUri || undefined,
          width: asset.width,
          additionalImages: [],
          texts: [],
          stickers: [],
          matrix: [],
          path: [],
          audio: null,
          isSoundOn: true,
          isVideoLess: duration <= 15,
          startTime: 0,
          endTime: duration,
          trimStartTime: 0,
          trimEndTime: duration,
        };
      }
      return {
        id,
        fileName: asset.filename || asset.fileName || 'image',
        fileSize: asset.size || asset.fileSize,
        isMp3PlayerActive: false,
        height: asset.height,
        type: asset.mime || asset.type || 'image/jpeg',
        uri: asset.path || asset.uri,
        width: asset.width,
        additionalImages: [],
        texts: [],
        stickers: [],
        matrix: [],
        path: [],
        audio: null,
      };
    };
    const items = Array.isArray(initialMedia)
      ? initialMedia.map(normalize)
      : [normalize(initialMedia)];
    setImages(items as any);
    setActiveStoryIdx(items[0]?.id);
    setOverlapExtra(items[0]);
  }, [initialMedia]);

  // Ensure video thumbnails exist when media comes from external picker (initialMedia)
  useEffect(() => {
    const ensureThumbnails = async () => {
      const needsThumb = images.some(
        (it: any) => (it.type || '').startsWith('video') && !it.thumbnailUri,
      );
      if (!needsThumb) return;
      const updated = await Promise.all(
        images.map(async (it: any) => {
          if ((it.type || '').startsWith('video') && !it.thumbnailUri) {
            try {
              const thumb =
                Platform.OS === 'ios'
                  ? await AudioTrimmer.generateVideoThumbnail(it.uri)
                  : await generateAndroidVideoThumbnail(it.uri);
              return { ...it, thumbnailUri: thumb };
            } catch {
              return it;
            }
          }
          return it;
        }),
      );
      setImages(updated);
      const active = updated.find((i: any) => i.id === activeStoryIdx);
      if (active) setOverlapExtra(active);
    };
    if (images.length > 0) {
      ensureThumbnails();
    }
  }, [images, activeStoryIdx]);

  // async function playAudio(audioUrl) {
  //   try {
  //     await TrackPlayer.stop();
  //     await TrackPlayer.reset();
  //     const queue = await TrackPlayer.getQueue();
  //     if (queue.length > 0 && queue[0].url === audioUrl) {
  //       await TrackPlayer.play();
  //     } else {
  //       await TrackPlayer.reset();
  //       await TrackPlayer.add({ url: audioUrl });
  //       await TrackPlayer.play();
  //     }
  //   } catch (err) {
  //     console.error('Error playing audio:', err);
  //   }
  // }

  // async function playStoryAudio(audioUrl) {
  //   if (images.length === 0) return;
  //   try {
  //     await TrackPlayer.stop();
  //     await TrackPlayer.reset();
  //     const queue = await TrackPlayer.getQueue();
  //     if (queue.length > 0 && queue[0].url === audioUrl) {
  //       await TrackPlayer.play();
  //     } else {
  //       await TrackPlayer.reset();
  //       await TrackPlayer.add({ url: audioUrl });
  //       await TrackPlayer.setRepeatMode(RepeatMode.Track);
  //       await TrackPlayer.play();
  //     }
  //   } catch (err) {
  //     console.error('Error playing story audio:', err);
  //   }
  // }

  // async function stopMusic() {
  //   try {
  //     await TrackPlayer.stop();
  //     await TrackPlayer.reset();
  //     setSelectedAudio(null);
  //     setTempSelectedAudio(null);
  //     setPreviousPlayingPosition(0);
  //   } catch (err) {
  //     console.error('Error in stopMusic:', err);
  //   }
  // }

  // async function stopStoryAudio() {
  //   try {
  //     await TrackPlayer.stop();
  //     await TrackPlayer.reset();
  //   } catch (err) {
  //     console.error('Error in stopStoryAudio:', err);
  //   }
  // }

  useEffect(() => {
    const handleAppStateChange = (state) => {
      if (state === 'active') {
        setTimeout(() => setIsActive(true), 500);
      } else {
        setIsActive(false);
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription.remove();
  }, []);

  // useEffect(() => {
  //   const manageAudio = async () => {
  //     if (isDrawing) return;
  //     if (images.length === 0 || !OverlapExtra) {
  //       await stopMusic();
  //       await stopStoryAudio();
  //       setSelectedAudio(null);
  //       setTempSelectedAudio(null);
  //       setPreviousPlayingPosition(0);
  //     } else if (OverlapExtra.audio?.url) {
  //       if (OverlapExtra?.type?.includes('image')) {
  //         await playStoryAudio(OverlapExtra.audio.url);
  //       }
  //     } else {
  //       await stopStoryAudio();
  //       if ((selectedAudio?.url && images.length > 0) || !memoizedAudio) {
  //         await playAudio(selectedAudio.url);
  //       }
  //     }
  //   };
  //   manageAudio();
  // }, [OverlapExtra.audio?.url, images.length, selectedAudio?.url]);

  // useEffect(() => {
  //   const manageModalAudio = async () => {
  //     if (isModalVisible) {
  //       const state = await TrackPlayer.getState();
  //       if (state === State.Playing) {
  //         setPreviousPlayingPosition(await TrackPlayer.getPosition());
  //         await TrackPlayer.pause();
  //       }
  //       if (tempSelectedAudio?.url && images.length > 0) {
  //         await playAudio(tempSelectedAudio.url);
  //       }
  //     } else {
  //       if (tempSelectedAudio?.url) {
  //         await stopMusic();
  //         setTempSelectedAudio(null);
  //       }
  //       if (images.length > 0 && OverlapExtra.audio?.url && OverlapExtra?.type?.includes('image')) {
  //         await playStoryAudio(OverlapExtra.audio.url);
  //         if (previousPlayingPosition > 0) {
  //           await TrackPlayer.seekTo(previousPlayingPosition);
  //         }
  //       } else if (images.length > 0 && selectedAudio?.url) {
  //         await playAudio(selectedAudio.url);
  //         if (previousPlayingPosition > 0) {
  //           await TrackPlayer.seekTo(previousPlayingPosition);
  //         }
  //       } else {
  //         await stopMusic();
  //         await stopStoryAudio();
  //       }
  //     }
  //   };
  //   manageModalAudio();
  // }, [isModalVisible, tempSelectedAudio, OverlapExtra.audio, images.length, selectedAudio]);

  // useEffect(() => {
  //   if (!isModalVisible && OverlapExtra?.type?.includes('image')) {
  //     const handleAudio = async () => {
  //       if (images.length > 0 && OverlapExtra.audio?.url) {
  //         const state = await TrackPlayer.getState();
  //         if (state !== State.Playing) {
  //           await playStoryAudio(OverlapExtra.audio.url);
  //           if (previousPlayingPosition > 0) {
  //             await TrackPlayer.seekTo(previousPlayingPosition);
  //           }
  //         }
  //       } else if (selectedAudio?.url && images.length > 0 && !OverlapExtra.audio?.url) {
  //         const state = await TrackPlayer.getState();
  //         if (state !== State.Playing) {
  //           await playAudio(selectedAudio.url);
  //           if (previousPlayingPosition > 0) {
  //             await TrackPlayer.seekTo(previousPlayingPosition);
  //           }
  //         }
  //       }
  //     };
  //     handleAudio();
  //   }
  // }, [isModalVisible, activeTab, OverlapExtra.audio, selectedAudio, images.length]);

  useEffect(() => {
    if (memorizedPosition && memorizedPosition.length > 0) {
      const lastPosition = memorizedPosition[memorizedPosition.length - 1];
      updatePosition(setImages, activeStoryIdx, lastPosition);
    }
    setIsDrawing(false);
    setActiveTool(null);
  }, [memorizedPosition]);

  useEffect(() => {
    const eventEmitter = new NativeEventEmitter(NativeModules.VideoTrim);
    const Trimsubscription = eventEmitter.addListener('VideoTrim', handleVideoTrimEvent);
    return () => Trimsubscription.remove();
  }, [activeStoryIdx, OverlapExtra]);

  const handleVideoTrimEvent = async (event: any) => {
    if (Platform.OS === 'ios') {
      var { generateVideoThumbnail } = AudioTrimmer;
    }
    switch (event.name) {
      case 'onFinishTrimming': {
        setIsVideoTrimmingStarted(false);
        if (activeStoryIdx && activeStoryIdx === OverlapExtra.id) {
          const { outputPath, startTime, duration, endTime } = event;
          console.log('onFinishTrimming event:', event);
          const file = Platform.OS === 'ios' ? outputPath : `file://${outputPath}`;
          const calEndTime = (endTime - startTime) / 1000;
          const thumbnailUri =
            Platform.OS === 'ios'
              ? await generateVideoThumbnail(file)
              : await generateAndroidVideoThumbnail(file);
          const obj = {
            uri: file,
            thumbnailUri,
            startTime: 0,
            endTime: calEndTime,
            trimStartTime: 0,
            trimEndTime: calEndTime,
            videoDuration: calEndTime,
          };
          setImages((prevImages) =>
            prevImages.map((item) => (item.id === activeStoryIdx ? { ...item, ...obj } : item)),
          );
          setOverlapExtra((prevState) => ({ ...prevState, ...obj }));
        }
        break;
      }
      case 'onShow':
        setIsVideoTrimmingStarted(true);
        break;
      case 'onHide':
        setIsVideoTrimmingStarted(false);
        break;
      default:
        break;
    }
  };

  const handleIsDeletable = useCallback((id: any, type: any, bool: boolean) => {
    setIsDeleteable(bool);
    return bool;
  }, []);

  const deleteItem = useCallback(
    (id: any, type: any) => {
      setImages((prevImages) =>
        prevImages.map((image: any) => {
          if (image.id === activeStoryIdx) {
            let updatedImage = { ...image };
            if (type === 'sticker') {
              updatedImage.stickers = updatedImage.stickers.filter((item: any) => item.id !== id);
            } else if (type === 'image') {
              updatedImage.additionalImages = updatedImage.additionalImages.filter(
                (item: any) => item.id !== id,
              );
            } else if (type === 'text') {
              updatedImage.texts = updatedImage.texts.filter((item: any) => item.id !== id);
              setActiveColor(false);
            }
            setOverlapExtra(updatedImage);
            setIsPropDragging(false);
            setIsDeleteable(false);
            return updatedImage;
          }
          return image;
        }),
      );
    },
    [activeStoryIdx],
  );

  // const onClose = useCallback(async () => {
  //   await stopMusic();
  //   setSelectedAudio(null);
  //   setTempSelectedAudio(null);
  //   setSelectedMusicIndex(null);
  //   if (images.length > 0 && OverlapExtra.audio?.url && OverlapExtra?.type?.includes('image')) {
  //     await playStoryAudio(OverlapExtra.audio.url);
  //     await TrackPlayer.seekTo(previousPlayingPosition);
  //   }
  // }, [images, OverlapExtra, previousPlayingPosition]);

  // const onDone = useCallback(
  //   async (trimmedAudio) => {
  //     setIsModalVisible(false);
  //     setImages((prevImages) =>
  //       prevImages.map((item) =>
  //         item.id === activeStoryIdx
  //           ? {
  //               ...item,
  //               audio: { url: trimmedAudio },
  //               audioFullUrl: trimmedAudio,
  //               isSoundOn: true,
  //             }
  //           : item,
  //       ),
  //     );
  //     setOverlapExtra((prev) => ({
  //       ...prev,
  //       audio: { url: trimmedAudio },
  //       audioFullUrl: trimmedAudio,
  //       isSoundOn: true,
  //     }));
  //     setSelectedAudio(null);
  //     setTempSelectedAudio(null);
  //     setSelectedMusicIndex(null);
  //     if (images.length > 0 && OverlapExtra?.type?.includes('image')) {
  //       await playStoryAudio(trimmedAudio);
  //     }
  //   },
  //   [activeStoryIdx, images, OverlapExtra],
  // );

  // const onStartPlay = useCallback(() => {
  //   videoRef?.current?.seek(0);
  //   videoRef?.current?.resume();
  // }, []);

  // const memoizedAudio = useMemo(() => selectedAudio, [selectedAudio]);

  useEffect(() => {
    if (activeStoryIdx !== null) {
      const val = images.find((img: any) => img.id === activeStoryIdx);
      setOverlapExtra(val || {});
    }
  }, [images, activeStoryIdx]);

  useEffect(() => {
    const handleAppStateChange = (state) => {
      if (state === 'active') {
        setTimeout(() => setIsActive(true), 500);
      } else {
        setIsActive(false);
      }
    };
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription.remove();
  }, []);

  useEffect(() => {
    setCaptured(images.length > 0);
  }, [images.length]);

  const handleTabPress = useCallback((tabName: string) => setActiveTab(tabName), []);

  const getMaxZIndex = () => {
    if (Object.keys(OverlapExtra).length > 0) {
      const additionalImages = OverlapExtra?.additionalImages || [];
      const stickers = OverlapExtra?.stickers || [];
      const texts = OverlapExtra?.texts || [];
      const allZIndexes = [
        ...additionalImages.map((item) => item.zIndex ?? 0),
        ...stickers.map((item) => item.zIndex ?? 0),
        ...texts.map((item) => item.zIndex ?? 0),
      ].filter((z) => Number.isFinite(z));
      return allZIndexes.length > 0 ? Math.max(...allZIndexes) + 1 : 1;
    }
    return 1;
  };

  const onClickAa = () => {
    setTempText('');
    setActiveInput(true);
    setActiveColor(false);
    setActiveColorInput(null);
  };

  const onChangeText = (text: string) => setTempText(text);

  const saveText = () => {
    if (tempText) {
      const uid = new Date().getTime();
      let updatedImages = images.map((image) => {
        if (image.id === activeStoryIdx) {
          return {
            ...image,
            texts: activeColorInput
              ? image.texts.map((text) =>
                  text.id === activeColorInput ? { ...text, text: tempText } : text,
                )
              : [
                  ...image.texts,
                  {
                    id: uid,
                    text: tempText,
                    color: '#000',
                    fontFamily: 'DMSerifDisplay-Italic',
                    assType: 3,
                    initialX: 0,
                    initialY: 0,
                    zIndex: getMaxZIndex(),
                  },
                ],
          };
        }
        return image;
      });
      setImages(updatedImages);
      setActiveColorInput(activeColorInput || uid);
    }
    setTempText(null);
    setActiveInput(false);
  };

  const editText = (id: any, text: any) => {
    const activeImage = images.find((image: any) => image.id === activeStoryIdx);
    if (activeImage) {
      const existingText = activeImage.texts.find((item: any) => item.id === id);
      if (existingText) {
        setTempText(existingText.text);
        setActiveColor(true);
        setActiveInput(true);
        setActiveColorInput(existingText.id);
      }
    }
  };

  const HandleDoneClick = async () => {
    saveText();
    if (activeInput) {
      saveText();
      if (tempText) {
        setActiveColor(true);
      }
    } else if (activeColor) {
      setActiveColor(false);
      setActiveColorInput(null);
    } else {
      childRef.current?.handleOkay();
      setActiveTool(null);

      if (isFromMediaPreview && OverlapExtra) {
        let exportedPath: string | undefined;
        try {
          exportedPath = await canvasRef.current?.handleCapture?.();
        } catch (e) {
          console.log('Canvas export error:', e);
        }
        const normalizedPath = exportedPath ? exportedPath.replace('file://', '') : undefined;
        const editedMediaData = {
          ...route.params?.originalMsgData,
          uri: exportedPath || OverlapExtra.uri || route.params?.originalMsgData?.mediaUrl,
          mediaUrl: exportedPath || OverlapExtra.uri || route.params?.originalMsgData?.mediaUrl,
          path:
            normalizedPath ||
            (OverlapExtra.uri ? OverlapExtra.uri.replace('file://', '') : undefined) ||
            route.params?.originalMsgData?.mediaUrl?.replace?.('file://', ''),
          additionalImages: OverlapExtra.additionalImages || [],
          texts: OverlapExtra.texts || [],
          stickers: OverlapExtra.stickers || [],
          matrix: OverlapExtra.matrix || [],
          appliedFilter: OverlapExtra.appliedFilter || 'default',
          drawingPaths: OverlapExtra.path && OverlapExtra.path.length > 0 ? OverlapExtra.path : [],
          width: OverlapExtra?.width,
          height: OverlapExtra?.height,
        };
        console.log('editedMediaData', editedMediaData);
        navigateTo('MediaPreviewScreen', {
          msgData: editedMediaData,
          otherUserData: effectiveOtherUserData,
          isFromCameraScreen: true,
          handleSendMessage: effectiveHandleSendMessage,
        });
      }
    }
  };

  const openTrimmer = async (uri: string) => {
    try {
      await showEditor(uri, {
        type: 'video',
        maxDuration: 60,
        minDuration: 15,
        autoplay: true,
      } as any);
    } catch (error) {
      console.error('Error trimming video:', error);
      return null;
    }
  };

  // const renderMusicPlayer = useMemo(() => {
  //   if (!memoizedAudio || isVideoTrimmingStarted || images.length === 0) {
  //     return null;
  //   }

  //   return (
  //     <MusicPlayer
  //       audio={memoizedAudio}
  //       videoDuration={OverlapExtra?.endTime ?? 15} // Fallback to default duration if undefined
  //       videoCurrentTime={videoCurrentTime}
  //       onStartPlay={onStartPlay}
  //       onClose={onClose}
  //       onDone={onDone}
  //       resetMusicPlayer={resetMusicPlayer}
  //       isImage={OverlapExtra?.type?.includes('image') ?? false} // Fallback for safety
  //     />
  //   );
  // }, [
  //   memoizedAudio,
  //   isVideoTrimmingStarted,
  //   images.length,
  //   OverlapExtra?.endTime,
  //   OverlapExtra?.type,
  //   videoCurrentTime,
  //   onStartPlay,
  //   onClose,
  //   onDone,
  //   resetMusicPlayer,
  // ]);

  const pickImages = async () => {
    const wasPlaying = (await TrackPlayer.getState()) === State.Playing;
    let position = 0;
    if (wasPlaying) {
      position = await TrackPlayer.getPosition();
      setPreviousPlayingPosition(position);
      await TrackPlayer.pause();
    }

    const options: any = {
      mediaType: images.length === 0 ? 'mixed' : 'photo',
      selectionLimit: 4,
    };

    launchImageLibrary(options, async (response) => {
      if (response.didCancel || response.errorMessage) {
        if (
          wasPlaying &&
          images.length > 0 &&
          OverlapExtra.audio?.url &&
          OverlapExtra?.type?.includes('image')
        ) {
          await playStoryAudio(OverlapExtra.audio.url);
          await TrackPlayer.seekTo(position);
        } else if (
          wasPlaying &&
          images.length > 0 &&
          // selectedAudio?.url &&
          !OverlapExtra.audio?.url
        ) {
          // await playAudio(selectedAudio.url);
          await TrackPlayer.seekTo(position);
        }
        return;
      } else if (response.assets && response.assets.length > 0) {
        const tooLongVideo = response.assets.find(
          (asset) => asset.type?.startsWith('video') && asset.duration > 60,
        );
        const tooShortVideo = response.assets.find(
          (asset) => asset.type?.startsWith('video') && asset.duration < 15,
        );
        if (tooLongVideo) {
          Alert.alert('Video Too Long', 'Please select videos shorter than 1 minute.');
          return;
        }
        // if (tooShortVideo) {
        // Alert.alert(
        //   'Video Too Short',
        //   'Please select videos longer than 15 secs.',
        // );
        // return;
        // }

        const newImages = await Promise.all(
          response.assets.map(async (asset: any) => {
            const UniqueId = `${Date.now()}-${Math.random()}`;
            if (asset.type.startsWith('video')) {
              setIsVideoProcessing(true);
              const thumbnailUri =
                Platform.OS === 'ios'
                  ? await AudioTrimmer.generateVideoThumbnail(asset.uri)
                  : await generateAndroidVideoThumbnail(asset.uri);
              const timing = {
                startTime: 0,
                endTime: asset.duration,
                trimStartTime: 0,
                trimEndTime: asset.duration,
              };
              return {
                id: UniqueId,
                fileName: asset.fileName,
                fileSize: asset.fileSize,
                isMp3PlayerActive: false,
                height: asset.height,
                type: asset.type,
                videoDuration: asset.duration,
                uri: asset.uri,
                appliedFilter: 'default',
                thumbnailUri,
                width: asset.width,
                additionalImages: [],
                texts: [],
                stickers: [],
                matrix: [],
                path: [],
                audio: null,
                isSoundOn: true,
                isVideoLess: asset.duration <= 15,
                ...timing,
              };
            }
            return {
              id: UniqueId,
              fileName: asset.fileName,
              fileSize: asset.fileSize,
              isMp3PlayerActive: false,
              height: asset.height,
              type: asset.type,
              uri: asset.uri,
              width: asset.width,
              additionalImages: [],
              texts: [],
              stickers: [],
              matrix: [],
              path: [],
              audio: null,
            };
          }),
        );

        setImages((prevImages) => {
          // setSelectedAudio(null);
          if (prevImages.length === 0) {
            return [...newImages];
          } else {
            return prevImages.map((image: any) => {
              if (image.id === activeStoryIdx) {
                const updatedImage = {
                  ...image,
                  isMp3PlayerActive: false,
                  additionalImages: [
                    ...(image.additionalImages || []),
                    ...response.assets.map((asset: any) => ({
                      id: `${Date.now()}-${Math.random()}`,
                      fileName: asset.fileName,
                      uri: asset.uri,
                      initialX: 0,
                      initialY: 0,
                      zIndex: getMaxZIndex(),
                    })),
                  ],
                };
                setOverlapExtra(updatedImage);
                return updatedImage;
              }
              return image;
            });
          }
        });

        if (activeStoryIdx == null) {
          setIsVideoProcessing(false);
          setActiveStoryIdx(newImages[0]?.id);
          setOverlapExtra(newImages[0]);
        }
        setIsVideoProcessing(false);

        if (
          wasPlaying &&
          images.length > 0 &&
          OverlapExtra.audio?.url &&
          OverlapExtra?.type?.includes('image')
        ) {
          await playStoryAudio(OverlapExtra.audio.url);
          await TrackPlayer.seekTo(position);
        }
      }
    });
    setIsVideoProcessing(false);
  };

  const deleteImage = async (id: any) => {
    // await stopMusic();
    // await stopStoryAudio();
    // setSelectedAudio(null);
    // setTempSelectedAudio(null);
    setPreviousPlayingPosition(0);
    const filteredImages = images.filter((item: any) => item.id !== id);
    setOverlapExtra(filteredImages.length > 0 ? filteredImages[0] : {});
    setActiveStoryIdx(filteredImages.length > 0 ? filteredImages[0]?.id : null);
    setImages(filteredImages);
    setTrimStartTime(0);
    setTrimEndTime(0);
    setStartTime(0);
    setEndTime(0);
    setTempUri(null);
  };

  const TabHeaders = () => (
    <View style={styles.tabContainer}>
      {['Filters'].map((tab) => (
        <TouchableWithoutFeedback key={tab} onPress={() => handleTabPress(tab)}>
          <View style={styles.tab}>
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>{tab}</Text>
            {activeTab === tab && <View style={styles.customUnderline} />}
          </View>
        </TouchableWithoutFeedback>
      ))}
    </View>
  );

  // const handlePressMusic = async (item) => {
  //   try {
  //     await stopMusic();
  //     setSelectedAudio({ url: item.url });
  //     setIsModalVisible(false);
  //     if (images.length > 0) {
  //       await playAudio(item.url);
  //     }
  //   } catch (err) {
  //     console.error('Error handling music press:', err);
  //   }
  // };

  // useEffect(() => {
  //   if (!isModalVisible) {
  //     setSelectedMusicIndex(null);
  //   }
  // }, [isModalVisible]);

  const TabBody = () => (
    <View style={{ flex: 1, paddingHorizontal: 10, width: '100%' }}>
      <View style={{ marginTop: hp(2), alignItems: 'center' }}>
        {/* {activeTab === 'Music' ? (
            <Music
              MusicItm={arrMusic}
              onPressMusic={handlePressMusic}
              onPlayMusic={async (idx) => {
                if (selectedMusicIndex !== idx) {
                  setSelectedMusicIndex(idx);
                  setTempSelectedAudio(arrMusic[idx]);
                  await playAudio(arrMusic[idx].url);
                } else {
                  setSelectedMusicIndex(null);
                  setTempSelectedAudio(null);
                  await stopMusic();
                }
              }}
              selectedMusicIndex={selectedMusicIndex}
            />
          ) : */}
        {activeTab === 'Stickers' ? (
          <StickerList
            data={ArrStickers}
            onPress={(img) => {
              toggleModal();
              addStickerToImage(activeStoryIdx, img);
            }}
          />
        ) : (
          <FilterCanvas
            selectedImage={selectedImage() as any}
            setSelectedImage={(filterImage) => {
              setImages((prevImages) =>
                prevImages.map((image: any) => {
                  if (image.id === activeStoryIdx) {
                    const newMatrix = [...filterImage.matrix];
                    const matrixId = filterImage.id;
                    setOverlapExtra({
                      ...image,
                      matrix: newMatrix,
                      appliedFilter: matrixId,
                    });
                    return {
                      ...image,
                      matrix: newMatrix,
                      appliedFilter: matrixId,
                    };
                  }
                  return image;
                }),
              );
              toggleModal();
            }}
          />
        )}
      </View>
    </View>
  );

  const selectedImage = () =>
    images
      .filter((itm: any) => itm.id === activeStoryIdx)
      .map((itm: any) => {
        const isVideo = itm.type?.startsWith('video/');
        const thumbnailUri = Platform.select({
          android: isVideo ? `file://${itm.thumbnailUri}` : itm.uri,
          ios: isVideo ? itm.thumbnailUri : itm.uri,
          default: itm.uri,
        });
        return { ...itm, uri: thumbnailUri };
      });

  const addStickerToImage = (unqId: any, stickerObj: any) => {
    setImages((prevImages) => {
      const updatedImages = prevImages.map((image: any) => {
        if (image?.id === unqId) {
          const newStickers = [
            ...image.stickers.map((sticker: any) => ({ ...sticker })),
            {
              ...stickerObj,
              initialX: 0,
              initialY: 0,
              id: `${Date.now()}-${Math.random()}`,
              zIndex: getMaxZIndex(),
            },
          ];
          setOverlapExtra((prev) => ({ ...prev, stickers: newStickers }));
          return { ...image, stickers: newStickers };
        }
        return image;
      });
      return updatedImages;
    });
  };

  // const addMusicToImage = (unqId, audioPath) => {
  //   setImages((prevImages) => {
  //     const updatedImages = prevImages.map((image) => {
  //       if (image?.id === unqId) {
  //         setOverlapExtra((prev) => ({
  //           ...prev,
  //           audio: audioPath ? { url: audioPath } : null,
  //           audioFullUrl: audioPath,
  //         }));
  //         return {
  //           ...image,
  //           audio: audioPath ? { url: audioPath } : null,
  //           audioFullUrl: audioPath,
  //         };
  //       }
  //       return image;
  //     });
  //     return updatedImages;
  //   });
  // };

  const memoizedCanvasEditor = useMemo(
    () => (
      <CanvasEditor
        ref={canvasRef}
        images={OverlapExtra as any}
        backgroundImage={OverlapExtra?.uri as any}
        itemDragging={(v: boolean) => {
          setIsPropDragging(v);
          return v;
        }}
        type={OverlapExtra?.type?.includes('video') ? 'video' : 'image'}
        idkey={OverlapExtra?.id}
        isDrawing={isDrawing}
        deleteItem={deleteItem}
        isDeletable={handleIsDeletable}
        onTextPress={(id, text) => editText(id, text)}
        activeTextId={activeColorInput}
        setResetMusicPlayer={(n: number) => {
          setResetMusicPlayer(n);
          return n;
        }}
        // Drawing-related props
        paintBrushRef={childRef}
        paintData={activePaintData()}
        strokeWidth={strokeWidth}
        eraserSize={strokeWidth}
        paintClr={paintBrsClr}
        activeStoryIdx={activeStoryIdx as any}
        onDrawStateChange={(drawing) => setIsPaintOn(drawing)}
        capture={undefined}
        selectedFilter={[]}
      />
    ),
    [
      OverlapExtra,
      isDrawing,
      isPainton,
      activeStoryIdx,
      handleIsDeletable,
      deleteItem,
      activeColorInput,
      strokeWidth,
      paintBrsClr,
    ],
  );

  // console.log('second render')

  const shouldRenderCanvasEditor = useMemo(
    () => OverlapExtra && Object.keys(OverlapExtra).length > 0 && !isVideoTrimmingStarted,
    [OverlapExtra, isVideoTrimmingStarted],
  );

  // Camera selection removed from editor. Media should be provided via navigation params.

  const handleToolChange = useCallback(
    (newTool: any) => setActiveTool(newTool === activeTool ? null : newTool),
    [activeTool],
  );

  // TrackPlayer.addEventListener('remote-duck', async ({ paused, permanent }) => {
  //   if (paused && !permanent) {
  //     const state = await TrackPlayer.getState();
  //     if (state !== State.Playing) {
  //       await TrackPlayer.play();
  //     }
  //   }
  // });

  // useEffect(() => {
  //   async function setupPlayer() {
  //     const isInitialized = await TrackPlayer.isServiceRunning();
  //     if (!isInitialized) {
  //       await TrackPlayer.setupPlayer();
  //       await TrackPlayer.updateOptions({
  //         stopWithApp: false,
  //         capabilities: [Capability.Play, Capability.Pause, Capability.Stop],
  //         android: {
  //           ducking: true,
  //           alwaysPauseOnInterruption: false,
  //           appKilledPlaybackBehavior: AppKilledPlaybackBehavior.ContinuePlayback,
  //           staysActiveInBackground: true,
  //         },
  //         iosCategory: 'playback',
  //         iosCategoryOptions: ['mixWithOthers'],
  //       });
  //     }
  //   }
  //   setupPlayer();
  // }, []);

  // useEffect(() => {
  //   if (resetMusicPlayer){
  //     VideoSeekToFirst();
  //   }
  // }, [resetMusicPlayer])

  const VideoSeekToFirst = () => {
    videoRef?.current?.seek(0);
    videoRef?.current?.resume();
  };

  const waitForTrackDuration = async (maxWait = 6000) => {
    const interval = 300;
    const maxRetries = Math.floor(maxWait / interval);
    let retries = 0;
    let duration = 0;

    while (duration === 0 && retries < maxRetries) {
      await new Promise((res) => setTimeout(res, interval));
      duration = await TrackPlayer.getDuration();
      retries++;
    }
    return duration;
  };

  // async function uploadMedia() {
  //   setIsVideoProcessing(true);
  //   try {
  //     const state = await TrackPlayer.getState();
  //     let position = 0;
  //     if (state === State.Playing) {
  //       position = await TrackPlayer.getPosition();
  //       setPreviousPlayingPosition(position);
  //       await TrackPlayer.pause();
  //     }

  //     await stopMusic();
  //     setSelectedAudio(null);
  //     setTempSelectedAudio(null);

  //     const res = await DocumentPicker.pickSingle({
  //       type: [DocumentPicker.types.audio],
  //     });

  //     if (!res?.uri) {
  //       if (
  //         position &&
  //         images.length > 0 &&
  //         OverlapExtra.audio?.url &&
  //         OverlapExtra?.type?.includes('image')
  //       ) {
  //         await playStoryAudio(OverlapExtra.audio.url);
  //         await TrackPlayer.seekTo(position);
  //       }
  //       return;
  //     }

  //     const fileUri = res.uri;
  //     let localPath = fileUri;
  //     if (Platform.OS === 'android' && fileUri.startsWith('content://')) {
  //       const destPath = `${RNFS.CachesDirectoryPath}/audio-${Date.now()}.mp3`;
  //       await RNFS.copyFile(fileUri, destPath);
  //       localPath = destPath;
  //     }

  //     await TrackPlayer.reset();
  //     const track = {
  //       url: Platform.OS === 'ios' ? localPath : `file://${localPath}`,
  //     };
  //     await TrackPlayer.add(track);
  //     const duration = await waitForTrackDuration();
  //     console.log('duration is getting checked ...', duration);
  //     await TrackPlayer.reset();

  //     // if (duration < 30) {
  //     //   Alert.alert(
  //     //     'Invalid Duration',
  //     //     'Don`t allowed to upload audio under 1 minutes.'
  //     //   );
  //     //   return;
  //     // }
  //     if (
  //       position &&
  //       images.length > 0 &&
  //       OverlapExtra.audio?.url &&
  //       OverlapExtra?.type?.includes('image')
  //     ) {
  //       await playStoryAudio(OverlapExtra.audio.url);
  //       await TrackPlayer.seekTo(position);
  //     }

  //     const audioObj = { url: localPath };
  //     setSelectedAudio(audioObj);
  //     toggleModal();
  //     setIsModalVisible(false);

  //     if (images.length > 0) {
  //       await playAudio(localPath);
  //     }
  //   } catch (err) {
  //     if (DocumentPicker.isCancel(err)) {
  //       if (images.length > 0 && OverlapExtra.audio?.url && OverlapExtra?.type?.includes('image')) {
  //         await playStoryAudio(OverlapExtra.audio.url);
  //         await TrackPlayer.seekTo(previousPlayingPosition);
  //       }
  //     } else {
  //       console.error('Error picking audio:', err);
  //     }
  //   } finally {
  //     setIsVideoProcessing(false);
  //   }
  // }

  // useEffect(() => {
  //   const subscription = AppState.addEventListener('change', async (nextAppState) => {
  //     if (nextAppState !== 'active') {
  //       await TrackPlayer.pause();
  //     } else {
  //       await TrackPlayer.play();
  //     }
  //   });
  //   return () => subscription.remove();
  // }, []);

  return (
    <View style={{ flex: 1, backgroundColor: captured ? 'black' : 'black' }}>
      <StatusBar backgroundColor={'transparent'} translucent={true} />

      {!isPainton && (
        <>
          {images.length > 0 && (
            <PaintTools
              erase={childRef?.current?.ToggleEraseMode}
              undo={() => childRef?.current?.Undo()}
              redo={() => childRef?.current?.Redo()}
              paintTools={paintTools}
              activeTool={activeTool}
              actontools={ActionTools}
              setActiveTool={handleToolChange}
              setIsDrawing={setIsDrawing}
              isDrawing={isDrawing}
              isSoundOn={OverlapExtra?.isSoundOn}
              OverlapExtra={OverlapExtra}
              onSound={() => {
                if (activeStoryIdx === OverlapExtra.id) {
                  const updatedImages = images.map((item) => {
                    if (item.id === activeStoryIdx) {
                      return { ...item, isSoundOn: !item.isSoundOn };
                    }
                    return item;
                  });
                  setImages(updatedImages);
                  setOverlapExtra((prevState) => ({
                    ...prevState,
                    isSoundOn: !prevState.isSoundOn,
                  }));
                }
              }}
            />
          )}
          <Header
            selectedMusicIndex={selectedFilterIndex}
            captured={captured}
            activePaint={!!activeTool}
            toggelModal={toggleModal}
            onSet={() => HandleDoneClick()}
            isBackCamera={captured && !isDrawing}
            // isAudioSelected={Object.keys(OverlapExtra).length > 0 && OverlapExtra.audio?.url}
            // onClickMusic={() => {
            //   Alert.alert(
            //     'Confirmation',
            //     'Are you sure you want to Delete the selected song?',
            //     [
            //       { text: 'Cancel', style: 'cancel' },
            //       {
            //         text: 'Delete',
            //         onPress: async () => {
            //           await stopMusic();
            //           await stopStoryAudio();
            //           addMusicToImage(activeStoryIdx, null);
            //           const updatedImages = images.map((item) => {
            //             if (item.id === activeStoryIdx) {
            //               return {
            //                 ...item,
            //                 isSoundOn: true,
            //                 audio: null,
            //                 audioFullUrl: null,
            //                 isMp3PlayerActive: false,
            //               };
            //             }
            //             return item;
            //           });
            //           setImages(updatedImages);
            //           setOverlapExtra((prevState) => ({
            //             ...prevState,
            //             isSoundOn: true,
            //             audio: null,
            //             audioFullUrl: null,
            //             isMp3PlayerActive: false,
            //           }));
            //           // setSelectedAudio(null);
            //           setPreviousPlayingPosition(0);
            //         },
            //       },
            //     ],
            //     { cancelable: false },
            //   );
            // }}
          />
          {OverlapExtra?.type?.startsWith('video') && (
            <View
              style={{ position: 'absolute', top: 120, left: 25, zIndex: isDrawing ? 0 : 1000 }}
            >
              {/** Trimming icon disabled for now */}
              {/*
                {!OverlapExtra?.isVideoLess && (
                  <TouchableOpacity
                    style={[styles.paintIconPack, { marginTop: 10 }]}
                    onPress={async () => await openTrimmer(OverlapExtra.uri)}
                  >
                    <Image source={ImagesBundle.trim} tintColor={'white'} style={styles.toolIcon} />
                  </TouchableOpacity>
                )}
                */}
              {!OverlapExtra?.isMp3PlayerActive && (
                <TouchableOpacity
                  activeOpacity={OverlapExtra?.audioFullUrl ? 1 : 0.2}
                  style={[styles.paintIconPack, { marginTop: 10 }]}
                  onPress={() => {
                    if (!OverlapExtra?.audioFullUrl && activeStoryIdx === OverlapExtra.id) {
                      const updatedImages = images.map((item) => {
                        if (item.id === activeStoryIdx) {
                          return { ...item, isSoundOn: !item.isSoundOn };
                        }
                        return item;
                      });
                      setImages(updatedImages);
                      setOverlapExtra((prevState) => ({
                        ...prevState,
                        isSoundOn: !prevState.isSoundOn,
                      }));
                    }
                  }}
                >
                  <Image
                    source={OverlapExtra?.isSoundOn ? ImagesBundle.SoundOn : ImagesBundle.SoundOff}
                    tintColor={OverlapExtra?.audioFullUrl ? 'gray' : 'white'}
                    style={styles.toolIcon}
                  />
                </TouchableOpacity>
              )}
            </View>
          )}
        </>
      )}

      <View style={[styles.container]}>
        {images.length === 0 && (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>Select an image or video to get started</Text>
          </View>
        )}
        {isVideoProcessing && (
          <ActivityIndicator
            size={80}
            color="#fff"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: [{ translateX: -40 }, { translateY: -40 }],
            }}
          />
        )}
      </View>

      {activeInput && (
        <BlurView
          style={[StyleSheet.absoluteFill, { zIndex: 10003 }]}
          blurAmount={1}
          reducedTransparencyFallbackColor="white"
          overlayColor="transparent"
          blurType={!captured ? 'regular' : 'ultraThinMaterialDark'}
        >
          <View style={styles.overlay}>
            <TextInput
              placeholder="Type something here.."
              placeholderTextColor="gray"
              autoFocus
              style={[styles.textInput]}
              value={tempText ?? ''}
              onChangeText={(text) => onChangeText(text)}
            />
          </View>
        </BlurView>
      )}
      {!isPainton && ((activeColor && !activeInput) || isDrawing) && (
        <TextColorPicker
          isDrawing={isDrawing}
          strokeWidthFun={setStrokeWidth}
          strokeWidth={strokeWidth}
          onColorChange={setPaintBrsClr}
          activeTool={activeTool}
          onOkPress={() => {
            childRef.current?.handleOkay();
            setIsDrawing(false);
            setActiveTool(null);
          }}
          selectedTextId={activeColorInput}
          textStyles={images.reduce((acc, image) => {
            if (image.id === activeStoryIdx) {
              image.texts.forEach((textItem) => {
                acc[textItem.id] = {
                  color: textItem.color,
                  font: textItem.fontFamily,
                };
              });
            }
            return acc;
          }, {})}
          setTextStyles={(updatedStyles) => {
            setImages((prevImages) =>
              prevImages.map((image) => {
                if (image.id === activeStoryIdx) {
                  return {
                    ...image,
                    texts: image.texts.map((text) =>
                      text.id === activeColorInput
                        ? {
                            ...text,
                            color: updatedStyles[text.id]?.color || text.color,
                            fontFamily: updatedStyles[text.id]?.font || text.fontFamily,
                          }
                        : text,
                    ),
                  };
                }
                return image;
              }),
            );
          }}
        />
      )}

      {isPropDragging && <Deletebtn isDeleteable={isDeleteable} />}
      {shouldRenderCanvasEditor && memoizedCanvasEditor}

      {/* {memoizedAudio && !isVideoTrimmingStarted && images.length > 0 && renderMusicPlayer} */}

      {!activeColor && (
        <BlurView
          style={styles.blurView}
          blurAmount={100}
          reducedTransparencyFallbackColor="white"
          overlayColor="transparent"
          blurType={!captured ? 'regular' : 'ultraThinMaterialDark'}
        >
          {/* <AfterCapture
            images={images}
            activeStoryIdx={activeStoryIdx as any}
            deleteImage={deleteImage}
            isDrawing={isDrawing}
            setBackgroundImage={(item) => {
              setOverlapExtra(item);
              setActiveStoryIdx((item as any).id);
            }}
          /> */}
        </BlurView>
      )}
      {!isPainton && (
        <Footer
          activeColor={activeColor}
          AaTextstyle={(activeInput || activeColor) && styles.AaTextStyle}
          Aastyle={(activeInput || activeColor) && styles.AaStyle}
          onPressAa={onClickAa}
          onpresscamera={() => {}}
          onPressEdit={toggleModal}
          Camera={false}
          onPressProfile={pickImages}
          isActiveInput={activeInput}
          isDrawing={isDrawing}
        />
      )}
      <CustomModal visible={isModalVisible} onClose={toggleModal} onCloseHandlers={async () => {}}>
        <TabHeaders />
        {TabBody()}
      </CustomModal>
    </View>
  );
};

const PaintTools: React.FC<any> = React.memo(
  ({
    paintTools,
    activeTool,
    undo,
    redo,
    erase,
    setActiveTool,
    actontools,
    setIsDrawing,
    isDrawing,
    isSoundOn,
    OverlapExtra,
    onSound,
  }) => {
    const opacity = useSharedValue(0);
    const scale = useSharedValue(0.8);
    const isToolVisible = activeTool === paintTools[0]?.id || activeTool === paintTools[1]?.id;

    useEffect(() => {
      if (isToolVisible) {
        opacity.value = withTiming(1, { duration: 250 });
        scale.value = withTiming(1, { duration: 250 });
      } else {
        opacity.value = withTiming(0, { duration: 250 });
        scale.value = withTiming(0.8, { duration: 250 });
      }
    }, [isToolVisible]);

    const handleToolPress = (toolId: string) => {
      if (activeTool !== toolId) {
        setActiveTool(toolId);
        setIsDrawing(true);
        if (toolId === 'erase') erase(true);
        else if (toolId === 'paint') erase(false);
      }
    };

    const animatedStyles = useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [{ scale: scale.value }],
    }));

    return (
      <View style={[styles.painttoolwrapper, { zIndex: 10002 }]}>
        {paintTools.map((tool, index) => (
          <TouchableOpacity
            key={tool.id}
            style={[
              styles.paintIconPack,
              index > 0 && { marginTop: 10 },
              activeTool === tool.id && styles.activePaintIconPack,
            ]}
            onPress={() => handleToolPress(tool.id)}
          >
            <Image
              source={tool.icon}
              resizeMode="contain"
              tintColor={activeTool === tool.id ? 'black' : 'white'}
              style={styles.toolIcon}
            />
          </TouchableOpacity>
        ))}
        <Animated.View
          style={[{ marginTop: 10 }, animatedStyles]}
          pointerEvents={isToolVisible ? 'auto' : 'none'}
        >
          {actontools.map((tool, index) => (
            <TouchableOpacity
              key={tool.id}
              style={[
                styles.paintIconPack,
                index > 0 && { marginTop: 10 },
                activeTool === tool.id && styles.activePaintIconPack,
              ]}
              onPress={() => (tool.id === 'undo' ? undo() : redo())}
            >
              <Image
                source={tool.icon}
                tintColor={activeTool === tool.id ? 'black' : 'white'}
                style={styles.toolIcon}
              />
            </TouchableOpacity>
          ))}
        </Animated.View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: { flex: 1, zIndex: 100, justifyContent: 'space-between' },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  placeholderText: {
    fontSize: 18,
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  selectButton: {
    backgroundColor: '#6A4DBB',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  selectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  tabContainer: {
    height: hp(5),
    width: '80%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tab: { alignItems: 'center' },
  tabText: { fontSize: 16, color: '#000', marginBottom: 2 },
  activeTabText: { color: '#6A4DBB' },
  customUnderline: {
    height: 3,
    width: wp(7.5),
    backgroundColor: '#6A4DBB',
    borderRadius: 2,
  },
  blurView: {
    width: '100%',
    overflow: 'visible',
    backgroundColor: Platform.OS === 'android' ? '#FFFFFF33' : 'transparent',
    position: 'absolute',
    bottom: hp(7),
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    zIndex: 1001,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  textInput: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    color: 'white',
  },
  AaStyle: {
    backgroundColor: 'rgb(233, 231, 240)',
    opacity: 0.8,
    borderRadius: 70,
    padding: 5,
  },
  AaTextStyle: { color: '#6A4DBB' },
  paintIconPack: {
    height: 40,
    width: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activePaintIconPack: { backgroundColor: '#ffff' },
  painttoolwrapper: {
    height: '20%',
    width: '10%',
    position: 'absolute',
    right: 10,
    top: 140,
    marginRight: 12,
  },
});

const CameraScreen = ({ handleSendMessage, otherUserData }) => (
  <DataProvider>
    <TimeProvider>
      <CameraScreenContent handleSendMessage={handleSendMessage} otherUserData={otherUserData} />
    </TimeProvider>
  </DataProvider>
);

export default CameraScreen;
