import React from 'react';
import {
    View,
    Text,
    ImageBackground,
    Image,
    TouchableOpacity,
    StyleSheet,
} from 'react-native';
import { hp } from '../../utils/Constants/dimensionUtils';
import { ScrollView } from 'react-native-gesture-handler';

// Define the type for a single music item
interface MusicItem {
    image: any;
    musicName: string;
    views: string;
}

// Define the props for the MusicItem component
interface MusicItemProps {
    MusicItm: MusicItem[];
    selectedMusicIndex: number;
    onPlayMusic: (index: number) => void;
    onPressMusic: any;
}

const Music: React.FC<MusicItemProps> = ({
    MusicItm,
    selectedMusicIndex,
    onPlayMusic,
    onPressMusic
}) => {
    return (
        <ScrollView
            style={styles.container}
            contentContainerStyle={{ flexGrow: 1, gap: hp(1.5) }}
            showsVerticalScrollIndicator={false}
        >
            {MusicItm.map((item, idx) => {
                return (
                    <View key={idx} style={styles.musicRow}>
                        {/* Index Number */}
                        <View style={{ width: 30 }}>
                            <Text style={{ color: 'black', fontSize: 18 }}>{idx + 1}.</Text>
                        </View>

                        <TouchableOpacity style={[styles.musicRow,]} onPress={() => onPressMusic(item)}>

                            {/* Music Image */}
                            <ImageBackground
                                resizeMode="contain"
                                source={{ uri: item?.artwork }}
                                style={[styles.imageIcon, { marginRight: 10 }]}>
                                {selectedMusicIndex === idx && (
                                    <Image
                                        resizeMode="contain"
                                        source={require('../../Assets/Images/SongPlaying.png')}
                                        style={styles.songPlaying}
                                    />
                                )}
                            </ImageBackground>

                            {/* Music Details */}
                            <View style={styles.musicDetails}>
                                <Text
                                    style={[
                                        styles.musicName,
                                        {
                                            color:
                                                selectedMusicIndex === idx
                                                    ? '#6A4DBB'
                                                    : 'black',
                                        },
                                    ]}>
                                    {item?.title}
                                </Text>
                                <Text style={styles.views}>{'28,8950' + idx}</Text>
                            </View>

                        </TouchableOpacity>

                        {/* Play/Pause Button */}
                        {/* <View style={styles.playButtonContainer}> */}
                        <TouchableOpacity
                            style={styles.playButtonContainer}
                            onPress={() => onPlayMusic(idx)}>
                            <Image
                                resizeMode="contain"
                                source={
                                    selectedMusicIndex === idx
                                        ? require('../../Assets/Images/Pause.png')
                                        : require('../../Assets/Images/Play.png')
                                }
                                style={styles.playIcon}
                            />
                        </TouchableOpacity>
                        {/* </View> */}
                    </View>
                )
            })}
        </ScrollView>
    );
};

// Styles
const styles = StyleSheet.create({
    container: {
        width: '100%',
        paddingHorizontal: hp(1),
    },
    musicRow: {
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
    },
    imageIcon: {
        height: hp(5),
        width: hp(5),
    },
    songPlaying: {
        height: '100%',
        alignSelf: 'center',
    },
    musicDetails: {
        height: 60,
        justifyContent: 'space-between',
        paddingVertical: hp(1.2),
        width: '70%',
    },
    musicName: {
        fontSize: 16,
        fontWeight: '600',
    },
    views: {
        fontSize: 13,
        color: '#232323',
        fontWeight: '400',
    },
    playButtonContainer: {
        right: 22,
        padding: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    playIcon: {
        height: 15.43,
        width: 12,
        alignSelf: 'center',
    },
});

export default React.memo(Music);
