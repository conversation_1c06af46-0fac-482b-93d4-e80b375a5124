import { all } from 'axios';
import { useQuery } from '../realm';
import { ChatSpaceSchema } from '../schemas/ChatSpaceSchema';
import { ConversationType } from '../schemas/MessageSchema';
import { MembershipStatus } from '../../../types/chats.types';

const useChatspaces = () => {
  const allChatspaces = useQuery(ChatSpaceSchema);

  const allGroups = allChatspaces.filtered(
    'type == $0 AND membershipStatus IN $1',
    ConversationType.GROUP,
    [MembershipStatus.MEMBER, MembershipStatus.ADMIN, MembershipStatus.OWNER],
  );

  return {
    allGroups,
  };
};

export default useChatspaces;
