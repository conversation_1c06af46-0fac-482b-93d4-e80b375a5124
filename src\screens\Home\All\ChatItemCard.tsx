// components/ChatItemCard.tsx

import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ViewStyle } from 'react-native';
import { colors } from '../../../theme/colors';
import GallerySVG from '../../../assets/svgIcons/GallerySVG';
import { dayPipe } from '../../../utils/commonFunction';
import SelectionSVG from '../../../assets/svgIcons/SelectionSVG';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';
import { MessageType } from '../../../device-storage/realm/schemas/MessageSchema';
import { useMe } from '../../../hooks/util/useMe';
import MessageStatusIcon from '../../../component/PersonalChat/MessageStatusIcon';
import { IMAGES } from '../../../assets/Images';
import MuteSVG from '../../../assets/svgIcons/MuteSVG';
import PinSVG from '../../../assets/svgIcons/PinSVG';
import VideoCallSVG from '../../../assets/svgIcons/VideoCallSVG';
import MicSVG2 from '../../../assets/svgIcons/MicSVG2';
import ReplyAvatarSVG from '../../../assets/svgIcons/ReplyAvatarSVG';
import ReplyDocSVG from '../../../assets/svgIcons/DocReplySVG';
import LocationSVG2 from '../../../assets/svgIcons/LocationSVG2';

interface ChatItemCardProps {
  item: IConversation;
  selectedUser?: any[];
  isSelectionEnabled?: boolean;
  onSelect?: (value: any) => void;
  onLongSelect?: (value: any) => void;
  containerStyle?: ViewStyle;
  type: 'chat' | 'channel' | 'group';
  disableSelection?: boolean;
}

const ChatItemCard: React.FC<ChatItemCardProps> = ({
  type,
  item,
  selectedUser,
  containerStyle,
  isSelectionEnabled = false,
  onSelect = () => {},
  onLongSelect = () => {},
  disableSelection = false,
}) => {
  const userData = item;
  const me = useMe();

  const isSelected = selectedUser?.some((list: any) => item?.id === list?.id);

  const isMyLastMsg = item.lastMessage?.senderId === me.user?._id;

  const description =
    type === 'channel'
      ? item.isDeleted
        ? 'This channel was deleted by the owner'
        : ''
      : item.lastMessage?.text?.trim();
  const image = item.user?.profilePic || item.chatSpace?.displayPic || item.displayPic;

  return (
    <TouchableOpacity
      key={userData.id}
      activeOpacity={1}
      style={[
        styles.chatItem,
        containerStyle,
        { alignItems: isSelectionEnabled ? 'center' : 'flex-start' },
      ]}
      onPress={() => {
        onSelect(item);
      }}
      onLongPress={() => {
        if (!disableSelection) onLongSelect(item);
      }}
    >
      <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
        <View style={{ position: 'relative' }}>
          {image ? (
            <Image source={{ uri: image }} style={styles.profileImage} />
          ) : (
            <Image source={IMAGES.profile_image} style={styles.profileImage} />
          )}
          {/* {item?.isActive && (
            <View style={styles.onlineDot}>
              <View style={[styles.newDotView, { backgroundColor: colors._33C200_green }]} />
            </View>
          )} */}
        </View>

        <View style={styles.chatContent}>
          <Text
            numberOfLines={1}
            style={[
              styles.username,
              type === 'channel' && item.isDeleted && { color: colors._B1B1B1_gray },
            ]}
          >
            {item.displayName}
          </Text>

          {item.typingUsers?.length ? (
            <View>
              <Text style={styles.typingIndicator}>Typing...</Text>
            </View>
          ) : (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 5,
                marginTop: 5,
              }}
            >
              {item.lastMessage && isMyLastMsg && (
                <MessageStatusIcon status={item.lastMessage?.status} />
              )}
              {item?.lastMessage ? (
                item?.lastMessage?.messageType == MessageType.TEXT ? (
                  <Text
                    numberOfLines={1}
                    style={[
                      styles.lastMessage,
                      {
                        color:
                          item.lastMessage.senderId === me.user?._id
                            ? colors.gray_80
                            : item.lastMessage.isSeenByMe
                            ? colors.gray_80
                            : colors.mainPurple,
                      },
                    ]}
                  >
                    {item.lastMessage.text}
                  </Text>
                ) : item?.lastMessage?.messageType == MessageType.IMAGE ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <GallerySVG size={14} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Photo
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.DOCUMENT ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <ReplyDocSVG size={14} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Document
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.AUDIO ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <MicSVG2 size={14} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Audio
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.VOICE ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <MicSVG2 size={10} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Voice
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.VIDEO ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <VideoCallSVG size={13} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Video
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.CONTACT ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <ReplyAvatarSVG size={12} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Contact
                    </Text>
                  </View>
                ) : item?.lastMessage?.messageType == MessageType.LOCATION ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <LocationSVG2 size={12} color={colors.gray_80} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      Location
                    </Text>
                  </View>
                ) : null
              ) : type === 'channel' ? (
                <Text
                  numberOfLines={1}
                  style={[styles.lastMessage, item.isDeleted && { color: colors._B1B1B1_gray }]}
                >
                  {description}
                </Text>
              ) : item?.lastMessage?.messageType === MessageType.TEXT ? (
                <Text
                  numberOfLines={1}
                  style={[
                    styles.lastMessage,
                    {
                      color:
                        item.lastMessage.senderId === me.user?._id
                          ? colors.gray_80
                          : item.lastMessage.isSeenByMe
                          ? colors.gray_80
                          : colors.mainPurple,
                    },
                  ]}
                >
                  {item.lastMessage.text}
                </Text>
              ) : item?.lastMessage?.messageType == MessageType.IMAGE ? (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                  <GallerySVG size={14} color={colors.gray_80} />
                  <Text numberOfLines={1} style={styles.lastMessage}>
                    Photo
                  </Text>
                </View>
              ) : (
                <Text numberOfLines={1} style={styles.lastMessage}></Text>
              )}
            </View>
          )}
        </View>
      </View>

      {isSelectionEnabled ? (
        <SelectionSVG size={24} isSelected={isSelected} />
      ) : (
        <View style={{ alignItems: 'flex-end' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
            <Text
              style={{
                color: colors._B1B1B1_gray,
                fontSize: 13,
                marginTop: 5,
              }}
            >
              {dayPipe(item.lastMessageTimestamp || item.createdAt, 'diffCheck')}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 4,
              marginTop: 6,
            }}
          >
            {item?.conversationSettings?.muteUntil == 'always' && (
              <MuteSVG size={14} color={colors._B1B1B1_gray} style={{ marginRight: 5 }} />
            )}
            {item?.conversationSettings.isPinned && (
              <PinSVG size={14} color={colors._B1B1B1_gray} />
            )}
            {item.lastMessage &&
              item.lastMessage.senderId !== me.user?._id &&
              !item.lastMessage.isSeenByMe && <View style={[styles.newDotView]} />}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default React.memo(ChatItemCard);

const styles = StyleSheet.create({
  chatItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  chatContent: {
    flex: 1,
  },
  username: {
    color: colors.black,
    fontSize: 16,
    fontWeight: 'bold',
  },
  lastMessage: {
    color: colors.gray_80,
    fontSize: 14,
    fontWeight: '400',
    flexShrink: 1,
  },
  newDotView: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: colors.mainPurple,
  },
  onlineDot: {
    position: 'absolute',
    width: 14,
    height: 14,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 7,
    right: 11,
    bottom: 5,
  },
  pinIcon: {
    width: 16,
    height: 16,

    marginTop: 5,
    tintColor: colors.gray_80,
  },
  typingIndicator: {
    color: colors.mainPurple,
    marginTop: 5,
  },
});
