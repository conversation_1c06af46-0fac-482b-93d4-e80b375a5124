import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Linking, Pressable } from 'react-native';
import GradientView from '../../../component/GradientView';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import BackArrowSVG from '../../../assets/svgIcons/BackArrowSVG';
import { IChatScreenProps } from '../Chats/ChatSpecificScreen';
import QRCode from 'react-native-qrcode-svg';
import { joinChatSpace } from '../../../api/Chatspace/chatspace.api';
import CustomImage from '../../../component/CustomImage';
import { IMAGES } from '../../../assets/Images';
import { colors } from '../../../theme/colors';
import { Ionicons } from '../../../utils/vectorIcons';
import ShareGroupSVG from '../../../assets/svgIcons/ShareGroupSVG';

type GroupInviteScreenParams = {
  GroupInviteScreen: {
    userData: IChatScreenProps;
  };
};

const GroupInviteScreen = () => {
  const route = useRoute<RouteProp<GroupInviteScreenParams, 'GroupInviteScreen'>>();
  const userData = route.params?.userData;

  const navigation = useNavigation();
  const inviteLink = `https://chatspace.com/join/`;
  const inviteCode = userData?.conversation.inviteCode;

  const handleJoinGroup = async () => {
    if (!inviteCode) {
      console.error('Invite code is missing.');
      return;
    }
    try {
      const response = await joinChatSpace(userData.id, inviteCode);
      console.log('Successfully joined group:', response);
    } catch (error: any) {
      console.error('Join group failed:', error.message);
    }
  };

  return (
    <GradientView>
      <View style={styles.container}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backBtn} onPress={() => navigation.goBack()}>
          <BackArrowSVG />
        </TouchableOpacity>

        {/* Card Container */}
        <View style={styles.card}>
          {/* Group Image */}
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: userData.displayPic }}
              style={{ width: 50, height: 50, borderRadius: 25 }}
            />
          </View>

          {/* Title & Subtitle */}
          <Text style={styles.title}>{userData.displayName}</Text>
          <Text style={styles.subtitle}>Group invite</Text>

          {/* QR Code */}
          <View style={styles.qrContainer}>
            <QRCode
              value={JSON.stringify({ chatSpaceId: userData.id, inviteCode })}
              size={200}
              color="rgba(0, 0, 0, 1)"
            />
          </View>

          {/* Link Text */}
          <View style={styles.linkcontainer}>
            <Text style={styles.linkLabel}>Open this link to join:</Text>
            <Text style={styles.inviteLink} onPress={() => Linking.openURL(inviteLink)}>
              {inviteLink}
            </Text>
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <Pressable
              style={styles.copyBtn}
              onPress={() => {
                // Clipboard logic
              }}
            >
              <Ionicons name={'copy-outline'} size={24} color={colors.black} />
              <Text style={styles.copyText}> Copy</Text>
            </Pressable>
            <Pressable
              style={styles.shareBtn}
              onPress={() => {
                // Share logic
              }}
            >
              <ShareGroupSVG size={16} color={colors.gray_f3} />
              <Text style={styles.shareText}> Share</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </GradientView>
  );
};

export default GroupInviteScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  backBtn: {
    position: 'absolute',
    top: 65,
    left: 21,
    zIndex: 10,
    backgroundColor: 'rgba(122, 93, 203, 0.42)',
    padding: 10,
    borderRadius: 50,
  },
  backArrow: {
    color: 'rgba(255, 255, 255, 1)',
  },
  card: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    borderRadius: 15,
    width: '90%',
    paddingTop: 60,
    paddingBottom: 30,
    marginTop: 150,
    elevation: 5,
    position: 'relative',
  },
  imageWrapper: {
    position: 'absolute',
    top: -25,
    backgroundColor: 'rgba(255, 255, 255, 1)',
    padding: 4,
    borderRadius: 50,
    transform: [{ translateX: -30 }],
    zIndex: 1,
    left: '50%',
    borderWidth: 3,
    borderColor: 'rgba(106, 77, 187, 1)',
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 35,
  },
  title: {
    fontSize: 23,
    fontWeight: '700',
    color: 'rgba(35, 35, 35, 1)',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(35, 35, 35, 1)',
    textAlign: 'center',
    marginBottom: 10,
  },
  qrContainer: {
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  linkcontainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  linkLabel: {
    fontSize: 16,
    fontWeight: '400',
    color: 'rgba(35, 35, 35, 1)',
    textAlign: 'left',
  },
  inviteLink: {
    color: 'rgba(106, 77, 187, 1)',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'center',
  },
  copyBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(243, 243, 243, 1)',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 15,
  },
  copyText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(35, 35, 35, 1)',
  },
  shareBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(106, 77, 187, 1)',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 15,
  },
  shareText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 1)',
  },
});
