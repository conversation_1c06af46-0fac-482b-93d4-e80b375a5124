import React, { useState, useRef, useEffect, memo, useContext } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  Modal,
  Pressable,
  Platform,
  PermissionsAndroid,
  ToastAndroid,
} from 'react-native';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp, statusBarHeight } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import { useTranslation } from 'react-i18next';

import SearchInput from '../../component/SearchInput';
import { useNavigation } from '@react-navigation/native';
import { navigateTo } from '../../utils/commonFunction';
import { SCREENS } from '../../navigation/screenNames';
import { AppStyles } from '../../theme/appStyles';
import { CallType } from '../../types/calls.types';
import { useCallContext } from '../../Context/CallProvider';
import { useContacts } from '../../hooks/contacts/useContacts';

import useMultiSelectList from '../../hooks/util/useMultiselect';
import { useMe } from '../../hooks/util/useMe';
import useSocket from '../../socket-client/useSocket';
import GradientView from '../../component/GradientView';
import { FontAwesome6Icons } from '../../utils/vectorIcons';
import Toast from 'react-native-toast-message';
import { maxMembersInCall } from '../../utils/constants';
import useUsers from '../../device-storage/realm/hooks/useUsers';
import { IUser } from '../../device-storage/realm/schemas/UserSchema';

const NewCallScreen = ({}) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [selectedValue, setSelectedValue] = useState('contacts');
  const { user } = useMe();

  const { createFilteredContactsQuery } = useUsers();
  const newContacts = createFilteredContactsQuery()
    .exclude([user?.e164CompliantNumber || ''])
    .onlyUnblocked()
    .get();

  const [search, setSearch] = useState('');
  const [confirmCallorVideo, setConfirmCallorVideo] = useState(false);
  const [selectCallMethod, setSelectedCallMethod] = useState<CallType>('audio');
  const {
    selectedItems: selectedContacts,
    isSelected: isContactSelected,
    toggleSelection,
    filteredItems,
    resetSelection,
  } = useMultiSelectList([], (item) => item.id, newContacts, search);

  const { callDetails } = useCallContext();
  const showCallButtons = callDetails.state === 'idle';

  const renderContacts = (item: IUser, index: number) => {
    return (
      <TouchableOpacity
        style={[AppStyles.flex]}
        onPress={() => {
          if (selectedContacts.length >= maxMembersInCall && !isContactSelected(item)) {
            Toast.show({
              type: 'error',
              text1: `You can only select up to ${maxMembersInCall} contacts for a call.`,
            });
            return;
          }
          toggleSelection(item);
        }}
      >
        <View style={styles.rowView}>
          <View style={AppStyles.flex}>
            <Text numberOfLines={1} style={styles.title}>
              {item?.name || item?.username}
            </Text>

            <Text numberOfLines={1} style={styles.lastMessage}>
              {item?.phoneNumber}
            </Text>
          </View>
          {isContactSelected(item) ? (
            <Image source={IMAGES.selected} style={{ width: 25, height: 25 }} />
          ) : (
            <Image source={IMAGES.unSelect} style={{ width: 25, height: 25 }} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <GradientView>
      <SafeAreaView style={[{ flex: 1 }]}>
        <View
          style={{
            backgroundColor: 'transparent',
            paddingVertical: 10,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingTop: 40,
          }}
        >
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              marginLeft: 15,
            }}
          >
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={{ color: '#FFFFFF', marginLeft: 10, fontSize: 15 }}>New call to</Text>
          </View>

          {showCallButtons && (
            <View style={[styles.buttonContainer1, { right: 18 }]}>
              <TouchableOpacity
                activeOpacity={selectedContacts?.length == 0 ? 1 : 0}
                onPress={() => {
                  if (selectedContacts?.length > 0) {
                    setConfirmCallorVideo(true);
                    setSelectedCallMethod('audio');
                  } else {
                    ToastAndroid.show(
                      'Please select atleast one contact to proceed.',
                      ToastAndroid.SHORT,
                    );
                  }
                }}
              >
                <Image source={IMAGES.callImageWhite} style={styles.iconStyle} />
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={selectedContacts?.length == 0 ? 1 : 0}
                onPress={() => {
                  if (selectedContacts?.length > 0) {
                    setConfirmCallorVideo(true);
                    setSelectedCallMethod('video');
                  } else {
                    ToastAndroid.show(
                      'Please select atleast one contact to proceed.',
                      ToastAndroid.SHORT,
                    );
                  }
                }}
              >
                <Image source={IMAGES.videoImageWhite} style={styles.iconStyle} />
              </TouchableOpacity>
            </View>
          )}
        </View>
        <View
          style={{
            flex: 1,
            paddingHorizontal: 10,
            paddingTop: 10,
            borderTopEndRadius: 10,
            borderTopLeftRadius: 10,
            backgroundColor: 'white',
          }}
        >
          <SearchInput
            onChangeText={(text) => {
              setSearch(text);
            }}
            value={search}
          />
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TouchableOpacity
              style={{
                backgroundColor: selectedValue === 'contacts' ? '#f6f4ff' : '#FFF',
                borderRadius: 20,
                borderColor: selectedValue === 'contacts' ? '#6a4dbb' : '#ddd',
                borderWidth: 1,
                marginTop: 10,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => setSelectedValue('contacts')}
            >
              <Text
                style={{
                  paddingVertical: 6,
                  paddingHorizontal: 12,
                  color: selectedValue === 'contacts' ? '#6a4dbb' : 'black',
                  fontSize: 13,
                }}
              >
                Contacts
              </Text>
            </TouchableOpacity>
          </View>

          {selectedValue == 'contacts' && (
            <>
              <FlatList
                showsVerticalScrollIndicator={false}
                data={[...filteredItems]}
                style={{
                  flex: 1,
                }}
                renderItem={({ item, index }) => renderContacts(item, index)}
                ListHeaderComponent={() =>
                  filteredItems.length > 0 && (
                    <Text style={styles.titleText}>{t('Contacts on ChatBucket')}</Text>
                  )
                }
              />
            </>
          )}
        </View>
        <SelectCallTypeModal
          visible={confirmCallorVideo}
          onClose={() => setConfirmCallorVideo(false)}
          selectCallMethod={selectCallMethod}
          selectedContacts={selectedContacts}
          onStartCall={() => {
            resetSelection();
            setConfirmCallorVideo(false);
          }}
        />
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  callBgIcon: {
    position: 'absolute',
    height: 215,
    resizeMode: 'stretch',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  iconStyle: {
    width: 40,
    height: 40,
  },
  iconStyle1: {
    width: 24,
    height: 24,
  },
  lineIcon: {
    width: 23,
    height: 22,
    alignSelf: 'center',
    marginTop: 18,
  },
  endcallStyle: {
    width: 70,
    height: 70,
  },
  profileImageBg: {
    width: 105,
    height: 105,
    borderRadius: 52.5,
    marginBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.12)',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  name: {
    ...commonFontStyle(600, 20, colors.white),
    textAlign: 'center',
  },
  callStateText: {
    ...commonFontStyle(400, 16, colors.white),
    textAlign: 'center',
    marginTop: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  buttonContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  acceptButton: {
    backgroundColor: 'green',
    padding: 15,
    borderRadius: 50,
  },
  endButton: {
    backgroundColor: 'red',
    padding: 15,
    borderRadius: 50,
  },
  centerContent: {
    alignSelf: 'center',
    flex: 1,
    justifyContent: 'center',
  },

  header: {
    fontSize: 15,
    fontWeight: '500',
    marginVertical: 12,
  },
  subHeader: {
    fontSize: 18,
    marginTop: 20,
  },

  video: {
    width: '100%',
    height: 200, // You can adjust the size of the video based on your requirements
    backgroundColor: '#000', // Add background color to simulate video placeholder
  },
  audioContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioText: {
    color: '#fff',
    fontSize: 18,
  },
  titleText: {
    ...commonFontStyle(500, 16, colors.gray_80),
    paddingHorizontal: hp(2),
    marginTop: hp(2),
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(2),
    paddingVertical: hp(2),
    gap: 15,
  },
  userImage: {
    height: 50,
    width: 50,
    borderRadius: 50 / 2,
    resizeMode: 'cover',
  },
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  lastMessage: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: 3,
  },
  timeText: {
    ...commonFontStyle(400, 13, colors._B1B1B1_gray),
  },
  rowInner: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  onlineView: {
    backgroundColor: colors._33C200_green,
    borderWidth: 2.5,
    height: 13,
    width: 13,
    borderColor: colors.white,
    borderRadius: 13 / 2,
    position: 'absolute',
    right: 0,
    bottom: 0,
  },
  btnStyle: {
    backgroundColor: colors.opacity_main_purple_15,
    height: 35,
    borderRadius: 10,
  },
});

type SelectCallTypeModalProps = {
  visible: boolean;
  onClose: () => void;
  selectCallMethod: CallType;
  selectedContacts: IUser[];
  onStartCall: () => void;
};

function SelectCallTypeModal({
  visible,
  onClose,
  selectCallMethod,
  selectedContacts,
  onStartCall,
}: SelectCallTypeModalProps) {
  const { startCall } = useCallContext();

  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={() => {
        onClose();
      }}
    >
      <TouchableOpacity
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          //   alignItems: 'center',
          backgroundColor: '#00000060',
        }}
        activeOpacity={0.6}
        onPress={() => {
          onClose();
        }}
      >
        <View
          style={{
            // margin: 20,
            backgroundColor: 'white',
            borderTopEndRadius: 25,
            borderTopLeftRadius: 25,
            padding: 25,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
            width: '100%',
          }}
        >
          <Text
            style={{
              marginVertical: 15,
              marginLeft: 20,
              fontWeight: '500',
              color: 'black',
              fontSize: 16,
            }}
          >
            {selectCallMethod === 'audio' ? 'Audio' : 'Video'} call to {selectedContacts.length}{' '}
            people
          </Text>

          <View style={{ flexDirection: 'row', justifyContent: 'space-around' }}>
            {/* <Pressable
              onPress={() => {
                onClose(), navigateTo(SCREENS.ScheduleCallScreen);
              }}
              style={{
                width: '40%',
                height: 40,
                backgroundColor: '#F3F3F3',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                marginTop: 10,
                // borderWidth: 1,
                borderColor: '#F3F3F3',
              }}
            >
              <Text style={{ color: 'black', fontSize: 16, fontWeight: '500' }}>Schedule</Text>
            </Pressable> */}

            <Pressable
              onPress={async () => {
                onClose();
                const micPermission = await requestMicrophonePermission();
                if (micPermission !== 'granted') {
                  console.log('Microphone permission denied');
                  return;
                }
                if (selectCallMethod === 'video') {
                  let resp = await requestCameraPermission();
                  if (resp === 'granted') {
                    startCall({
                      callType: 'video',
                      recipients: selectedContacts.map((item) => item),
                      origin: { type: 'contacts' },
                    });
                    onStartCall();
                  } else {
                    console.log('Camera permission denied');
                  }
                } else {
                  startCall({
                    callType: 'audio',
                    recipients: selectedContacts.map((item) => item),
                    origin: { type: 'contacts' },
                  });
                  onStartCall();
                }
              }}
              style={{
                width: '40%',
                height: 40,
                backgroundColor: '#6a4dbb',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                marginTop: 10,
              }}
            >
              <Text style={{ color: '#FFF', fontSize: 16, fontWeight: '500' }}>Call now</Text>
            </Pressable>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

export default NewCallScreen;
const requestCameraPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, {
      title: 'Camera Permission',
      message: 'We need access to your camera to take photos and videos',
      buttonPositive: 'OK',
      buttonNegative: 'Cancel',
    });
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
    } else {
      console.log('Camera permission denied');
    }
    return granted;
  }

  if (Platform.OS === 'ios') {
  }
};

const requestMicrophonePermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, {
      title: 'Microphone Permission',
      message: 'We need access to your microphone to make calls',
      buttonPositive: 'OK',
      buttonNegative: 'Cancel',
    });
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
    } else {
      console.log('Microphone permission denied');
    }
    return granted;
  }

  if (Platform.OS === 'ios') {
  }
};
