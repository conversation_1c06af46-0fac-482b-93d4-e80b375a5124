import { nanoid } from 'nanoid/non-secure';
import { Realm } from 'realm';
import { getRealm } from './realm';

export const generateLocalMessageId = () => {
  return nanoid(21);
};

export const safeRealmWrite = <T>(realm: Realm, writeFn: () => T): T => {
  let _realm = realm;
  if (_realm.isClosed) {
    _realm = getRealm();
  }

  if (_realm.isInTransaction) {
    return writeFn();
  } else {
    let result!: T;
    _realm.write(() => {
      result = writeFn();
    });
    return result;
  }
};
