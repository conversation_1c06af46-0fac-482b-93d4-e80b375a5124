import * as React from "react";
import Svg, { Circle, Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    backgroundColor?: string;
    iconColor?: string;
}

const PauseSVG: React.FC<SvgComponentProps> = ({
    size = 30,
    backgroundColor = "#fff",
    iconColor = "#6A4DBB",
    ...props
}) => {


    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 40 40"
            fill="none"
            {...props}
        >
            <Circle cx={20} cy={20} r={20} fill={backgroundColor} />
            <Path
                d="M15 26.439V14M25 26.439V14"
                stroke={iconColor}
                strokeWidth={4}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </Svg>
    );
};

export default PauseSVG;




