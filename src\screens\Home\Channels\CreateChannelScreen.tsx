import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { createChannelSpaceApi } from '../../../service/ChatSpacesService';
import { ChatService } from '../../../service/ChatService';
import { ChatSpecificScreenParamsT } from '../Chats/ChatSpecificScreen';
import { resetAndNavigateTo, showToast } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import ChannelForm, { ChannelFormValues } from './Screens/ChannelForm';
import { updateChatspaceInfo } from '../../../api/Chatspace/chatspace.api';
import useConversationInfo from '../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../types/chats.types';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';

const CreateChannelScreen = () => {
  const navigation = useNavigation();
  const { params }: any = useRoute();
  const { isEdit, userDetails } = params || {};
  const id = isEdit ? userDetails : null;

  const convData = isEdit ? useConversationInfo(id) : null;
  const conversationInfo =
    convData && 'conversationInfo' in convData ? convData.conversationInfo : convData;

  // console.log('===(EDIT)conversationInfo', conversationInfo);

  const handleSubmit = async (values: ChannelFormValues) => {
    try {
      if (isEdit && conversationInfo) {
        const payload: any = {};
        if (values.name !== conversationInfo?.displayName) payload.name = values.name;
        if (values.description !== conversationInfo?.bio) payload.bio = values.description;
        if (values.image && values.image !== conversationInfo?.displayPic) {
          payload.file = {
            uri: values.image,
            type: 'image/jpeg',
            name: 'channel_image.jpg',
          };
        }

        if (Object.keys(payload).length > 0) {
          const result = await updateChatspaceInfo(id, payload);
          if (result?.systemMessage) {
            ChatService.updateChatSpace(result.systemMessage.eventPayload);
          }
        }
        showToast('Channel updated successfully');
        navigation.pop(2);
      } else {
        // Create channel logic
        let displayPic: any = null;
        if (values.image) {
          displayPic =
            typeof values.image === 'string'
              ? { uri: values.image, type: 'image/jpeg', name: 'channel_image.jpg' }
              : values.image;
        }

        const response = await createChannelSpaceApi({
          name: values.name,
          description: values.description,
          type: 'channel',
          displayPic,
        });

        if (response?.data) {
          const localChatSpace = ChatService.saveChatSpace(
            response.data?.chatSpace,
            MembershipStatus.OWNER,
            response.data?.systemMessage,
          );

          const chatScreenRouteParams: ChatSpecificScreenParamsT = {
            userData: {
              displayName: localChatSpace.name,
              displayPic: localChatSpace.displayPic,
              type: localChatSpace.type,
              chatSpaceId: localChatSpace.id,
              id: localChatSpace.id || '',
              conversation: {} as IConversation,
            },
            isFollowing: false,
            data: {
              convId: localChatSpace.id,
            },
          };

          // Navigate to chat screen
          // console.log(userData, '====>userdata(creatingchannel)<====');
          showToast('Channel created successfully');

          resetAndNavigateTo(SCREENS.ChatSpecificScreen, chatScreenRouteParams, true);
          return;
        }
      }
    } catch (err) {
      console.error('Error creating/updating channel:', err);
      showToast('Failed to create/update channel');
    } finally {
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{ flex: 1 }}
    >
      <ChannelForm
        isEditing={!!isEdit}
        initialValues={
          isEdit && conversationInfo
            ? {
                name: conversationInfo.displayName || '',
                description: conversationInfo.bio || '',
                image: conversationInfo.displayPic || null,
              }
            : undefined
        }
        onSubmit={handleSubmit}
      />
    </KeyboardAvoidingView>
  );
};

export default CreateChannelScreen;
