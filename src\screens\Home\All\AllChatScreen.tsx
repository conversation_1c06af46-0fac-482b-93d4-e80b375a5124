import React, { useCallback, useRef, useState, useEffect, useMemo } from 'react';
import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  ViewToken,
  Platform,
  ToastAndroid,
  Alert,
} from 'react-native';
import { colors } from '../../../theme/colors';
import SingleTickSVG from '../../../assets/svgIcons/SingleTickSVG';
import { dayPipe, navigateTo, showToast } from '../../../utils/commonFunction';
import DoubleTicksSVG from '../../../assets/svgIcons/DoubleTicksSVG';
import GallerySVG from '../../../assets/svgIcons/GallerySVG';
import { SwipeListView } from 'react-native-swipe-list-view';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import MenuSVG from '../../../assets/svgIcons/MenuSVG';
import ChatItemCard from './ChatItemCard';
import { useNavigation } from '@react-navigation/native';
import { SCREENS } from '../../../navigation/screenNames';
import useConversations from '../../../hooks/conversations/useConversations';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';
import RenderHiddenItem from '../components/RenderHiddenItem';
import { ChatService } from '../../../service/ChatService';
import { ChatScreenParams, IChatScreenProps } from '../Chats/ChatSpecificScreen';
import { deleteChatSpace } from '../../../api/Chatspace/chatspace.api';
import { ChannelType, ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import debounce from 'lodash.debounce';
import DeleteConfirmModal from '../../../DeleteModal';
import { notifEvents } from '../../../socket-client/notificationTypes';
import useSocket from '../../../socket-client/useSocket';
import useProfileViewability from '../../../lib/hooks/useProfileViewability';
import { MembershipStatus } from '../../../types/chats.types';
import { isConversationMuted } from '../../../lib/chatLib';

interface AllChatScreenProps {
  onSelectionChange?: (selectedUsers: any[]) => void;
  clearSelectionTrigger?: number;
  searchText?: string;
}

type ChatItemType = 'chat' | 'channel' | 'group';

const getChatItemType = (type: ConversationType): ChatItemType => {
  return type === 'p2p' ? 'chat' : type;
};

const AllChatScreen: React.FC<AllChatScreenProps> = ({
  onSelectionChange,
  clearSelectionTrigger,
  searchText,
}) => {
  const { socket } = useSocket();
  const [selectedUsers, setSelectedUsers] = useState<IConversation[]>([]);
  const [isSelectionEnabled, setIsSelectionEnabled] = useState<boolean>(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const { createViewableItemsHandler } = useProfileViewability();
  const onViewableConversationsChanged = createViewableItemsHandler('id');

  const [idsToDelete, setIdsToDelete] = useState<string[]>([]);

  const swipeListRef = useRef<any>(undefined);

  const { unArchivedConversations } = useConversations();

  const filteredData = useMemo(() => {
    if (!searchText?.trim()) return unArchivedConversations;
    const lower = searchText.toLowerCase();
    return unArchivedConversations.filter(
      (conv) =>
        conv.displayName?.toLowerCase().includes(lower) ||
        conv.lastMessage?.text?.toLowerCase().includes(lower) ||
        conv.phoneNumber?.toLowerCase().includes(lower),
    );
  }, [unArchivedConversations, searchText]);

  const updateSelection = (newSelectedUsers: any[]) => {
    setSelectedUsers(newSelectedUsers);
    onSelectionChange?.(newSelectedUsers);
  };

  useEffect(() => {
    setIsSelectionEnabled(false);
    updateSelection([]);
  }, [clearSelectionTrigger]);

  const closeRow = (rowMap: any, rowKey: any) => {
    if (rowMap[rowKey]) {
      rowMap[rowKey].closeRow();
    }
  };

  const renderItem: ListRenderItem<IConversation> = ({ item }) => {
    const isSelected = selectedUsers?.some((list: any) => item?.id === list?.id);
    return (
      <ChatItemCard
        type={getChatItemType(item.type)}
        item={item}
        selectedUser={selectedUsers}
        isSelectionEnabled={isSelectionEnabled}
        onSelect={onSelect}
        onLongSelect={onLongSelect}
        containerStyle={
          isSelected ? { backgroundColor: colors._F6F6F6_gray || '#F6F6F6' } : undefined
        }
      />
    );
  };

  const confirmDeletion = async () => {
    if (idsToDelete.length === 0) return;

    try {
      for (const id of idsToDelete) {
        const conversation = unArchivedConversations.find((c) => c.id === id);

        if (!conversation) continue;

        // Copy values BEFORE deleting from Realm
        const { id: convoId, type, chatSpace } = conversation;

        if (
          type === ConversationType.CHANNEL &&
          chatSpace?.membershipStatus === MembershipStatus.OWNER
        ) {
          try {
            await deleteChatSpace(convoId); // backend delete
            await ChatService.deleteConversation(convoId); // local Realm delete
            socket?.emit(notifEvents.chatSpaces.deleted, { chatSpaceId: convoId });
            showToast('Channel deleted successfully');
          } catch (err) {
            console.error('Failed to delete channel:', err);
            showToast('Failed to delete channel');
          }
        } else {
          ChatService.deleteConversation(convoId);
        }
      }

      setIsSelectionEnabled(false);
      updateSelection([]);
    } catch (error) {
      console.error('Error deleting chat(s):', error);
    } finally {
      setDeleteModalVisible(false);
      setIdsToDelete([]);
    }
  };

  const handleDeleteChat = (item: IConversation, rowMap: any) => {
    closeRow(rowMap, item.id);
    setIdsToDelete([item.id]);
    setDeleteModalVisible(true);
  };

  const renderHiddenItem = (list: { item: any; index: any }, rowMap: any) => {
    const { item } = list;
    const showDeleteButton = ConversationType.P2P === item.type;
    return (
      <RenderHiddenItem
        item={item}
        type="Home"
        onPressDeleteChat={() => handleDeleteChat(item, rowMap)}
        onPressIcon={(itemType) => onPressCardMenuItems(rowMap, item, itemType)}
        hideDeleteButton={!showDeleteButton}
      />
    );
  };

  const onPressCardMenuItems = (rowMap: any, item: IConversation, type: string) => {
    closeRow(rowMap, item?.id);

    if (type === 'ARCHIVE') {
      ChatService.toggleConversationArchive(item?.id);
    } else if (type == 'MUTE') {
      const isMuted = isConversationMuted(item.conversationSettings.muteUntil);
      ChatService.toggleConversationMute(item?.id, isMuted ? undefined : 'always');
    } else if (type == 'CLEAR') {
      closeRow(rowMap, item?.id);

      ChatService.clearChat(item?.id);
    } else if (type == 'LOCK') {
      closeRow(rowMap, item?.id);
    } else {
      ChatService.toggleConversationPin(item?.id);
    }
  };

  const onLongSelect = (user: any) => {
    if (selectedUsers?.length > 0) {
      onSelect(user);
    } else {
      setIsSelectionEnabled(true);
      updateSelection([user]);
    }
  };

  const onSelect = (user: IChatScreenProps) => {
    if (ConversationType.CHANNEL && user.isDeleted) {
      return;
    }
    const isUserExist = selectedUsers?.some((prev: any) => prev?.id == user?.id);

    if (selectedUsers.length >= 1) {
      let newSelected: any;
      if (isUserExist) {
        if (selectedUsers.length === 1) {
          setIsSelectionEnabled(false);
          newSelected = [];
        } else {
          newSelected = selectedUsers.filter((list: any) => list?.id !== user?.id);
          if (swipeListRef.current) {
            swipeListRef.current.closeAllOpenRows();
          }
        }
      } else {
        newSelected = [...selectedUsers, user];
      }
      updateSelection(newSelected);
    } else {
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
      const conversation = unArchivedConversations.find(
        (conv: IConversation) => conv.id === user.id,
      );

      if (!conversation) {
        console.warn('Conversation not found ,navigation cancelled.');
        return;
      }
      const userData: IChatScreenProps = {
        displayName: user.displayName,
        displayPic: user.displayPic,
        type: user.type,
        id: user.id,
        chatSpaceId: user.chatSpaceId,
        isActive: user.isActive,
        isDeleted: user.isDeleted,
        conversation: JSON.parse(JSON.stringify(conversation)),
      };

      const convParams: ChatScreenParams = { convId: user.id };
      navigateTo(SCREENS.ChatSpecificScreen, {
        userData: { ...userData, isFollowing: true },
        data: convParams,
      });
    }
  };

  return (
    <View style={styles.container}>
      {/* <FlatList
                data={chatData}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
            /> */}

      <SwipeListView
        ref={swipeListRef}
        data={filteredData}
        useFlatList={true}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderItem}
        renderHiddenItem={renderHiddenItem}
        rightOpenValue={-120}
        disableRightSwipe
        showsVerticalScrollIndicator={false}
        onViewableItemsChanged={onViewableConversationsChanged}
      />

      <DeleteConfirmModal
        isVisible={deleteModalVisible}
        onConfirm={confirmDeletion}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </View>
  );
};

export default AllChatScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
    // paddingVertical: 10,
    marginBottom: 60,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    // marginBottom: 20,
    backgroundColor: colors.white,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  chatContent: {
    flex: 1,
  },
  username: {
    color: colors.black,
    fontSize: 16,
    fontWeight: 'bold',
  },
  lastMessage: {
    color: colors.gray_80,
    fontSize: 14,
    fontWeight: '400',
    flexShrink: 1,
  },
  newDotView: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: colors.mainPurple,
  },
  onlineDot: {
    position: 'absolute',
    width: 14,
    height: 14,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 7,
    right: 11,
    bottom: 5,
  },

  rowBack: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    // backgroundColor: "skyblue"
  },
  deleteBtn: {
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors._F61B1B_red,
    // height: 40,
    height: '128%',
    // paddingVertical: 8,
  },
});
