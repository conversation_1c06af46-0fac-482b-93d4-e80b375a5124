import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import ModalWrapper from '../ModalWrapper';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import { IMAGES } from '../../assets/Images';
import RenderRadioButton from '../RenderRadioButton';
import SearchInput from '../SearchInput';
import ButtonPurple from '../ButtonPurple';
import { useTranslation } from 'react-i18next';
// import { useAppSelector } from "../../redux/hooks";
import RenderUserIcon from '../RenderUserIcon';
import { setAsyncStoryPrivacy, getAsyncStoryPrivacy } from '../../utils/asyncStorage';
import { STORY_PRIVACY } from '../../redux/actionTypes';
import SelectionSVG from '../../assets/svgIcons/SelectionSVG';
interface IProps {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
  setSelectedFriends?: (value: any[]) => void;
  setSelectedOption?: (value: any) => void;
  onDone?: (value: any) => void;
}

const StoryPostModal = ({
  isVisible,
  setIsVisible,
  setSelectedFriends = () => {},
  setSelectedOption = () => {},
  onDone,
}: IProps) => {
  const { t } = useTranslation();

  const [_isVisible, _setIsVisible] = useState(isVisible);

  const [heading, setHeading] = useState(t('Story Options'));

  const [selectOption, setSelectOption] = useState<any>(null);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const [value, setValue] = useState('');

  const [selectAll, setSelectAll] = useState(false);
  const [selectedList, setSelectedList] = useState<any[]>([]);
  const _tab = [
    {
      id: 1,
      title: t('Schedule'),
      image: IMAGES.time,
    },
    {
      id: 2,
      title: t('Privacy'),
      image: IMAGES.lock_profile,
    },
  ];

  const _tab2 = [
    {
      id: 1,
      title: t('Share all'),
    },
    {
      id: 2,
      title: t('Selected Friends'),
    },
  ];

  useEffect(() => {
    if (selectedList.length > 0) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
    setSelectedFriends(selectedList);
  }, [selectedList]);

  useEffect(() => {
    if (selectOption?.title === t('Schedule')) {
      setSelectedOption(selectOption);
      setSelectOption(null);
    }
  }, [selectOption]);

  useEffect(() => {
    _setIsVisible(isVisible);
  }, [isVisible]);

  const onClose = () => {
    _setIsVisible(false);
    setIsVisible(!_isVisible);
    resetData();
  };

  const resetData = () => {
    setSelectedList([]);
    setSelectAll(false);
    setSelectedIndex(0);
    setSelectOption(null);
  };

  const renderOptionList = ({ item, index }: { item: any; index: number }) => {
    return (
      <TouchableOpacity
        key={index}
        style={styles.renderListView}
        onPress={() => {
          if (selectOption?.title === t('Privacy')) {
            setSelectedIndex(index);
          } else {
            if (item.id === 2) {
              setHeading(t('Story Privacy'));
            }
            setSelectOption(item);
          }
        }}
      >
        {selectOption?.title === t('Privacy') ? (
          <RenderRadioButton value={selectedIndex === index} />
        ) : (
          <Image source={item.image} style={styles.imageIcon} resizeMode="contain" />
        )}
        <Text style={styles.title2}>{item.title}</Text>
      </TouchableOpacity>
    );
  };

  const toggleSelection = (id: number) => {
    setSelectedList((prevSelectedList) => {
      if (prevSelectedList.includes(id)) {
        return prevSelectedList.filter((i) => i !== id);
      } else {
        return [...prevSelectedList, id];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectAll) {
      // Deselect all
      setSelectedList([]);
      // dispatchAction(dispatch, STORY_PRIVACY, false);
      // setAsyncStoryPrivacy('false')
    } else {
      // Select all - assuming data is an array of numbers
      // const allIndices = chatHistory.map((_i: any) => _i?.opponentDetails?._id); // Select all indices
      // setSelectedList(allIndices);
    }
    setSelectAll(!selectAll);
  };

  const renderItem = ({ item, index }: { item: any; index: number }) => {
    return (
      <View style={styles.listContainer}>
        <RenderUserIcon url={item?.opponentDetails?.image} size={50} />
        <View style={{ flex: 1 }}>
          <Text style={styles.titleText}>{item?.opponentDetails?.name}</Text>
          <Text style={styles.decText}>{'🌟 Living life one moment at a time ✨.'}</Text>
        </View>
        <TouchableOpacity onPress={() => toggleSelection(item?.opponentDetails?._id)}>
          {selectedList?.includes(item?.opponentDetails?._id) ? (
            <SelectionSVG size={20} />
          ) : (
            <View style={styles.circleView} />
          )}
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
      <Text style={styles.title}>{heading}</Text>
      <View style={styles.lineView} />
      {selectedIndex === 1 ? (
        <>
          <View style={styles.renderListView}>
            <Text style={styles.selectFrdText}>
              {t('Select yor friends')}{' '}
              <Text style={styles.frdNText}>
                {/* {selectedList?.length + '/' + chatHistory?.length} */}
              </Text>
            </Text>
            <TouchableOpacity onPress={handleSelectAll}>
              <Text style={styles.selectText}>
                {selectAll ? t('Deselect all') : t('Select all')}
              </Text>
            </TouchableOpacity>
          </View>
          <SearchInput onChangeText={setValue} value={value} />
          <View style={{ height: hp(40) }}>
            {/* <FlatList
              data={chatHistory}
              renderItem={renderItem}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                padding: hp(1),
                gap: hp(1),
                flexGrow: 1,
              }}
            /> */}
          </View>
          <ButtonPurple
            title={t('Done')}
            disabled={selectedList.length === 0}
            // onPress={() => onDone(selectedList)}
          />
        </>
      ) : (
        (selectOption?.title === t('Privacy') ? _tab2 : _tab).map((item, index) => {
          return renderOptionList({ item, index });
        })
      )}
    </ModalWrapper>
  );
};

export default StoryPostModal;

const styles = StyleSheet.create({
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  lineView: {
    height: 1,
    backgroundColor: colors._DADADA_gray,
    marginVertical: hp(2),
  },
  renderListView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 10,
  },
  imageIcon: {
    height: 20,
    width: 20,
  },

  title2: {
    ...commonFontStyle(400, 16, colors.black_23),
  },

  selectFrdText: {
    ...commonFontStyle(400, 16, colors.black_23),
    flex: 1,
  },

  frdNText: {
    ...commonFontStyle(400, 14, colors.gray_80),
  },
  selectText: {
    ...commonFontStyle(500, 14, colors.mainPurple),
  },

  listContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  titleText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  decText: {
    ...commonFontStyle(400, 14, colors.black_23),
  },

  circleView: {
    height: 20,
    width: 20,
    borderRadius: 20,
    borderColor: colors.gray_80,
    borderWidth: 1.5,
  },
});
