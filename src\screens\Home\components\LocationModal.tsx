import {
  BackHandler,
  Image,
  Linking,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import ModalWrapper from '../../../component/ModalWrapper';
import { colors } from '../../../theme/colors';
import { hexToRgba, hp, SCREEN_HEIGHT, SCREEN_WIDTH } from '../../../theme/fonts';
import SelecteUnselectSVG from '../../../assets/svgIcons/SelecteUnselectSVG';
import MapView, { Callout, Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import ButtonPurple from '../../../component/ButtonPurple';
import LocationSVG2 from '../../../assets/svgIcons/LocationSVG2';
import { useIsFocused } from '@react-navigation/native';
import Geolocation from '@react-native-community/geolocation';
import ExpandSVG from '../../../assets/svgIcons/ExpandSVG';
import CurrentLocationSVG from '../../../assets/svgIcons/CurrentLocationSVG';
import { IMAGES } from '../../../assets/Images';
import SendSVG from '../../../assets/svgIcons/SendSVG';

interface IProps {
  isVisible: boolean;
  onCloseModal: () => void;
  onSendLocation: (location: object) => void;
}

const LocationModal = ({ isVisible = false, onCloseModal, onSendLocation = () => {} }: IProps) => {
  const [locationType, setLocationType] = useState<string>('current');
  const [pointMyLocation, setPointMyLocation] = useState<boolean>(false);
  const [region, setRegion] = useState<any>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [liveTimer, setLiveTimer] = useState<NodeJS.Timeout | null>(null);

  const [shareLive, setShareLive] = useState<boolean>(false);
  const [sharingTime, setSharingTime] = useState<string>('15_minutes');
  const [sharingMessage, setSharingMessage] = useState<string>('');

  // akhill states
  const [changeReg, setChangeReg] = useState<any>();

  const mapViewRef = React.useRef<MapView>(null);
  const isFocused = useIsFocused();

  //request for permmistion of maps
  const requestLocationPer = async () => {
    try {
      let granted = false;
      if (Number(Platform.Version) >= 33) {
        granted = true; // Assume 'granted' for newer versions if required logic applies
      } else {
        // Request multiple permissions for older Android versions
        const permissions = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
        ]);

        // Check the results of each permission
        granted =
          permissions['android.permission.ACCESS_FINE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_COARSE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_BACKGROUND_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED;
      }

      if (granted) {
        getCurrentLocationssss(); // Call this function if permission is granted
      } else {
        console.log('Location permission denied');
        // Optionally open settings if the permission is denied
        Linking.openSettings();
      }
    } catch (err) {
      console.log('Error requesting location permission:', err);
    }
  };

  const getCurrentLocationssss = () => {
    try {
      console.log('hooooo may god');
      Geolocation.getCurrentPosition(
        (info) => {
          console.log('Current position:', info);
          const { latitude, longitude } = info.coords;
          // console.log('map', latitude, longitude, info);/
          // 17.406137421670998, 78.49609025409312
          // const locData = {
          //     latitude: 17.406137421670998,
          //     longitude: 78.49609025409312,
          //     latitudeDelta: 0.001,
          //     longitudeDelta: 0.001,
          // }
          const locData = {
            latitude: Number(latitude),
            longitude: Number(longitude),
            latitudeDelta: 0.0001,
            longitudeDelta: 0.0001,
          };
          setRegion(locData);
          setTimeout(() => {
            gotoInitialPostion(locData);
          }, 100);
        },
        (error) => console.log(error),
        { enableHighAccuracy: false, timeout: 50000 },
      );
    } catch (error) {
      console.log('Catch block error:', error);
    }
  };

  // const startLiveLocationSharing = () => {
  //     const durationMap: Record<string, number> = {
  //         '15 minutes': 15 * 60 * 1000,
  //         '1_hour': 60 * 60 * 1000,
  //         '8_hours': 8 * 60 * 60 * 1000,
  //     };

  //     const intervalId = setInterval(() => {
  //         Geolocation.getCurrentPosition(
  //             position => {
  //                 const { latitude, longitude } = position.coords;
  //                 const locationData = {
  //                     latitude,
  //                     longitude,
  //                     message: sharingMessage || '',
  //                     timestamp: Date.now(),
  //                     type: 'live', // optional tag
  //                 };
  //                 console.log('Live location sent:', locationData);
  //                 onSendLocation(locationData);
  //             },
  //             error => {
  //                 console.log('Live location error:', error);
  //             },
  //             { enableHighAccuracy: true, timeout: 15000, maximumAge: 0 }
  //         );
  //     }, 5000); // every 5 seconds

  //     setLiveTimer(intervalId);

  //     // Stop live sharing after selected time
  //     setTimeout(() => {
  //         stopLiveLocationSharing();
  //         onCloseModal(); // auto close modal
  //     }, durationMap[sharingTime]);
  // };

  // startLiveLocationSharing()

  // const stopLiveLocationSharing = () => {
  //     if (liveTimer) {
  //         clearInterval(liveTimer);
  //         setLiveTimer(null);
  //         setShareLive(false);
  //     }
  // };

  const gotoInitialPostion = (values: any) => {
    const { latitude, longitude } = values;
    mapViewRef.current?.animateToRegion(
      {
        latitude: latitude,
        longitude: longitude,
        latitudeDelta: 0.0006,
        longitudeDelta: 0.0006,
      },
      1000,
    );
  };

  const onSendLiveLocation = () => {
    const now = new Date();
    const min15Later = new Date(now.getTime() + 15 * 60 * 1000).getTime();
    const hourLater = new Date(now.getTime() + 60 * 60 * 1000).getTime();
    const hours8Later = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000).getTime();
    const expiresAt =
      sharingTime == '15_minutes' ? min15Later : sharingTime == '1_hour' ? hourLater : hours8Later;

    onSendLocation({
      ...region,
      type: locationType,
      expiresAt: expiresAt,
      text: sharingMessage?.trim(),
    });
  };

  console.log(locationType === 'live' && shareLive);

  // useEffect(() => {
  //     const backHandler = BackHandler.addEventListener("hardwareBackPress", () => {
  //         if (locationType === 'live' && shareLive) {
  //             console.log("share location ---------------")
  //         }
  //         return true
  //     })

  //     return () => backHandler.remove();
  // }, [])

  useEffect(() => {
    if (isVisible) {
      console.log('permisstions from location modal');
      requestLocationPer();
    }
    return () => {
      console.log('empty region----------');
      // setRegion({})
    };
  }, [isVisible]);

  return (
    <ModalWrapper
      isVisible={isVisible}
      onCloseModal={onCloseModal}
      onModelShow={() => {
        // if (hasLocationPermission) {
        //     getCurrentPosition();
        // }
      }}
    >
      <View style={{ flexGrow: 1, paddingHorizontal: hp(1) }}>
        <Text style={{ fontSize: 16, fontWeight: '600', color: colors.black_23 }}>
          Share {shareLive ? 'live' : 'your'} location
        </Text>
        <View
          style={{ backgroundColor: hexToRgba(colors.black, 0.1), height: 1.5, marginTop: hp(2) }}
        />

        {shareLive ? (
          <View style={{}}>
            <View
              style={{
                marginTop: hp(3),
                marginBottom: hp(4),
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <TouchableOpacity
                style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                onPress={() => {
                  setSharingTime('15_minutes');
                }}
              >
                <SelecteUnselectSVG size={20} isSelected={sharingTime == '15_minutes'} />
                <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                  15 minutes
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                onPress={() => {
                  setSharingTime('1_hour');
                }}
              >
                <SelecteUnselectSVG size={20} isSelected={sharingTime == '1_hour'} />
                <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                  1 hour
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                onPress={() => {
                  setSharingTime('8_hours');
                }}
              >
                <SelecteUnselectSVG size={20} isSelected={sharingTime == '8_hours'} />
                <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                  8 hours
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ flex: 1, marginRight: 15 }}>
                <TextInput
                  value={sharingMessage}
                  placeholderTextColor={colors._B5B5B5_gray}
                  placeholder="Write your message"
                  onChangeText={setSharingMessage}
                  style={{
                    paddingHorizontal: hp(2),
                    padding: 0,
                    height: 50,
                    borderRadius: 15,
                    backgroundColor: colors.gray_f3,
                    color: colors.black_23,
                    fontSize: 16,
                    fontWeight: '400',
                  }}
                />
              </View>
              {/* <TouchableOpacity
                onPress={() => {
                  onCloseModal();
                }}
              > */}
              <TouchableOpacity onPress={onSendLiveLocation}>
                <SendSVG size={46} color={colors.white} backgroundColor={colors.mainPurple} />
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View>
            <View style={{ flexDirection: 'column', alignItems: 'center', marginTop: hp(3) }}>
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: hp(3),
                }}
              >
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                  onPress={() => {
                    setLocationType('current');
                  }}
                >
                  <SelecteUnselectSVG size={20} isSelected={locationType == 'current'} />
                  <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                    Current location
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                  onPress={() => {
                    setLocationType('live');
                  }}
                >
                  <SelecteUnselectSVG size={20} isSelected={locationType == 'live'} />
                  <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                    Live location
                  </Text>
                </TouchableOpacity>
              </View>

              {error && <Text style={{ color: 'red', marginBottom: 10 }}>{error}</Text>}

              <View style={{}}>
                <View
                  style={{
                    height: SCREEN_WIDTH - 38,
                    width: SCREEN_WIDTH - 38,
                    borderRadius: 10,
                    overflow: 'hidden',
                  }}
                >
                  {region?.latitude ? (
                    <MapView
                      ref={mapViewRef}
                      provider={PROVIDER_GOOGLE}
                      mapType="standard"
                      style={{
                        flex: 1,
                      }}
                      initialRegion={{
                        latitude: 17.385,
                        longitude: 78.4867,
                        latitudeDelta: 0.0922,
                        longitudeDelta: 0.0421,
                      }}
                      loadingEnabled={true}
                      loadingIndicatorColor="#ff0000"
                      loadingBackgroundColor="#eeeeee"
                      moveOnMarkerPress={false} // to show google navigation and maps
                      // showsUserLocation={true} // to showing blue dot from google maps
                      showsMyLocationButton={true}
                      showsCompass={true}
                      region={region}
                      // onRegionChangeComplete={(props) => { console.log("----- ===== -----", props) }}
                      // onRegionChange={(props) => { console.log("-------"); setChangeReg(props) }}
                    >
                      <Marker
                        title="You"
                        titleVisibility="adaptive"
                        onMagicTap={() => {
                          console.log('Magic tap');
                        }}
                        renderToHardwareTextureAndroid={true}
                        // anchor={{ x: 0.5, y: 0.5 }}
                        // image={require('../../../assets/locationPin.png')}
                        coordinate={{
                          latitude: region.latitude,
                          longitude: region.longitude,
                        }}
                        onDeselect={() => {
                          console.log('Marker deselected');
                        }}
                        onCalloutPress={() => {
                          console.log('Callout pressed');
                        }}
                      >
                        <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                          <Image
                            source={require('../../../assets/locationPin2.png')}
                            style={{ width: 20, height: 20 }} // Increased from 30x30 to 50x50
                            resizeMode="contain"
                          />
                        </View>
                      </Marker>
                    </MapView>
                  ) : (
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: hexToRgba(colors.black, 0.1),
                      }}
                    >
                      <Text>Loading map...</Text>
                    </View>
                  )}
                </View>
                <View
                  style={{ position: 'absolute', right: 0, marginTop: hp(2), marginRight: hp(2) }}
                >
                  <TouchableOpacity
                    style={{
                      elevation: 2,
                      height: 40,
                      width: 40,
                      borderRadius: 30,
                      backgroundColor: colors.white,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <ExpandSVG color={colors.black_23} size={14} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      gotoInitialPostion(region);
                    }}
                    style={{
                      marginTop: hp(2),
                      elevation: 2,
                      height: 40,
                      width: 40,
                      borderRadius: 30,
                      backgroundColor: colors.white,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <CurrentLocationSVG color={colors.black_23} size={19} />
                  </TouchableOpacity>
                </View>
              </View>

              <ButtonPurple
                extraStyle={{ marginVertical: hp(4), width: '100%' }}
                withRightIcon={false}
                titleColor={colors.white}
                onPress={() => {
                  if (locationType == 'live') {
                    setShareLive(true);
                  } else {
                    console.log('Get Verification Call Pressed');
                    // onCloseModal()
                    console.log('region----------', region);
                    onSendLocation(region);
                    // onSendLocation({ ...region, isLiveSharing: shareLive, liveCreated: null })
                  }
                }}
                leftSVG={<LocationSVG2 size={20} color={colors.white} />}
                title={'Send location'}
                disabled={!region}
              />
            </View>
          </View>
        )}
      </View>
    </ModalWrapper>
  );
};

export default LocationModal;

const styles = StyleSheet.create({});
