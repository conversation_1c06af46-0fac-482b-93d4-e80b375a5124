import {
  Alert,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  ToastAndroid,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';

import ButtonPurple from '../../component/ButtonPurple';
import { AppStyles } from '../../theme/appStyles';
import { useTranslation } from 'react-i18next';
import Input from '../../component/Input';
import { updateProfile } from '../../service/ProfileServices';
import { colors } from '../../theme/colors';
import { commonFontStyle, hp } from '../../theme/fonts';
import { useKeyboard } from '../../utils/useKeyboard';
import { errorToast, showToast, VALIDATION_RULES } from '../../utils/commonFunction';
import { useMe } from '../../hooks/util/useMe';
import Toast from 'react-native-toast-message';

type Props = {
  isEdit?: boolean;
  onPressUpdate: () => void;
  imageSource?: any;
  imageIndex?: any;
};

const ProfileTab = ({ onPressUpdate, isEdit, imageSource, imageIndex }: Props) => {
  const { user, updateUser } = useMe();

  const { t } = useTranslation();
  const { keyboardHeight } = useKeyboard();
  const [name, setName] = useState('');
  const [bio, setBio] = useState('');
  const [userName, setUserName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [email, setEmail] = useState('');
  const [validateUsername, setValidateUsername] = useState<any>(undefined);
  const [suggetionList, setsuggetionList] = useState('');

  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (user && isEdit === false) {
      setName(user?.name);
      setBio(user?.bio || '');
      setUserName(user?.username || '');
      setMobileNumber(user?.phoneNumber || '');
      setEmail(user?.email || '');
    }
  }, [user, isEdit]);

  const onSave = async () => {
    if (name.trim() === '') return errorToast('Please enter name');
    if (bio.trim() === '') return errorToast('Please enter bio');

    setIsUpdating(true);

    let imageData: any = null;

    if (imageSource) {
      imageData = {
        uri: imageSource.uri,
        type: imageSource.type || 'image/jpeg',
        name: imageSource.fileName || 'photo.jpg',
      };
    }

    try {
      const res = await updateProfile({
        name,
        bio,
        image: imageData,
      });
      const updatedUser = res?.data?.user;

      if (updatedUser) {
        updateUser(updatedUser);
        Toast.show({
          type: 'success',
          text1: t('Profile updated successfully'),
        });
        if (isEdit) {
          setName(updatedUser.name);
          setBio(updatedUser.bio);
        }
      } else {
        console.warn('⚠️ No user object returned from response');
      }

      onPressUpdate();
    } catch (e) {
      console.error('❗ Unhandled error during profile update:', e);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <View style={AppStyles.flex}>
      <ScrollView
        contentContainerStyle={{
          ...styles.container,
          paddingBottom: keyboardHeight + 20,
        }}
        showsVerticalScrollIndicator={false}
      >
        <Input
          title={t('Name')}
          value={name}
          editable={isEdit}
          extraStyle={styles.extraStyle}
          onChangeText={(text: string) => {
            if (text.length <= VALIDATION_RULES.NAME_LENGTH) {
              setName(text);
            } else {
              showToast(`Name cannot exceed ${VALIDATION_RULES.NAME_LENGTH} characters`);
            }
          }}
        />

        <Input
          title={t('Bio')}
          value={bio}
          editable={isEdit}
          extraStyle={styles.extraStyle}
          onChangeText={(text: string) => {
            if (text.length <= VALIDATION_RULES.BIO_LENGTH) {
              setBio(text);
            } else {
              showToast(`Bio cannot exceed ${VALIDATION_RULES.BIO_LENGTH} characters`);
            }
          }}
        />
        {/* <Input
          title={t("User Name")}
          value={userName}
          editable={isEdit}
          extraStyle={styles.extraStyle}
          onChangeText={setUserName}
        /> */}
        <Input
          value={userName}
          extraStyle={styles.extraStyle}
          editable={false}
          placeHolder={t('Enter username')}
          title={t('User Name')}
          onChangeText={setUserName}

          // RenderRightIcon={() => {
          //   return isEdit ==
          //     false ? null : !userName ? null : validateUsername == true ? (
          //     <Image
          //       source={IMAGES.wrong}
          //       style={AppStyles.rightIconTextInput}
          //     />
          //   ) : (
          //     <Image
          //       source={IMAGES.right}
          //       style={AppStyles.rightIconTextInput}
          //     />
          //   );
          // }}
        />

        {/* {isEdit === false
          ? null
          : validateUsername == false && (
              <View style={styles.notAvailableView}>
                <Text style={styles.titleRed}>
                  {t("Username not available")}
                </Text>
                <Text style={styles.titleTextAvailable}>
                  {t("Try for :")} {suggetionList.replace(",", ", ")}
                </Text>
              </View>
            )} */}
        {email ? (
          <Input
            title={t('Email')}
            value={email}
            editable={false}
            extraStyle={styles.extraStyle}
            onChangeText={setEmail}
          />
        ) : null}
        <Input
          title={t('Mobile number')}
          value={mobileNumber}
          editable={false}
          extraStyle={styles.extraStyle}
          onChangeText={setMobileNumber}
        />
      </ScrollView>

      <View style={styles.btnContainer}>
        {isEdit && (
          <ButtonPurple
            title={t('Update')}
            onPress={() => {
              onSave();
            }}
            isLoading={isUpdating}
            disabled={isUpdating}
          />
        )}
      </View>
    </View>
  );
};

export default ProfileTab;

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  btnContainer: {
    marginBottom: 20,
  },
  extraStyle: {
    marginTop: 10,
  },
  notAvailableView: {
    backgroundColor: colors._FFF4F4_red,
    borderRadius: 15,
    padding: hp(2),
    marginVertical: hp(2),
  },
  titleRed: {
    ...commonFontStyle(500, 16, colors._DB1515_red),
  },
  titleTextAvailable: {
    ...commonFontStyle(400, 16, colors.black_23),
    marginTop: 5,
  },
});
