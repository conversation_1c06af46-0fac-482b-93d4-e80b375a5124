import {
  ChatSpaceSchema,
  IChatSpace,
  RemoteChatSpace,
} from '../device-storage/realm/schemas/ChatSpaceSchema';
import { IConversation } from '../device-storage/realm/schemas/ConversationSchema';
import { ConversationType } from '../device-storage/realm/schemas/MessageSchema';
import { IUser, UserSchema } from '../device-storage/realm/schemas/UserSchema';
import { ChatPermissions, MembershipStatus } from '../types/chats.types';
import { RemoteUser } from '../types/index.types';
import { ServerMessage } from '../types/socketPayload.type';
import { getDateInMillies } from './lib';

// Default permissions helper
export const getDefaultChatPermissions = (
  status: MembershipStatus,
  type: ConversationType,
): ChatPermissions => {
  const basePermissions: ChatPermissions = {
    canSend: {
      text: false,
      media: false,
    },
    canAddMembers: false,
    canRemoveMembers: false,
    canEditChatSpaceInfo: false,
    canPinMessages: false,
    canDeleteMessages: false,
  };

  if (type === ConversationType.GROUP) {
    switch (status) {
      case MembershipStatus.OWNER:
        return {
          ...basePermissions,
          canSend: { text: true, media: true },
          canAddMembers: true,
          canRemoveMembers: true,
          canEditChatSpaceInfo: true,
          canPinMessages: true,
          canDeleteMessages: true,
        };
      case MembershipStatus.ADMIN:
        return {
          ...basePermissions,
          canSend: { text: true, media: true },
          canAddMembers: true,
          canRemoveMembers: true,
          canPinMessages: true,
          canDeleteMessages: true,
          // cannot edit chat info (kept for owner only)
        };
      case MembershipStatus.MEMBER:
        return {
          ...basePermissions,
          canSend: { text: true, media: true },
        };
      default:
        return basePermissions;
    }
  }

  if (type === ConversationType.CHANNEL) {
    switch (status) {
      case MembershipStatus.OWNER:
        return {
          ...basePermissions,
          canSend: { text: true, media: true },
          canAddMembers: true,
          canRemoveMembers: true,
          canEditChatSpaceInfo: true,
          canPinMessages: true,
          canDeleteMessages: true,
        };
      case MembershipStatus.ADMIN:
        return {
          ...basePermissions,
          canSend: { text: true, media: true },
          canAddMembers: true,
          canRemoveMembers: true,
          canPinMessages: true,
          canDeleteMessages: true,
          // cannot edit channel info
        };
      case MembershipStatus.MEMBER:
        return {
          ...basePermissions,
          // read-only like Telegram channels
        };
      default:
        return basePermissions;
    }
  }

  return basePermissions;
};

export const isChatSpace = (id: string) => {
  return id.startsWith('cs');
};

export const getChatSpaceType = (id: string): ConversationType.GROUP | ConversationType.CHANNEL => {
  return id.startsWith('csg') ? ConversationType.GROUP : ConversationType.CHANNEL;
};

export const getConversationIdFromMessage = (message: ServerMessage, myId: string) => {
  const isChatSpaceMessage = isChatSpace(message.receiverId);
  if (isChatSpaceMessage) {
    return message.receiverId;
  } else {
    return message.receiverId === myId ? message.senderId : message.receiverId;
  }
};

export const getConversationDataFromLocalChatSpace = (
  localChatSpace: ChatSpaceSchema,
): IConversation => {
  return {
    id: localChatSpace.id,
    displayName: localChatSpace.name,
    displayPic: localChatSpace.displayPic,
    type: localChatSpace.type,
    unreadCount: 0,
    createdAt: localChatSpace.createdAt,
    updatedAt: localChatSpace.updatedAt,
    lastMessageTimestamp: Date.now(),
    conversationSettings: localChatSpace.conversationSettings,
    chatSpace: localChatSpace,
  };
};

export const getConversationDataFromLocalUser = (localUser: UserSchema): IConversation => {
  return {
    id: localUser.id,
    displayName: localUser.contactName || localUser.name,
    displayPic: localUser.profilePic,
    type: ConversationType.P2P,
    unreadCount: 0,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    lastMessageTimestamp: Date.now(),
    conversationSettings: localUser.conversationSettings,
    user: localUser,
  };
};

export const isConversationMuted = (dateString: string | undefined) => {
  if (!dateString) return false;
  if (dateString === 'always') return true;
  const muteDate = getDateInMillies(dateString);
  if (!muteDate) return false;
  return muteDate > Date.now();
};

export const getMuteUntilString = (period?: string): string | undefined => {
  if (!period) return undefined;

  if (period === 'always') {
    return 'always';
  }

  // Parse format: <number>_<unit>
  const match = period.match(/^(\d+)_(min|day|week|months|years)$/);
  if (!match) return undefined;

  const [, numberStr, unit] = match;
  const number = parseInt(numberStr, 10);

  if (isNaN(number) || number <= 0) return undefined;

  let millisecondsToAdd: number;

  switch (unit) {
    case 'min':
      millisecondsToAdd = number * 60 * 1000;
      break;
    case 'day':
      millisecondsToAdd = number * 24 * 60 * 60 * 1000;
      break;
    case 'week':
      millisecondsToAdd = number * 7 * 24 * 60 * 60 * 1000;
      break;
    case 'months':
      millisecondsToAdd = number * 30 * 24 * 60 * 60 * 1000; // Approximate 30 days per month
      break;
    case 'years':
      millisecondsToAdd = number * 365 * 24 * 60 * 60 * 1000; // Approximate 365 days per year
      break;
    default:
      return undefined;
  }

  return (Date.now() + millisecondsToAdd).toString();
};
