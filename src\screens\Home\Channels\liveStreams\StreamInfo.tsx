import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';

import EyeOutlineSvg from '../../../../assets/svgIcons/EyeOutlineSvg';
import { colors } from '../../../../theme/colors';

import { ChatSpace } from '../../../../types/socketPayload.type';
import { IStreamState, UserRole } from '../../../../hooks/channels/useLivestreamSpace';

interface ChatSpaceStreamInfoProps {
  onStopLive: () => void;
  chatspace: ChatSpace | null;
  streamInfo: IStreamState;
}
// Streamer Info Component
const ChatspaceStreamInfo: React.FC<ChatSpaceStreamInfoProps> = ({
  onStopLive,
  chatspace,
  streamInfo,
}) => {
  return (
    <View style={StreamerInfoStyles.streamerInfo}>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',

          gap: 15,
        }}
      >
        <Image
          source={{ uri: chatspace?.displayPic || 'https://i.pravatar.cc/40?img=5' }}
          style={StreamerInfoStyles.streamerAvatar}
        />
        <View style={StreamerInfoStyles.streamerDetails}>
          <Text style={StreamerInfoStyles.streamerName}>{chatspace?.name}</Text>
          <Text style={StreamerInfoStyles.streamerStats}>{chatspace?.memberCount} followers</Text>
        </View>

        {streamInfo.userRole === UserRole.STREAMER && (
          <TouchableOpacity onPress={onStopLive} style={StreamerInfoStyles.stopLiveButton}>
            <Text style={StreamerInfoStyles.stopLiveText}>Stop live</Text>
          </TouchableOpacity>
        )}
        {streamInfo.userRole === UserRole.VIEWER && (
          <TouchableOpacity
            disabled={streamInfo.isFollowing}
            onPress={() => {
              console.log('follow channel clicked');
            }}
            style={StreamerInfoStyles.followChannelButton}
          >
            <Text style={StreamerInfoStyles.stopLiveText}>
              {streamInfo.isFollowing ? 'Following' : 'Follow'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={StreamerInfoStyles.viewersCount}>
        <EyeOutlineSvg />
        <Text style={StreamerInfoStyles.viewersCountText}>{streamInfo.viewCount}</Text>
      </View>
    </View>
  );
};

export default ChatspaceStreamInfo;

export const StreamerInfoStyles = StyleSheet.create({
  streamerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.black,
  },
  streamerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  streamerDetails: {},
  streamerName: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  streamerStats: {
    color: colors.gray_cc,
    fontSize: 12,
  },
  stopLiveButton: {
    backgroundColor: colors.red_ff4444,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 12,
  },
  stopLiveText: {
    color: colors.white,
    fontSize: 15,
    fontWeight: '600',
  },
  followChannelButton: {
    backgroundColor: colors.overlayWhite_10,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 12,
  },

  viewersCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  viewersCountText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
});
