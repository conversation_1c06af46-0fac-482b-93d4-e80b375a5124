import React, { useState } from 'react';
import { View, TextInput, Button, TouchableOpacity, Dimensions } from 'react-native';
import { Canvas, Text as SkiaText, useFont } from '@shopify/react-native-skia';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

export default function TextEditor() {
  const font = useFont(require('../Assets/Fonts/BadScript-Regular.ttf'), 40);
  const [texts, setTexts] = useState([]);
  const [selectedTextId, setSelectedTextId] = useState(null);
  const [inputText, setInputText] = useState('');

  // Function to Add a New Text Layer
  const addText = () => {
    setTexts([
      ...texts,
      {
        id: Date.now(),
        text: 'New Text',
        x: width / 2,
        y: height / 2,
        color: 'black',
        scale: 1,
        rotate: 0,
      },
    ]);
  };

  // Function to Update Selected Text
  const updateText = (text) => {
    setTexts((prevTexts) => prevTexts.map((t) => (t.id === selectedTextId ? { ...t, text } : t)));
  };

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      {/* Canvas for Multiple Texts */}
      <Canvas style={{ flex: 1 }}>
        {font &&
          texts.map((item) => (
            <DraggableText
              key={item.id}
              textData={item}
              font={font}
              onSelect={() => setSelectedTextId(item.id)}
            />
          ))}
      </Canvas>

      {/* Input Box to Edit Selected Text */}
      {selectedTextId && (
        <TextInput
          value={texts.find((t) => t.id === selectedTextId)?.text || ''}
          onChangeText={updateText}
          style={{
            position: 'absolute',
            bottom: 50,
            left: 20,
            right: 20,
            height: 40,
            backgroundColor: '#fff',
            borderRadius: 8,
            paddingHorizontal: 10,
            fontSize: 18,
            borderWidth: 1,
            borderColor: '#ccc',
          }}
        />
      )}

      {/* Button to Add New Text */}
      <TouchableOpacity
        onPress={addText}
        style={{
          position: 'absolute',
          bottom: 100,
          right: 20,
          backgroundColor: 'blue',
          padding: 10,
          borderRadius: 8,
        }}
      >
        <Button title="Add Text" onPress={addText} color="white" />
      </TouchableOpacity>
    </View>
  );
}

// Draggable Text Component
const DraggableText = ({ textData, font, onSelect }) => {
  const translateX = useSharedValue(textData.x);
  const translateY = useSharedValue(textData.y);
  const scale = useSharedValue(textData.scale);
  const rotate = useSharedValue(textData.rotate);

  // Drag Gesture
  const dragGesture = Gesture.Pan()
    .onUpdate((e) => {
      translateX.value = e.translationX;
      translateY.value = e.translationY;
    })
    .onEnd(() => {
      translateX.value = withSpring(translateX.value);
      translateY.value = withSpring(translateY.value);
    });

  // Scale Gesture
  const pinchGesture = Gesture.Pinch().onUpdate((e) => {
    scale.value = e.scale;
  });

  // Rotation Gesture
  const rotateGesture = Gesture.Rotation().onUpdate((e) => {
    rotate.value = e.rotation;
  });

  // Apply gestures
  const gestures = Gesture.Simultaneous(dragGesture, pinchGesture, rotateGesture);

  // Animated Styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
      { rotate: `${rotate.value}rad` },
    ],
  }));

  return (
    <GestureDetector gesture={gestures}>
      <SkiaText
        text={textData.text}
        x={textData.x}
        y={textData.y}
        font={font}
        color={textData.color}
        onTouchStart={onSelect}
      />
    </GestureDetector>
  );
};
