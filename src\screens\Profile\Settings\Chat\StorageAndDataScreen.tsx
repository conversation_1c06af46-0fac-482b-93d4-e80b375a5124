import React from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';

const StorageAndDataScreen = () => {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Storage and Data" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>{/* Content will go here */}</View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
    paddingHorizontal: hp(2),
  },
});

export default StorageAndDataScreen;
