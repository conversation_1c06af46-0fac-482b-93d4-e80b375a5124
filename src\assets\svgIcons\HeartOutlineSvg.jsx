import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function HeartOutlineSvg(props) {
  return (
    <Svg
      width={26}
      height={23}
      viewBox="0 0 26 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M18.296 1.013c1.873 0 3.599.738 4.855 2.079 1.242 1.325 1.924 3.133 1.924 5.09 0 2.018-.766 3.861-2.398 5.798-1.458 1.73-3.551 3.485-5.967 5.51-.826.692-1.762 1.476-2.733 2.312a1.498 1.498 0 01-1.954 0c-.969-.834-1.903-1.617-2.728-2.308v0l-.005-.004v0c-2.416-2.025-4.51-3.78-5.967-5.51C1.691 12.043.925 10.2.925 8.182c0-1.957.683-3.765 1.925-5.09 1.256-1.34 2.981-2.08 4.855-2.08 1.402 0 2.686.438 3.816 1.297.544.414 1.04.917 1.479 1.5a7.682 7.682 0 011.48-1.5 6.177 6.177 0 013.816-1.296zm0 1.533a4.64 4.64 0 00-2.866.975c-.76.579-1.292 1.312-1.604 1.826a.958.958 0 01-.826.462.958.958 0 01-.826-.462c-.312-.514-.843-1.247-1.604-1.826a4.638 4.638 0 00-2.865-.975c-1.436 0-2.754.563-3.713 1.586C3.02 5.17 2.48 6.607 2.48 8.182c0 1.656.626 3.141 2.04 4.82 1.37 1.625 3.409 3.335 5.778 5.32l.005.004v.001c.814.682 1.736 1.454 2.695 2.278.965-.826 1.889-1.598 2.704-2.282 2.37-1.986 4.409-3.695 5.778-5.321 1.415-1.679 2.04-3.164 2.04-4.82 0-1.575-.539-3.012-1.512-4.05-.959-1.023-2.276-1.586-3.712-1.586z"
        fill={ props.fill ? props.fill : "#fff"}
        stroke="#fff"
        strokeWidth={0.15}
         
      />
    </Svg>
  );
}

export default HeartOutlineSvg;
