import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface ReplyDocSVGProps {
    size?: number;
    color?: string;
}

const ReplyDocSVG: React.FC<ReplyDocSVGProps> = ({
    size = 13,
    color = "#232323",
    ...props
}) => {
    const width = (size * 11) / 13;
    const height = size;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 11 13"
            fill="none"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.31 11.046a1.623 1.623 0 01-1.62 1.62H1.62A1.623 1.623 0 010 11.047V1.62A1.623 1.623 0 011.62 0H6.68c.273 0 .535.108.728.302l2.6 2.6c.195.194.303.456.303.73v7.414zm-.884 0V3.633a.148.148 0 00-.043-.105l-2.6-2.6a.148.148 0 00-.104-.043H1.62a.738.738 0 00-.736.736v9.426a.738.738 0 00.736.737h7.07a.738.738 0 00.736-.736z"
                fill={color}
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6.48.737a.442.442 0 01.884 0v2.062c0 .***************.147h2.062a.442.442 0 010 .884H7.511a1.03 1.03 0 01-1.03-1.031V.737zM2.797 5.892a.442.442 0 010-.884h4.714a.442.442 0 010 .884H2.797zM2.797 7.954a.442.442 0 010-.884h4.714a.442.442 0 010 .884H2.797zM2.797 10.016a.442.442 0 010-.884h2.651a.442.442 0 010 .884h-2.65z"
                fill={color}
            />
        </Svg>
    );
};

export default ReplyDocSVG;