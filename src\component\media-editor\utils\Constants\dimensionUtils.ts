import { Dimensions, PixelRatio } from "react-native";
let { width: ScreenWidth, height: ScreenHeight } = Dimensions.get("window");

const widthPercentageToDP = (widthPercent: number) => {
  const elemWidth = typeof widthPercent === "number" ? widthPercent : parseFloat(widthPercent);
  return PixelRatio.roundToNearestPixel(ScreenWidth * widthPercent / 100);
}

const heightPercentageToDP = (heightPercent: number) => {
  const elemHeight = typeof heightPercent === "number" ? heightPercent : parseFloat(heightPercent);
  return PixelRatio.roundToNearestPixel(ScreenHeight * heightPercent / 100);
}

const listenOrientationChange = (that: any) => {
  Dimensions.addEventListener("change", newDimensions => {
    ScreenWidth = newDimensions.window.width;
    ScreenHeight = newDimensions.window.height;
    that.setState({
      orientation: ScreenWidth < ScreenHeight ? "portrait" : "landscape"
    });
  });
}

export { widthPercentageToDP as wp, heightPercentageToDP as hp, listenOrientationChange, ScreenHeight, ScreenWidth };

