import {
  ViewStyle,
  Image,
  StyleProp,
  Pressable,
  StyleSheet,
  useWindowDimensions,
} from 'react-native';
import React, { FC, useEffect, useState } from 'react';
import { Canvas, Path, Image as SkiaImage, useImage, Mask } from '@shopify/react-native-skia';

import { useData } from '../utils/Context';

const shapes = ['square', 'star', 'hexagon', 'triangle', 'clover', 'butterfly'];

interface FilterImgCanvasProps {
  images: any;
  cnvStyle?: StyleProp<ViewStyle>;
  index: number;
}

const FilterImgCanvas: FC<FilterImgCanvasProps> = ({ images }) => {
  const { setMemorizedPosition } = useData();
  const [shapeIndex, setShapeIndex] = useState(images?.shape || 0);
  const [imgSize, setImgSize] = useState({ width: 100, height: 100 });

  const image = useImage(images?.uri);

  useEffect(() => {
    if (!images?.uri) return;
    Image.getSize(
      images?.uri,
      (imgWidth, imgHeight) => {
        const scale = Math.min(200 / imgWidth, 200 / imgHeight);
        setImgSize({ width: imgWidth * scale, height: imgHeight * scale });
      },
      (error) => console.error('Failed to get image size:', error),
    );
  }, [images?.uri]);

  useEffect(() => {
    setMemorizedPosition({ type: 'shape', id: images?.id, shape: shapeIndex });
  }, [shapeIndex]);

  if (!image || imgSize.width === 0 || imgSize.height === 0) return null;

  const nextShape = () => setShapeIndex((prev) => (prev + 1) % shapes.length);

  const getClipPath = () => {
    if (shapeIndex == null) return '';
    const minSize = Math.min(imgSize.width, imgSize.height);
    const centerX = imgSize.width / 2;
    const centerY = imgSize.height / 2;
    const radius = minSize * 0.5;

    switch (shapes[shapeIndex]) {
      case 'star':
        return `M ${centerX},${centerY - radius} 
                        L ${centerX + radius * 0.38},${centerY - radius * 0.38} 
                        L ${centerX + radius},${centerY - radius * 0.15} 
                        L ${centerX + radius * 0.5},${centerY + radius * 0.2} 
                        L ${centerX + radius * 0.62},${centerY + radius * 0.8} 
                        L ${centerX},${centerY + radius * 0.5} 
                        L ${centerX - radius * 0.62},${centerY + radius * 0.8} 
                        L ${centerX - radius * 0.5},${centerY + radius * 0.2} 
                        L ${centerX - radius},${centerY - radius * 0.15} 
                        L ${centerX - radius * 0.38},${centerY - radius * 0.38} Z`;
      case 'hexagon':
        return `M ${centerX},${centerY - radius} L ${centerX + radius},${centerY - radius / 2} L ${
          centerX + radius
        },${centerY + radius / 2} L ${centerX},${centerY + radius} L ${centerX - radius},${
          centerY + radius / 2
        } L ${centerX - radius},${centerY - radius / 2} Z`;
      case 'triangle':
        return `M ${centerX},${centerY - radius} L ${centerX + radius},${centerY + radius} L ${
          centerX - radius
        },${centerY + radius} Z`;
      case 'clover':
        return `M ${centerX},${centerY - radius} C ${centerX + radius},${centerY - radius} ${
          centerX + radius
        },${centerY + radius} ${centerX},${centerY + radius} C ${centerX - radius},${
          centerY + radius
        } ${centerX - radius},${centerY - radius} ${centerX},${centerY - radius} Z`;
      case 'butterfly':
        return `M ${centerX},${centerY - radius} L ${centerX + radius * 0.38},${
          centerY - radius * 0.38
        } L ${centerX + radius},${centerY} L ${centerX + radius * 0.38},${
          centerY + radius * 0.38
        } L ${centerX},${centerY + radius} L ${centerX - radius * 0.38},${
          centerY + radius * 0.38
        } L ${centerX - radius},${centerY} L ${centerX - radius * 0.38},${
          centerY - radius * 0.38
        } Z`;
      default:
        return '';
    }
  };

  return (
    <Pressable
      onPress={nextShape}
      style={[styles.pressableStyle, { width: imgSize.width, height: imgSize.height }]}
    >
      <Canvas style={{ width: imgSize.width, height: imgSize.height, position: 'absolute' }}>
        {['star', 'hexagon', 'triangle', 'clover', 'butterfly'].includes(shapes[shapeIndex]) &&
        image ? (
          <Mask mask={<Path path={getClipPath()} color="red" />}>
            <SkiaImage
              x={0}
              y={0}
              width={imgSize.width}
              height={imgSize.height}
              image={image}
              fit="contain"
            />
          </Mask>
        ) : (
          <SkiaImage
            x={0}
            y={0}
            width={imgSize.width}
            height={imgSize.height}
            image={image}
            fit="contain"
          />
        )}
      </Canvas>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  pressableStyle: {
    overflow: 'hidden',
    alignSelf: 'center',
  },
});

export default FilterImgCanvas;
