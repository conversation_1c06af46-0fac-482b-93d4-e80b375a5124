import React, { memo, useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Image, Platform } from 'react-native';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedProps,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import ColorPicker, { Swatches, HueSlider } from 'reanimated-color-picker';
import { BlurView } from '@react-native-community/blur';
import { Images } from '../Assets/Images';
import { PanGestureHandler, GestureHandlerGestureEvent } from 'react-native-gesture-handler';
import Svg, { Path } from 'react-native-svg';

const AnimatedPath = Animated.createAnimatedComponent(Path);

const SLIDER_WIDTH = 132;
const KNOB_RADIUS = 10;
const SLIDER_START = KNOB_RADIUS;
const SLIDER_END = SLIDER_WIDTH - KNOB_RADIUS;

type PaintColorPickerProps = {
  initialColor?: string;
  initialBrushSize?: number;
  onColorChange?: (color: string) => void;
  isBrushSelected: boolean;
  isPaintActive: boolean;
  strokeWidthFun: (stock: number) => void;
};

const PaintColorPicker: React.FC<PaintColorPickerProps> = ({
  initialColor = '#CC4F49',
  initialBrushSize = 10,
  onColorChange,
  isBrushSelected = true,
  isPaintActive = true,
  strokeWidthFun,
}) => {
  const [selectedColor, setSelectedColor] = useState<string>(initialColor);
  const [colorSelected, setColorSelected] = useState(false);
  const brushSize = useSharedValue(initialBrushSize);
  const ColorsArr1 = ['#CC4F49', '#309dba'];

  const onColorSelect = (color: any) => {
    setSelectedColor(color.hex);
    onColorChange?.(color.hex);
  };

  const handleColorComplete = (color: string) => {
    setSelectedColor(color);
    onColorChange?.(color);
  };

  const handleOkPress = () => {
    setColorSelected(false);
  };

  const Slider = memo(() => {
    const translateX = useSharedValue(SLIDER_WIDTH / 2);

    const gestureHandler = useAnimatedGestureHandler<
      GestureHandlerGestureEvent,
      { startX: number }
    >({
      onStart: (_, ctx) => {
        ctx.startX = translateX.value;
      },
      onActive: (event, ctx) => {
        translateX.value = Math.min(
          Math.max(ctx.startX + event.translationX, SLIDER_START),
          SLIDER_END,
        );
      },
      onEnd: () => {
        translateX.value = withSpring(translateX.value);
        runOnJS(strokeWidthFun)(translateX.value * 0.08);
      },
    });

    const animatedKnobStyle = useAnimatedStyle(() => ({
      transform: [{ translateX: translateX.value - KNOB_RADIUS }],
    }));

    const derivedPath = useDerivedValue(() => {
      const adjustedWidth = translateX.value;
      return `M0 8L${adjustedWidth} 0.321A5 5 0 0 1 ${
        adjustedWidth + (SLIDER_WIDTH - adjustedWidth) * 0.04
      } 5.312v1.534a5 5 0 0 1-${(SLIDER_WIDTH - adjustedWidth) * 0.0001} 3.998z`;
    });

    const animatedProps = useAnimatedProps(() => ({
      d: derivedPath.value,
    }));

    return (
      <View style={styles.container}>
        <Svg width={SLIDER_WIDTH} height={12}>
          <Path d="M126.698.321A5 5 0 0 1 132 5.312v1.534a5 5 0 0 1-5.151 4.998L0 8z" fill="gray" />
          <AnimatedPath animatedProps={animatedProps} fill="#fff" />
        </Svg>

        <PanGestureHandler onGestureEvent={gestureHandler}>
          <Animated.View style={[styles.knob, animatedKnobStyle]} />
        </PanGestureHandler>
      </View>
    );
  });

  return isPaintActive ? (
    <BlurView
      style={styles.blurView}
      blurAmount={1}
      overlayColor="transparent"
      reducedTransparencyFallbackColor="white"
      blurType="ultraThinMaterialDark"
    >
      <ColorPicker
        value={selectedColor}
        onChange={onColorSelect}
        onComplete={(color) => handleColorComplete(color.hex)}
      >
        <Animated.View style={styles.colorPickerRow}>
          {isBrushSelected &&
            (colorSelected ? (
              <HueSlider style={styles.hueSlider} sliderThickness={15} thumbSize={22} />
            ) : (
              <Swatches
                style={styles.swatchesContainer}
                swatchStyle={styles.swatchStyle}
                colors={ColorsArr1}
              />
            ))}

          {!colorSelected && (
            <TouchableOpacity
              style={styles.paintbtn}
              onPress={() => {
                setColorSelected(true);
              }}
            >
              <Image source={Images.Paint} style={styles.iconStyle} resizeMode="contain" />
            </TouchableOpacity>
          )}
          <Slider />

          <TouchableOpacity style={styles.paintbtn} onPress={handleOkPress}>
            <Image source={Images.Submit} style={styles.iconStyle} resizeMode="contain" />
          </TouchableOpacity>
        </Animated.View>
      </ColorPicker>
    </BlurView>
  ) : null;
};

const styles = StyleSheet.create({
  blurView: {
    alignItems: 'center',
    width: '100%',
    position: 'absolute',
    bottom: '8%',
    height: '10%',
    justifyContent: 'space-between',
    backgroundColor: Platform.OS == 'android' ? '#FFFFFF33' : 'transparent',
    paddingHorizontal: '1%',
  },
  swatchesContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  swatchStyle: {
    borderRadius: 21,
    height: 40,
    width: 40,
  },
  colorPickerRow: {
    width: '100%',
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-between',
  },
  hueSlider: {
    height: 30,
    width: '30%',
  },
  paintbtn: {
    width: 41,
    height: 41,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  iconStyle: {
    width: 16,
    height: 16,
  },
  container: {
    width: 150,
    height: 40,
    justifyContent: 'center',
  },
  knob: {
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: 'white',
    position: 'absolute',
    top: '50%',
    marginTop: -9.8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
});

export default memo(PaintColorPicker);
