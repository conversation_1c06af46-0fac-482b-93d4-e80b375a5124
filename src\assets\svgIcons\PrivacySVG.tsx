import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const PrivacySVG: React.FC<IconProps> = ({
  size = 22,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 25) / 22} // maintain aspect ratio based on original width/height
      viewBox="0 0 22 25"
      fill="none"
      {...props}
    >
      <Path
        d="M10.902 24.762c-.332 0-.66-.09-.95-.26l-3.77-2.19a12.3 12.3 0 01-4.52-4.616A13.027 13.027 0 010 11.32V5.243c0-.506.179-.994.503-1.37.324-.376.77-.612 1.25-.664A18.63 18.63 0 006.15 2.17 19.018 19.018 0 009.88.302 1.895 1.895 0 0110.903 0c.36 0 .714.104 1.022.301a19.048 19.048 0 003.73 1.869c1.422.524 2.897.873 4.396 1.04.482.05.927.287 1.251.663.324.376.503.864.503 1.37v6.077c0 2.243-.574 4.444-1.662 6.377a12.3 12.3 0 01-4.52 4.615l-3.77 2.19c-.29.17-.618.26-.95.26zm0-23.01a.258.258 0 00-.14.041 20.705 20.705 0 01-4.053 2.03 20.21 20.21 0 01-4.778 1.13.273.273 0 00-.181.094.298.298 0 00-.073.196v6.077c.001 1.93.495 3.824 1.43 5.487a10.583 10.583 0 003.89 3.971l3.77 2.19a.267.267 0 00.27.001l3.77-2.19a10.582 10.582 0 003.891-3.972 11.207 11.207 0 001.43-5.486V5.242a.297.297 0 00-.073-.196.273.273 0 00-.18-.094 20.21 20.21 0 01-4.78-1.13 20.701 20.701 0 01-4.053-2.03.254.254 0 00-.14-.04z"
        fill={color}
      />
      <Path
        d="M10.343 14.984a.813.813 0 01-.557-.222l-2.516-2.34a.902.902 0 01-.068-1.237.816.816 0 011.182-.075l1.905 1.773 3.636-4.056a.842.842 0 01.266-.203.808.808 0 01.926.157.911.911 0 01.031 1.247l-4.194 4.679a.805.805 0 01-.612.277z"
        fill={color}
      />
    </Svg>
  );
};

export default PrivacySVG;
