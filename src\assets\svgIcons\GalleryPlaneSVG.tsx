import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const GalleryPlaneSVG: React.FC<SvgComponentProps> = ({
    size = 24,
    color = "#232323",
    ...props
}) => {


    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 10 10"
            fill="none"
            {...props}
        >
            <Path
                d="M8.828 0H1.172C.526 0 0 .526 0 1.172v7.656C0 9.474.526 10 1.172 10h7.656C9.474 10 10 9.474 10 8.828V1.172C10 .526 9.474 0 8.828 0zM1.172.781h7.656c.216 0 .39.175.39.39V6.91L7.89 5.579a1.173 1.173 0 00-1.657 0L5.8 6.01 4.588 4.798a1.173 1.173 0 00-1.657 0l-2.15 2.15V1.172c0-.216.175-.39.39-.39zM8.828 9.22H1.172a.391.391 0 01-.39-.39v-.777L3.483 5.35a.391.391 0 01.552 0l1.765 1.765.983-.984a.391.391 0 01.553 0l1.882 1.882v.815c0 .216-.175.39-.39.39zm-2.5-5.313c.646 0 1.172-.525 1.172-1.172 0-.646-.526-1.171-1.172-1.171-.646 0-1.172.525-1.172 1.171 0 .647.526 1.172 1.172 1.172zm0-1.562a.391.391 0 110 .782.391.391 0 010-.782z"
                fill={color}
            />
        </Svg>
    );
};

export default GalleryPlaneSVG;


