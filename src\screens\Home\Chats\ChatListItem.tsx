import {
  ActivityIndicator,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  Vibration,
  View,
} from 'react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { colors } from '../../../theme/colors';
import {
  commonFontStyle,
  hexToRgba,
  hp,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from '../../../theme/fonts';
import { dayPipe } from '../../../utils/commonFunction';
import { IMAGES } from '../../../assets/Images';
import GallerySVG from '../../../assets/svgIcons/GallerySVG';
import VideoSVG from '../../../assets/svgIcons/VideoSVG';
import DoubleTicksSVG from '../../../assets/svgIcons/DoubleTicksSVG';
import DownloadSVG from '../../../assets/svgIcons/DownloadSVG';
import MediaPlaySVG from '../../../assets/svgIcons/MediaPlaySVG';
import FileSVG from '../../../assets/svgIcons/FileSVG';
import XlsSVG from '../../../assets/svgIcons/XlsSVG';
import DocumentSVG from '../../../assets/svgIcons/DocumentSVG';
import { IMessage, MessageStatus, MessageType } from '../../../device-storage/realm/schemas/MessageSchema';
import TimerSVG from '../../../assets/svgIcons/TimerSVG';
import MessageStatusIcon from '../../../component/PersonalChat/MessageStatusIcon';
import ModalWrapper from '../../../component/ModalWrapper';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import EditSVG from '../../../assets/svgIcons/EditSVG';
import SendArrowSVG from '../../../assets/svgIcons/SendArrowSVG';
import { useMe } from '../../../hooks/util/useMe';
// import FastImage from '@d11/react-native-fast-image'
import AudioWavesComponent from './AudioWavesComponent';
import ReplyMessageCard from './ReplyMessageCard';
import { ChatService } from '../../../service/ChatService';
import GalleryPlaneSVG from '../../../assets/svgIcons/GalleryPlaneSVG';
import Sound from 'react-native-sound';
import MicSVG2 from '../../../assets/svgIcons/MicSVG2';


interface ChatListProps {
  data: IMessage,
  onCardPress?: () => void;
  selectedMsgs?: any[];
  setSelectedMsgs?: (value: any) => void;
  otheUserName?: string;
  scrollToItem?: (value?: any) => void;
  isMsgHightlighted?: any;
  unHightlightFunc?: () => void;
}




const ChatListItem = ({ data, onCardPress = () => { }, selectedMsgs = [], setSelectedMsgs = () => { }, otheUserName = '', scrollToItem = () => { }, isMsgHightlighted = false, unHightlightFunc = () => { }, }: ChatListProps) => {

  const { user: me } = useMe();
  const [isPlaying, setIsPlaying] = useState(false);
  const [sentMsgLength, setSentMsgLength] = useState<number>(0);
  const [isSelected, setIsSelected] = useState<boolean>(false)
  const [scheduleItemModal, setScheduleItemModal] = useState(false)
  const [audioDuration, setAudioDuration] = useState<number>(0);

  console.log(data);
  




  const msgData = { ...data, fileName: 'file name' };

  const images = [msgData.mediaUrl];
  const isMyData = msgData?.senderId === me?._id;



  const replyedMsgData = ChatService.getMessageByGlobalId((msgData?.replyToMessageId as string))
  // console.log("🚀 ~ ReplyMessageCard ~ replyedMsgData:", JSON.stringify(replyedMsgData, null, 2))

  const isYourMsgReply = replyedMsgData?.senderId == me?._id
  // console.log("🚀 ~ ReplyMessageCard ~ isYourMsgReply:", isYourMsgReply)


  // console.log("🚀 ~ ChatListItem ~ msgData:", msgData)

  const memoizedIsSelected = useMemo(() => {
    return selectedMsgs.some((m) => m.localId === msgData.localId);
  }, [selectedMsgs, msgData.localId]);


  const file = msgData?.fileName?.split('.');
  const fileExtension = file?.[file?.length - 1];



  function formatToMinutesSeconds(seconds: any) {
    if (typeof seconds !== 'number' || seconds < 0) return '00:00';

    const totalSeconds = Math.floor(seconds); // Round down to nearest second
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;

    const paddedMins = String(mins).padStart(2, '0');
    const paddedSecs = String(secs).padStart(2, '0');

    return `${paddedMins}:${paddedSecs}`;
  }


  const getAudioDuration = (audioUrl: any) => {
    Sound.setCategory('Playback'); // Required for iOS playback

    const sound = new Sound(audioUrl, undefined, (error) => {
      if (error) {
        console.log('failed to load the sound', error);
        return;
      }
      // console.log('duration in seconds: ' + sound.getDuration() +
      //   'number of channels: ' + sound.getNumberOfChannels());
      setTimeout(() => {
        setAudioDuration(sound.getDuration());
      }, 100);
      sound.release();
    });
  };







  useEffect(() => {
    if (replyedMsgData?.messageType == MessageType.AUDIO && replyedMsgData?.mediaUrl) {
      getAudioDuration(replyedMsgData?.mediaUrl)
    }
    setIsSelected(isMsgHightlighted ? isMsgHightlighted : memoizedIsSelected);
    if (isMsgHightlighted) {
      setTimeout(() => {
        setIsSelected(false)
        unHightlightFunc()
      }, 500);
    }
    return () => {
      setIsSelected(false); // reset on unmount
    };
  }, [memoizedIsSelected, isMsgHightlighted])




  return (
    <>
      <TouchableOpacity key={msgData?.localId}
        activeOpacity={msgData?.replyToMessageId ? 0.7 : 1}
        onPress={() => {
          console.log("---------", msgData?.replyToMessageId)
          if (msgData?.replyToMessageId && selectedMsgs.length == 0) {
            // console.log("---------1111", (replyedMsgData?.localId === data?.localId), replyedMsgData?.localId)
            scrollToItem(replyedMsgData?.localId)
            // setIsSelected(replyedMsgData?.localId === data?.localId ? true : false);
            // setTimeout(() => {
            //   setIsSelected(false)
            // }, 3000);
            return;
          }
          if (!isSelected) {
            onCardPress()
          }
          if (selectedMsgs.length > 0) {
            setSelectedMsgs(data)
            // setIsSelected(true)
          }
        }}
        onLongPress={() => {
          // setIsSelected(true)
          Vibration.vibrate(150)
          setSelectedMsgs(data)
        }}
        style={[styles.selectedStyle, {
          backgroundColor: isSelected ? hexToRgba(colors.mainPurple, 0.3) : 'transparent', paddingHorizontal: 20
        }]}
      >
        {/* Render text */}
        {(msgData?.messageType == MessageType.TEXT && msgData?.status !== MessageStatus.SCHEDULED) ? (
          <View
            style={[msgData?.replyToMessageId ? styles.replyContainer :
              styles.container,
            {
              flex: 1,
              alignSelf: isMyData ? 'flex-end' : 'flex-start',
              backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
              borderTopLeftRadius: isMyData ? 15 : 1,
              borderBottomRightRadius: isMyData ? 1 : 15,
            },
            ]}>
            {/* TEST REPLY MESSAGE IN CHATITEMLIST */}
            {msgData?.replyToMessageId ?
              <View
                style={{
                  paddingLeft: 11,
                  backgroundColor: isMyData
                    ? colors._CDC5E8_purple
                    : colors.gray_f3,
                  borderRadius: 8,
                  borderLeftColor: colors.mainPurple,
                  borderLeftWidth: 4,
                  // flex: 1,
                  width: hp(28),
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <View style={{ paddingVertical: replyedMsgData?.messageType === MessageType.IMAGE ? 0 : 8, }}>
                  <Text style={[styles.fileText, { color: colors.mainPurple, fontSize: 14, fontWeight: '800', marginBottom: 3 }]}>
                    {isYourMsgReply ? "You" : otheUserName}
                  </Text>
                  {replyedMsgData?.messageType == MessageType.IMAGE ?
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                      <GalleryPlaneSVG size={10} color={colors.black_23} />
                      <Text numberOfLines={1} style={styles.lastMessage}>Photo</Text>
                    </View>
                    : replyedMsgData?.messageType == MessageType.AUDIO ?
                      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                        <MicSVG2 size={14} color={colors.black_23} />
                        <Text style={[styles.fileText, { color: colors.black, fontSize: 14 }]} numberOfLines={2} >{`Voice mesage (${formatToMinutesSeconds(audioDuration)})`}</Text>
                      </View>
                      :
                      <Text style={[styles.fileText, { color: colors.black, fontSize: 14 }]} numberOfLines={2} >{replyedMsgData?.text}</Text>
                  }
                </View>
                {replyedMsgData?.messageType == MessageType.IMAGE ?
                  <Image
                    source={{ uri: replyedMsgData?.mediaUrl }}
                    style={{ width: 49, height: 49, borderTopRightRadius: 8, borderBottomRightRadius: 8 }}
                    resizeMode='cover'
                  />
                  : null}
              </View> : null}
            <View style={{}}>
              <View style={{}}>
                <Text
                  // onTextLayout={handleTextLayout}
                  style={[styles.text, { marginBottom: 8 }]}>
                  {msgData?.translatedText || msgData?.text}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  alignSelf: isMyData ? 'flex-end' : 'flex-start',
                }}>
                <Text
                  style={{
                    color: colors._7A6A90_purple,
                    fontSize: 12,
                    marginRight: 4,
                  }}>
                  {dayPipe(msgData?.createdAt, 'time')}
                </Text>
                <View>
                  {isMyData && (
                    <MessageStatusIcon status={msgData.status} />
                  )}
                </View>
              </View>
            </View>
          </View>
        ) : null}





        {/* Render scheduled text */}
        {(msgData?.messageType === MessageType.TEXT && msgData?.scheduledAt && msgData?.status === MessageStatus.SCHEDULED) ? (
          <View
            style={[
              styles.container,
              {
                alignSelf: isMyData ? 'flex-end' : 'flex-start',
                backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                borderTopLeftRadius: isMyData ? 15 : 1,
                borderBottomRightRadius: isMyData ? 1 : 15,
              },
            ]}>
            <View style={{}}>
              <View style={{ flexDirection: sentMsgLength > 1 ? 'column' : 'row', alignItems: 'center', justifyContent: sentMsgLength > 1 ? 'flex-start' : 'space-between' }}>
                <Text
                  // onTextLayout={handleTextLayout}
                  style={[styles.text, { marginBottom: sentMsgLength > 1 ? 8 : 0, marginRight: 8, }]}
                  onTextLayout={(e) => {
                    console.log("----=======----", e.nativeEvent.lines)
                    setSentMsgLength(e.nativeEvent.lines.length)
                  }}
                >
                  {msgData?.text}
                </Text>

                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'flex-end',
                    marginBottom: sentMsgLength > 1 ? 8 : 0
                  }}>
                  <Text
                    style={{
                      color: colors._7A6A90_purple,
                      fontSize: 12,
                      marginRight: 4,
                    }}>
                    {dayPipe(msgData?.createdAt, 'time')}
                  </Text>
                  {(isMyData && msgData.status !== MessageStatus.SCHEDULED) && (
                    <MessageStatusIcon status={msgData.status} />
                  )}
                </View>
              </View>



              {/* Hide this after the message has been sent */}
              {msgData.status === MessageStatus.SCHEDULED && <View style={{ flexDirection: 'row', marginTop: sentMsgLength < 2 ? 8 : 0, alignItems: "center", backgroundColor: colors._DFD9F3_purple, borderRadius: 10, paddingHorizontal: 9, paddingVertical: 7, gap: 10 }}>
                <MessageStatusIcon status={msgData.status} />
                <Text
                  style={{
                    color: colors.black_23,
                    fontSize: 12,
                    fontWeight: '400',
                  }}>
                  {`Scheduled for ${dayPipe(msgData?.scheduledAt, 'time')} on ${dayPipe(msgData?.scheduledAt, 'monthDay')}`}
                </Text>
              </View>}

            </View>
          </View>
        ) : null}




        {/* Replyed messages */}
        {/* {msgData?.replyToMessageId ?
          <ReplyMessageCard scrollToItem={scrollToItem} isMyData={isMyData} msgData={msgData} otheUserName={otheUserName} myUserId={me?._id} />
          : null} */}




        {/* Render images */}
        {msgData?.messageType === MessageType.IMAGE ? (
          <View
            style={[
              styles.imageContainer,
              {
                alignSelf: isMyData ? 'flex-end' : 'flex-start',
                borderTopLeftRadius: isMyData ? 15 : 1,
                borderBottomRightRadius: isMyData ? 1 : 15,
              },
            ]}>
            <View style={{ flexDirection: 'column', justifyContent: 'center' }}>
              {images?.map((list: any, i: any) => {
                const isTopImage = i === 0;
                const imageWidth = isTopImage ? 250 : 220;
                const imageHeight = isTopImage ? 169 : 145;

                return (
                  <View
                    key={i}
                    style={{
                      alignSelf: 'center',
                      zIndex: isTopImage ? 10 : 1,
                      marginTop: isTopImage ? 0 : -135,
                    }}>
                    <View
                      style={{
                        width: imageWidth,
                        height: imageHeight,
                        borderRadius: 15,
                        overflow: 'hidden',
                        backgroundColor: '#e0e0e0', // Fallback background
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      {/* {imageLoad && (
                        <ActivityIndicator size="small" color="#ff0000" style={{ position: 'absolute', zIndex: 2 }} />
                      )} */}
                      {/* <FastImage
                        source={{ uri: list, priority: FastImage.priority.normal }}
                        // onLoadStart={() => setImageLoad(true)}
                        // onLoadEnd={() => setImageLoad(false)}
                        // onError={() => setImageLoad(false)}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode={FastImage.resizeMode.cover}
                      /> */}
                      <Image
                        source={{ uri: list, }}
                        // onLoadStart={() => setImageLoad(true)}
                        // onLoadEnd={() => setImageLoad(false)}
                        // onError={() => setImageLoad(false)}
                        style={{ width: '100%', height: '100%' }}
                        resizeMode='contain'
                      />
                      {/* Overlay Content */}
                      {isTopImage && msgData?.imageStatus !== 'download' && (
                        <View
                          style={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            backgroundColor:
                              images?.length == 1
                                ? 'transparent'
                                : hexToRgba('#000000', 0.5),
                            padding: 6,
                            paddingHorizontal: 13,
                            flexDirection: 'row',
                            justifyContent:
                              images?.length == 1 ? 'flex-end' : 'space-between',
                            alignItems: 'center',
                            borderBottomLeftRadius: 15,
                            borderBottomRightRadius: 15,
                          }}>
                          {images?.length > 1 ? (
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 13 }}>
                              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                                <GallerySVG size={11} />
                                <Text style={{ color: 'white', fontSize: 12 }}>8</Text>
                              </View>
                              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                                <VideoSVG size={11} />
                                <Text style={{ color: 'white', fontSize: 12 }}>2</Text>
                              </View>
                            </View>
                          ) : null}

                          {/* Right side: time + tick */}
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Text style={{ color: 'white', fontSize: 12, marginRight: 4 }}>
                              {dayPipe(msgData?.createdAt, 'time')}
                            </Text>
                            {isMyData && (
                              <MessageStatusIcon status={msgData.status} />
                            )}
                          </View>
                        </View>
                      )}

                      {/* Download Overlay */}
                      {msgData?.imageStatus === 'download' && (
                        <View
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: hexToRgba('#000000', 0.5),
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: 15,
                          }}>
                          <DownloadSVG
                            size={36}
                            color={isMyData ? colors.white : colors.black}
                            backgroundColor={isMyData ? colors.mainPurple : colors.white}
                          />
                          <View
                            style={{
                              position: 'absolute',
                              bottom: 6,
                              width: '100%',
                              paddingLeft: 11,
                              paddingRight: 8,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                            }}>
                            <Text style={{ color: 'white', fontSize: 12, marginRight: 4 }}>
                              {msgData?.fileSize}
                            </Text>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                              <Text style={{ color: 'white', fontSize: 12, marginRight: 4 }}>
                                {dayPipe(msgData?.createdAt, 'time')}
                              </Text>
                              {isMyData && <DoubleTicksSVG size={18} />}
                            </View>
                          </View>
                        </View>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        ) : null}





        {/* image with content */}
        {msgData?.messageType === MessageType.IMAGE &&
          msgData?.text !== '' &&
          !isMyData ? (
          <View
            style={[
              {
                maxWidth: hp(36),
                paddingVertical: 12,
                paddingLeft: 16,
                paddingRight: 13,
                borderRadius: 15,
                marginBottom: 22,
                alignSelf: isMyData ? 'flex-end' : 'flex-start',
                backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                borderTopLeftRadius: isMyData ? 15 : 1,
                borderBottomRightRadius: isMyData ? 1 : 15,
                flex: 1,
              },
            ]}>
            <View style={{}}>
              <View style={{}}>
                <Text style={[styles.text, { marginBottom: 8 }]}>
                  {msgData?.content}
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  {images?.slice(0, 3)?.map((item: any, i: number) => {
                    const isLastVisible = i === 2 && images?.length > 3;

                    return (
                      <View
                        key={i}
                        style={{
                          position: 'relative',
                          marginRight: i !== 2 ? 8 : 0,
                        }}>
                        <Image
                          source={item}
                          style={{
                            width: 75,
                            height: 75,
                            resizeMode: 'cover',
                            borderRadius: 8,
                          }}
                        />
                        {isLastVisible && (
                          <View
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: 0,
                              bottom: 0,
                              borderRadius: 8,
                              backgroundColor: 'rgba(0, 0, 0, 0.4)',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: 16,
                              }}>
                              +{images?.length - 3}
                            </Text>
                          </View>
                        )}
                      </View>
                    );
                  })}
                </View>
              </View>

              <Text style={[styles.smallText, { marginTop: 8 }]}>
                {dayPipe(msgData?.createdAt, 'time')}
              </Text>
            </View>
          </View>
        ) : null}





        {/* Render music and voice message */}
        {msgData?.messageType === MessageType.AUDIO && msgData?.mediaUrl ? (
          <AudioWavesComponent isMyData={isMyData} msgData={msgData} audioDuration={audioDuration} />
        ) : null}





        {/* Render files */}
        {msgData?.messageType === 'file' ||
          msgData?.messageType === 'image_document' ||
          msgData?.messageType === 'document' ? (
          <View
            style={[
              styles.fileContainer,
              {
                alignSelf: isMyData ? 'flex-end' : 'flex-start',
                backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                borderTopLeftRadius: isMyData ? 16 : 1,
                borderBottomRightRadius: isMyData ? 1 : 16,
                minWidth: hp(28),
                flex: 1,
              },
            ]}>
            <View
              style={{
                padding: 10,
                backgroundColor: isMyData
                  ? colors._CDC5E8_purple
                  : colors.gray_f3,
                borderRadius: 10,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 8,
              }}>
              <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
                {fileExtension == 'jpg' || fileExtension == 'pdf' ? (
                  <FileSVG size={20} color={isMyData ? colors.mainPurple : colors.black}
                  />
                ) : fileExtension == 'xls' ? (
                  <XlsSVG width={22} height={23} color={isMyData ? colors.mainPurple : colors.black} />
                ) : (
                  <DocumentSVG width={19} height={25} color={isMyData ? colors.mainPurple : colors.black} />
                )}

                <Text
                  style={[
                    styles.fileText,
                    {
                      color: isMyData ? colors.mainPurple : colors.black,
                      fontSize: 14,
                      marginLeft: 10,
                      flexShrink: 1,
                      flexWrap: 'wrap',
                    },
                  ]}
                  numberOfLines={2}
                  ellipsizeMode="tail">
                  {msgData?.fileName}
                </Text>
              </View>

              <DownloadSVG
                size={28}
                color={isMyData ? colors.white : colors.black}
                backgroundColor={isMyData ? colors.mainPurple : colors.white}
              />
            </View>

            {/* Right side: time + tick */}
            <View
              style={{
                flexDirection: isMyData ? 'row' : 'row-reverse',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginTop: 6,
              }}>
              <Text
                style={{
                  color: isMyData ? colors._7A6A90_purple : colors.black,
                  fontSize: 12,
                  marginRight: 4,
                }}>
                {msgData?.fileSize}
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text
                  style={{
                    color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                    fontSize: 12,
                    marginRight: 4,
                  }}>
                  {dayPipe(msgData?.createdAt, 'time')}
                </Text>
                {isMyData && (
                  <DoubleTicksSVG size={18} color={colors._7A6A90_purple} />
                )}
              </View>
            </View>
          </View>
        ) : null}





        {/* Render contact */}
        {msgData?.messageType === 'contact' ? (
          <View
            style={{
              flex: 1,
              alignSelf: isMyData ? 'flex-end' : 'flex-start',
              padding: 10,
              borderRadius: 16,
              marginBottom: 22,
              backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
              borderTopLeftRadius: isMyData ? 16 : 1,
              borderBottomRightRadius: isMyData ? 1 : 16,
              minWidth: hp(32),
            }}>
            <View
              style={{
                padding: 9,
                backgroundColor: isMyData
                  ? colors._CDC5E8_purple
                  : colors.gray_f3,
                borderRadius: 10,
                flexDirection: 'row',
                alignItems: 'flex-end',
                justifyContent: 'space-between',
                flex: 1,
                gap: 8,
                marginBottom: 15,
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image
                  source={msgData?.contactData?.image}
                  style={{ width: 43, height: 43, resizeMode: 'contain' }}
                />

                <Text
                  style={{
                    color: isMyData ? colors.mainPurple : colors.black,
                    marginLeft: 8,
                    fontWeight: '700',
                    fontSize: 16,
                  }}>
                  {msgData?.contactData?.name}
                </Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text
                  style={{
                    color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                    fontSize: 12,
                    marginRight: 4,
                  }}>
                  {dayPipe(msgData?.createdAt, 'time')}
                </Text>
                {isMyData && (
                  <DoubleTicksSVG size={18} color={colors._7A6A90_purple} />
                )}
              </View>
            </View>

            {/* Right side: time + tick */}
            <View style={styles.contactView}>
              <TouchableOpacity>
                <Text
                  style={{
                    fontWeight: '500',
                    color: colors.mainPurple,
                    fontSize: 16,
                  }}>
                  Message
                </Text>
              </TouchableOpacity>
              <TouchableOpacity>
                <Text
                  style={{
                    fontWeight: '500',
                    color: colors.mainPurple,
                    fontSize: 16,
                  }}>
                  Add to a group
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : null}



        {/* schedule item sheet */}
        < ModalWrapper isVisible={scheduleItemModal} onCloseModal={() => setScheduleItemModal(false)} >
          <View style={{ paddingHorizontal: hp(2) }}>
            <View>
              <Text style={{ fontSize: 16, color: colors.black_23, fontWeight: '600', marginBottom: 23 }}>Scheduled messages options</Text>

              <TouchableOpacity onPress={() => { }} style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 20 }}>
                <DeleteSVG color={colors._EC0B0B_red} size={18} />
                <Text style={[styles.bottomText, { color: colors._EC0B0B_red }]}>Delete</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => { }} style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 20 }}>
                <EditSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {

                  setScheduleItemModal(false);
                  // setTimeout(() => {
                  // setScheduleTimerSheet(true)
                  // }, 250);
                }}
                style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 20 }}
              >
                <TimerSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Reschedule</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => { }} style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginBottom: 20 }}>
                <SendArrowSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Send now</Text>
              </TouchableOpacity>

            </View>
          </View>
        </ModalWrapper >



      </TouchableOpacity >



    </>
  );
};

export default ChatListItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    paddingVertical: 13,
    paddingLeft: 16,
    paddingRight: 13,
    borderRadius: 15,
    // marginBottom: 22,
  },
  replyContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 15,
    // marginBottom: 22,
  },
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 16,
    // marginBottom: 22,
  },
  imageContainer: {
    // backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(40),
    // paddingVertical: 13,
    // paddingLeft: 16,
    // paddingRight: 13,
    // borderRadius: 15,
    // marginBottom: 22,
  },
  text: {
    marginBottom: 8,
    ...commonFontStyle(700, 14, colors.black),
    alignSelf: 'flex-start',
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  smallText: {
    ...commonFontStyle(700, 12, colors._757575_gray),
    // fontWeight: 'SF-Pro-Text-Medium',
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH * 0.4,
    justifyContent: 'flex-end',
  },
  contactView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 13,
  },
  selectedStyle: {
    paddingVertical: 11,
    backgroundColor: 'transperant'
  },
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400'
  },
  lastMessage: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
    // flexShrink: 1,
  }
});
