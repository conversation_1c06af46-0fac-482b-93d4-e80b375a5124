import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useRoute } from '@react-navigation/native';
import { Channel } from '../../../types/chatSpace.types';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import { useMe } from '../../../hooks/util/useMe';
import { getChannelDetailsApi, getStreamInfoApi } from '../../../api/Chatspace/chatspace.api';
import { ViewerStreamInfo } from '../../../types/chatSpace.types';

const ChannelChatScreen = () => {
  const route = useRoute();
  const params: { channelData: Channel } = route.params;
  const channel = params.channelData;
  const insets = useSafeAreaInsets();
  const { user } = useMe();

  const isChannelowner = user?._id === channel.createdBy;

  return (
    <View
      style={{
        paddingTop: 30,
        paddingBottom: insets.bottom,
      }}
    >
      {isChannelowner ? (
        <ChannelOwnerView channel={channel} />
      ) : (
        <ChannelMemberView channel={channel} />
      )}
    </View>
  );
};

function ChannelOwnerView({ channel }: { channel: Channel }) {
  return (
    <>
      <View>
        <Text style={{ fontSize: 20, color: 'black' }}>{channel.name}</Text>
      </View>
      <View>
        <TouchableOpacity
          style={{
            padding: 10,
            backgroundColor: 'blue',
            borderRadius: 10,
          }}
          onPress={() => {
            navigateTo(SCREENS.ChannelStreamScreen, { channelData: channel });
          }}
        >
          <Text style={{ fontSize: 20, color: 'white' }}> start streaming </Text>
        </TouchableOpacity>
      </View>
    </>
  );
}

function ChannelMemberView({ channel }: { channel: Channel }) {
  const [channelData, setChannelData] = useState<Channel>(channel);

  const getChannelData = async () => {
    try {
      const channelDataRes = await getChannelDetailsApi(channel.chatSpaceId);

      setChannelData(channelDataRes.channel);
    } catch (err) {
      return Promise.reject(err);
    }
  };

  useEffect(() => {
    const id = setInterval(() => {
      getChannelData();
    }, 5 * 1000);

    return () => {
      clearInterval(id);
    };
  }, []);

  return (
    <>
      <View>
        <Text style={{ fontSize: 20, color: 'black' }}>Channel Member View</Text>
        <Text style={{ fontSize: 20, color: 'black' }}> {channelData.name} </Text>
        <Text style={{ fontSize: 20, color: 'blue' }}>
          {' '}
          {channelData.isLiveStreaming ? 'Live' : 'Offline'}{' '}
        </Text>

        {channelData.isLiveStreaming && (
          <View
            style={{
              // backgroundColor: 'red',
              padding: 30,
            }}
          >
            <TouchableOpacity
              style={{
                backgroundColor: 'green',
                padding: 10,
              }}
              onPress={async () => {
                const streamInfo = await getStreamInfoApi<ViewerStreamInfo>(channel);
                console.log(streamInfo, '>>>>>>>>>>>>>>>>>>>>>>>>>>>>.');
                navigateTo(SCREENS.ViewerScreenView, {
                  channelData: channelData,
                  streamInfo,
                });
              }}
            >
              <Text> Join stream </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </>
  );
}

export default ChannelChatScreen;

const styles = StyleSheet.create({});
