//todo: move all these functions to api file
import Api from '../utils/api';
import { ChatSpacesResponse } from '../types/chatSpace.types';
import { IGroupAnalytics } from '../screens/Home/Groups/GroupAnalyticsScreen';
import { RemoteChatSpace } from '../device-storage/realm/schemas/ChatSpaceSchema';
import { MembershipStatus } from '../types/chats.types';

export const createGroupSpaceApi = async ({
  name,
  description,
  type,
  members,
  displayPic,
  isPrivate,
}: {
  name: string;
  type: 'group' | 'channel';
  description?: string;
  isPrivate?: boolean;
  members: string[];
  displayPic?: {
    uri: string;
    type: string;
    name: string;
  };
}) => {
  // TODO: Implement types here
  const formData = new FormData();

  formData.append('name', name);
  formData.append('type', type);
  formData.append('description', description || '');
  formData.append('isPrivate', isPrivate ? 'true' : 'false');

  members.forEach((memberId: string) => {
    formData.append('members', memberId);
  });

  if (displayPic?.uri && displayPic?.type && displayPic?.name) {
    formData.append('displayPic', {
      uri: displayPic.uri,
      type: displayPic.type,
      name: displayPic.name,
    } as any);
  }

  try {
    const response = await Api.post('v1/chatspaces', formData, true);
    console.log(response);

    return response.body || response;
  } catch (err: any) {
    throw err;
  }
};

export const getChatspaces = async (): Promise<ChatSpacesResponse> => {
  try {
    const res = await Api.get(`v1/chatspaces/chatSpaces`);
    const data = res?.body?.data;
    if (data) {
      return data;
    } else {
      return { groups: [], groupCount: 0, channels: [], channelCount: 0 };
    }
  } catch (error) {
    console.error('Error fetching chatspaces:', error);
    throw error;
  }
};

export interface IUserInfo {
  username: string;
  name: string;
  image: string | null;
  e164CompliantNumber: string;
}

export interface IGroupMember {
  _id: string;
  userId: string;
  role: MembershipStatus;
  updatedAt: string;
  user: IUserInfo;
}

export interface IGroupMembersData {
  members: IGroupMember[];
  totalCount: number;
  hasMore: boolean;
}

export const getChatSpaceMembers = async (
  chatSpaceId: string,
): Promise<IGroupMembersData | undefined> => {
  try {
    const response = await Api.get(`v1/chatspaces/allFollowers/${chatSpaceId}`);
    const data = response.body;
    if (data.status) {
      return data.data;
    }
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

export interface ICommonGroup {
  _id: string;
  chatSpaceId: string;
  chatType: 'GroupChatSpace';
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  createdBy: string;
  description: string | null;
  displayPic: string | null;
  inviteCode: string | null;
  inviteLink: string | null;
  isPrivate: boolean;
  memberCount: number;
  messageCounts: {
    text: number;
    image: number;
    video: number;
    audio: number;
    document: number;
    location: number;
    contact: number;
  };
  name: string;
}

export const getCommonGroups = async (userId: string): Promise<ICommonGroup[] | undefined> => {
  try {
    const response = await Api.get(`v1/chatspaces/commonGroup/${userId}`);
    const data = response.body.data;
    if (data) {
      return data;
    }
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

export const getGroupAnalytics = async (chatSpaceId: string): Promise<IGroupAnalytics> => {
  try {
    const response = await Api.get(`v1/chatspaces/summary/${chatSpaceId}`);
    return response.body.data;
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

export const createChannelSpaceApi = async ({
  name,
  type,
  description,
  displayPic,
}: {
  name: string;
  type: 'channel' | 'group';
  description?: string;
  displayPic?: {
    uri: string;
    type: string;
    name: string;
  };
}) => {
  const formData = new FormData();
  formData.append('name', name);
  formData.append('type', type);
  formData.append('description', description || '');

  if (displayPic?.uri && displayPic?.type && displayPic?.name) {
    formData.append('displayPic', {
      uri: displayPic.uri,
      type: displayPic.type,
      name: displayPic.name,
    } as any);
  }

  try {
    const response = await Api.post('v1/chatspaces', formData, true);
    return response.body;
  } catch (error: any) {
    throw error.response?.data || error;
  }
};

export const getChatSpaceWithMembershipStatus = async (
  chatSpaceId: string,
): Promise<(RemoteChatSpace & { membershipStatus: MembershipStatus }) | null> => {
  try {
    const result = await Api.get(`v1/chatspaces/memberShip/${chatSpaceId}`);
    return result.body.data ? result.body.data : null;
  } catch (error) {
    console.error('Error fetching chat space:', error);
    return null;
  }
};
