/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';

import { onFCMNotification } from './src/firebase/fcmNotificationHandler';

import { initSocket } from './src/socket-client/socket';
import { getRealm } from './src/device-storage/realm/realm';
import { notificationChannels } from './src/firebase/notifications/createNotificationChannels';
import { handleIncomingCallInteraction } from './src/firebase/notifications/callNoitificationHandler';
import { Client } from './src/lib/Client';
import { setUseWhatChange } from '@simbathesailor/use-what-changed';

// Enable only in development
setUseWhatChange(process.env.NODE_ENV === 'development');

// Set up background message handler for FCM notifications
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
  // Retrieve saved access token from AsyncStorage
  const accessToken = await Client.AuthToken.get();
  const lastSynced = await Client.LastSyncedDate.get();

  // Get the current FCM token
  const fcmToken = await messaging().getToken();

  // Initialize socket connection
  const socket = initSocket(accessToken || 'wwe', lastSynced, fcmToken);

  // Open Realm DB
  const realm = getRealm();

  // Handle the incoming FCM notification
  await onFCMNotification(socket, remoteMessage, realm);
});

messaging().onMessage(async (event) => {
  console.log('[Notif-Log] Foreground service triggered with notification:', event);
});

// Register foreground service for handling notifications while app is active
notifee.registerForegroundService((notification) => {
  console.log('[Notif-Log] Foreground service triggered with notification:', notification);

  return new Promise(() => {
    // You can handle long-running tasks here if needed
  });
});

// Handle notification events in the background (e.g., button presses)
notifee.onBackgroundEvent(async (event) => {
  const notificationData = event.detail.notification.data;
  console.log('[Notif-Log] Background service triggered with notification:', notificationData);
  if (
    notificationData &&
    notificationData.channel &&
    notificationData.channel === notificationChannels.calls.id
  ) {
    console.log('[Notif-Log] -----------End--------------------- ');
    handleIncomingCallInteraction(event);
    return;
  }
});

// Register the main app component
AppRegistry.registerComponent(appName, () => App);
