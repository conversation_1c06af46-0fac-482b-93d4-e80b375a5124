import { View, Text, Image, TouchableOpacity, StyleSheet, StatusBar, Platform } from 'react-native';
import React, { ReactNode, useEffect } from 'react';
import { colors } from '../theme/colors';
import { IMAGES } from '../assets/Images';
import { AppStyles } from '../theme/appStyles';
import { useNavigation } from '@react-navigation/native';
import { commonFontStyle } from '../theme/fonts';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome6Icons } from '../utils/vectorIcons';

type Props = {
  leftTitile: string;
  rightTitle: string;
  hideLeftTitle?: boolean;
  children?: ReactNode;
};

const CommonHeader = ({
  leftTitile = '',
  rightTitle = '',
  hideLeftTitle = false,
  children,
}: Props) => {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors._7A5DCB_purple }}>
      {Platform.OS === 'android' && (
        <StatusBar backgroundColor={colors._7A5DCB_purple} barStyle="light-content" />
      )}
      <View
        style={{
          backgroundColor: colors._7A5DCB_purple,
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 25,
          paddingVertical: 15,
          ...(rightTitle && { justifyContent: 'space-between' }),
        }}
      >
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <FontAwesome6Icons name="arrow-right-long" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        {!hideLeftTitle && leftTitile !== '' && (
          <Text style={[styles.titleText, { marginLeft: 20 }]}>{leftTitile}</Text>
        )}
        {rightTitle !== '' && <Text style={[styles.titleText]}>{rightTitle}</Text>}
      </View>

      <View style={{ flexGrow: 1 }}>{children}</View>
    </SafeAreaView>
  );
};

export default CommonHeader;

const styles = StyleSheet.create({
  titleText: {
    ...commonFontStyle(700, 18, colors.white),
    textAlign: 'center',
  },
});
