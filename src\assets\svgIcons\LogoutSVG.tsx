import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

const Lo<PERSON>ut<PERSON><PERSON>: React.FC<SvgProps> = (props) => (
  <Svg width={19} height={21} viewBox="0 0 19 21" fill="none" {...props}>
    <Path d="M9.45503 20.9111C7.44666 20.9102 5.49068 20.2702 3.87034 19.0836C2.25 17.8969 1.0494 16.2254 0.442387 14.311C-0.164625 12.3965 -0.146539 10.3385 0.494028 8.43507C1.13459 6.53159 2.36439 4.8814 4.00534 3.72346C4.09711 3.65873 4.20073 3.61271 4.31028 3.58802C4.41984 3.56334 4.53318 3.56048 4.64384 3.5796C4.75451 3.59872 4.86032 3.63945 4.95524 3.69946C5.05016 3.75947 5.13233 3.8376 5.19706 3.92937C5.26179 4.02114 5.30781 4.12476 5.33249 4.23431C5.35717 4.34387 5.36004 4.45721 5.34092 4.56787C5.32179 4.67853 5.28107 4.78434 5.22105 4.87927C5.16104 4.97419 5.08292 5.05636 4.99115 5.12109C3.64543 6.0693 2.63662 7.42139 2.11091 8.98142C1.5852 10.5414 1.56991 12.2283 2.06725 13.7976C2.5646 15.3669 3.54874 16.7371 4.87704 17.7095C6.20535 18.682 7.8088 19.2062 9.45503 19.2062C11.1012 19.2062 12.7047 18.682 14.033 17.7095C15.3613 16.7371 16.3455 15.3669 16.8428 13.7976C17.3401 12.2283 17.3249 10.5414 16.7991 8.98142C16.2734 7.42139 15.2646 6.0693 13.9189 5.12109C13.8255 5.05713 13.7458 4.97524 13.6844 4.88019C13.623 4.78513 13.5811 4.67881 13.5611 4.56741C13.5412 4.45601 13.5436 4.34176 13.5682 4.23129C13.5928 4.12083 13.6391 4.01636 13.7045 3.92397C13.7699 3.83158 13.8529 3.75312 13.9489 3.69314C14.0449 3.63316 14.1518 3.59287 14.2635 3.57461C14.3752 3.55634 14.4894 3.56048 14.5995 3.58676C14.7096 3.61305 14.8133 3.66097 14.9047 3.72772C16.5438 4.88629 17.7719 6.53624 18.4113 8.4389C19.0507 10.3416 19.0682 12.3983 18.4614 14.3116C17.8546 16.2249 16.6549 17.8956 15.0358 19.082C13.4168 20.2684 11.4622 20.909 9.45503 20.9111ZM9.45503 12.3077C9.22866 12.3077 9.01157 12.2178 8.8515 12.0577C8.69144 11.8976 8.60151 11.6805 8.60151 11.4542V0.853514C8.60151 0.627148 8.69144 0.410054 8.8515 0.249989C9.01157 0.0899236 9.22866 0 9.45503 0C9.68139 0 9.89849 0.0899236 10.0586 0.249989C10.2186 0.410054 10.3085 0.627148 10.3085 0.853514V11.4542C10.3085 11.6805 10.2186 11.8976 10.0586 12.0577C9.89849 12.2178 9.68139 12.3077 9.45503 12.3077Z" fill="#ED2828"/>
  </Svg>
);

export default LogoutSVG; 