import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import CallTabSVG from '../../../../assets/svgIcons/CallTabSVG';
import ChatSVG from '../../../../assets/svgIcons/ChatSVG';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { colors } from '../../../../theme/colors';
import { maxMembersInCall } from '../../../../utils/constants';
import { useState } from 'react';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { useCallContext } from '../../../../Context/CallProvider';
import { useNavigation } from '@react-navigation/native';
import CallTypeSelectionModal from '../ChatModals/CallTypeSelectionModal';
import { CallType, GroupConversationDetails } from '../../../../types/calls.types';
import Toast from 'react-native-toast-message';
import { hp, wp } from '../../../../theme/fonts';
import { MembershipStatus } from '../../../../types/chats.types';
import { navigateTo } from '../../../../utils/commonFunction';
import { useTranslation } from 'react-i18next';
import { IUser } from '../../../../device-storage/realm/schemas/UserSchema';

type ProfileActionButtonsProps = {
  conversationInfo: ConversationInfo | null;
  myId: string;
};

const ProfileActionButtons: React.FC<ProfileActionButtonsProps> = ({ conversationInfo, myId }) => {
  const navigation = useNavigation();
  const { t } = useTranslation();

  const [showCallTypeModal, setShowCallTypeModal] = useState(false);

  const { startCall } = useCallContext();

  const { callDetails } = useCallContext();

  const conversationId = conversationInfo?.id;

  const isMyProfile = conversationId === myId;

  const conversationType = conversationInfo?.type;

  const isChannel = conversationType === ConversationType.CHANNEL;

  const isBlocked = conversationInfo?.type === ConversationType.P2P && conversationInfo?.isBlocked;
  const showCallButton =
    callDetails.state === 'idle' &&
    !isMyProfile &&
    !isBlocked &&
    conversationInfo?.type === ConversationType.P2P
      ? true
      : conversationInfo?.type === ConversationType.GROUP &&
        (conversationInfo?.memberCount || maxMembersInCall + 1) <= maxMembersInCall;

  const memberCount =
    conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.memberCount : null;
  const StatBox = ({ value, title }: { value: number; title: string }) => {
    return (
      <View style={styles.statBox}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statLabel}>{title}</Text>
      </View>
    );
  };

  const isOwner =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus === MembershipStatus.OWNER
      : null;

  const isMember =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus === MembershipStatus.MEMBER
      : null;

  async function handleCall(type: CallType) {
    setShowCallTypeModal(false);
    if (
      conversationType === ConversationType.CHANNEL ||
      (conversationType === ConversationType.GROUP &&
        (conversationInfo?.memberCount ?? maxMembersInCall + 1) > maxMembersInCall)
    ) {
      Toast.show({
        type: 'error',
        text1: 'Call Limit Exceeded',
        text2: 'You can only make a call to 10 members at a time.And Cannot make call in channel',
      });
      return;
    }

    if (conversationType === ConversationType.P2P) {
      const recipeient: Partial<IUser> = {
        id: conversationId,
        name: conversationInfo?.displayName,
        profilePic: conversationInfo?.displayPic,
        username: conversationInfo?.displayName,
      };
      startCall({
        recipients: [recipeient],
        callType: type,

        origin: { type: 'directConversation', conversationId: conversationId as string },
      });
    } else if (conversationType === ConversationType.GROUP && conversationId != undefined) {
      const groupConvDetail: GroupConversationDetails = {
        chatSpaceId: conversationId,
        displayName: conversationInfo?.displayName || '',
        displayPic: conversationInfo?.displayPic,
        memberCount: conversationInfo?.memberCount,
        id: conversationId,
      };
      startCall({
        recipients: [],
        callType: type,
        origin: {
          type: 'groupConversation',

          conversation: groupConvDetail,
        },
      });
    }
  }

  return (
    <>
      {!isChannel ? (
        <View style={styles.actionContainer}>
          {/* todo: implement Gift feature */}
          {/* <TouchableOpacity style={styles.actionButton}>
        <EditImageSVG size={20} color={colors.black_23} />
        <Text style={styles.actionLabel}>Gift</Text>
      </TouchableOpacity> */}

          {!isMyProfile && showCallButton && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowCallTypeModal(true)}
            >
              <CallTabSVG size={20} color={colors.black} />
              <Text style={styles.actionLabel}>Call</Text>
            </TouchableOpacity>
          )}
          {conversationInfo?.type !== ConversationType.CHANNEL && (
            <TouchableOpacity style={styles.actionButton} onPress={() => navigation.goBack()}>
              <ChatSVG size={20} />
              <Text style={styles.actionLabel}>Message</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View style={styles.actionButtonContainer}>
          <View style={styles.statsRow}>
            {isMember && <StatBox value={memberCount || 0} title={t('Followers')} />}
            {isOwner && (
              <TouchableOpacity
                onPress={() =>
                  navigateTo('FollowersScreen', {
                    chatSpaceId: conversationInfo?.id,
                  })
                }
              >
                <StatBox value={memberCount || 0} title={t('Followers')} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}
      {showCallTypeModal && (
        <CallTypeSelectionModal
          handleCall={handleCall}
          visible={showCallTypeModal}
          onClose={() => setShowCallTypeModal(false)}
        />
      )}
    </>
  );
};

export default ProfileActionButtons;

const styles = StyleSheet.create({
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 5,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  actionButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  actionIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 8,
    tintColor: colors.black_23,
  },
  actionLabel: {
    fontSize: 16,
    color: colors.black,
    fontWeight: '400',
    marginLeft: 4,
  },
  statBox: {
    width: wp(28),
    height: hp(10),
    borderRadius: 10,
    backgroundColor: colors.gray_f3,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 4,
    elevation: 1,
    marginHorizontal: 2,
  },
  statValue: {
    color: colors.black_23,
    fontSize: 18,
    fontWeight: '600',
  },
  statLabel: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 3,
  },
});
