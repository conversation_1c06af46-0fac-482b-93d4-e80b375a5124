import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';

const LAST_UPDATED = 'July 2025';

const termsSummary = `Welcome to ChatBucket! By accessing or using our app, you agree to comply with and be bound by the following terms and conditions of use. Please read these terms carefully before using our app.\n\nAcceptance of Terms: By using ChatBucket, you agree to these Terms and Conditions and Privacy Policy. If you do not agree, please do not use the app.`;

const termsFull = `1. Introduction\nThese Terms and Conditions govern your use of ChatBucket.\n\n2. Acceptance of Terms\nBy accessing or using ChatBucket, you agree to be bound by these Terms.\n\n3. User Obligations\nYou agree to use the app lawfully and not misuse its features.\n\n4. Prohibited Activities\nYou may not use the app for unlawful, harmful, or abusive purposes.\n\n5. Intellectual Property\nAll content and features are the property of ChatBucket or its licensors.\n\n6. Termination\nWe reserve the right to terminate or suspend your access for violations.\n\n7. Limitation of Liability\nWe are not liable for damages arising from your use of the app.\n\n8. Governing Law\nThese terms are governed by the laws of your jurisdiction.\n\n9. Contact Information\nFor questions, contact <EMAIL>.`;

const privacySummary = `Protecting your privacy is important to us. This Privacy Policy outlines how we collect, use, and protect your personal information when you use ChatBucket.\n\nInformation We Collect: We may collect personal information such as your name, email address, and device information when you use our app.`;

const privacyFull = `1. Introduction\nThis Privacy Policy explains how ChatBucket collects, uses, and protects your information.\n\n2. Information We Collect\nWe may collect your name, email, device info, and usage data.\n\n3. How We Use Your Information\nWe use your data to provide and improve our services, communicate with you, and ensure security.\n\n4. How We Share Your Information\nWe do not sell your data. We may share it with trusted third parties for service provision, legal compliance, or protection.\n\n5. Data Security\nWe implement security measures to protect your data.\n\n6. User Rights and Choices\nYou may access, update, or delete your information by contacting us.\n\n7. Children's Privacy\nWe do not knowingly collect data from children under 13.\n\n8. Changes to This Policy\nWe may update this policy. Changes will be posted in the app.\n\n9. Contact Information\nFor privacy questions, contact <EMAIL>.`;

const TermsPrivacyScreen = () => {
  const [showTermsFull, setShowTermsFull] = useState(false);
  const [showPrivacyFull, setShowPrivacyFull] = useState(false);

  return (
    <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <SafeAreaView />
      <HeaderBackWithTitle title="Terms & Privacy Policy" />
      <View style={styles.whiteContainer}>
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} showsVerticalScrollIndicator={false}>
          <Text style={styles.lastUpdated}>Last Updated: {LAST_UPDATED}</Text>
          <Text style={styles.sectionTitle}>Terms and Conditions</Text>
          <Text style={styles.sectionText}>{termsSummary}</Text>
          {showTermsFull && <Text style={styles.sectionText}>{termsFull}</Text>}
          {!showTermsFull && (
            <TouchableOpacity onPress={() => setShowTermsFull(true)}>
              <Text style={styles.readMore}>Read More</Text>
            </TouchableOpacity>
          )}
          <View style={styles.separator} />
          <Text style={styles.sectionTitle}>Privacy Policy</Text>
          <Text style={styles.sectionText}>{privacySummary}</Text>
          {showPrivacyFull && <Text style={styles.sectionText}>{privacyFull}</Text>}
          {!showPrivacyFull && (
            <TouchableOpacity onPress={() => setShowPrivacyFull(true)}>
              <Text style={styles.readMore}>Read More</Text>
            </TouchableOpacity>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  lastUpdated: {
    color: colors.gray_80,
    fontSize: 13,
    marginBottom: 10,
    textAlign: 'left',
  },
  sectionTitle: {
    fontWeight: 'bold',
    fontSize: 19,
    color: colors.black,
    marginBottom: 6,
    marginTop: 10,
  },
  sectionText: {
    fontSize: 15,
    color: colors.black_23,
    marginBottom: 8,
    lineHeight: 22,
  },
  readMore: {
    color: colors.mainPurple,
    fontWeight: '500',
    marginBottom: 12,
    fontSize: 15,
  },
  separator: {
    height: 1,
    backgroundColor: colors._EFEFEF_gray,
    marginVertical: 16,
    width: '100%',
  },
});

export default TermsPrivacyScreen;
