import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
  size?: number;
  color?: string;
  isReset?: boolean;
}


const RefreshSVG: React.FC<SvgComponentProps> = ({
  size = 20,
  color = "#fff",
  isReset = false,
  ...props
}) => {
  const scale = size / 22;
  return (
    <>
      {isReset ? 
        <Svg
          width={19 * scale}
          height={22 * scale}
          viewBox="0 0 19 22"
          fill="none"
          {...props}
        >
          <Path
            d="M9.8 3.71c4.914 0 8.925 4.01 8.925 8.925 0 4.914-4.011 8.925-8.925 8.925-4.914 0-8.925-4.011-8.925-8.925 0-.432.353-.784.784-.784.432 0 .784.352.784.784 0 4.05 3.306 7.356 7.357 7.356 4.05 0 7.356-3.305 7.356-7.356S13.851 5.278 9.8 5.278a.786.786 0 01-.784-.784c0-.431.352-.784.784-.784z"
            fill={color}
            stroke={color}
            strokeWidth={0.25}
          />
          <Path
            d="M10.567 1.11a.762.762 0 011.1 0 .762.762 0 010 1.099l-2.352 2.35 2.352 2.351a.762.762 0 010 1.1c-.13.162-.332.234-.55.234a.776.776 0 01-.55-.234l-2.9-2.9a.762.762 0 010-1.1l2.9-2.9z"
            fill={color}
            stroke={color}
            strokeWidth={0.25}
          />
        </Svg>
        :<Svg
          width={size}
          height={(size * 21) / 20} // maintain aspect ratio (original is 20x21)
          viewBox="0 0 20 21"
          fill="none"
          {...props}
          >
          <Path
            d="M19.036 10.877a1.118 1.118 0 00-1.282.952 7.227 7.227 0 01-2.066 4.085c-2.862 2.862-7.515 2.86-10.376 0-2.86-2.86-2.86-7.516 0-10.376a7.24 7.24 0 013.955-2.032 6.985 6.985 0 012.085-.063 7.24 7.24 0 012.784.935l-1.47.251a1.128 1.128 0 10.382 2.226l3.94-.674a1.131 1.131 0 00.922-1.303l-.675-3.94a1.129 1.129 0 10-2.225.381l.178 1.037A9.508 9.508 0 0011.609 1.2a9.147 9.147 0 00-2.74.085A9.46 9.46 0 003.715 3.94c-3.741 3.74-3.741 9.828 0 13.57a9.564 9.564 0 006.784 2.805 9.563 9.563 0 006.785-2.806 9.473 9.473 0 002.703-5.352 1.129 1.129 0 00-.952-1.281z"
            fill={color}
            />
        </Svg>
      }
    </>
  );
};

export default RefreshSVG;


