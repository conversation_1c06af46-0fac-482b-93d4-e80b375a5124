import * as React from "react"
import Svg, { Circle, Path } from "react-native-svg"

function FilledPlusSVG({ size = 27, color = "#6A4DBB", iconColor = "#fff", ...props }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 27 27"
            fill="none"
            {...props}
        >
            <Circle cx={13.0954} cy={13.0954} r={13.0954} fill={color} />
            <Path
                d="M13.096 7.403c.43 0 .78.35.78.78v4.132h4.132a.78.78 0 010 1.559h-4.132v4.132a.78.78 0 01-1.56 0v-4.132H8.185a.78.78 0 010-1.56h4.131V8.184c0-.43.35-.78.78-.78z"
                fill={iconColor}
                stroke={iconColor}
                strokeWidth={0.467747}
            />
        </Svg>
    )
}

export default FilledPlusSVG
