import { StyleSheet } from 'react-native';
import { hp, wp } from '../utils/Constants/dimensionUtils'; // Adjust the path as necessary

const styles = StyleSheet.create({
  blurView: {
    width: '100%',
    overflow: 'visible',
    // backgroundColor: 'green',
    position: 'absolute',
    bottom: hp(8.5),
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageItem: {
    marginHorizontal: wp(1),
    alignItems: 'center',
    overflow: 'hidden',
    justifyContent: 'center',
  },
  listImage: {
    height: wp(12),
    width: wp(12),
    borderRadius: 10,
  },
  deleteImageOverlay: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0000004D',
    height: '100%',
    width: '100%',
    borderRadius: 10,
  },
  binIcon: {
    width: wp(8),
    height: wp(8),
    tintColor: 'white',
  },
});

export default styles;
