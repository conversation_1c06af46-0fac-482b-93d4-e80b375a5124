import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { IMAGES } from '../../../assets/Images';
import { colors } from '../../../theme/colors';
import { useTranslation } from 'react-i18next';
import { commonFontStyle } from '../../../theme/fonts';

interface IProps {
  data?: any;
}

const EaringView = ({ data }: IProps) => {
  const { t } = useTranslation();
  return (
    <View style={styles.container}>
      <View style={styles.iconView}>
        <Image source={IMAGES.live_stream} style={styles.iconStyle} />
      </View>
      <View style={{ gap: 5, flex: 1 }}>
        <Text style={styles.liveText}>{t('Live Stream')}</Text>
        <Text style={styles.timeText}>{data?.time ?? '10:24 AM,11/12/2024'}</Text>
      </View>
      <Text style={styles.moneyText}>{data?.earning ?? '$40'}</Text>
    </View>
  );
};

export default EaringView;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconView: {
    height: 50,
    width: 50,
    borderRadius: 50,
    backgroundColor: colors.gray_f3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  liveText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  timeText: {
    ...commonFontStyle(400, 14, colors.gray_80),
  },
  moneyText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
});
