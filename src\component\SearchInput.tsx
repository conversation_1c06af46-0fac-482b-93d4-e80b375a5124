import { Image, StyleSheet, Text, TextInput, View } from 'react-native';
import React, { useMemo } from 'react';
import { colors } from '../theme/colors';
import { IMAGES } from '../assets/Images';
import { commonFontStyle } from '../theme/fonts';
import { useTranslation } from 'react-i18next';
import SearchSVG from '../assets/svgIcons/SearchSVG';

type Props = {
  value: string;
  onChangeText: (text: string) => void;
  containerStyle?: any;
  placeholder?: string;
  activeTab?: string;
};

const SearchInput = ({ value, onChangeText, containerStyle, placeholder, activeTab }: Props) => {
  const { t } = useTranslation();

  const placeholderText =
    activeTab === 'Groups' ? t('Search Global Groups') : t('Search on ChatBucket');
  
  return (
    <View style={{ ...styles.searchView, ...containerStyle }}>
      <SearchSVG color={colors._B5B5B5_gray} style={styles.searchIcon} size={15} />
      <TextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholderText}
        placeholderTextColor={colors._B5B5B5_gray}
        style={styles.textInput}
      />
    </View>
  );
};

export default SearchInput;

const styles = StyleSheet.create({
  searchView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray_f3,
    borderRadius: 15,
  },
  searchIcon: {
    height: 17,
    width: 17,
    resizeMode: 'contain',
    marginHorizontal: 10,
  },
  textInput: {
    height: 50,
    ...commonFontStyle(400, 14, colors.black_23),
    flex: 1,
    paddingRight: 10,
  },
});
