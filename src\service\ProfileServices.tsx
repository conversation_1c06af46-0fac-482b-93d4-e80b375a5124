//todo: move these funcs to api file
import Api from '../utils/api';

export const updateProfile = async ({
  name,
  bio,
  image,
}: {
  name: string;
  bio: string;
  image: { uri: string; type: string; name: string };
}) => {
  try {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('bio', bio);
    if (image?.uri && image?.type && image?.name) {
      formData.append('file', {
        uri: image.uri,
        type: image.type,
        name: image.name,
      } as any);
    }
    const response = await Api.put('v1/users/update-profile', formData, true);
    return response.body;
  } catch (error: any) {
    console.error('updateProfile API error:', error);
    throw error;
  }
};
