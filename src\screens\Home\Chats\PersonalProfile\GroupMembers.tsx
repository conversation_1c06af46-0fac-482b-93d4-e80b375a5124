import {
  View,
  Text,
  TouchableOpacity,
  Image,
  NativeSyntheticEvent,
  NativeTouchEvent,
  StyleSheet,
  useWindowDimensions,
} from 'react-native';
import { IMAGES } from '../../../../assets/Images';
import { SCREENS } from '../../../../navigation/screenNames';
import { colors } from '../../../../theme/colors';
import { MembershipStatus } from '../../../../types/chats.types';
import { navigateTo } from '../../../../utils/commonFunction';
import { FeatherIcons } from '../../../../utils/vectorIcons';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { commonFontStyle, wp } from '../../../../theme/fonts';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  getChatSpaceMembers,
  IGroupMember,
  IGroupMembersData,
} from '../../../../service/ChatSpacesService';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import MemberActionsModal, { ModalOption } from '../../Groups/reUsableComponents/MemberActionModal';
import RenderMemberActionModal from './MemberActionModal';
import { addChatsSpaceMember, assignMemberRole } from '../../../../api/Chatspace/chatspace.api';
import { ChatService } from '../../../../service/ChatService';
import MemberActionModal from './MemberActionModal';

type GroupMembersProps = {
  conversationInfo: ConversationInfo | null;
  myId?: string;
  currentConversation?: IConversation;
};

const GroupMembers: React.FC<GroupMembersProps> = ({
  conversationInfo,
  myId,
  currentConversation,
}) => {
  const [groupMembers, setGroupMembers] = useState<IGroupMember[]>([]);
  const [memberData, setMemberData] = useState<IGroupMembersData | null>(null);
  const memberRefs = useRef<Record<string, any>>({});
  const memberIds = groupMembers.map((member) => member.userId);
  const adminsAndOwner = groupMembers.filter(
    (member) => member.role === 'admin' || member.role === 'owner',
  );

  const [selectedMember, setSelectedMember] = useState<IGroupMember | null>(null);

  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState<{
    top?: number;
    bottom?: number;
    right: number;
  }>({ right: 0 });

  const { height: screenHeight } = useWindowDimensions();

  const conversationId = conversationInfo?.id || '';
  
  const currentUserRole =
    conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.membershipStatus : null;

  const groupCount =
    conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.memberCount : null;

  useFocusEffect(
    useCallback(() => {
      if (conversationInfo?.type === ConversationType.GROUP && currentConversation) {
        handleGroupMembers();
      }

      return () => {};
    }, [conversationInfo]),
  );

  const handleGroupMembers = async () => {
    try {
      const memberData = await getChatSpaceMembers(conversationId);
      if (memberData) {
        setMemberData(memberData);
        setGroupMembers(memberData.members);
      }
    } catch (error) {
      setGroupMembers([]);
    }
  };

  const handleMemberPress = (item: IGroupMember, event: NativeSyntheticEvent<NativeTouchEvent>) => {
    const currentUserRole =
      conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.membershipStatus : null;
    const selectedMemberRole = item.role;

    if (item.userId === myId) return;

    if (selectedMemberRole === 'owner') return;

    if (currentUserRole === 'member') return;

    if (currentUserRole === 'admin' && selectedMemberRole === 'admin') return;

    const { pageY } = event.nativeEvent;
    setSelectedMember(item);

    const MODAL_ESTIMATED_HEIGHT = 250;

    if (pageY > screenHeight - MODAL_ESTIMATED_HEIGHT) {
      setMenuPosition({ bottom: screenHeight - pageY, right: wp(5) });
    } else {
      setMenuPosition({ top: pageY, right: wp(5) });
    }

    setIsMenuVisible(true);
  };

  const menuOptions: ModalOption[] = useMemo(() => {
    if (!selectedMember) return [];

    const currentUserRole =
      conversationInfo?.type !== ConversationType.P2P ? conversationInfo?.membershipStatus : null;
    const selectedUserRole = selectedMember.role;
    const options: ModalOption[] = [];

    // for owners
    if (currentUserRole === 'owner') {
      if (selectedUserRole === 'admin') {
        options.push(
          // {
          //   label: 'Admin Privileges',
          //   icon: 'settings',
          //   onPress: () => handleMemberAction('adminPermissions', selectedMember),
          // },
          {
            label: 'Revoke Admin Rights',
            icon: 'shield-off',
            onPress: () => handleMemberAction('demote', selectedMember),
          },
        );
      } else if (selectedUserRole === 'member') {
        options.push(
          // {
          //   label: 'Member Privileges',
          //   icon: 'settings',
          //   onPress: () => handleMemberAction('memberPermissions', selectedMember),
          // },
          {
            label: 'Grant Admin Rights',
            icon: 'shield',
            onPress: () => handleMemberAction('promote', selectedMember),
          },
        );
      }

      options.push({
        label: 'Remove from group',
        icon: 'trash-2',
        onPress: () => handleMemberAction('remove', selectedMember),
        isDestructive: true,
      });
    }

    // for admins
    else if (currentUserRole === 'admin') {
      if (selectedUserRole === 'member') {
        options.push(
          // {
          //   label: 'Member Privileges',
          //   icon: 'settings',
          //   onPress: () => handleMemberAction('memberPermissions', selectedMember),
          // },
          {
            label: 'Remove from group',
            icon: 'trash-2',
            onPress: () => handleMemberAction('remove', selectedMember),
            isDestructive: true,
          },
        );
      }
    }

    return options;
  }, [selectedMember, currentUserRole, conversationId]);

  const handleMemberAction = async (action: string, member: IGroupMember) => {
    if (!member?.userId) return;

    try {
      switch (action) {
        case 'remove':
          const res = await addChatsSpaceMember(conversationId, [], [member.userId]);
          if (res?.systemMessage) {
            ChatService.onIncomingMessage(res.systemMessage);
          }
          setGroupMembers((prev) => prev.filter((m) => m.userId !== member.userId));
          break;

        case 'promote':
          const promoteRes = await assignMemberRole(
            conversationId,
            [member.userId],
            MembershipStatus.ADMIN,
          );
          if (promoteRes) {
            setGroupMembers((prev) =>
              prev.map((m) =>
                m.userId === member.userId ? { ...m, role: MembershipStatus.ADMIN } : m,
              ),
            );
            ChatService.onIncomingMessage(promoteRes.systemMessage);
          }
          break;

        case 'demote':
          const demoteRes = await assignMemberRole(
            conversationId,
            [member.userId],
            MembershipStatus.MEMBER,
          );
          if (demoteRes) {
            setGroupMembers((prev) =>
              prev.map((m) =>
                m.userId === member.userId ? { ...m, role: MembershipStatus.MEMBER } : m,
              ),
            );
            ChatService.onIncomingMessage(demoteRes.systemMessage);
          }
          break;

        case 'memberPermissions':
          navigateTo(SCREENS.GroupPermissions, { conversationId, selectedMember: member });
          break;

        case 'adminPermissions':
          navigateTo(SCREENS.AdminPermissions, {
            userData: member,
            userDetails: adminsAndOwner,
          });
          break;
      }
    } catch (error) {
      console.error(`Error handling ${action}:`, error);
    }
  };

  return (
    <>
      <View style={styles.section}>
        <View style={styles.memberHeader}>
          <Text style={styles.sectionTitle}>Group Members({groupCount})</Text>
          <TouchableOpacity
            style={styles.viewAllBadge}
            onPress={() =>
              navigateTo(SCREENS.GroupMembersScreen, {
                membersData: memberData,
                id: conversationId,
              })
            }
          >
            <Text style={styles.viewAllText}>View all</Text>
          </TouchableOpacity>
        </View>
        {currentUserRole !== MembershipStatus.MEMBER && (
          <TouchableOpacity
            style={styles.addMemberRow}
            onPress={() =>
              navigateTo(SCREENS.AddChatspaceMembers, {
                data: memberIds,
                chatSpaceId: conversationId,
              })
            }
          >
            <View style={styles.imageContainer}>
              <FeatherIcons name="users" size={20} color={colors.white} />
            </View>
            <Text style={styles.addMemberText}>{'Add member'}</Text>
          </TouchableOpacity>
        )}

        {groupMembers
          .sort((a, b) => {
            if (a.userId === myId) return -1;
            if (b.userId === myId) return 1;
            return 0;
          })
          .slice(0, 5)
          .map((item) => {
            const { user: memberUser, role } = item;

            return (
              <TouchableOpacity
                key={item._id}
                style={styles.memberRow}
                ref={(ref) => {
                  memberRefs.current[item.userId] = ref;
                }}
                onPress={(event) => handleMemberPress(item, event)}
              >
                <Image
                  source={memberUser?.image ? { uri: memberUser.image } : IMAGES.profile_image}
                  style={styles.memberAvatar}
                />
                <View style={styles.memberInfo}>
                  <Text style={styles.memberName}>{memberUser.name}</Text>
                  <Text style={styles.memberUsername}>@{memberUser.username}</Text>
                </View>
                {role !== 'member' && (
                  <View style={styles.roleBadge}>
                    <Text style={styles.roleText}>{role === 'owner' ? 'Owner' : 'Admin'}</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
      </View>

      <MemberActionModal
        visible={isMenuVisible}
        selectedMember={selectedMember}
        onClose={() => setIsMenuVisible(false)}
        menuOptions={menuOptions}
        onAction={handleMemberAction}
      />

      {/* Member Actions Modal */}
      <MemberActionsModal
        isVisible={isMenuVisible}
        onClose={() => setIsMenuVisible(false)}
        options={menuOptions}
        position={menuPosition}
      />
    </>
  );
};

export default GroupMembers;

const styles = StyleSheet.create({
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: colors.black_23,
  },
  memberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderColor: '#eee',
    gap: 12,
  },
  roleBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 14,
    backgroundColor: 'rgba(128, 128, 128, 0.08)',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray_80,
    lineHeight: 15,
    textAlign: 'center',
  },
  viewAllBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 19,
    backgroundColor: colors._F6F3FF_purple,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewAllText: {
    fontSize: 15,
    color: colors.mainPurple,
    fontWeight: '500',
    lineHeight: 20,
    textAlign: 'center',
  },
  addMemberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 10,
  },
  addMemberIcon: {
    backgroundColor: colors.mainPurple,
    borderRadius: 25,
    height: 50,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMemberText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  memberAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black_23,
  },
  memberUsername: {
    fontSize: 13,
    color: colors.gray_80,
  },
  imageContainer: {
    backgroundColor: colors.mainPurple,
    borderRadius: 40,
    height: 50,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  memberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
});
