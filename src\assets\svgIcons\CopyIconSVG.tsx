import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgIconProps {
  size?: number;
  color?: string;
}

const CopyIconSVG: React.FC<SvgIconProps> = ({
  size = 19,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 19 19"
      fill="none"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.373 5.232c-.141.635-.202 1.469-.202 2.579 0 2.045.215 3.084.651 3.664.387.514 1.1.846 2.772.944a.844.844 0 01-.098 1.684c-1.764-.103-3.159-.467-4.022-1.614-.813-1.08-.99-2.666-.99-4.679 0-1.147.06-2.128.242-2.944.184-.826.502-1.535 1.054-2.086.551-.552 1.26-.87 2.086-1.054.816-.181 1.797-.242 2.945-.242 2.012 0 3.598.177 4.678.99 1.147.863 1.511 2.258 1.614 4.022a.843.843 0 11-1.684.098c-.098-1.672-.43-2.385-.944-2.772-.58-.436-1.62-.65-3.664-.65-1.11 0-1.944.06-2.579.201-.625.139-1.005.345-1.26.6-.254.254-.46.634-.599 1.26z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.59 9.45c-.14.636-.2 1.47-.2 2.58 0 1.109.06 1.943.2 2.578.14.625.345 1.004.6 ************.634.46 1.26.599.635.14 1.469.201 2.578.201 1.11 0 1.944-.06 2.579-.201.625-.14 1.004-.345 1.26-.6.254-.255.46-.634.599-1.26.14-.634.201-1.468.201-2.578 0-1.11-.06-1.943-.201-2.578-.14-.626-.345-1.005-.6-1.26-.255-.255-.634-.46-1.26-.6-.634-.14-1.468-.2-2.578-.2-1.11 0-1.943.06-2.578.2-.626.14-1.005.345-1.26.6-.255.255-.46.634-.6 1.26zm1.494-3.505c.816-.181 1.796-.242 2.944-.242s2.128.06 2.945.242c.826.184 1.535.502 2.086 1.053.551.552.87 1.261 1.054 2.087.181.816.241 1.796.241 2.944s-.06 2.128-.241 2.945c-.184.825-.503 1.535-1.054 2.086-.551.552-1.26.87-2.086 1.053-.817.182-1.797.242-2.945.242s-2.128-.06-2.944-.242c-.826-.183-1.535-.502-2.087-1.053-.551-.551-.87-1.26-1.053-2.086-.181-.817-.242-1.797-.242-2.945s.06-2.128.242-2.944c.184-.826.502-1.535 1.053-2.087.552-.551 1.261-.87 2.087-1.053z"
        fill={color}
      />
    </Svg>
  );
};

export default CopyIconSVG;
