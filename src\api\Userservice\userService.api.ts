import Api from '../../utils/api';

const userServiceRoutes = {
  submitFeedback: 'v1/users/userFeedbacks',
};

export interface UserFeedbackPayload {
  name: string;
  email: string;
  message: string;
}

export const submitUserFeedback = async (payload: UserFeedbackPayload) => {
  try {
    const res = await Api.post(userServiceRoutes.submitFeedback, payload);
    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
};
