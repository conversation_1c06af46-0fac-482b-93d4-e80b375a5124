// components/GroupOptionsModal.tsx
import React from 'react';
import { Modal, TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { colors } from '../../../theme/colors';
import { ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import { MembershipStatus } from '../../../types/chats.types';

interface GroupOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onOptionPress: (option: string) => void;
  type: ConversationType;
  isMuted: boolean;
  role: MembershipStatus;
}

const GroupOptionsModal: React.FC<GroupOptionsModalProps> = ({
  visible,
  onClose,
  onOptionPress,
  type,
  isMuted,
  role,
}) => {
  const muteOptionText = isMuted ? 'Unmute notifications' : 'Mute notifications';
  const showExitGroup = [MembershipStatus.ADMIN, MembershipStatus.MEMBER].includes(role)
    ? 'Exit group'
    : null;

  const GroupOptions = (
    ['Group info', muteOptionText, 'Clear chat', showExitGroup] as (string | null)[]
  ).filter((opt): opt is string => typeof opt === 'string');

  const p2pOptions = ['View info', muteOptionText, 'Clear chat'];
  const optionsToShow = type === ConversationType.GROUP ? GroupOptions : p2pOptions;

  return (
    <Modal transparent animationType="fade" visible={visible} onRequestClose={onClose}>
      <TouchableOpacity activeOpacity={1} style={styles.overlay} onPressOut={onClose}>
        <View style={styles.menu}>
          {optionsToShow.map((option) => (
            <TouchableOpacity
              key={option}
              style={styles.item}
              onPress={() => {
                onClose();
                onOptionPress(option);
              }}
            >
              <Text style={styles.itemText}>{option}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default GroupOptionsModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  menu: {
    position: 'absolute',
    top: 30,
    right: 33,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 10,
  },
  item: {
    paddingVertical: 5,
  },
  itemText: {
    fontSize: 16,
    color: colors.black, // Or any dark color
  },
});
