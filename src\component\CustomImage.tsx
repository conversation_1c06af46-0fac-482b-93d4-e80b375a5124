import { StyleSheet, TouchableOpacity, ViewStyle, Image } from 'react-native';
import React from 'react';
// import FastImage, { FastImageProps, ImageStyle } from "react-native-fast-image";
import { hp } from '../theme/fonts';
import { colors } from '../theme/colors';

interface Props {
  onPress?: () => void;
  source?: any;
  size?: number;
  containerStyle?: ViewStyle;
  imageStyle?: ImageStyle;
  tintColor?: any | undefined;
  uri?: string;
  disabled?: boolean;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  props?: FastImageProps;
}

const CustomImage = ({
  onPress,
  source,
  size,
  containerStyle,
  imageStyle,
  tintColor = colors.white,
  uri,
  disabled = false,
  resizeMode = 'contain',
  ...props
}: Props) => {
  return (
    <TouchableOpacity
      activeOpacity={onPress ? 0.5 : 1}
      onPress={onPress}
      disabled={disabled}
      style={{ ...containerStyle }}
    >
      {/* <FastImage
        source={uri ? { uri: uri } : source}
        defaultSource={source ? source : undefined}
        style={[{ width: size, height: size }, imageStyle]}
        resizeMode={resizeMode}
        tintColor={uri ? undefined : tintColor}
        {...props}
      /> */}
      <Image
        source={uri ? { uri: uri } : source}
        defaultSource={source || undefined}
        style={[{ width: size, height: size }, imageStyle]}
        resizeMode={resizeMode}
        // Note: tintColor only works on static image assets
        tintColor={uri ? undefined : tintColor}
        {...props}
      />
    </TouchableOpacity>
  );
};

export default CustomImage;

const styles = StyleSheet.create({
  btnContainer: {
    height: hp(4),
    width: hp(4),
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    borderColor: colors.white,
  },
});
