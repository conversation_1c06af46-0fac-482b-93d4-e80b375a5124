import * as React from "react"
import Svg, { Path } from "react-native-svg"

function ChatHistorySVG({ size = 16, color = "#232323", ...props }) {
  return (
    <Svg
      width={size}
      height={(size * 16) / 15} 
      viewBox="0 0 15 16"
      fill="none"
      {...props}
    >
      <Path
        d="M8.086 4.543v3.214l2.133 2.133a.586.586 0 11-.829.829L7.086 8.414A.586.586 0 016.914 8V4.543a.586.586 0 111.172 0zM9.805.5h-4.61A5.177 5.177 0 001.79 1.774v-.688a.586.586 0 10-1.172 0V3.39c0 .323.264.586.586.586h2.304a.586.586 0 100-1.172H2.4a4.037 4.037 0 012.796-1.133h4.61a4.028 4.028 0 014.023 4.023v4.61a4.028 4.028 0 01-4.023 4.023h-4.61a4.028 4.028 0 01-4.023-4.023V8A.586.586 0 100 8v2.305A5.201 5.201 0 005.195 15.5h4.61A5.201 5.201 0 0015 10.305v-4.61A5.201 5.201 0 009.805.5z"
        fill={color}
      />
    </Svg>
  )
}

export default ChatHistorySVG
