import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import CommonView from '../../../component/CommonView';
import { colors } from '../../../theme/colors';
import ChatSVG from '../../../assets/svgIcons/ChatSVG';
import CallSVG from '../../../assets/svgIcons/CallSVG';
import VideoCallSVG from '../../../assets/svgIcons/VideoCallSVG';
import { RouteProp, useRoute } from '@react-navigation/native';
import ContactAvatarSVG from '../../../assets/svgIcons/ContactAvatarSVG';
import { useMe } from '../../../hooks/util/useMe';
import { SCREENS } from '../../../navigation/screenNames';
import { navigateTo } from '../../../utils/commonFunction';
import { useCallContext } from '../../../Context/CallProvider';
import { CallType } from '../../../types/calls.types';

export type ViewContactScreenParams = {
  ViewContactScreen: {
    contact: {
      name?: string;
      phoneNumber: string;
      image?: string;
      userId: string;
    };
    onMessagePress?: () => void;
  };
};

const ViewContactScreen = () => {
  const route = useRoute<RouteProp<ViewContactScreenParams, 'ViewContactScreen'>>();
  const { contact, onMessagePress } = route.params;

  const { startCall } = useCallContext();

  const { user } = useMe();
  const isMyNumber = contact?.userId === user?._id;

  const isRegistered = contact?.userId !== null;
  const formatPhoneNumber = (phone: string): string => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('91') && cleaned.length > 2) {
      return `+91-${cleaned.slice(2)}`;
    }
    return `+${cleaned}`;
  };

  async function handleCall(type: CallType) {
    const recipeient = {
      userId: contact.userId,
      name: contact.name,
      image: contact.image,
      username: contact.name,
    };
    startCall({
      recipients: [recipeient],
      callType: type,
      origin: { type: 'directConversation', conversationId: contact.userId },
    });
    navigateTo(SCREENS.MainCallScreen, {
      recipients: [recipeient],
      callType: type,
      origin: { type: 'directConversation', conversationId: contact.userId },
    });
  }

  if (!contact) {
    return (
      <CommonView headerTitle="View Contact">
        <Text style={styles.errorText}>No contact details provided.</Text>
      </CommonView>
    );
  }
  return (
    <CommonView headerTitle="View contact">
      <View style={styles.card}>
        <View style={styles.topRow}>
          <ContactAvatarSVG style={styles.avatar} />

          <View style={{ flex: 1 }}>
            <Text style={styles.name}>{contact.name}</Text>
            <Text style={styles.bio}>Bio</Text>
          </View>
          {isRegistered && !isMyNumber ? (
            <TouchableOpacity onPress={onMessagePress}>
              <ChatSVG size={24} color={colors.mainPurple} />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={styles.divider} />

        <View style={styles.phoneRow}>
          <Text style={styles.phone}>{formatPhoneNumber(contact.phoneNumber)}</Text>

          {isRegistered && !isMyNumber ? (
            <>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => {
                  handleCall('audio');
                }}
              >
                <CallSVG size={22} color={colors.mainPurple} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => {
                  handleCall('video');
                }}
              >
                <VideoCallSVG size={22} color={colors.mainPurple} />
              </TouchableOpacity>
            </>
          ) : null}
        </View>
      </View>
    </CommonView>
  );
};

export default ViewContactScreen;

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 16,
    margin: 2,
    padding: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 8,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.black,
  },
  bio: {
    fontSize: 13,
    color: colors.gray_80 || '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginVertical: 8,
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  phone: {
    fontSize: 16,
    color: colors.black,
    flex: 1,
  },
  iconButton: {
    marginLeft: 12,
  },
  errorText: {
    padding: 16,
    fontSize: 16,
    color: 'red',
  },
});
