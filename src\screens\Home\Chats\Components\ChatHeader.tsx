import {
  ActivityIndicator,
  Image,
  InteractionManager,
  Keyboard,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState } from 'react';
import { IChatScreenProps } from '../ChatSpecificScreen';
import {
  ChannelType,
  ConversationType,
  IMessage,
  MessageStatus,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { FontAwesome6Icons } from '../../../../utils/vectorIcons';
import { useNavigation } from '@react-navigation/native';
import { useMe } from '../../../../hooks/util/useMe';
import ThreeDotsSVG from '../../../../assets/svgIcons/ThreeDotsSVG';
import { colors } from '../../../../theme/colors';
import { ChatService } from '../../../../service/ChatService';
import {
  dayPipe,
  errorToast,
  navigateTo,
  safeResetIfInsideDeletedChat,
  showToast,
} from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';
import { ChatSocket } from '../../../../socket-client/ChatSocket';
import useSocket from '../../../../socket-client/useSocket';
import useConversations from '../../../../hooks/conversations/useConversations';
import {
  ConversationSchema,
  IConversation,
} from '../../../../device-storage/realm/schemas/ConversationSchema';
import { maxMembersInCall } from '../../../../utils/constants';
import { useCallContext } from '../../../../Context/CallProvider';
import { IUser, UserSchema } from '../../../../device-storage/realm/schemas/UserSchema';
import { IMAGES } from '../../../../assets/Images';
import CallSVG from '../../../../assets/svgIcons/CallSVG';
import VideoCallSVG from '../../../../assets/svgIcons/VideoCallSVG';
import {
  CallOrigin,
  CallType,
  GroupConversationDetails,
  StartCallParams,
} from '../../../../types/calls.types';
import Toast from 'react-native-toast-message';
import ButtonPurple from '../../../../component/ButtonPurple';
import DeleteSVG from '../../../../assets/svgIcons/DeleteSVG';
import ForwardArrow from '../../../../assets/svgIcons/ForwardArrow';
import PinSVG from '../../../../assets/svgIcons/PinSVG';
import ReplySVG from '../../../../assets/svgIcons/ReplySVG';
import PinModal from '../../../../component/PersonalChat/PinModal';
import { useFetchMessages } from '../../../../hooks/chats/messages/useFetchMessages';
import DeleteMessageModal from '../../../../component/PersonalChat/DeleteMessageModal';
import GroupOptionsModal from '../../Groups/GroupOptionsModal';
import CustomAlert from '../../../../component/Common/CustomAlert';
import { SCREEN_HEIGHT, SCREEN_WIDTH } from '../../../../theme/fonts';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import UserAvatar from '../../../../component/utils/UserAvatar';
import { MembershipStatus } from '../../../../types/chats.types';
import { isConversationMuted } from '../../../../lib/chatLib';
import Logger from '../../../../lib/Logger';
// import { useWhatChanged } from '@simbathesailor/use-what-changed';

interface IChatHeaderProps {
  userData: IChatScreenProps;
  editedMessage: IMessage | null;
  selectedMsgs: IMessage[] | null;
  setEditedMessage: (value: IMessage | null) => void;
  userMemberDataState: UserSchema | null;
  onReplyMessage?: () => void;
  setSelectedMsgs?: (value?: any) => void;
  replyMsgData?: IMessage | null;
  // memberPermissions: any;
  presenceSnapshot?: { isUserOnline?: boolean; lastSeen?: number } | null;
  conversationInfo: ConversationInfo;
}

const ChatHeader = ({
  userData,
  editedMessage,
  selectedMsgs = [],
  setEditedMessage = () => {},
  userMemberDataState,
  onReplyMessage = () => {},
  setSelectedMsgs = () => {},
  // memberPermissions,
  replyMsgData,
  presenceSnapshot,
  conversationInfo,
}: IChatHeaderProps) => {
  // Logger.logBg('Rendering ChatHeader', '#6a1b9a', 'white');
  const [isGroupModalVisible, setGroupModalVisible] = useState<boolean>(false);
  const [showExitGroupAlert, setShowExitGroupAlert] = useState<boolean>(false);
  const [isExiting, setIsExiting] = useState(false);
  const [isFollowing, setIsFollowing] = useState<boolean | undefined>(userData?.isFollowing);
  const [deleteModalLoading, setDeleteModalLoading] = useState<boolean>(false);
  // console.log('=====>HEADERconversationInfo<=====', conversationInfo);
  console.log(' [renderlog] chatheader rendered');

  const role =
    conversationInfo?.type !== ConversationType.P2P
      ? conversationInfo?.membershipStatus
      : MembershipStatus.MEMBER;

  const { socket } = useSocket();
  const navigation = useNavigation();
  const { user: me } = useMe();
  const { startCall, callDetails } = useCallContext();
  const { pinnedMessages } = useFetchMessages(String(conversationInfo?.id));
  const insets = useSafeAreaInsets();

  const isMyNumber = conversationInfo?.id === me?._id;
  const { getConversattionById } = useConversations();
  const showCallButtons =
    callDetails.state === 'idle' &&
    !isMyNumber &&
    ((conversationInfo?.type === ConversationType.GROUP &&
      (conversationInfo?.memberCount ?? maxMembersInCall + 1) <= maxMembersInCall) ||
      (conversationInfo?.type === ConversationType.P2P && !userMemberDataState?.isBlocked));
  const [isPinMessage, setIsPinMessage] = useState<boolean>(false);
  const [deleteBtmSheet, setDeleteBtmSheet] = useState<boolean>(false);

  const ConversationControls = () => (
    <>
      <TouchableOpacity
        onPress={() => setGroupModalVisible(true)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <ThreeDotsSVG color={colors.white} size={19} />
      </TouchableOpacity>

      <GroupOptionsModal
        visible={isGroupModalVisible}
        onClose={() => setGroupModalVisible(false)}
        onOptionPress={
          conversationInfo?.type === ConversationType.GROUP
            ? handleGroupOptionPress
            : handleP2POptionPress
        }
        type={conversationInfo?.type as ConversationType}
        isMuted={isConversationMuted(conversationInfo?.conversationSettings?.muteUntil)}
        role={role}
      />

      <CustomAlert
        visible={showExitGroupAlert}
        onCancel={() => setShowExitGroupAlert(false)}
        onConfirm={handleConfirmExitGroup}
        title="Exit Group"
        message={`Are you sure you want to exit the group "${conversationInfo?.displayName}"?`}
        confirmText="Exit"
        cancelText="Cancel"
      />
    </>
  );

  const handleConfirmExitGroup = async () => {
    setShowExitGroupAlert(false);
    setIsExiting(true);

    ChatSocket.emitLeaveChatspace(socket, conversationInfo?.id as string, async (response) => {
      setIsExiting(false);
      if (response?.error === 'owner can not leave chatSpace') {
        errorToast('The group owner cannot leave the group.');
        return;
      }

      if (response?.error) {
        console.log('Failed to exit group:', response.error);
        errorToast('Failed to exit group. Please try again.');
        return;
      }

      if (response?.status === true) {
        InteractionManager.runAfterInteractions(() => {
          ChatService.deleteConversation(conversationInfo?.id as string);
        });
        safeResetIfInsideDeletedChat(conversationInfo?.id as string);
      }
    });
  };

  const handleGroupOptionPress = (option: string) => {
    setGroupModalVisible(false);
    switch (option) {
      case 'Group info':
        navigateTo(SCREENS.PersonalProfileScreen, { userData, conversationInfo });
        break;
      case 'Mute notifications':
      case 'Unmute notifications':
        if (conversationInfo.conversationSettings?.id) {
          const isMuted = isConversationMuted(conversationInfo?.conversationSettings.muteUntil);
          ChatService.toggleConversationMute(conversationInfo.id, isMuted ? undefined : 'always');
        }
        break;
      case 'Clear chat':
        ChatService.clearChat(conversationInfo?.id as string);
        break;
      case 'Exit group':
        setShowExitGroupAlert(true);
        break;
    }
  };

  const handleP2POptionPress = (option: string) => {
    setGroupModalVisible(false);
    switch (option) {
      case 'View info':
        navigateTo(SCREENS.PersonalProfileScreen, { userData, conversationInfo });
        break;
      case 'Clear chat':
        ChatService.clearChat(conversationInfo?.id as string);
        break;
      case 'Mute notifications':
      case 'Unmute notifications':
        if (conversationInfo.conversationSettings?.id) {
          const isMuted = isConversationMuted(conversationInfo?.conversationSettings.muteUntil);
          ChatService.toggleConversationMute(conversationInfo.id, isMuted ? undefined : 'always');
        }
        break;
    }
  };

  const handleFollowToggle = async () => {
    const chatSpaceId = conversationInfo?.id;
    if (isFollowing) {
      ChatSocket.emitLeaveChatspace(socket, chatSpaceId as string, async (response) => {
        if (response?.error) {
          console.log('Unfollow failed:', response.error);
          return;
        }
        setIsFollowing(false);
        navigation.goBack();
        ChatService.deleteConversation(chatSpaceId as string);
        showToast(`Unfollowed ${conversationInfo?.displayName}`);
      });
    } else {
      ChatSocket.joinChannel(socket, chatSpaceId as string, async (response) => {
        if (response?.error) {
          console.log('Follow failed:', response.error);
          return;
        }
        // Create conversation
        ChatService.onIncomingMessage(response);

        ChatService.updateMemberCount(chatSpaceId as string, 1, 0);

        setIsFollowing(true);
        showToast(`Now following ${conversationInfo?.displayName}`);
      });
    }
  };

  async function handleCall(type: CallType) {
    if (
      conversationInfo?.type === ConversationType.CHANNEL ||
      (conversationInfo?.type === ConversationType.GROUP &&
        (conversationInfo?.memberCount ?? maxMembersInCall + 1) > maxMembersInCall)
    ) {
      Toast.show({
        type: 'error',
        text1: 'Call Limit Exceeded',
        text2: 'You can only make a call to 10 members at a time.And Cannot make call in channel',
      });
      return;
    }

    if (conversationInfo?.type === ConversationType.P2P) {
      const recipeient: Partial<IUser> = {
        id: conversationInfo.id,
        name: conversationInfo.displayName,
        profilePic: conversationInfo.displayPic,
        username: conversationInfo.displayName,
      };

      startCall({
        recipients: [recipeient],
        callType: type,
        origin: { type: 'directConversation', conversationId: conversationInfo?.id },
      });
    } else if (
      conversationInfo?.type === ConversationType.GROUP &&
      conversationInfo?.id != undefined
    ) {
      const origin: GroupConversationDetails = {
        //todo: update to id in chatspaceId
        chatSpaceId: conversationInfo.id,
        displayName: conversationInfo.displayName,
        displayPic: conversationInfo.displayPic,
        memberCount: conversationInfo.memberCount,
        id: conversationInfo.id,
      };
      startCall({
        recipients: [],
        callType: type,
        origin: {
          type: 'groupConversation',
          conversation: origin,
        },
      });
    }
  }

  const onPinMessage = (value?: any) => {
    setIsPinMessage(false);

    let hours = 24;
    switch (value) {
      case '1_HOUR':
        hours = 1;
        break;
      case '24_HOURS':
        hours = 24;
        break;
      case '7_DAYS':
        hours = 168; // 7 * 24 hours
        break;
      case '30_DAYS':
        hours = 720; // 30 * 24 hours
        break;
    }

    selectedMsgs?.forEach((msg) => {
      if (msg?.status !== MessageStatus.SCHEDULED) {
        ChatService.pinMessage(msg.localId, conversationInfo?.id as string, hours);
      }
    });

    setSelectedMsgs([]);
  };

  // Single useWhatChanged with ALL variables
  // useWhatChanged(
  //   [
  //     // Props
  //     userData,
  //     editedMessage,
  //     selectedMsgs,
  //     setEditedMessage,
  //     userMemberDataState,
  //     onReplyMessage,
  //     setSelectedMsgs,
  //     replyMsgData,
  //     presenceSnapshot,
  //     conversationInfo,
  //     role,
  //     socket,
  //     navigation,
  //     me,
  //     callDetails,
  //     pinnedMessages,
  //     isGroupModalVisible,
  //     showExitGroupAlert,
  //     isExiting,
  //     isFollowing,
  //     deleteModalLoading,
  //     isPinMessage,
  //     deleteBtmSheet,
  //   ],
  //   'userData, editedMessage, selectedMsgs, setEditedMessage, userMemberDataState, onReplyMessage, setSelectedMsgs, replyMsgData, presenceSnapshot, conversationInfo, role, socket, navigation, me, callDetails, pinnedMessages, isGroupModalVisible, showExitGroupAlert, isExiting, isFollowing, deleteModalLoading, isPinMessage, deleteBtmSheet',
  //   'ChatHeader-all-variables',
  // );

  return (
    <>
      <View style={{ backgroundColor: 'transparent' }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingTop: insets.top,
            paddingBottom: 18,
          }}
        >
          <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => {
                if (editedMessage) {
                  setEditedMessage(null);
                  return;
                }
                // navigateTo(SCREENS.HomeScreen);
                navigation.goBack();
              }}
            >
              <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
            </TouchableOpacity>

            {Number(selectedMsgs?.length) > 0 || editedMessage ? (
              <View style={{ marginLeft: 12, height: 50, justifyContent: 'center' }}>
                <Text
                  style={{
                    color: colors.white,
                    fontSize: editedMessage ? 18 : 16,
                    fontWeight: editedMessage ? '600' : '800',
                    marginBottom: 4,
                    textTransform: editedMessage ? 'none' : 'capitalize',
                  }}
                >
                  {editedMessage ? 'Edit message' : selectedMsgs?.[0]?.status}
                </Text>
                {!editedMessage && (
                  <Text
                    style={{ color: colors.white, fontSize: 14, fontWeight: '400' }}
                    numberOfLines={1}
                  >
                    {dayPipe(selectedMsgs?.[selectedMsgs.length - 1]?.createdAt, 'time')}
                  </Text>
                )}
              </View>
            ) : (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginLeft: 17,
                }}
              >
                <View style={{ marginRight: 15 }}>
                  <UserAvatar imgUrl={conversationInfo?.displayPic} />
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      if (
                        conversationInfo?.type === ConversationType.CHANNEL &&
                        conversationInfo?.membershipStatus === 'discovering'
                      ) {
                        return;
                      }
                      navigateTo(SCREENS.PersonalProfileScreen, {
                        userData,
                        convId: conversationInfo.id,
                      });
                    }}
                  >
                    <View>
                      <Text
                        style={{
                          color: colors.white,
                          fontSize: 16,
                          fontWeight: '800',
                        }}
                      >
                        {(conversationInfo?.displayName || 'Unknown').length > 13
                          ? (conversationInfo?.displayName || 'Unknown').substring(0, 12) + '...'
                          : conversationInfo?.displayName || 'Unknown'}
                      </Text>
                      <Text
                        style={{
                          color: colors.white,
                          fontSize: 12,
                          fontWeight: '400',
                        }}
                        numberOfLines={1}
                      >
                        {conversationInfo?.type === ConversationType.P2P &&
                        !userMemberDataState?.isBlocked
                          ? presenceSnapshot?.isUserOnline === true
                            ? 'online'
                            : presenceSnapshot?.lastSeen
                            ? `Last seen at ${dayPipe(
                                new Date(presenceSnapshot.lastSeen),
                                'diffCheck',
                              )}`
                            : null
                          : conversationInfo?.type === ConversationType.GROUP
                          ? `${conversationInfo?.memberCount} members`
                          : conversationInfo?.type === ConversationType.CHANNEL
                          ? `${conversationInfo?.memberCount} followers`
                          : null}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>

          {Number(selectedMsgs?.length) > 0 ? (
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 30 }}>
              {Number(selectedMsgs?.length) <= 1 && !replyMsgData ? (
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    onReplyMessage();
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <ReplySVG color={colors.white} size={19} />
                </TouchableOpacity>
              ) : null}
              {pinnedMessages?.length + Number(selectedMsgs?.length) <= 5 && (
                // (conversationInfo?.type === ConversationType.P2P ||
                //   liveConversation?.role === ChannelType.OWNER ||
                //   liveConversation?.role === ChannelType.ADMIN ||
                //   (liveConversation?.role === ChannelType.MEMBER &&
                //     memberPermissions?.canPinMessage)) &&
                <TouchableOpacity
                  onPress={() => setIsPinMessage(true)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <PinSVG color={colors.white} size={20} />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={{}}
                onPress={() => {
                  navigateTo(SCREENS.ForwardMessagesScreen, {
                    messages: selectedMsgs,
                    onForwardComplete: () => {
                      setSelectedMsgs([]);
                    },
                  });
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <ForwardArrow color={colors.white} size={19} />
              </TouchableOpacity>
              <TouchableOpacity
                style={{}}
                onPress={() => {
                  setDeleteBtmSheet(true);
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <DeleteSVG color={colors.white} size={18} />
              </TouchableOpacity>
            </View>
          ) : conversationInfo?.type === 'channel' &&
            (conversationInfo?.membershipStatus === MembershipStatus.MEMBER ||
              conversationInfo?.membershipStatus === MembershipStatus.DISCOVERING) ? (
            <View style={{ paddingTop: 6 }}>
              <ButtonPurple
                title={isFollowing ? 'Following' : 'Follow'}
                extraStyle={{
                  borderRadius: 34,
                  height: 34,
                  paddingHorizontal: 16,
                  backgroundColor: colors._785DC3_purple,
                }}
                titleColor={colors.white}
                onPress={handleFollowToggle}
              />
            </View>
          ) : conversationInfo?.type === 'channel' ? (
            <View style={{ paddingRight: 16 }}>
              {/* {userData.conversation?.role === 'owner' && (
                <TouchableOpacity
                  onPress={() => {
                    openModal(ModalType.SelectLiveMode);
                  }}
                >
                  <HeartSVG color={colors.white} size={24} />
                </TouchableOpacity>
              )} */}
            </View>
          ) : editedMessage ? null : (
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
              {!isMyNumber && showCallButtons && (
                <View style={{ flexDirection: 'row', gap: 5 }}>
                  <TouchableOpacity
                    style={{
                      padding: 10,
                      borderRadius: 100,
                    }}
                    onPress={() => handleCall('audio')}
                  >
                    <CallSVG color={colors.white} size={21} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      padding: 10,
                      borderRadius: 100,
                    }}
                    onPress={() => handleCall('video')}
                  >
                    <VideoCallSVG color={colors.white} size={23} />
                  </TouchableOpacity>
                </View>
              )}

              {conversationInfo?.type === ConversationType.GROUP && (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 22 }}>
                  <ConversationControls />
                </View>
              )}

              {conversationInfo?.type === ConversationType.P2P && (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 22 }}>
                  <ConversationControls />
                </View>
              )}
            </View>
          )}
        </View>
      </View>

      {isExiting && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
            height: SCREEN_HEIGHT,
            width: SCREEN_WIDTH,
          }}
        >
          <ActivityIndicator size="large" color={colors.white} />
        </View>
      )}

      {/* Pin modal */}
      <PinModal
        isVisible={isPinMessage}
        onCloseModal={() => setIsPinMessage(false)}
        onConfirmPin={onPinMessage}
      />

      <DeleteMessageModal
        isVisible={deleteBtmSheet}
        onCloseModal={() => setDeleteBtmSheet(false)}
        onPressDeleteForMe={async () => {
          try {
            setDeleteModalLoading(true);
            const messageIds = selectedMsgs?.map((msg) => msg.localId) || [];
            ChatService.deleteMessages(messageIds, conversationInfo?.id as string, false);
            setSelectedMsgs([]);
          } finally {
            setDeleteModalLoading(false);
            setDeleteBtmSheet(false);
          }
        }}
        onPressDeleteForBoth={async () => {
          try {
            setDeleteModalLoading(true);
            const messageIds = selectedMsgs?.map((msg) => msg.localId) || [];
            ChatService.deleteMessages(messageIds, conversationInfo?.id as string, true);
            setSelectedMsgs([]);
          } finally {
            setDeleteModalLoading(false);
            setDeleteBtmSheet(false);
          }
        }}
        selectedMsgs={selectedMsgs}
        user={me}
        type={conversationInfo?.type}
        role={role}
        loading={deleteModalLoading}
      />
    </>
  );
};

export default React.memo(ChatHeader);

const styles = StyleSheet.create({
  typingIndicator: {
    color: colors.white,
    fontWeight: '400',
  },
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
});
