import Realm from 'realm';
import { ChannelType, ConversationType, MessageSchema } from './MessageSchema';
import { realmSchemaNames } from './schemaNames';
import { MemberPermissions } from '../../../screens/Home/Groups/GroupPermissions';
import { AdminPermissions } from '../../../screens/Home/Groups/AdminPermissions';
import { ConversationSettingsSchema } from './ConversationSettingsSchema';
import { UserSchema } from './UserSchema';
import { ChatSpaceSchema } from './ChatSpaceSchema';

export enum joinRequestStatus {
  NONE = 'none',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  MEMBER = 'member',
}

export interface IConversation {
  role: ChannelType;
  id: string;
  displayName: string;
  displayPic?: string;
  phoneNumber?: string;
  type: ConversationType;
  lastMessage?: MessageSchema;
  lastMessageTimestamp: number;
  unreadCount: number;
  muteUntil?: string;
  isPinned?: boolean;
  isArchived?: boolean;
  createdAt: number;
  updatedAt: number;

  // for channel
  isLiveStreaming?: boolean;
  isDeleted?: boolean;
  typingUsers?: string[];

  conversationSettings: ConversationSettingsSchema;

  user?: UserSchema;
  chatSpace?: ChatSpaceSchema;
}

export interface IExploreChannelCardItem {
  _id: string;
  name?: string;
  displayName?: string;
  description?: string;
  displayPic?: string;
  chatSpaceId?: string;
  isPrivate?: boolean;
  inviteLink?: string | null;
  memberCount?: number;
  isLiveStreaming?: boolean;
  createdAt?: number | string;
  updatedAt?: number | string;
  createdBy?: string;
  type?: string;
  chatType?: string;
  membership?: any[];
  __v?: number;
  isDeleted?: boolean;
  inviteCode: string;
}

export const mapConversationToExploreChannelItem = (
  conversation: IConversation,
): IExploreChannelCardItem => {
  return {
    _id: conversation.id,
    name: conversation.displayName,
    displayPic: conversation.displayPic || '',
    isLiveStreaming: conversation.isLiveStreaming || false,
    createdAt: conversation.createdAt || '',
    updatedAt: conversation.updatedAt || '',
    type: ConversationType.CHANNEL,
    chatType: 'ChannelChatSpace',
    membership: [],
    __v: 0,
    isDeleted: conversation.isDeleted,
  };
};

export class ConversationSchema extends Realm.Object<ConversationSchema> implements IConversation {
  id!: string;
  displayName!: string;
  displayPic?: string;
  phoneNumber?: string;
  type!: ConversationType;
  lastMessage?: MessageSchema;
  lastMessageTimestamp!: number;
  unreadCount!: number;
  muteUntil?: string;
  isPinned?: boolean;
  isArchived?: boolean;
  createdAt!: number;
  updatedAt!: number;
  isLiveStreaming?: boolean;
  isDeleted?: boolean;
  typingUsers?: string[];
  conversationSettings!: ConversationSettingsSchema;
  user?: UserSchema;
  chatSpace?: ChatSpaceSchema;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.conversation,
    primaryKey: 'id',
    properties: {
      id: 'string',
      displayName: 'string',
      displayPic: 'string?',
      phoneNumber: 'string?',
      type: 'string',
      lastMessage: `${realmSchemaNames.message}?`,
      lastMessageTimestamp: 'int',
      unreadCount: 'int',
      muteUntil: 'string?',
      isPinned: { type: 'bool', default: false },
      isArchived: { type: 'bool', default: false },
      createdAt: 'int',
      updatedAt: 'int',
      isLiveStreaming: 'bool?',
      isDeleted: { type: 'bool', default: false },
      typingUsers: 'string[]',
      conversationSettings: `${realmSchemaNames.conversation_settings}`,
      user: `${realmSchemaNames.user}?`,
      chatSpace: `${realmSchemaNames.chat_space}?`,
    },
  };
}
