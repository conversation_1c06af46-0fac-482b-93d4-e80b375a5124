import React, { useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View, StyleSheet, Image } from 'react-native';
import CommonView from '../../../component/CommonView';
import { IMAGES } from '../../../assets/Images';
import CustomImage from '../../../component/CustomImage';
import { colors } from '../../../theme/colors';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';
import ModalWrapper from '../../../component/ModalWrapper';
import { deleteChatSpace } from '../../../api/Chatspace/chatspace.api';
import DeleteSVG from '../../../assets/svgIcons/DeleteSVG';
import AddMemberSVG from '../../../assets/svgIcons/AddMemberSVG';
import AdminSettingSVG from '../../../assets/svgIcons/AdminSettingSVG';
import PermissionSettingSVG from '../../../assets/svgIcons/PermissionSettingSVG';
import ChatHistorySVG from '../../../assets/svgIcons/ChatHistorySVG';
import GroupTypeSVG from '../../../assets/svgIcons/GroupTypeSVG';
import ReactionSVG from '../../../assets/svgIcons/ReactionSVG';
import RecentActionSVG from '../../../assets/svgIcons/ReactActionSVG';
import { ChatService } from '../../../service/ChatService';

type GroupSettingsScreenParams = {
  GroupSettingsScreen: {
    userData: any;
    userDetails: any;
    data: any;
  };
};

const GroupSettingsScreen = () => {
  const route = useRoute<RouteProp<GroupSettingsScreenParams, 'GroupSettingsScreen'>>();

  const AdminsandOwner = route.params.userData;
  const user = route.params.userDetails;
  const chatSpaceId = user.id;
  const navigation = useNavigation();

  const memberIds = route.params.data;

  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const handleDeleteGroup = async (chatSpaceId: string) => {
    setDeleteModal(true);
    const res = await deleteChatSpace(chatSpaceId);
    if (res?.status === true) {
      const unsubscribe = navigation.addListener('beforeRemove', () => {
        ChatService.deleteConversation(chatSpaceId);
        unsubscribe();
      });
      navigation.reset({ index: 0, routes: [{ name: 'HomeScreen' as never }] });
      setDeleteModal(false);
    } else {
      console.error('Failed to delete group');
    }
  };

  return (
    <CommonView headerTitle="Group settings">
      <View>
        <TouchableOpacity
          style={styles.row}
          onPress={() => navigateTo(SCREENS.AddChatspaceMembers, { data: memberIds, chatSpaceId })}
        >
          <AddMemberSVG />
          <Text style={styles.label}>Add members</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.row}>
          <PermissionSettingSVG size={18} />
          <Text style={styles.label}>Permissions</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.row}
          onPress={() =>
            navigateTo(SCREENS.AdminsScreen, { userData: AdminsandOwner, userDetails: user })
          }
        >
          <AdminSettingSVG size={21} />
          <Text style={styles.label}>Admins</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.row}>
          <GroupTypeSVG size={14} />

          <Text style={styles.label}>Group type</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.row}>
          <ChatHistorySVG size={15} />
          <Text style={styles.label}>Chat history</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.row}>
          <ReactionSVG size={21} />
          <Text style={styles.label}>Reactions</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.row}>
          <RecentActionSVG size={19} />
          <Text style={styles.label}>Recent actions</Text>
        </TouchableOpacity>

        {user.type === 'owner' && (
          <>
            <View style={styles.divider} />
            <TouchableOpacity style={styles.row} onPress={() => setDeleteModal(true)}>
              <DeleteSVG color={colors.thick_red} size={20} />
              <Text style={styles.label1}>Delete Group</Text>
            </TouchableOpacity>
          </>
        )}

        <ModalWrapper isVisible={deleteModal} onCloseModal={() => setDeleteModal(false)}>
          <View>
            <Text
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 16, color: colors.gray_80 }}
            >
              Delete group
            </Text>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Image
                source={{ uri: user.displayPic }} // Replace with actual image
                style={{ width: 48, height: 48, borderRadius: 24, marginRight: 12 }}
              />
              <View>
                <Text style={{ fontSize: 15, fontWeight: '600', color: colors.black_23 }}>
                  {user.displayName}
                </Text>
                <Text style={{ fontSize: 15, fontWeight: '400', color: '#666' }}>{user.type}</Text>
              </View>
            </View>

            <Text
              style={{ fontSize: 15, fontWeight: '400', marginBottom: 12, color: colors.black_23 }}
            >
              Confirm deletion and exit from the group?
            </Text>

            <TouchableOpacity
              style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 20 }}
              // onPress={() => setDeleteForAll(prev => !prev)}
            >
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: 'rgba(218, 218, 218, 1)',
                  marginRight: 10,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: 'red' }} />
                {/* )} */}
              </View>
              <Text style={{ fontSize: 15, fontWeight: '400', color: colors.black_23 }}>
                Delete the group for all members
              </Text>
            </TouchableOpacity>

            {/* Buttons */}
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(243, 243, 243, 1)',
                  paddingVertical: 12,
                  borderRadius: 15,
                  marginRight: 10,
                }}
                onPress={() => setDeleteModal(false)}
              >
                <Text
                  style={{
                    textAlign: 'center',
                    color: colors.black_23,
                    fontWeight: '500',
                    fontSize: 16,
                  }}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: 'rgba(236, 11, 11, 1)',
                  paddingVertical: 12,
                  borderRadius: 15,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  handleDeleteGroup(chatSpaceId);
                  setDeleteModal(false);
                }}
              >
                <View style={{ marginRight: 8 }}>
                  <DeleteSVG color={colors.white} size={20} />
                </View>
                <Text style={{ color: 'rgba(255, 255, 255, 1)', fontWeight: '500', fontSize: 16 }}>
                  Delete group
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ModalWrapper>
      </View>
    </CommonView>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    gap: 10,
  },
  label: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  label1: {
    fontSize: 16,
    color: colors.thick_red,
    fontWeight: '400',
  },
  divider: {
    borderWidth: 1,
    borderColor: colors.black,
    opacity: 0.08,
    marginVertical: 10,
  },
});

export default GroupSettingsScreen;
