import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const FavSVG: React.FC<IconProps> = ({
  size = 20,
  color = '#6A4DBB',
  ...restProps
}) => {
  return (
    <Svg
      width={size}
      height={(size * 19) / 20} // maintains original aspect ratio
      viewBox="0 0 20 19"
      fill="none"
      {...restProps}>
      <Path
        d="M10 0.900391C10.4909 0.900391 10.9192 1.22938 11.1855 1.7666L13.2461 5.92188L17.8525 6.58887C18.4478 6.67501 18.895 6.97879 19.0469 7.44434C19.1988 7.90994 19.0168 8.41781 18.5859 8.83594L15.252 12.0693L16.0391 16.6367C16.146 17.2568 15.9647 17.6411 15.7822 17.8564C15.5702 18.1068 15.2619 18.2431 14.9189 18.2432H14.918C14.661 18.2429 14.3927 18.1675 14.1211 18.0254L10 15.8682L5.87988 18.0254C5.60815 18.1676 5.33929 18.2431 5.08203 18.2432C4.73892 18.2432 4.42997 18.1068 4.21777 17.8564C4.03536 17.6411 3.85413 17.2567 3.96094 16.6367L4.74707 12.0693L1.41406 8.83496C0.98355 8.41711 0.800575 7.9097 0.952148 7.44434C1.104 6.97879 1.55222 6.67501 2.14746 6.58887L6.75293 5.92188L8.81445 1.7666C9.08098 1.22942 9.50908 0.900404 10 0.900391ZM9.93945 2.32031L7.7334 6.77051C7.64197 6.95495 7.46493 7.08273 7.26074 7.1123L2.32715 7.82617C2.27976 7.83305 2.24216 7.84306 2.21191 7.85156C2.23142 7.87618 2.25565 7.90705 2.29004 7.94043L5.85938 11.4043C6.00723 11.5476 6.07475 11.7543 6.04004 11.957L5.19727 16.8486C5.18922 16.8952 5.18779 16.9337 5.18652 16.9648C5.21616 16.954 5.25322 16.9413 5.2959 16.9189L9.70801 14.6094C9.79932 14.5616 9.89981 14.5381 10 14.5381C10.1002 14.5381 10.2006 14.5616 10.292 14.6094L14.7041 16.9189C14.7463 16.941 14.7831 16.954 14.8125 16.9648C14.8112 16.9338 14.8108 16.8952 14.8027 16.8486L13.96 11.957C13.9251 11.7544 13.993 11.5477 14.1406 11.4043L17.71 7.94043C17.7441 7.90732 17.7677 7.87608 17.7871 7.85156C17.757 7.84316 17.7198 7.83298 17.6729 7.82617L12.7393 7.1123C12.5353 7.08266 12.3591 6.9547 12.2676 6.77051L10.0615 2.32031C10.0401 2.2771 10.0176 2.24491 10 2.21875C9.98245 2.24483 9.96084 2.27727 9.93945 2.32031Z"
        fill={color}
        stroke={color}
        strokeWidth="0.2"
      />
      <Path
        d="M7.00014 6.57254L1.85728 7.42969L1.42871 7.85826L5.28585 12.144L4.85728 17.7154L10.4287 15.5725L15.143 17.7154L14.7144 11.7154L18.5716 7.42969L12.5716 6.14397L10.8573 2.28683L9.57157 1.42969L7.00014 6.57254Z"
        fill={color}
      />
    </Svg>
  );
};

export default FavSVG; 