import Toast from 'react-native-toast-message';
import { navigationRef } from '../navigation/RootContainer';
import { CommonActions } from '@react-navigation/native';
import { API } from './apiConstant';
import moment from 'moment';

import { nanoid } from 'nanoid/non-secure';
import Api from './api';
import { createThumbnail } from 'react-native-create-thumbnail';
import { Alert, Platform, Keyboard, ToastAndroid, InteractionManager } from 'react-native';
import { ChatService } from '../service/ChatService';
import config from '../appConfig';
import Geolocation from '@react-native-community/geolocation';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { ChatSpecificScreenParamsT } from '../screens/Home/Chats/ChatSpecificScreen';
import { PersonalProfileScreenScreenT } from '../screens/Home/Chats/Screens/PersonalProfileScreen';
import { SCREENS } from '../navigation/screenNames';

let keyboardVisible = false;

Keyboard.addListener('keyboardDidShow', () => {
  keyboardVisible = true;
});
Keyboard.addListener('keyboardDidHide', () => {
  keyboardVisible = false;
});

export const generateUUID = (length: number = 20) => {
  return nanoid(length);
};

export const successToast = (message: string) => {
  Toast.show({
    type: 'success',
    text1: message,
    position: keyboardVisible ? 'top' : 'bottom',
    topOffset: 50,
    bottomOffset: 40,
  });
};

export const errorToast = (message: string) => {
  Toast.show({
    type: 'error',
    text1: message,
    position: keyboardVisible ? 'top' : 'bottom',
    topOffset: 50,
    bottomOffset: 40, // spacing when at bottom
  });
};

export const infoToast = (message: string) => {
  if (config.debugToasts) {
    Toast.show({
      type: 'info',
      text1: message,
      position: keyboardVisible ? 'top' : 'bottom',
      topOffset: 50,
      bottomOffset: 40,
    });
  }
};

export const emailCheck = (email: string) => {
  let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;
  if (reg.test(email) === false) {
    return false;
  } else {
    return true;
  }
};

export const nameCheck = (name: string) => {
  let reg = /^([a-zA-Z ]){2,30}$/;
  if (reg.test(name) === false) {
    return false;
  } else {
    return true;
  }
};

export const passwordCheck = (string: string) => {
  let reg = /^(?=.*[0-8])(?=.*[a-z])(?=.*[A-Z])(?=.*\W)(?!.* ).{8,}$/;
  return reg.test(string);
};

export const mobileNumberCheck = (mobileNo: string) => {
  let reg = /^\d*$/;
  return reg.test(mobileNo);
};

export const formatPhoneForDisplay = (digitsOnly: string, countryCallingCode: string) => {
  try {
    const { AsYouType } = require('libphonenumber-js');
    const formatter = new AsYouType();
    const libFormat = formatter.input(`+${countryCallingCode}${digitsOnly}`);
    const withoutCode = libFormat.replace(new RegExp(`^\\+${countryCallingCode}\\s*`), '');
    return withoutCode.replace(/[()]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-');
  } catch (e) {
    return digitsOnly;
  }
};

export const validateInternationalPhone = (
  rawNumber: string,
  countryIso2: string,
):
  | {
      isValid: true;
      e164: string;
      countryCallingCode: string;
      nationalNumber: string;
      country?: string;
    }
  | { isValid: false } => {
  try {
    const phone = parsePhoneNumberFromString(rawNumber, countryIso2 as any);
    if (!phone || !phone.isValid()) return { isValid: false };

    return {
      isValid: true,
      e164: phone.number,
      countryCallingCode: phone.countryCallingCode,
      nationalNumber: phone.nationalNumber,
      country: phone.country,
    };
  } catch (e) {
    return { isValid: false };
  }
};

export const resetNavigation = (screenName: any, params: any) => {
  navigationRef.dispatch(
    CommonActions.reset({
      index: 1,
      routes: [
        {
          name: screenName,
          params: params,
        },
      ],
    }),
  );
};

export const navigateTo = (screenName: string, params?: any | {}) => {
  navigationRef.navigate(screenName, params);
};

export const resetAndNavigateTo = (
  screenName: string,
  params?: any,
  shouldResetStack: boolean = false,
  toScreen: string = 'HomeScreen',
) => {
  if (shouldResetStack) {
    navigationRef.current?.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [{ name: toScreen }, { name: screenName, params }],
      }),
    );
  } else {
    navigationRef.current?.navigate(screenName, params);
  }
};

// location-details

export const getCurrentLocation = (): Promise<{
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}> => {
  return new Promise((resolve, reject) => {
    try {
      Geolocation.getCurrentPosition(
        async (info) => {
          const { latitude, longitude } = info.coords;

          const locData = {
            latitude: Number(latitude),
            longitude: Number(longitude),
            latitudeDelta: 0.0001,
            longitudeDelta: 0.0001,
          };
          console.log('getcurrentlocation-------', JSON.stringify(info.coords, null, 2));
          resolve(locData); // ✅ return location details
        },
        (error) => {
          console.log('Error getting location:', error);
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 50000 },
      );
    } catch (error) {
      console.log('Catch block error:', error);
      reject(error);
    }
  });
};

export function formatBytes(bytes: number) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${value} ${sizes[i]}`;
}

export const dayPipe = (date: any, flag?: any) => {
  var DATE = moment(date);
  var REFERENCE = moment();
  var TODAY = REFERENCE.clone().startOf('day');
  var YESTERDAY = REFERENCE.clone().subtract(1, 'days').startOf('day');
  var START_OF_WEEK = REFERENCE.clone().startOf('week');

  if (flag === 'diffCheck') {
    if (DATE.isSame(TODAY, 'd')) {
      return DATE.format('hh:mm A'); // e.g., "10:30 AM"
    } else if (DATE.isSame(YESTERDAY, 'd')) {
      return 'Yesterday';
    } else if (DATE.isAfter(START_OF_WEEK)) {
      return DATE.format('dddd'); // e.g., "Monday", "Tuesday"
    } else {
      return DATE.format('DD/MM/YYYY'); // e.g., "02/05/2025"
    }
  }
  if (flag === 'monthDay') {
    return DATE.format('MMM D'); // <-- New format: e.g., "Sep 24"
  }

  if (flag === 'time') {
    // If you want only time from date object
    return DATE.format(API.FORMAT_TIME);
  } else if (flag === 'date') {
    // If you want only date from date object
    if (DATE.isSame(REFERENCE, 'week')) {
      // Check if date is today, yesterday, weekday or others
      if (DATE.isSame(TODAY, 'd')) return DATE.format(API.FORMAT_TIME);
      else if (DATE.isSame(YESTERDAY, 'd')) return 'Yesterday';
      else return DATE.format('ddd');
    } else return DATE.format(API.FORMAT_DATE);
  } else {
    // If you want only date and time both from date object
    if (DATE.isSame(REFERENCE, 'week')) {
      // Check if date is today, yesterday, weekday or others
      if (DATE.isSame(TODAY, 'd')) return `${DATE.format(API.FORMAT_TIME)}`;
      // else if (DATE.isSame(YESTERDAY, "d"))
      //   return `Yesterday ${DATE.format(API.FORMAT_TIME)}`;
      else return DATE.format(API.FORMAT_DAY_TIME);
    } else return DATE.format(API.FORMAT_DATE_TIME);
  }
};

export const VALIDATION_RULES = {
  USERNAME_LENGTH: 30,
  NAME_LENGTH: 30,
  BIO_LENGTH: 150,
  MIN_AGE: 14,
  MAX_AGE: 100,
  TEXT_MESSAGE_LENGTH: 3000,
  USERNAME_REGEX: /^[a-zA-Z0-9._]+$/,
  MIN_CHANNEL_NAME: 10,
  MAX_CHANNEL_NAME: 50,
  MIN_CHANNEL_DESCRIPTION: 10,
  MAX_CHANNEL_DESCRIPTION: 250,
};

export const showToast = (message: string) => {
  if (Platform.OS === 'android') {
    ToastAndroid.show(message, ToastAndroid.SHORT);
  } else {
    Alert.alert('', message);
  }
};

export const checkFirstLetterHash = (str: any) => {
  if (str?.length > 0 && str?.charAt(0) === '#') {
    return true;
  } else {
    return false;
  }
};

function padTo2Digits(num: any) {
  return num.toString().padStart(2, '0');
}

export const fileTypes = {
  images: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
  videos: ['mp4', 'mov', 'avi', 'mkv', 'webm'],
};

export const getFileType = (url: any) => {
  if (url) {
    const extension = url?.uri?.split('.').pop().toLowerCase();
    if (fileTypes.images.includes(extension)) {
      return 'IMAGE';
    } else if (fileTypes.videos.includes(extension) || url?.type === 'video/mp4') {
      return 'VIDEO';
    } else {
      return 'IMAGE';
    }
  }
};

export const convertMsToTime = (milliseconds: any) => {
  let seconds = Math.floor(milliseconds / 1000);
  let minutes = Math.floor(seconds / 60);
  seconds = seconds % 60;
  minutes = minutes % 60;
  return `${padTo2Digits(minutes)}:${padTo2Digits(seconds)}`;
};

export function bytesToMB(bytes: number) {
  return bytes / (1024 * 1024);
}

export const handleMediaFileSharing = async (
  formData: any,
  data: any,
  isVideoMsg: boolean = false,
) => {
  try {
    const result = await Api.post('upload', formData, true);
    // console.log("result---------", result)
    const mediaUrl = result?.body?.data?.url;
    const fileSize = result?.body?.data?.size;

    const sizeInMB = bytesToMB(fileSize);

    if (
      result?.body?.message == 'File too large' ||
      !result?.body?.status ||
      result?.statusCode !== 201 ||
      sizeInMB > 50 ||
      !mediaUrl
    ) {
      ToastAndroid.show(
        `File size too large. Please upload a file under 50 MB.`,
        ToastAndroid.LONG,
      );
      await ChatService.deleteMediaMessage([data?.localId]);
      return;
    }

    console.log('result', result);
    let thumbnail: string = '';
    if (isVideoMsg && mediaUrl) {
      try {
        const response = await createThumbnail({ url: mediaUrl, timeStamp: 1000 });
        thumbnail = response?.path || '';
      } catch (err) {
        console.log('Thumbnail error', err);
      }
    }

    await ChatService.updateMediaMessage(data?.localId, mediaUrl, fileSize, thumbnail);
  } catch (error) {
    console.warn('error handlemediasharing', error);
  }
};

export const callBackgroundsApi = (dispatch: any) => {
  console.log(' dispatch ', dispatch);
};

export const safeResetIfInsideDeletedChat = (chatSpaceId: string) => {
  if (!navigationRef.isReady()) {
    console.warn('Navigator not ready, will wait for state before reset');

    const unsubscribe = navigationRef.addListener('state', () => {
      if (navigationRef.isReady()) {
        resetNavigation(SCREENS.HomeScreen, {});
        unsubscribe();
      }
    });

    setTimeout(() => {
      if (navigationRef.isReady()) {
        resetNavigation(SCREENS.HomeScreen, {});
      } else {
        console.warn('Navigator still not ready, using navigate fallback');
        navigationRef.navigate(SCREENS.HomeScreen as never);
      }
    }, 2000);

    return;
  }

  const currentScreen = navigationRef.current?.getCurrentRoute();
  if (!currentScreen) return;

  const { name, params } = currentScreen;

  const currentChatSpaceId =
    (params as ChatSpecificScreenParamsT)?.userData?.id ??
    (params as PersonalProfileScreenScreenT)?.userData?.id;

  const isInsideDeletedChat =
    (name === SCREENS.ChatSpecificScreen || name === SCREENS.PersonalProfileScreen) &&
    currentChatSpaceId === chatSpaceId;

  if (isInsideDeletedChat) {
    InteractionManager.runAfterInteractions(() => {
      requestAnimationFrame(() => {
        if (navigationRef.isReady()) {
          resetNavigation(SCREENS.HomeScreen, {});
        } else {
          console.warn('Navigator lost state during reset, using navigate fallback');
          navigationRef.navigate(SCREENS.HomeScreen as never);
        }
      });
    });
  }
};

export const getContactId = (c: any) => c?.id ?? c?.userId ?? c?._id ?? null;

export const isSelfById = (contact: any, myId?: string | null) => {
  if (!myId) return false;
  const contactId = getContactId(contact);
  return contactId === myId;
};
