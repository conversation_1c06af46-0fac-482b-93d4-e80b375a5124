import { use, useCallback, useEffect, useRef, useState } from 'react';
import {
  BaseLiveStreamSession,
  getChannelDetailsApi,
  getCurrentLiveStreamApi,
  getLiveStreamChatsApi,
  getStreamChatMessagesApi,
  getStreamInfoApi,
  ViewerLiveStreamSession,
} from '../../api/Chatspace/chatspace.api';
import { ChatSpace } from '../../types/socketPayload.type';
import useSocket from '../../socket-client/useSocket';
import { useMe } from '../util/useMe';
import { ChatRank, Comment } from '../../screens/Home/Channels/liveStreams/CommentContainer';
import { generateUUID } from '../../utils/commonFunction';
import useConversations from '../conversations/useConversations';
import { mockComments } from '../../screens/Home/Channels/liveStreams/data';
import { IPaginatedChatController, usePaginatedChatMessages } from '../calls/useLiveStreamchats';

export const StreamEvents_incoming = {
  end_stream: 'end_stream',
  error_stream: 'error_stream',
  stream_upate: 'stream_upate',
};

export enum StreamInteractionType {
  GIFT = 'gift',
  STICKER = 'sticker',
  LIKE = 'like',
  SHARE = 'share',
  SYSTEM = 'system',
  UNLIKE = 'unlike',

  // use comment only for chat.schema
  COMMENT = 'comment',
}

export const StreamEvents = {
  outgoing: {
    joinStream: 'joinstream',
    leaveStream: 'leavestream',
    likeStream: 'like_stream',
    shareStream: 'share_stream',
    commentStream: 'comment_stream',
    giftStream: 'gift_stream',
    sendSticker: 'send_sticker',

    // only for streamer
    stopStream: 'stop_stream',
  },
  incoming: {
    endStream: 'end_stream',
    errorStream: 'error_stream',
    streamUpdate: 'stream_upate',
    viewerJoined: 'viewer:joined',
    viewerLeft: 'viewer:left',
  },
};

type CommentStream = {
  liveStreamId: string;
  senderId: string;
  message: string;
  displayName: string;
  displayImageUrl?: string | null;
};

export enum UserRole {
  VIEWER = 'VIEWER',
  STREAMER = 'STREAMER',
  MODERATOR = 'MODERATOR',
  GUEST = 'GUEST',
}

type Props = {
  chatSpaceId: string;
  liveStreamId: string;
  userRole: UserRole;
};

export enum StreamStatus {
  PENDING = 'PENDING',
  LIVE = 'LIVE',
  RECONNECTING = 'RECONNECTING',
  ENDED = 'ENDED',
  FAILED = 'FAILED',
}

export interface IStreamState {
  state: StreamStatus;
  likeCount: number;
  shareCount: number;
  viewCount: number;
  commentCount: number;
  streamStartTime: Date;
  giftId: string;
  isLiked: boolean;
  userRole: UserRole;
  isFollowing: boolean;
}

type LikeInteraction = {
  liveStreamId: string;
  interactionType: StreamInteractionType.LIKE;
  likeCount: number;
  isLiked: boolean;
};
type UnlikeInteraction = {
  liveStreamId: string;
  interactionType: StreamInteractionType.UNLIKE;
  likeCount: number;
  isLiked: boolean;
};

type ShareInteraction = {
  liveStreamId: string;
  interactionType: StreamInteractionType.SHARE;
  shareCount: number;
};

type CommentInteraction = {
  liveStreamId: string;
  interactionType: StreamInteractionType.COMMENT;
} & Comment;

type StreamInteractionData =
  | LikeInteraction
  | UnlikeInteraction
  | ShareInteraction
  | CommentInteraction;

export interface ILivestreamSession {
  streamDetails: IStreamState;
  setStreamDetails: React.Dispatch<React.SetStateAction<IStreamState>>;
  streamChat: any[]; // you can replace `any` with a proper type if available
  isPrevChatLoading: boolean;
  joinLiveStream: () => void;
  leaveLiveStream: () => void;
  likeStream: () => void;
  shareStream: () => void;
  sendSticker: () => void;
  sendGift: () => void;
  sendMessageToStream: (messageData: CommentStream & { chatRank: ChatRank }) => void;
  followChannel: () => void;
  updateStreamDetails: (data: Partial<IStreamState>) => void;
  stopStream: () => void;
  elapsedTime: null | string;
  chatspaceinfo: ChatSpace | null;
  livestream: BaseLiveStreamSession | null;
  liveStreamChatController: IPaginatedChatController;
}

export default function useLivestreamSpace({
  chatSpaceId,
  liveStreamId,
  userRole,
}: Props): ILivestreamSession {
  const { socket } = useSocket();
  const [livestream, setLivestream] = useState<BaseLiveStreamSession | null>(null);
  const [chatspaceinfo, setChatSpace] = useState<ChatSpace | null>(null);
  const [streamChat, setStreamChat] = useState<Comment[]>([]);
  const [isPrevChatLoading, setIsPrevChatLoading] = useState(false);

  const [streamStartTime, setStreamStartTime] = useState<null | Date>(new Date());
  const { getConversattionById } = useConversations();
  const isFollowing = getConversattionById(chatSpaceId)?.role === 'member';
  const initialChatRef = useRef<Comment[] | null>(null);

  const liveStreamChatController = usePaginatedChatMessages({
    liveStreamId,
    limit: 20,
    initialChatRef,
    loadInitialChats: userRole === UserRole.VIEWER,
  });

  // keep this augument for now
  const elapsedTime = useElapsedTimeFormatted(streamStartTime);

  const [streamDetails, setStreamDetails] = useState<IStreamState>({
    commentCount: 0,
    giftId: '',
    likeCount: 0,
    shareCount: 0,
    streamStartTime: new Date(),
    viewCount: 0,
    isLiked: false,
    state: StreamStatus.PENDING,
    userRole,
    isFollowing,
  });

  function updateStreamDetails(data: Partial<IStreamState>) {
    setStreamDetails((prev) => ({
      ...prev,
      ...data,
    }));
  }

  function joinLiveStream() {
    socket?.emit(
      StreamEvents.outgoing.joinStream,
      {
        liveStreamId,
        chatSpaceId,
      },
      (data: { viewCount: number }) => {
        updateStreamDetails({
          viewCount: data.viewCount,
        });
      },
    );
  }

  function leaveLiveStream() {
    socket?.emit(StreamEvents.outgoing.leaveStream, {
      liveStreamId,
      chatSpaceId,
    });
  }
  function stopStream() {
    socket?.emit(StreamEvents.outgoing.stopStream, {
      liveStreamId,
      chatSpaceId,
    });
  }

  async function getChatSpaceAndLivestream(chatSpaceId: string, liveStreamId: string) {
    try {
      const [chatSpace, livestream]: [{ channel: any }, ViewerLiveStreamSession] =
        await Promise.all([
          getChannelDetailsApi(chatSpaceId),
          getCurrentLiveStreamApi<ViewerLiveStreamSession>(liveStreamId),
        ]);
      setChatSpace(chatSpace.channel);
      setLivestream(livestream);
      joinLiveStream();
      if (livestream && livestream?.createdAt && userRole === UserRole.VIEWER) {
        const createdDate = new Date(livestream.createdAt);
        // print seconds from now
        setStreamStartTime(createdDate);
        updateStreamDetails({
          shareCount: livestream?.shareNum || 0,
          likeCount: livestream?.likesNum || 0,
          streamStartTime: createdDate,
        });
      }
    } catch (err) {
      console.error(err);
    }
  }
  const likeStream = async () => {
    setStreamDetails((prev) => {
      const newIsLiked = !prev.isLiked;
      const newLikeCount = newIsLiked ? prev.likeCount + 1 : prev.likeCount - 1;

      socket.emit(StreamEvents.outgoing.likeStream, {
        liveStreamId,
        isLike: newIsLiked,
      });

      return {
        ...prev,
        isLiked: newIsLiked,
        likeCount: newLikeCount,
      };
    });
  };

  const shareStream = () => {
    setStreamDetails((prev) => ({
      ...prev,
      shareCount: prev.shareCount + 1,
    }));
  };

  const sendSticker = () => {
    socket.emit(StreamEvents.outgoing.sendSticker, {
      liveStreamId,
      message: 'sticker',
    });
  };

  const sendGift = () => {
    socket.emit(StreamEvents.outgoing.giftStream, {
      liveStreamId,
      giftId: '1',
    });
  };
  const sendMessageToStream = (messageData: CommentStream & { chatRank: ChatRank }) => {
    const chatId = generateUUID();
    socket.emit(StreamEvents.outgoing.commentStream, { ...messageData, chatId });
    const comment: Comment = {
      ...messageData,
      chatId,
      displayImageUrl: messageData.displayImageUrl || 'https://i.pravatar.cc/40?img=1',
      createdAt: new Date().toISOString(),
    };
    setStreamChat((prev) => [...prev, comment]);
    liveStreamChatController.setMessages((prev) => [...prev, comment]);
  };

  const followChannel = () => {
    // todo: call follow chatspace api.
  };

  const handleEndStream = useCallback(
    (data: any) => {
      if (liveStreamId === data.liveStreamId) {
        updateStreamDetails({ state: StreamStatus.ENDED });
      }
    },
    [liveStreamId],
  );

  const handleErrorStream = useCallback(
    (data: any) => {
      if (liveStreamId === data.liveStreamId) {
        updateStreamDetails({ state: StreamStatus.FAILED });
      }
    },
    [liveStreamId],
  );

  const handleUpdateStreamDetails = useCallback(
    // assuming server only sends the required fields

    (data: StreamInteractionData) => {
      const streamUpdateHandlers = {
        [StreamInteractionType.LIKE]: (data: any) => {
          setStreamDetails((prev) => {
            return {
              ...prev,
              likeCount: prev.likeCount + 1,
            };
          });
        },
        [StreamInteractionType.SHARE]: (data: any) => {
          updateStreamDetails({
            shareCount: data.shareCount,
          });
        },
        [StreamInteractionType.COMMENT]: (data: any) => {
          if (liveStreamChatController.loadingIntialChats) {
            initialChatRef.current = [...(initialChatRef.current || []), data];
            initialChatRef.current = [];

            return;
          }
          setStreamChat((prev) => [...prev, data]);
          liveStreamChatController.setMessages((prev) => [...prev, data]);
        },
        [StreamInteractionType.UNLIKE]: (data: any) => {
          setStreamDetails((prev) => {
            return {
              ...prev,
              likeCount: prev.likeCount - 1,
            };
          });
        },
      };

      switch (data.interactionType) {
        case StreamInteractionType.LIKE:
          streamUpdateHandlers[StreamInteractionType.LIKE](data);
          break;
        case StreamInteractionType.UNLIKE:
          streamUpdateHandlers[StreamInteractionType.UNLIKE](data);
          break;
        case StreamInteractionType.SHARE:
          streamUpdateHandlers[StreamInteractionType.SHARE](data);
          break;
        case StreamInteractionType.COMMENT:
          streamUpdateHandlers[StreamInteractionType.COMMENT](data);
          break;
      }

      // if (liveStreamId === data.liveStreamId) {
      //   updateStreamDetails(data);
      // }
    },
    [liveStreamId, liveStreamChatController.loadingIntialChats],
  );

  function handleViewerJoined(data: any) {
    setStreamDetails((prev) => {
      const viewCount = prev.viewCount + 1;
      return {
        ...prev,
        viewCount: viewCount,
      };
    });
  }

  function handleViewerLeft(data: any) {
    setStreamDetails((prev) => {
      const viewCount = prev.viewCount - 1;
      return {
        ...prev,
        viewCount: viewCount,
      };
    });
  }

  function intialiseSocketEvents() {
    socket?.on(StreamEvents.incoming.endStream, handleEndStream);
    socket?.on(StreamEvents.incoming.errorStream, handleErrorStream);
    socket?.on(StreamEvents.incoming.streamUpdate, handleUpdateStreamDetails);
    socket?.on(StreamEvents.incoming.viewerJoined, handleViewerJoined);
    socket?.on(StreamEvents.incoming.viewerLeft, handleViewerLeft);
  }

  function unregisterSocketEvents() {
    socket?.off(StreamEvents.incoming.endStream, handleEndStream);
    socket?.off(StreamEvents.incoming.errorStream, handleErrorStream);
    socket?.off(StreamEvents.incoming.streamUpdate, handleUpdateStreamDetails);
    socket?.off(StreamEvents.incoming.viewerJoined, handleViewerJoined);
    socket?.off(StreamEvents.incoming.viewerLeft, handleViewerLeft);
  }

  useEffect(() => {
    intialiseSocketEvents();
    return () => {
      unregisterSocketEvents();
    };
  }, [liveStreamChatController.loadingIntialChats]);

  useEffect(() => {
    getChatSpaceAndLivestream(chatSpaceId, liveStreamId);
    // userRole === UserRole.VIEWER && getPreviousChats();
    userRole === UserRole.VIEWER && liveStreamChatController.loadInitialMessages();

    return () => {
      unregisterSocketEvents();
    };
  }, []);

  return {
    streamDetails,
    setStreamDetails,
    streamChat,
    isPrevChatLoading,
    joinLiveStream,
    leaveLiveStream,
    likeStream,
    shareStream,
    sendSticker,
    sendGift,
    sendMessageToStream,
    followChannel,
    updateStreamDetails,
    stopStream,
    elapsedTime,
    chatspaceinfo,
    liveStreamChatController,
    livestream,
  };
}

function formatTime(ms: number) {
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  return `${minutes}:${seconds} sec`;
}

function useElapsedTimeFormatted(startTime: Date | null): string | null {
  const [formattedTime, setFormattedTime] = useState<string | null>(null);

  useEffect(() => {
    if (!startTime) return;

    const update = () => {
      const now = Date.now();
      const diff = now - startTime.getTime();
      setFormattedTime(formatTime(diff));
    };

    update(); // Set immediately
    const interval = setInterval(update, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  return startTime ? formattedTime : null;
}
