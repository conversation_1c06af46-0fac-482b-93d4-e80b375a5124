import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';

// TypeScript interface (optional for type hinting outside Realm usage)
export interface IContact {
  phoneNumber: string;
  name: string;
  isRegistered: boolean;
  isInDevice: boolean;
  // userId?: string;
  // username?: string;
  // image?: string;
  // accountName?: string;
  // bio: string;
}

// Realm Contact class model
export class ContactSchema extends Realm.Object<ContactSchema> implements IContact {
  phoneNumber!: string;
  name!: string;
  isRegistered!: boolean;
  isInDevice!: boolean;
  // userId?: string;
  // username?: string;
  // image?: string;
  // accountName?: string; // Added missing field
  // bio!: string; // Made required (was optional)

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.contact,
    primaryKey: 'phoneNumber',
    properties: {
      phoneNumber: 'string',
      name: 'string',
      isRegistered: { type: 'bool', default: false },
      isInDevice: { type: 'bool', default: false },
      // userId: { type: 'string', optional: true },
      // username: { type: 'string', optional: true },
      // image: { type: 'string', optional: true },
      // accountName: { type: 'string', optional: true }, // Added missing field
      // bio: 'string', // Made required (was 'string?')
    },
  };
}
