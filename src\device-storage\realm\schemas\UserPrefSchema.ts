// Enums
export enum PrivacyEnum {
  EVERYONE = 'everyone',
  CONTACTS = 'contacts',
  NOBODY = 'nobody',
  CONTACTS_EXCEPT = 'contacts_except',
}

export enum DefaultMessageTimerEnum {
  HOURS_24 = '24hours',
  DAYS_7 = '7days',
  DAYS_90 = '90days',
  OFF = 'off',
}

export enum ThemeEnum {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
}

// Interfaces
export interface IColorPreference {
  primary: string;
  secondary?: string;
  opacity?: number;
}

export interface IPrivacySetting {
  privacy: PrivacyEnum;
  exceptedContacts?: string[];
  allowedContacts?: string[];
}

export interface IMediaPrivacySetting extends IPrivacySetting {
  allowDownloads: boolean;
  disableForwarding: boolean;
}

export interface IChatWallpaper {
  url?: string;
  color?: string;
  opacity: number;
}

export interface ITranslateLanguage {
  textMessageEnabled: boolean;
  voiceMessageEnabled: boolean;
  languageId?: string;
  mediaEnabled: boolean;
}

export interface IAppLanguage {
  id?: string;
  name?: string;
}

export interface IFavoritePreferences {
  contacts: string[];
  chatSpaces: string[];
}

export interface INotificationPreferences {
  showNotification: boolean;
  notificationSound: boolean;
  reactionNotification: boolean;
}

export interface IAccountPreferences {
  selectedTheme: ThemeEnum;
  twoFactorEnabled: boolean;
  pinEnabled: boolean;
  profileColors?: IColorPreference;
  messageColor?: IColorPreference;
}

export interface IChatPreferences {
  chatWallpaper?: IChatWallpaper;
  translateLanguage?: ITranslateLanguage;
  selectedLanguage?: string;
  saveToGallery?: boolean;
  archiveChats?: boolean;
}

export interface IPrivacyPreferences {
  lastSeen?: IPrivacySetting;
  phoneNumber?: IPrivacySetting;
  defaultMessageTimer: DefaultMessageTimerEnum;
  forwardMessage?: IPrivacySetting;
  groupInvite?: IPrivacySetting;
  messagePrivacy?: IPrivacySetting;
  imagesPrivacy?: IMediaPrivacySetting;
  documentPrivacy?: IMediaPrivacySetting;
  readReceipts?: boolean;
}

export interface IUserPreferences {
  userId: string;
  account: IAccountPreferences;
  chats: IChatPreferences;
  privacy: IPrivacyPreferences;
  notifications: INotificationPreferences;
  favorites: IFavoritePreferences;
  appLanguage: IAppLanguage;
  createdAt: number;
  updatedAt: number;
}

// // Embedded object: PrivacyPreferences
// export class PrivacyPreferencesSchema extends Realm.Object<PrivacyPreferencesSchema> {
//   lastSeen?: PrivacySettingSchema;
//   phoneNumber?: PrivacySettingSchema;
//   defaultMessageTimer!: DefaultMessageTimerEnum;
//   forwardMessage?: PrivacySettingSchema;
//   groupInvite?: PrivacySettingSchema;
//   messagePrivacy?: PrivacySettingSchema;
//   imagesPrivacy?: MediaPrivacySettingSchema;
//   documentPrivacy?: MediaPrivacySettingSchema;

//   static schema: Realm.ObjectSchema = {
//     name: realmSchemaNames.privacyPreferences,
//     embedded: true,
//     properties: {
//       lastSeen: `${realmSchemaNames.privacySetting}?`,
//       phoneNumber: `${realmSchemaNames.privacySetting}?`,
//       defaultMessageTimer: { type: 'string', default: DefaultMessageTimerEnum.OFF },
//       forwardMessage: `${realmSchemaNames.privacySetting}?`,
//       groupInvite: `${realmSchemaNames.privacySetting}?`,
//       messagePrivacy: `${realmSchemaNames.privacySetting}?`,
//       imagesPrivacy: `${realmSchemaNames.mediaPrivacySetting}?`,
//       documentPrivacy: `${realmSchemaNames.mediaPrivacySetting}?`,
//     },
//   };
// }

// // Main UserPreferences object
// export class UserPreferencesSchema extends Realm.Object<UserPreferencesSchema> {
//   userId!: string;
//   account!: AccountPreferencesSchema;
//   chats!: ChatPreferencesSchema;
//   privacy!: PrivacyPreferencesSchema;
//   notifications!: NotificationPreferencesSchema;
//   favorites!: FavoritePreferencesSchema;
//   appLanguage!: AppLanguageSchema;
//   createdAt!: number;
//   updatedAt!: number;

//   static schema: Realm.ObjectSchema = {
//     name: realmSchemaNames.userPreferences,
//     primaryKey: 'userId',
//     properties: {
//       userId: 'string',
//       account: realmSchemaNames.accountPreferences,
//       chats: realmSchemaNames.chatPreferences,
//       privacy: realmSchemaNames.privacyPreferences,
//       notifications: realmSchemaNames.notificationPreferences,
//       favorites: realmSchemaNames.favoritePreferences,
//       appLanguage: realmSchemaNames.appLanguage,
//       createdAt: { type: 'int', default: () => Date.now() },
//       updatedAt: { type: 'int', default: () => Date.now() },
//     },
//   };
// }

// // Export aliases for backward compatibility with API
// export type UserPreferences = IUserPreferences;
// export type AccountPreferences = IAccountPreferences;
// export type ChatPreferences = IChatPreferences;
// export type PrivacyPreferences = IPrivacyPreferences;
// export type NotificationPreferences = INotificationPreferences;
// export type FavoritePreferences = IFavoritePreferences;
// export type AppLanguage = IAppLanguage;
// export type ColorPreference = IColorPreference;
// export type PrivacySetting = IPrivacySetting;
// export type MediaPrivacySetting = IMediaPrivacySetting;
// export type ChatWallpaper = IChatWallpaper;
// export type TranslateLanguage = ITranslateLanguage;
