import React, { useCallback, useEffect, useState } from 'react';
import {
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    SafeAreaView,
    ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { languages } from '../../lib/Languages';
import { colors } from '../../theme/colors';
import { asyncKeys } from '../../utils/asyncStorage';

interface Language {
    id: string;
    symbol: string;
    code: string;
    s2stSupported: boolean;
}

const AppLanguageScreen = () => {
    const [s2stSupportedLanguages, setS2stSupportedLanguages] = useState<Language[]>([]);
    const [selectedLanguage, setSelectedLanguage] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);

    const filterS2stSupportedLanguages = useCallback(() => {
        const filtered = languages.filter((lang) => lang.s2stSupported);
        setS2stSupportedLanguages(filtered);
    }, []);

    useEffect(() => {
        filterS2stSupportedLanguages();
        const fetchAppLanguage = async () => {
            try {
                const lang = await AsyncStorage.getItem(asyncKeys.appLanguage);
                if (lang) {
                    setSelectedLanguage(lang);
                }
                else {
                    // If no language is set, default to "eng" and store it
                    setSelectedLanguage("eng");
                    await AsyncStorage.setItem(asyncKeys.appLanguage, "eng");
                }
            } catch (error) {
                console.error('Error fetching app language:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchAppLanguage();
    }, [filterS2stSupportedLanguages]);

    const handleLanguageSelect = useCallback(async (langCode: string) => {
        try {
            setSelectedLanguage(langCode);
            await AsyncStorage.setItem(asyncKeys.appLanguage, langCode);
        } catch (error) {
            console.error('Error setting app language:', error);
        }
    }, []);

    const renderLanguageItem = useCallback(
        ({ item }: { item: Language }) => {
            const isSelected = selectedLanguage === item.code;
            return (
                <TouchableOpacity
                    style={styles.languageOptionRow}
                    onPress={() => handleLanguageSelect(item.code)}
                    activeOpacity={0.7}
                >
                    <View style={styles.languageLeft}>
                        <Text style={styles.symbol}>{item.symbol}</Text>
                        <Text
                            style={[
                                styles.language,
                                isSelected && { color: colors.mainPurple },
                            ]}
                        >
                            {item.id}
                        </Text>
                    </View>
                    {isSelected && (
                        <View style={styles.radioOuter}>
                            <View style={styles.radioInner} />
                        </View>
                    )}
                </TouchableOpacity>
            );
        },
        [selectedLanguage, handleLanguageSelect]
    );

    if (loading) {
        return (
            <SafeAreaView style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.mainPurple} />
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            <FlatList
                data={s2stSupportedLanguages}
                keyExtractor={(item) => item.code}
                renderItem={renderLanguageItem}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.contentContainer}
            />
        </SafeAreaView>
    );
};

export default AppLanguageScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
        paddingHorizontal: 16,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.white,
    },
    contentContainer: {
        paddingVertical: 16,
        paddingBottom: 100,
    },
    languageOptionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderWidth: 1,
        borderColor: colors._DADADA_gray,
        borderRadius: 12,
        padding: 12,
        marginBottom: 10,
        backgroundColor: colors.white,
    },
    languageLeft: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    symbol: {
        fontSize: 22,
        marginRight: 12,
    },
    language: {
        fontSize: 16,
        color: colors.black_23,
    },
    radioOuter: {
        width: 20,
        height: 20,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: colors.black_23,
        alignItems: 'center',
        justifyContent: 'center',
    },
    radioInner: {
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: colors.mainPurple,
    },
});
