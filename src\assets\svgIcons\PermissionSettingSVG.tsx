import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const PermissionSettingSVG: React.FC<IconProps> = ({
  size = 16, // original height
  color = "#232323",
  ...restProps
}) => {
  const width = (size * 17) / 16; // maintain original 17x16 aspect ratio

  return (
    <Svg
      width={width}
      height={size}
      viewBox="0 0 17 16"
      fill="none"
      {...restProps}
    >
      <Path
        d="M8.151 8.728c.372 0 .736.014 1.092.043a.573.573 0 11-.09 1.143 12.76 12.76 0 00-1.002-.04c-2.065 0-3.897.488-5.025 1.258-.634.431-1.03.929-1.03 1.483v1.012a.09.09 0 00.027.063c.017.017.04.027.064.027h5.267a.574.574 0 010 1.146v-.05.05H2.186A1.236 1.236 0 01.95 13.626v-1.013c0-.905.55-1.761 1.53-2.43C3.763 9.31 5.826 8.729 8.15 8.729zM8.151.45a3.889 3.889 0 013.888 3.887 3.89 3.89 0 01-3.888 3.888 3.888 3.888 0 010-7.774zm0 1.147a2.742 2.742 0 000 5.481 2.743 2.743 0 002.742-2.741 2.742 2.742 0 00-2.742-2.74zM11.198 8.958a2.842 2.842 0 111.61 4.822l-1.601 1.603a.574.574 0 01-.406.168H9.197a.573.573 0 01-.573-.574v-1.603c0-.152.06-.299.168-.406l1.602-1.602a2.839 2.839 0 01.804-2.408zm3.21.81a1.698 1.698 0 00-2.4 0 1.698 1.698 0 00-.438 1.64.574.574 0 01-.148.552L9.77 13.611v.793h.793l1.652-1.651a.574.574 0 01.553-.149 1.697 1.697 0 001.638-2.836z"
        fill={color}
        stroke={color}
        strokeWidth={0.1}
      />
      <Path
        d="M12.637 10.396a.807.807 0 111.14 1.143.807.807 0 01-1.14-1.143z"
        fill={color}
        stroke={color}
        strokeWidth={0.1}
      />
    </Svg>
  );
}

export default PermissionSettingSVG;