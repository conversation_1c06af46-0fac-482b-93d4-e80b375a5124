import { useEffect, useRef, useState } from 'react';

const useMultiSelectList = <T>(
  initialSelectedItems: T[] = [],
  getId: (item: T) => any,
  initialData: T[],
  filterText?: string,
) => {
  const initialDataRef = useRef(initialData);
  const [selectedItems, setSelectedItems] = useState<T[]>(initialSelectedItems);
  const [filteredItems, setFilteredItems] = useState<T[]>(initialData);

  const toggleSelection = (item: T) => {
    setSelectedItems((prev) =>
      prev.some((i) => getId(i) === getId(item))
        ? prev.filter((i) => getId(i) !== getId(item))
        : [...prev, item],
    );
  };

  const isSelected = (item: T) => selectedItems.some((i) => getId(i) === getId(item));

  const selectAll = () => {
    if (filteredItems.length === selectedItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems);
    }
  };

  useEffect(() => {
    if (!filterText) {
      setFilteredItems(initialDataRef.current);
    } else {
      const lowerFilter = filterText.toLowerCase();
      setFilteredItems(
        initialDataRef.current.filter((item) =>
          JSON.stringify(item).toLowerCase().includes(lowerFilter),
        ),
      );
    }
  }, [filterText]);

  return {
    selectedItems,
    toggleSelection,
    isSelected,
    resetSelection: () => setSelectedItems([]),
    selectAll,
    filteredItems,
  };
};

export default useMultiSelectList;
