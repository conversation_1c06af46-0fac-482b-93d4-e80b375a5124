import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  StatusBar,
  ActivityIndicator,
  BackHandler,
  Alert,
} from 'react-native';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle } from '../../theme/fonts';
import { colors } from '../../theme/colors';

import { useCallContext } from '../../Context/CallProvider';
import OutgoingCal from './components/OutgoingCal';
import AddPeopleModal from './components/AddPeopleModal';
import SwitchToVideo from './components/notifications/SwitchToVideo';
import SwitchToVideoRequest from './components/notifications/SwitchToVideoRequest';
import { _openAppSetting } from '../../utils/locationHandler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Participant } from '../../types/calls.types';
import IncomingCall from './components/IncomingCall';
import CallMemberModal from './components/CallMembersModal';
import LanguageModal from './components/LanguageModal';
import OptionsModal from './components/OptionsModal';
import OngoingCall from './components/OngoingCall';
import { zIndex } from '../../utils/Filters';

const languages = [
  { name: 'Original language', code: 'original', flag: null },
  { name: 'English', code: 'en', flag: '🇬🇧' },
  { name: 'Arabic', code: 'ar', flag: '🇸🇦' },
  { name: 'Bengali', code: 'bn', flag: '🇧🇩' },
  { name: 'French', code: 'fr', flag: '🇫🇷' },
  { name: 'German', code: 'de', flag: '🇩🇪' },
  { name: 'Hindi', code: 'hi', flag: '🇮🇳' },
  { name: 'Italian', code: 'it', flag: '🇮🇹' },
  { name: 'Japanese', code: 'ja', flag: '🇯🇵' },
  { name: 'Javanese', code: 'jv', flag: '🇮🇩' },
];
export type RootStackParamList = {
  Details: any;
};
export type CallScreenModals = {
  showAddPeople: boolean;
  showSwitchToVideoRequest: boolean;
};

export type CallScreenModalKey = keyof CallScreenModals;

const MainCallScreen = () => {
  const [languageModal, setLanguageModal] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [optionsModal, setOptionsModal] = useState(false);
  const [showingFooter, setShowningFooter] = useState(true);
  const [callMembersModal, setCallMembersModal] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [fullScreenMode, setFullScreenMode] = useState(false);
  const { callDetails, callNotifications, setShowFullScreenCall, showFullScreenCall } =
    useCallContext();

  const [callModalState, setCallModalState] = useState<CallScreenModals>({
    showAddPeople: false,
    showSwitchToVideoRequest: false,
  });

  const insets = useSafeAreaInsets();
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [participantsCopy, setParticipantsCopy] = useState<Participant[]>([]);

  useEffect(() => {
    if (callDetails?.state === 'ongoing' && Array.isArray(callDetails?.participants)) {
      const currJsonpts = JSON.stringify(participants);
      const newJsonpts = JSON.stringify(callDetails.participants);

      if (currJsonpts !== newJsonpts) {
        setParticipants(callDetails.participants);
        setParticipantsCopy(callDetails.participants);
      }
    }
  }, [callDetails?.state, callDetails?.participants]);

  // Auto-hide footer for video calls
  useEffect(() => {
    if (callDetails?.type === 'video' && callDetails?.state === 'ongoing' && showingFooter) {
      const timeoutId = setTimeout(() => {
        setShowningFooter(false);
      }, 7000);

      return () => clearTimeout(timeoutId); // Clear if state/type changes
    }
  }, [callDetails?.type, callDetails?.state, showingFooter]);

  const openModal = (modal: CallScreenModalKey) => {
    setCallModalState((prevState) => {
      const newState = Object.keys(prevState).reduce((acc, key) => {
        acc[key as CallScreenModalKey] = key === modal;
        return acc;
      }, {} as Record<CallScreenModalKey, boolean>);
      return newState;
    });
  };

  const closeModal = (modal: CallScreenModalKey) => {
    setCallModalState((prevState) => ({ ...prevState, [modal]: false }));
  };

  const filteredLanguages = useMemo(() => {
    return languages.filter((lang) => lang.name.toLowerCase().includes(search.toLowerCase()));
  }, [search]); // FIXED: Added memoization to prevent recalculations

  const searchCallMembers = useCallback(
    (text: string) => {
      setSearchText(text);

      const searchText = text.toLowerCase();

      const filteredResults = participantsCopy.filter((item) => {
        const name = item?.client?.name?.toLowerCase() || '';
        return name.includes(searchText);
      });

      setParticipants(filteredResults);
    },
    [participantsCopy],
  );

  useEffect(() => {
    if (!showFullScreenCall) return; // don’t attach if false

    const backAction = () => {
      setShowFullScreenCall(false);
      return true; // block default back action
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    // cleanup: runs when showFullScreenCall changes OR component unmounts
    return () => {
      backHandler.remove();
    };
  }, [showFullScreenCall]);

  if (callDetails.state === 'idle') {
    return null;
  }

  return (
    <View
      style={{
        flex: 1,
        paddingTop: insets.top,
        paddingBottom: insets.bottom,
      }}
    >
      <StatusBar barStyle="light-content" backgroundColor="#111111" translucent={false} />
      <Image source={IMAGES.callBg} style={styles.callBgIcon} />

      {callDetails.state === 'outgoing' && (
        <OutgoingCal setCallMembersModal={setCallMembersModal} setOptionsModal={setOptionsModal} />
      )}

      {callDetails.state === 'incoming' && (
        <IncomingCall setCallMembersModal={setCallMembersModal} setOptionsModal={setOptionsModal} />
      )}

      {callDetails.state === 'ongoing' && (
        <OngoingCall
          fullScreenMode={fullScreenMode}
          participants={participants}
          setFullScreenMode={setFullScreenMode}
          setShowningFooter={setShowningFooter}
          showingFooter={showingFooter}
          callModalState={callModalState}
          closeModal={closeModal}
          openModal={openModal}
          setOptionsModal={setOptionsModal}
          setCallMembersModal={setCallMembersModal}
        />
      )}

      {/* {callDetails.state === 'idle' && (
        <View style={{ backgroundColor: '#FFF', flex: 1, justifyContent: 'center' }}>
          <ActivityIndicator
            style={{ justifyContent: 'center', alignItems: 'center' }}
            color="black"
          />
        </View>
      )} */}

      <OptionsModal
        optionsModal={optionsModal}
        setLanguageModal={setLanguageModal}
        setOptionsModal={setOptionsModal}
      />
      <LanguageModal
        filteredLanguages={filteredLanguages}
        languageModal={languageModal}
        search={search}
        selectedLanguage={selectedLanguage}
        setLanguageModal={setLanguageModal}
        setSearch={setSearch}
        setSelectedLanguage={setSelectedLanguage}
      />
      <AddPeopleModal closeModal={closeModal} showModal={callModalState.showAddPeople} />
      <CallMemberModal
        callMembersModal={callMembersModal}
        participants={participants}
        searchCallMembers={searchCallMembers}
        searchText={searchText}
        setCallMembersModal={setCallMembersModal}
      />
      {callNotifications.showSwitchToVideoPopup && <SwitchToVideo />}
      {callModalState.showSwitchToVideoRequest && <SwitchToVideoRequest closeModal={closeModal} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  callBgIcon: {
    position: 'absolute',
    height: 215,
    resizeMode: 'stretch',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  iconStyle: {
    width: 40,
    height: 40,
  },
  iconStyle1: {
    width: 24,
    height: 24,
  },
  endcallStyle: {
    width: 50,
    height: 50,
  },
  name: {
    ...commonFontStyle(600, 20, colors.white),
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingVertical: 15,
  },
  buttonContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    right: 10,
  },

  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  video: {
    width: '100%',
    height: 200,
    backgroundColor: '#000',
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    color: '#fff',
    fontSize: 14,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  flag: {
    fontSize: 18,
    marginRight: 10,
  },
  languageName: {
    flex: 1,
    fontSize: 16,
    color: '#fff',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  box: {
    width: 100,
    height: 100,
    backgroundColor: 'salmon',
    borderRadius: 8,
    position: 'absolute',
    top: 100,
    left: 100,
    zIndex: 1000,
    elevation: 10,
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  centerContent: {
    flex: 1,
    width: '100%',
  },

  gridContainer: {
    flex: 1,
    width: '100%',
  },

  fullScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  twoParticipantsContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  halfScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  threeParticipantsContainer: {
    flex: 1,
    width: '100%',
  },
  firstRow: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  secondRow: {
    flex: 1,
    width: '100%',
  },
  halfWidthTile: {
    flex: 1,
    width: '50%',
    height: '100%',
  },
  fullWidthTile: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});

export default MainCallScreen;

export const MainCallScreenOverlay = () => {
  const { callDetails, showFullScreenCall } = useCallContext();

  if (callDetails?.state === 'idle') {
    return null;
  }

  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: zIndex.level_5,
        display: showFullScreenCall ? 'flex' : 'none',
      }}
    >
      <MainCallScreen />
    </View>
  );
};
