import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const EndCallSvg: React.FC<IconProps> = ({
  size = 16,
  color = 'black',
  ...restProps
}) => {
  return (
    <Svg width={23} height={12} viewBox="0 0 23 12" fill="none" {...restProps}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23 8.13c-.004 1.721-1.577 2.988-3.265 2.745l-2.089-.302c-1.374-.199-2.42-1.349-2.418-2.741l.001-.338a.655.655 0 00-.212-.223c-.334-.244-1.188-.616-3.208-.612-2.02.004-2.878.38-3.216.628a.669.669 0 00-.217.23l-.001.576c-.003 1.238-.84 2.306-2.026 2.656l-2.09.617C2.467 11.894.596 10.61.6 8.714l.005-2.009c0-.633.164-1.323.636-1.892C2.404 3.412 5.546.576 11.813.563c5.852-.013 8.96 1.97 10.286 3.153l-.5.56.5-.56c.67.598.907 1.434.906 2.194L23 8.13zm-3.052 1.256c.839.121 1.546-.51 1.548-1.262l.005-2.22c0-.466-.143-.835-.403-1.066-1.053-.94-3.805-2.783-9.28-2.772C6.076 2.08 3.333 4.651 2.4 5.774c-.19.23-.29.548-.29.936l-.005 2.01c-.001.816.83 1.467 1.726 1.203l2.09-.616c.578-.17.948-.676.95-1.219l.001-.644.752.003a27.047 27.047 0 01-.752-.005V7.406a1.03 1.03 0 01.022-.171c.02-.095.053-.212.112-.34.12-.264.335-.556.697-.821.708-.52 1.941-.915 4.1-.92 2.159-.004 3.392.385 4.101.904.363.265.578.557.698.822a1.531 1.531 0 01.128.466.99.99 0 01.003.048v.035c0 .002 0 .003-.752 0l.752.003v.405c-.002.606.458 1.15 1.126 1.247l2.089.302z"
        fill="#fff"
      />
    </Svg>
  );
};

export default EndCallSvg;
