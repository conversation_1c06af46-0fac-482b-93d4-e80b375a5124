//import liraries
import React, { Component } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import ModalWrapper from '../../../../component/ModalWrapper';
import { CallType } from '../../../../types/calls.types';

interface CallTypeSelectionModalProps {
  visible: boolean;
  handleCall: (type: CallType) => void;
  onClose: () => void;
}

const CallTypeSelectionModal: React.FC<CallTypeSelectionModalProps> = ({
  visible,
  handleCall,
  onClose,
}) => {
  return (
    <ModalWrapper isVisible={visible} transparent animationType="slide" onCloseModal={onClose}>
      <View style={{}}>
        <Text style={styles.modalTitle}> Call Type </Text>
        <View style={styles.callTypeOptions}>
          <TouchableOpacity
            style={styles.callTypeButton}
            onPress={() => {
              handleCall('audio');
            }}
          >
            <Text style={styles.callTypeButtonText}>Audio</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.callTypeButton}
            onPress={() => {
              handleCall('video');
            }}
          >
            <Text style={styles.callTypeButtonText}>Video</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ModalWrapper>
  );
};

const styles = StyleSheet.create({
  modalHandle: {
    width: 40,
    height: 5,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 24,
    color: 'black',
  },
  callTypeOptions: {
    flexDirection: 'row',
    gap: 16,
    justifyContent: 'center',
    width: '100%',
  },
  callTypeButton: {
    backgroundColor: '#6A41D9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 16,
    width: '45%',
    alignItems: 'center',
  },
  callTypeButtonText: {
    color: 'white',
    fontSize: 18,
  },
});

export default CallTypeSelectionModal;
