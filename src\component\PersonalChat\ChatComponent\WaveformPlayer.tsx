import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import Svg, { Rect } from 'react-native-svg';

const AnimatedRect = Animated.createAnimatedComponent(Rect);

const WaveformPlayer = ({ isPlaying }: { isPlaying: boolean }) => {
  const waveAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isPlaying) {
      startWaveAnimation();
    } else {
      stopWaveAnimation();
    }
  }, [isPlaying]);

  const startWaveAnimation = () => {
    Animated.loop(
      Animated.timing(waveAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ).start();
  };

  const stopWaveAnimation = () => {
    waveAnimation.stopAnimation();
    waveAnimation.setValue(0); // Reset animation
  };

  const generateBars = () => {
    const barCount = 60; // Number of bars
    const barWidth = 3;
    const spacing = 5;
    const barHeights = Array.from({ length: barCount }, () => Math.random() * 50 + 10);

    return barHeights.map((height, index) => (
      <AnimatedRect
        key={index}
        x={index * (barWidth + spacing)}
        y={10 - height / 2}
        width={barWidth}
        height={height}
        rx={2} // Rounded corners
        fill="#8A2BE2"
        opacity={0.8}
        transform={[
          {
            scaleY: waveAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [1, Math.random() * 1.5 + 0.5],
            }),
          },
        ]}
        originY="50"
      />
    ));
  };

  return (
    <View style={styles.container}>
      <Svg width={180} height={15}>
        {generateBars()}
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    // backgroundColor: "#E6E6FA",
    borderRadius: 20,
  },
});

export default WaveformPlayer;
