import React, { useState } from 'react'
import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import CommonView from '../../../component/CommonView'
import { RouteProp, useRoute } from '@react-navigation/native';
import { colors } from '../../../theme/colors';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';

type AdminsScreenParams = {
    AdminsScreen: {
        userData: any;
        userDetails: any;
    };
};

const AdminsScreen = () => {
    const route = useRoute<RouteProp<AdminsScreenParams, 'AdminsScreen'>>();
    const users = route.params.userData
    const userDetails = route.params.userDetails

    const role = userDetails.conversation.role

    //   const [selectedMember, setSelectedMember] = useState<any>(null);
    // console.log("selectedMember", user)

    

    return (
        <CommonView headerTitle="Admins">
            <FlatList
            showsVerticalScrollIndicator={false}
                data={users}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => {
                    return (
                        <TouchableOpacity
                            style={styles.memberRow}
                            onPress={() => {
                                if (item.role === 'owner') return;

                                navigateTo(SCREENS.AdminPermissions, { userDetails: userDetails, userData: item });
                            }}
                        >
                            <Image
                                source={
                                    {uri: item.user.image}
                                }
                                style={styles.memberAvatar}
                            />
                            <View style={{ flex: 1 }}>
                                <Text style={styles.memberName}>{item.user.name}</Text>
                                <Text style={styles.memberUsername}>{item.user.username}</Text>
                            </View>
                            <View style={styles.roleBadge}>
                                <Text style={styles.roleText}>{item.role}</Text>
                            </View>
                        </TouchableOpacity>
                    );
                }}
            />
        </CommonView>
    )
}

const styles = StyleSheet.create({
    memberRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 10,
        borderBottomWidth: 0.5,
        borderColor: '#eee',
        gap: 12,
    },

    memberAvatar: {
        width: 48,
        height: 48,
        borderRadius: 24,
        marginRight: 8,
    },

    memberName: {
        fontSize: 16,
        fontWeight: '600',
        color: colors.black_23,
    },

    memberUsername: {
        fontSize: 13,
        color: colors.gray_80,
    },

    roleBadge: {
        paddingHorizontal: 14,
        paddingVertical: 6,
        borderRadius: 14,
        borderColor: colors.gray_80,
        borderWidth: 1
    },

    roleText: {
        fontSize: 16,
        fontWeight: '500',
        color: colors.gray_80,
        lineHeight: 15,
        verticalAlign: 'middle',
        textAlign: 'center',
    },

})

export default AdminsScreen