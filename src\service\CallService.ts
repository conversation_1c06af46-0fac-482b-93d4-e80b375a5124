import Realm from 'realm';
import { CallSchema } from '../device-storage/realm/schemas/CallSchema';
import { Socket } from 'socket.io-client';

class CallServiceController {
  public realm?: Realm;
  private socket?: Socket;
  setRealm(realmInstance: Realm) {
    if (!this.realm) {
      this.realm = realmInstance;
    }
  }

  setSocket(socket: Socket) {
    this.socket = socket;
  }

  validateRealm() {
    if (!this.realm) {
      throw new Error('Realm instance not found');
    }
    return this.realm;
  }
  createCall(callData: Partial<CallSchema>) {
    try {
      const realm = this.validateRealm();
      realm.write(() => {
        console.log('Creating call:', callData);
        realm.create(
          CallSchema.schema.name,
          {
            ...callData,
            originJson: callData.origin ? JSON.stringify(callData.origin) : undefined,
          },
          Realm.UpdateMode.Modified,
        );
      });
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  updateCall(callId: string, callData: Partial<Omit<CallSchema, 'callId'>>) {
    try {
      const realm = this.validateRealm();
      realm.write(() => {
        const call = this.getCallById(callId);
        if (!call) return;
        realm.create(
          CallSchema.schema.name,
          {
            callId,
            ...callData,
            originJson: callData.origin ? JSON.stringify(callData.origin) : undefined,
          },
          Realm.UpdateMode.Modified,
        );
      });
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
  // Create or update a call record
  upsertCall(callData: Partial<CallSchema>) {
    try {
      const realm = this.validateRealm();

      realm.write(() => {
        realm.create(
          CallSchema.schema.name,
          {
            ...callData,
            originJson: callData.origin ? JSON.stringify(callData.origin) : undefined,
          },
          Realm.UpdateMode.Modified,
        );
      });
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  // Get call by callId
  getCallById(callId: string): CallSchema | null {
    const realm = this.validateRealm();
    return realm.objectForPrimaryKey(CallSchema.schema.name, callId);
  }

  getAllCalls(): Realm.Results<CallSchema> {
    const realm = this.validateRealm();
    return realm.objects<CallSchema>(CallSchema.schema.name);
  }

  // Get all ongoing calls
  getOngoingCalls(): Realm.Results<CallSchema> {
    const realm = this.validateRealm();
    return realm.objects<CallSchema>(CallSchema.schema.name).filtered('isOngoingCall == true');
  }

  // Delete a call by callId
  deleteCall(callId: string) {
    const realm = this.validateRealm();
    const call = this.getCallById(callId);
    if (!call) return;
    realm.write(() => {
      realm.delete(call);
    });
  }
  deleteAllCalls() {
    const prmObj = new Promise<boolean>((resolve, reject) => {
      const realm = this.validateRealm();
      realm.write(() => {
        const allCalls = realm.objects(CallSchema.schema.name);
        realm.delete(allCalls);
        console.log('All calls deleted');
        resolve(true);
      });
    });
    return prmObj;
  }

  // List calls by user id participation
  getCallsByUserId(userId: string): Realm.Results<CallSchema> {
    const realm = this.validateRealm();
    return realm
      .objects<CallSchema>(CallSchema.schema.name)
      .filtered(
        'participants CONTAINS $0 OR invitedUserIds CONTAINS $0 OR initiator == $0',
        userId,
      );
  }
}

const CallService = new CallServiceController();
export default CallService;
