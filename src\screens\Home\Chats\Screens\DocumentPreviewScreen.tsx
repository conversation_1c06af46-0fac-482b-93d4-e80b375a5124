import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  Platform,
  ScrollView,
  Keyboard,
} from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import Pdf from 'react-native-pdf';
import { StackNavigationProp } from '@react-navigation/stack';
import { WebView } from 'react-native-webview';
import { MessageType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { hp } from '../../../../theme/fonts';
import { colors } from '../../../../theme/colors';
import CommonView from '../../../../component/CommonView';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import SendSVG from '../../../../assets/svgIcons/SendSVG';
import Api from '../../../../utils/api';
import { ComposedMessage, RootStackParamList } from '../../../../types/index.types';
import { formatBytes } from '../../../../common/CFilename';
import { handleMediaFileSharing } from '../../../../utils/commonFunction';
import { KeyboardStickyView } from 'react-native-keyboard-controller';

type RouteProps = RouteProp<RootStackParamList, 'DocumentPreviewScreen'>;
type NavigationProps = StackNavigationProp<RootStackParamList, 'DocumentPreviewScreen'>;

const DocumentPreviewScreen = () => {
  const route = useRoute<RouteProps>();
  const navigation = useNavigation<NavigationProps>();
  const { fileUrl, fileName, fileSize, composedMessageProp, onSend, isFromChat } = route.params;
  const [composedMessage, setComposedMessage] = useState(composedMessageProp);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  const fileExtension = fileName?.split('.').pop()?.toLowerCase() || '';
  const isPDF = fileExtension === 'pdf';
  const isImage = /\.(jpg|jpeg|png|webp)$/i.test(fileName);
  const isComposing = typeof onSend === 'function';

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleUploadAndSend = async () => {
    try {
      const formData = new FormData();
      const fileData = {
        uri: Platform.OS === 'android' ? fileUrl : fileUrl.replace('file://', ''),
        name: fileName,
        type: 'application/octet-stream',
      };
      formData.append('file', fileData);
      const finalMessage: ComposedMessage = {
        ...composedMessage,
        messageType: MessageType.DOCUMENT,
        mediaUrl: fileUrl,
        fileName,
        fileSize,
        isMedia: true,
        text: 'text' in composedMessage ? composedMessage.text?.trim() || '' : '',
      };
      navigation.goBack();
      if (onSend) {
        const sentData = await onSend(finalMessage, true);
        console.log('documentpreview component', sentData);
        handleMediaFileSharing(formData, sentData);
      }
      // const result = await Api.post('upload', formData, true);
      // const uploadedUrl = result.body?.data?.url;

      // console.log("finalmessageeeee", JSON.stringify(finalMessage, null, 2));
    } catch (err) {
      console.error('Upload failed:', err);
    }
  };

  const renderPreview = () => {
    const isVideo = /\.(mp4|mov|avi|mkv|webm)$/i.test(fileName);
    if (isComposing) {
      if (isPDF) {
        return (
          <Pdf
            source={{ uri: fileUrl }}
            style={{ flex: 1 }}
            trustAllCerts={false}
            onError={(err) => console.log('PDF Load Error:', err)}
          />
        );
      } else if (isVideo) {
        return (
          <View style={styles.otherFilePreview}>
            <Text style={styles.fileNameLabel}>{fileName}</Text>
            <Text style={styles.fileSizeLabel}>{formatBytes(fileSize)}</Text>
          </View>
        );
      } else {
        return (
          <View style={styles.otherFilePreview}>
            <Text style={styles.fileNameLabel}>{fileName}</Text>
            <Text style={styles.fileSizeLabel}>{formatBytes(fileSize)}</Text>
          </View>
        );
      }
    } else {
      if (isPDF) {
        return (
          <Pdf
            source={{ uri: fileUrl }}
            style={{ flex: 1 }}
            trustAllCerts={false}
            onError={(err) => console.log('PDF Load Error:', err)}
          />
        );
      } else if (isImage) {
        return <Image source={{ uri: fileUrl }} style={{ flex: 1, resizeMode: 'contain' }} />;
      } else if (isVideo) {
        return (
          <View style={styles.otherFilePreview}>
            <Text style={styles.fileNameLabel}>{fileName}</Text>
            <Text style={styles.fileSizeLabel}>{formatBytes(fileSize)}</Text>
          </View>
        );
      } else {
        const previewURL = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(
          fileUrl,
        )}`;
        return (
          <WebView
            source={{ uri: previewURL }}
            startInLoadingState
            javaScriptEnabled
            domStorageEnabled
            style={styles.webview}
            renderError={() => (
              <View style={styles.otherFilePreview}>
                <Text style={styles.noPreviewText}>Preview not available.</Text>
              </View>
            )}
          />
        );
      }
    }
  };

  return (
    <CommonView
      headerTitle="Document Preview"
      customHeader={() => (
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <BackArrowSVG />
          </TouchableOpacity>
          <View style={styles.fileInfoWrapper}>
            <Text style={styles.fileNameText} numberOfLines={1}>
              {fileName}
            </Text>
            <Text style={styles.fileSizeText}>{formatBytes(fileSize)}</Text>
          </View>
        </View>
      )}
    >
      <View style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          style={{ flex: 1 }}
        >
          <View style={[styles.previewContainer, { height: keyboardVisible ? '51%' : '97%' }]}>
            {renderPreview()}
          </View>
        </ScrollView>

        {isComposing && (
          <KeyboardStickyView>
            <View style={styles.composerBox}>
              {'text' in composedMessage && (
                <TextInput
                  placeholder="Add a caption (optional)"
                  value={composedMessage.text || ''}
                  onChangeText={(text) => setComposedMessage((prev) => ({ ...prev, text }))}
                  style={[styles.textInput, { minHeight: 40, maxHeight: 100 }]}
                  placeholderTextColor={colors._B5B5B5_gray}
                  multiline
                  textAlignVertical="top"
                />
              )}
              <TouchableOpacity onPress={handleUploadAndSend} style={{ opacity: 1, marginLeft: 8 }}>
                <SendSVG size={36} color={colors.white} backgroundColor={colors.mainPurple} />
              </TouchableOpacity>
            </View>
          </KeyboardStickyView>
        )}
      </View>
    </CommonView>
  );
};

export default DocumentPreviewScreen;

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: hp(2),
    paddingBottom: hp(1),
    backgroundColor: colors.mainPurple,
  },
  fileInfoWrapper: {
    marginLeft: 10,
    flex: 1,
  },
  fileNameText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  fileSizeText: {
    fontSize: 12,
    color: colors.white,
    marginTop: 2,
  },
  previewContainer: {
    width: '100%',
    backgroundColor: '#E2DFEA',
    marginBottom: hp(2),
    borderRadius: 12,
    overflow: 'hidden',
    minHeight: 300,
  },

  otherFilePreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  fileNameLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black,
  },
  fileSizeLabel: {
    fontSize: 14,
    color: colors.black_23,
    marginBottom: 8,
  },
  noPreviewText: {
    color: '#777',
    fontSize: 12,
    marginTop: 6,
    textAlign: 'center',
  },
  composerBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 15,
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    color: colors.black,
    padding: 10,
  },
  webview: {
    flex: 1,
    width: '100%',
    height: '100%',
    alignSelf: 'stretch',
    backgroundColor: '#fff',
  },
});
