import * as React from "react";
import Svg, { Circle } from "react-native-svg";
import { colors } from "../../theme/colors";

interface SvgComponentProps {
    size?: number;
    color?: string;
    unSelectColor?: string;
    isSelected?: boolean;
}

const SelecteUnselectSVG: React.FC<SvgComponentProps> = ({
    size = 20,
    color = colors.mainPurple,
    unSelectColor = colors.black_23,
    isSelected = false,
    ...props
}) => {
    return (
        <>
            {isSelected ? <Svg
                width={size}
                height={size}
                viewBox="0 0 20 20"
                fill="none"
                {...props}
            >
                <Circle cx={10} cy={10} r={9.5} stroke={color} />
                <Circle cx={10} cy={10} r={6} fill={color} />
            </Svg> :
                <Svg
                    width={size}
                    height={size}
                    viewBox="0 0 20 20"
                    fill="none"
                    {...props}
                >
                    <Circle cx={10} cy={10} r={9.5} stroke={unSelectColor} />
                </Svg>
            }
        </>
    );
};

export default SelecteUnselectSVG;

