import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { colors } from '../../../theme/colors';
import { hp, commonFontStyle } from '../../../theme/fonts';
import Radio from './Radio';
import { IContact } from '../../../device-storage/realm/schemas/ContactSchema';

interface ContactRowProps {
  contact: IContact;
  selected: boolean;
  onToggle: (contact: IContact) => void;
  showRadio?: boolean;
  radioSize?: number;
  radioColor?: string;
  radioType?: 'radio' | 'tick';
}

const ContactRow: React.FC<ContactRowProps> = ({
  contact,
  selected,
  onToggle,
  showRadio = true,
  radioSize = 20,
  radioColor,
  radioType = 'radio',
}) => (
  <TouchableOpacity style={styles.rowView} onPress={() => onToggle(contact)} activeOpacity={1}>
    {contact.image ? (
      <Image source={{ uri: contact.image }} style={styles.avatar} />
    ) : (
      <View style={styles.avatar} />
    )}
    <View style={{ flex: 1 }}>
      <Text style={styles.title}>{contact.name}</Text>
      {contact.bio ? <Text style={styles.bio}>{contact.bio}</Text> : null}
    </View>
    {showRadio && (
      <View style={styles.radioWrapper}>
        <Radio selected={selected} size={radioSize} color={radioColor} type={radioType} />
      </View>
    )}
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(1),
    paddingVertical: hp(1),
    marginLeft: 10,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.gray_80,
    marginRight: 12,
  },
  radioWrapper: {
    marginRight: 12,
  },
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  bio: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: 3,
  },
});

export default ContactRow;
