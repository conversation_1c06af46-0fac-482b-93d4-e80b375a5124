import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
    size?: number;
    color?: string;
}

const LiveLocationSVG: React.FC<SvgComponentProps> = ({
    size = 24,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={(size * 13) / 17} // maintain aspect ratio
            viewBox="0 0 17 13"
            fill="none"
            {...props}
        >
            <Path
                d="M8.5 3.762a2.415 2.415 0 00-2.412 2.412A2.415 2.415 0 008.5 8.586a2.415 2.415 0 002.413-2.412A2.415 2.415 0 008.5 3.762zm0 3.702c-.711 0-1.29-.579-1.29-1.29 0-.711.579-1.29 1.29-1.29.711 0 1.29.579 1.29 1.29 0 .711-.579 1.29-1.29 1.29zM5.438 2.319a.561.561 0 00-.793 0 5.417 5.417 0 00-1.598 3.855c0 1.455.567 2.824 1.598 3.855a.56.56 0 00.793 0 .561.561 0 000-.794A4.302 4.302 0 014.17 6.174c0-1.156.451-2.243 1.27-3.062a.561.561 0 000-.793z"
                fill={color}
            />
            <Path
                d="M1.122 6.174c0-1.97.768-3.822 2.162-5.216a.561.561 0 10-.793-.794A8.444 8.444 0 000 6.174c0 2.269.885 4.403 2.49 6.009a.56.56 0 00.794 0 .561.561 0 000-.794 7.33 7.33 0 01-2.162-5.215zM12.355 2.319a.561.561 0 10-.794.793 4.302 4.302 0 011.269 3.062c0 1.155-.45 2.243-1.269 3.061a.561.561 0 10.794.794 5.417 5.417 0 001.597-3.855 5.417 5.417 0 00-1.597-3.855z"
                fill={color}
            />
            <Path
                d="M14.509.164a.561.561 0 10-.794.794 7.33 7.33 0 012.162 5.216 7.33 7.33 0 01-2.162 5.215.561.561 0 10.794.794 8.444 8.444 0 002.49-6.01A8.444 8.444 0 0014.51.165z"
                fill={color}
            />
        </Svg>
    );
};

export default LiveLocationSVG;


