import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const ForwardArrow: React.FC<IconProps> = ({
    size = 22,
    color = "#fff",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={(size * 21) / 22} // maintains original aspect ratio (22:21)
            viewBox="0 0 22 21"
            fill="none"
            {...restProps}
        >
            <Path
                d="M14.08.925a.74.74 0 01.519.215l6.626 6.595.091.11a.738.738 0 01-.08.924h0l-6.627 6.596a.738.738 0 01-1.039 0 .731.731 0 010-1.034l5.365-5.35H7.27a5.558 5.558 0 00-3.905 1.643 5.505 5.505 0 00-1.597 3.907v4.913a.735.735 0 01-.738.735.742.742 0 01-.41-.124l-.111-.091a.738.738 0 01-.217-.52v-4.913a6.972 6.972 0 012.03-4.946A7.035 7.035 0 017.27 7.513h11.665l-5.374-5.34a.73.73 0 010-1.033l.112-.092a.737.737 0 01.407-.123z"
                fill={color}
                stroke={color}
                strokeWidth={0.15}
            />
        </Svg>
    );
};

export default ForwardArrow;

