import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import ModalWrapper from '../../../../../component/ModalWrapper';
import { colors } from '../../../../../theme/colors';

type ConversationMuteTimerModalProps = {
  isVisible: boolean;
  onClose: () => void;
  onSelectDuration: (duration: '8_hours' | '1_week' | 'always') => void;
};

const muteOptions = [
  { label: '8 hours', duration: '8_hours' },
  { label: '1 week', duration: '1_week' },
  { label: 'Always', duration: 'always' },
];

const ConversationMuteTimerModal: React.FC<ConversationMuteTimerModalProps> = ({
  isVisible,
  onClose,
  onSelectDuration,
}) => {
  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onClose}>
      <View style={styles.modalContent}>
        <Text style={styles.modalTitle}>Mute notifications for...</Text>

        {muteOptions.map((option) => (
          <TouchableOpacity
            key={option.label}
            style={styles.optionItemRow}
            onPress={() => onSelectDuration(option.duration as any)}
          >
            <Text style={styles.optionText}>{option.label}</Text>
            <View style={styles.optionItemRadioOuter}></View>
          </TouchableOpacity>
        ))}
      </View>
    </ModalWrapper>
  );
};

export default ConversationMuteTimerModal;

const styles = StyleSheet.create({
  modalContent: {
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
    marginBottom: 20,
  },
  muteRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  muteLabel: {
    fontSize: 16,
    color: colors.black,
  },
  optionItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionText: {
    fontSize: 16,
    color: colors.black_23,
  },
  optionItemRadioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: colors.gray_80,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
