import notifee, { AndroidStyle } from '@notifee/react-native';
import { SCREENS } from '../../navigation/screenNames';
import { ServerMessage } from '../../types/socketPayload.type';
import { notificationChannels } from './createNotificationChannels';
import { IConversation } from '../../device-storage/realm/schemas/ConversationSchema';
import { IMessage } from '../../device-storage/realm/schemas/MessageSchema';
import { colors } from '../../theme/colors';

export const displayIncomingCallNotification = async (callerName: string) => {
  await notifee.displayNotification({
    title: `${callerName} is calling...`,
    body: 'Tap to answer or reject',
    android: {
      channelId: notificationChannels.calls.id,
      sound: 'default',
      pressAction: { id: 'default' },
      actions: [
        {
          title:
            '<p style="background-color: red; color: white; border-radius: 8px; padding: 8px 12px; display: inline-block;"> answer </p>',
          pressAction: {
            id: 'answer',
            launchActivity: 'default',
            mainComponent: SCREENS.MainCallScreen,
          },
        },
        { title: 'Reject', pressAction: { id: 'reject' } },
      ],
      style: {
        type: AndroidStyle.BIGTEXT,
        text: 'You have an incoming call',
      },
    },
  });
};

export class Notify {
  static async displayIncomingCallNotification(callerName: string) {
    await notifee.displayNotification({
      title: `${callerName} is calling...`,
      body: 'Tap to answer or reject',
      android: {
        channelId: notificationChannels.calls.id,
        sound: 'default',
        pressAction: { id: 'default' },
        actions: [
          {
            title: 'Answer',
            pressAction: {
              id: 'answer',
              launchActivity: 'default',
              mainComponent: SCREENS.MainCallScreen,
            },
          },
          { title: 'Reject', pressAction: { id: 'reject' } },
        ],
        style: {
          type: AndroidStyle.BIGTEXT,
          text: 'You have an incoming call',
        },
      },
    });
  }

  static async incomingMessage(
    message: IMessage,
    conversationData: IConversation,
    soundEnabled: boolean = true,
  ) {
    const channelId = soundEnabled
      ? notificationChannels.chats.id
      : notificationChannels.chats_silent.id;
    console.log('Notification channel ID:', channelId);

    await notifee.displayNotification({
      id: message.globalId,
      title: conversationData.displayName,
      body: message.text,
      data: { openScreen: SCREENS.ContactsScreen },
      android: {
        channelId: channelId,
        pressAction: { id: 'message', mainComponent: SCREENS.ContactsScreen },
        color: colors.mainPurple,
        smallIcon: 'cb_notification',
        style: {
          type: AndroidStyle.MESSAGING,
          person: { name: conversationData.displayName, icon: conversationData.displayPic },
          messages: [
            {
              text: message.text || 'Mock message',
              timestamp: Date.now(),
              person: { name: conversationData.displayName, icon: conversationData.displayPic },
            },
          ],
        },
        actions: [
          {
            title: 'Reply',
            pressAction: {
              id: 'reply',
              launchActivity: 'default',
              mainComponent: SCREENS.ChatSpecificScreen,
            },
          },
        ],
      },
    });
  }
}
