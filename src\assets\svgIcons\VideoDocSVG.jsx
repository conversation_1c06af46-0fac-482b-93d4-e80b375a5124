import * as React from "react";
import Svg, { Path } from "react-native-svg";

function VideoDocSVG({ width = 20, height = 22, color = "#6A4DBB", ...props }) {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 20 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M8.03 0c.606 0 1.092 0 1.5.019a1.818 1.818 0 00-.021.268L9.5 3.404c0 1.207 0 2.274.104 3.133.114.932.377 1.864 1.068 2.624.691.76 1.538 1.049 2.385 1.174.781.115 1.751.115 2.848.115h4.052C20 11.038 20 11.76 20 12.72v.481c0 4.148 0 6.222-1.172 7.51C17.657 22 15.771 22 12 22H8c-3.771 0-5.657 0-6.828-1.29C0 19.423 0 17.349 0 13.2V8.8c0-4.148 0-6.222 1.172-7.51C2.343 0 4.239 0 8.03 0zm-.707 11a.32.32 0 00-.28.166.335.335 0 00-.043.166v6.336a.337.337 0 00.161.288.317.317 0 00.324 0l5.353-3.169A.33.33 0 0013 14.5a.329.329 0 00-.162-.287l-5.353-3.168a.319.319 0 00-.16-.045h-.002zM11.01.292c.692.292 1.256.85 2.384 1.967l3.959 3.92c1.216 1.204 1.866 1.846 2.228 2.62H16c-2.357 0-3.535.001-4.268-.804C11 7.19 11 5.893 11 3.3l.009-3.008z"
        fill={color}
      />
    </Svg>
  );
}

export default VideoDocSVG;
