import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const ExpandSVG: React.FC<SvgComponentProps> = ({
    size = 16,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 16 16"
            fill="none"
            {...props}
        >
            <Path
                d="M15.413 0h-3.665a.586.586 0 000 1.173h3.079V4.25a.586.586 0 001.172 0V.586A.586.586 0 0015.413 0zM4.25 0H.585A.586.586 0 000 .586v3.665a.586.586 0 101.173 0V1.173H4.25A.586.586 0 104.25 0zM4.25 14.828H1.172v-3.079a.586.586 0 00-1.173 0v3.665A.586.586 0 00.585 16H4.25a.586.586 0 100-1.172zM15.413 11.163a.586.586 0 00-.587.586v3.079h-3.078a.587.587 0 000 1.172h3.665a.586.586 0 00.586-.586v-3.665a.586.586 0 00-.586-.586z"
                fill={color}
            />
        </Svg>
    );
};

export default ExpandSVG;


