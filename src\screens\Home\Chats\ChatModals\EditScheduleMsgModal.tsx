import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import ModalWrapper from '../../../../component/ModalWrapper';
import { hp } from '../../../../theme/fonts';
import { colors } from '../../../../theme/colors';
import DeleteSVG from '../../../../assets/svgIcons/DeleteSVG';
import EditSVG from '../../../../assets/svgIcons/EditSVG';
import TimerSVG from '../../../../assets/svgIcons/TimerSVG';
import SendArrowSVG from '../../../../assets/svgIcons/SendArrowSVG';

interface IProps {
  scheduleItemModal: boolean;
  onDeletePress: () => void;
  onEditPress: () => void;
  onReschedulePress: () => void;
  onSendNowPress: () => void;
  onCloseModal: () => void;
}

const EditScheduleMsgModal = ({
  scheduleItemModal,
  onCloseModal = () => {},
  onDeletePress = () => {},
  onEditPress = () => {},
  onReschedulePress = () => {},
  onSendNowPress = () => {},
}: IProps) => {
  return (
    <ModalWrapper isVisible={scheduleItemModal} onCloseModal={onCloseModal}>
      <View style={{ paddingHorizontal: hp(2) }}>
        <View>
          <Text
            style={{
              fontSize: 16,
              color: colors.black_23,
              fontWeight: '600',
              marginBottom: 23,
            }}
          >
            Scheduled messages options
          </Text>

          <TouchableOpacity
            onPress={onDeletePress}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 10,
              marginBottom: 20,
            }}
          >
            <DeleteSVG color={colors._EC0B0B_red} size={18} />
            <Text style={[styles.bottomText, { color: colors._EC0B0B_red }]}>Delete</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onEditPress}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 10,
              marginBottom: 20,
            }}
          >
            <EditSVG color={colors.black_23} size={19} />
            <Text style={styles.bottomText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onReschedulePress}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 10,
              marginBottom: 20,
            }}
          >
            <TimerSVG color={colors.black_23} size={19} />
            <Text style={styles.bottomText}>Reschedule</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onSendNowPress}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 10,
              marginBottom: 20,
            }}
          >
            <SendArrowSVG color={colors.black_23} size={19} />
            <Text style={styles.bottomText}>Send now</Text>
          </TouchableOpacity>
        </View>
        {/* {_renderComposer()} */}
      </View>
    </ModalWrapper>
  );
};

export default EditScheduleMsgModal;

const styles = StyleSheet.create({
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
});
