import { useQuery } from '../realm';
import { UserSchema, IUser } from '../schemas/UserSchema';

type QueryBuilder = {
  search: (query: string, fields?: string[]) => QueryBuilder;
  exclude: (numbers: (string | number)[]) => QueryBuilder;
  onlyUnblocked: () => QueryBuilder;
  searchIds: (userIds: (string | number)[]) => QueryBuilder;
  get: () => IUser[];
};

const useUsers = () => {
  const contactUsers = useQuery(UserSchema)
    .filtered('isDeviceContact == true')
    .sorted('contactName');

  const createFilteredContactsQuery = (): QueryBuilder => {
    let results = contactUsers;

    return {
      search(query: string, fields: string[] = ['contactName']) {
        if (query.trim()) {
          const conditions = fields.map((f) => `${f} CONTAINS[c] $0`).join(' OR ');
          results = results.filtered(conditions, query.trim());
        }
        return this;
      },

      exclude(numbers: (string | number)[]) {
        if (numbers.length > 0) {
          results = results.filtered('NOT phoneNumber IN $0', numbers);
        }
        return this;
      },
      searchIds(userIds: (string | number)[]) {
        if (userIds.length > 0) {
          results = results.filtered('id IN $0', userIds);
        }
        return this;
      },
      onlyUnblocked() {
        results = results.filtered('isBlocked == false');
        return this;
      },

      get() {
        return Array.from(results);
      },
    };
  };

  const blockedUsers = useQuery(UserSchema).filtered('isBlocked == true').sorted('contactName');

  const isUserBlocked = (userId: string) => {
    return blockedUsers.filtered('id == $0', userId).length > 0;
  };

  return {
    contactUsers,
    blockedUsers,
    createFilteredContactsQuery,
    isUserBlocked,
  };
};

export default useUsers;
