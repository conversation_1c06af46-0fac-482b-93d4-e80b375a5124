import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import ModalWrapper from '../../../../component/ModalWrapper';
import DollarCircle from '../../../../assets/svgIcons/DollarCircle';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import GalleryPlaneSVG from '../../../../assets/svgIcons/GalleryPlaneSVG';
import GalleryImgSvg from '../../../../assets/svgIcons/GalleryImgSvg';
import { colors } from '../../../../theme/colors';

import {
  initializeStream,
  LiveStreamPayload,
  StreamType,
} from '../../../../api/Chatspace/chatspace.api';
import { navigateTo } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';
import StreamToggleOption from './StreamToggle';
import { LiveStreamState } from '../../../../hooks/channels/useLiveStreamController';
import ImageCropPicker from 'react-native-image-crop-picker';
import { uploadImage } from '../../../../utils/apiGlobal';
import { uploadFiles } from '../../../../utils/ApiService';

type GoLiveInfoProps = {
  isVisible: boolean;
  onCloseModal: () => void;
  liveStreamData: LiveStreamPayload;
  setStreamDetails: React.Dispatch<React.SetStateAction<LiveStreamState>>;
  resetStreamingState(): void;
};
const GoLiveInfoModal = ({
  isVisible,
  onCloseModal,
  liveStreamData,
  setStreamDetails,
  resetStreamingState,
}: GoLiveInfoProps) => {
  const [showloading, setShowloading] = useState(false);
  async function handleStartLive() {
    try {
      setShowloading(true);
      let thumbnailUrl = '';
      if (liveStreamData.thumbnailImage && liveStreamData.thumbnailImage.path) {
        const data: any = await uploadFiles(liveStreamData.thumbnailImage);
        thumbnailUrl = data?.url || '';
      }

      const resp = await initializeStream({ ...liveStreamData, thumbnail: thumbnailUrl });

      navigateTo(SCREENS.ChannelStreamScreen, { liveStreamInfo: resp });
      setShowloading(false);
      resetStreamingState();
    } catch (err) {
      console.log(err);
    } finally {
      setShowloading(false);
    }
  }

  function handlePickImage() {
    ImageCropPicker.openPicker({
      cropping: true,
    }).then((image) => {
      console.log(image);
      setStreamDetails((prev) => {
        return {
          ...prev,
          thumbnailImage: image,
        };
      });
    });
  }

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={onCloseModal}>
      <View style={styles.container}>
        <Text style={styles.title}>Go live</Text>

        {/* Target Amount */}
        <TouchableOpacity style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter target amount"
            keyboardType="numeric"
            placeholderTextColor="black"
            value={liveStreamData.targetAmount?.toString()}
            onChangeText={(text) => {
              setStreamDetails((prev) => {
                return {
                  ...prev,
                  targetAmount: Number(text),
                };
              });
            }}
          />
          <DollarCircle />
        </TouchableOpacity>

        {/* Cause */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter the cause for"
            placeholderTextColor="black"
            value={liveStreamData.streamCause?.toString()}
            onChangeText={(text) => {
              setStreamDetails((prev) => {
                return {
                  ...prev,
                  streamCause: text,
                };
              });
            }}
          />
        </View>

        {/* Upload Thumbnail */}
        {/* implement image upload  */}
        <TouchableOpacity style={styles.inputContainer} onPress={handlePickImage}>
          <TextInput
            style={styles.input}
            placeholder="Upload thumbnail"
            placeholderTextColor="black"
            editable={false}
          />
          <TouchableOpacity style={styles.iconButton}>
            <GalleryImgSvg />
          </TouchableOpacity>
        </TouchableOpacity>

        {/* Toggles */}
        <View style={styles.toggleContainer}>
          <StreamToggleOption
            label="Allow comments"
            value={liveStreamData.allowChat}
            onToggle={(val) => setStreamDetails((prev) => ({ ...prev, allowChat: val }))}
          />
          <StreamToggleOption
            label="Allow gifts"
            value={liveStreamData.allowGifts}
            onToggle={(val) => setStreamDetails((prev) => ({ ...prev, allowGifts: val }))}
          />
          <StreamToggleOption
            label="Private Stream"
            value={liveStreamData.streamType === StreamType.private ? true : false}
            onToggle={(val) => {
              const messageType: StreamType = val ? StreamType.private : StreamType.public;
              setStreamDetails((prev) => ({ ...prev, streamType: messageType }));
            }}
          />
        </View>

        {/* Start Live Button */}
        <TouchableOpacity
          style={styles.startButton}
          onPress={() => {
            handleStartLive();
          }}
        >
          {!showloading && <Text style={styles.startButtonText}>Start live</Text>}
          {showloading && <ActivityIndicator />}
        </TouchableOpacity>
      </View>
    </ModalWrapper>
  );
};

export default GoLiveInfoModal;

const styles = StyleSheet.create({
  container: {},
  title: {
    fontSize: 22,
    fontWeight: '600',
    marginBottom: 24,
    color: 'black',
  },
  input: {
    flex: 1,
    padding: 12,
    borderRadius: 12,

    fontSize: 16,
    color: 'black',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '##808080',
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 12,
  },
  suffix: {
    fontSize: 18,
    color: 'black',
    marginLeft: 8,
  },
  iconButton: {
    padding: 6,
    backgroundColor: '#EDEDED',
    borderRadius: 10,
    marginLeft: 8,
  },
  imageIcon: {
    fontSize: 20,
    color: 'black',
  },
  toggleContainer: {
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  toggleText: {
    fontSize: 16,
    color: 'black',
  },
  startButton: {
    // backgroundColor: '#6A41D9',
    backgroundColor: colors.mainPurple,

    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
  },
  startButtonText: {
    color: 'white', // changed from white to black
    fontSize: 18,
    fontWeight: '600',
  },
});
