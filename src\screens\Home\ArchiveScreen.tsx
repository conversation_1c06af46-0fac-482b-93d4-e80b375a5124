import React, { useRef, useState, useEffect } from 'react';
import { ListRenderItem, StyleSheet, Text, TouchableOpacity, Switch, View } from 'react-native';
import { colors } from '../../theme/colors';
import { navigateTo } from '../../utils/commonFunction';
import { SwipeListView } from 'react-native-swipe-list-view';
import ChatItemCard from './All/ChatItemCard';
import useConversations from '../../hooks/conversations/useConversations';
import { IConversation } from '../../device-storage/realm/schemas/ConversationSchema';
import RenderHiddenItem from './components/RenderHiddenItem';
import { ChatService } from '../../service/ChatService';
import { SCREENS } from '../../navigation/screenNames';
import { deleteChatSpace } from '../../api/Chatspace/chatspace.api';
import { ConversationType } from '../../device-storage/realm/schemas/MessageSchema';
import CommonView from '../../component/CommonView';
import { errorToast } from '../../utils/commonFunction';
import ThreeDotsSVG from '../../assets/svgIcons/ThreeDotsSVG';
import ModalWrapper from '../../component/ModalWrapper';
import { useMe } from '../../hooks/util/useMe';
import { ChatScreenParams } from './Chats/ChatSpecificScreen';

const getChatItemType = (type: ConversationType): 'chat' | 'channel' | 'group' => {
  return type === 'p2p' ? 'chat' : type;
};

const ArchiveScreen: React.FC = () => {
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const [isSelectionEnabled, setIsSelectionEnabled] = useState<boolean>(false);
  const swipeListRef = useRef<any>(undefined);
  const { archivedConversations } = useConversations();
  const { userPreferencesState } = useMe();

  const archiveAllChats = userPreferencesState.userPreferences?.chats?.archiveChats ?? false;
  const handleArchiveAllToggle = () => {
    const newValue = !archiveAllChats;
    // userPreferencesState.updatePreferences('chats', { archiveChats: newValue });
    // if (newValue) {
    //   ChatService.toggleArchiveAllChats(true);
    // }
  };

  const [archiveModal, setArchiveModal] = useState(false);

  useEffect(() => {
    setIsSelectionEnabled(false);
    setSelectedUsers([]);
  }, []);

  const updateSelection = (newSelectedUsers: any[]) => {
    setSelectedUsers(newSelectedUsers);
  };

  const closeRow = (rowMap: any, rowKey: any) => {
    if (rowMap[rowKey]) {
      rowMap[rowKey].closeRow();
    }
  };

  const handleDeleteChat = async (item: IConversation, rowMap: any) => {
    closeRow(rowMap, item.id);
    try {
      const isChannelOwner = item.type === 'channel' && item.role === 'owner';
      const isGroupOwner = item.type === 'group' && item.role === 'owner';
      if (isChannelOwner || isGroupOwner) {
        await deleteChatSpace(item.id);
      }
      await ChatService.deleteConversation(item.id);
    } catch (error) {
      errorToast('Error while deleting chat');
    }
  };

  const handleOpenModal = () => {
    setArchiveModal(true);
  };

  const handleCloseModal = () => {
    setArchiveModal(false);
  };

  const renderHiddenItem = (list: { item: any; index: any }, rowMap: any) => {
    const { item } = list;
    return (
      <RenderHiddenItem
        item={item}
        type="Archive"
        onPressDeleteChat={() => handleDeleteChat(item, rowMap)}
        onPressIcon={(itemType) => onPressCardMenuItems(rowMap, item, itemType)}
      />
    );
  };

  const onPressCardMenuItems = (rowMap: any, item: any, type: string) => {
    closeRow(rowMap, item?.id);
    if (type === 'ARCHIVE') {
      ChatService.toggleConversationArchive(item?.id);
    } else if (type === 'MUTE') {
      ChatService.toggleConversationMute(item?.id, 'always');
    } else if (type === 'CLEAR') {
      ChatService.clearChat(item?.id);
    } else if (type === 'LOCK') {
      // Implement lock if needed
    } else {
      ChatService.toggleConversationPin(item?.id);
    }
  };

  const onLongSelect = (user: any) => {
    if (selectedUsers?.length > 0) {
      onSelect(user);
    } else {
      setIsSelectionEnabled(true);
      updateSelection([user]);
    }
  };

  const onSelect = (user: IConversation) => {
    if (ConversationType.CHANNEL && user.isDeleted) {
      return;
    }
    const isUserExist = selectedUsers?.some((prev: any) => prev?.id == user?.id);
    if (isSelectionEnabled) {
      let newSelected;
      if (isUserExist) {
        if (selectedUsers.length === 1) {
          setIsSelectionEnabled(false);
          newSelected = [];
        } else {
          newSelected = selectedUsers.filter((list: any) => list?.id !== user?.id);
          if (swipeListRef.current) {
            swipeListRef.current.closeAllOpenRows();
          }
          navigateTo(SCREENS.ChatSpecificScreen, { user });
        }
      } else {
        newSelected = [...selectedUsers, user];
      }
      updateSelection(newSelected);
    } else {
      if (swipeListRef.current) {
        swipeListRef.current.closeAllOpenRows();
      }
      const conversation = archivedConversations.find((conv: IConversation) => conv.id === user.id);
      if (!conversation) {
        errorToast('Conversation not found');
        return;
      }
      const userData = {
        displayName: user.displayName,
        displayPic: user.displayPic,
        type: user.type,
        id: user.id,
        isActive: user.isActive,
        isDeleted: user.isDeleted,
        conversation: JSON.parse(JSON.stringify(conversation)),
      };
      const convParams: ChatScreenParams = { convId: user.id };
      navigateTo(SCREENS.ChatSpecificScreen, {
        userData,
        data: convParams,
      });
    }
  };

  const renderItem: ListRenderItem<IConversation> = ({ item }) => {
    const isSelected = selectedUsers?.some((list: any) => item?.id === list?.id);
    return (
      <ChatItemCard
        type={getChatItemType(item.type)}
        item={item}
        selectedUser={selectedUsers}
        isSelectionEnabled={isSelectionEnabled}
        onSelect={onSelect}
        onLongSelect={onLongSelect}
        disableSelection={true}
      />
    );
  };

  const [enableToggle, setEnableToggle] = useState(true);

  return (
    <CommonView
      headerTitle="Archived Chats"
      headerColor={colors.mainPurple}
      containerStyle={{ backgroundColor: colors.white, paddingHorizontal: 0 }}
      renderRight={() => (
        <TouchableOpacity
          onPress={handleOpenModal}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <ThreeDotsSVG color={colors.white} size={19} />
        </TouchableOpacity>
      )}
    >
      <SwipeListView
        ref={swipeListRef}
        data={archivedConversations}
        useFlatList={true}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderItem}
        renderHiddenItem={renderHiddenItem}
        rightOpenValue={-120}
        disableRightSwipe
        showsVerticalScrollIndicator={false}
      />
      <ModalWrapper isVisible={archiveModal} onCloseModal={handleCloseModal}>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>Archive settings</Text>
          <View style={styles.settingRow}>
            <View style={styles.textContainer}>
              <Text style={styles.label}>Keep chats archived</Text>
              <Text style={styles.description}>
                You don't receive notifications for new messages if it is enabled
              </Text>
            </View>
            <Switch
              trackColor={{ false: '#E9E9EA', true: '#7B50D7' }}
              thumbColor={'#FFFFFF'}
              ios_backgroundColor="#E9E9EA"
              onValueChange={() => {
                setEnableToggle((prev) => !prev);
              }}
              value={enableToggle}
            />
          </View>
        </View>
      </ModalWrapper>
    </CommonView>
  );
};

export default ArchiveScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    flex: 1,
    marginBottom: 60,
  },
  contentContainer: {
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.gray_80,
    marginBottom: 14,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.black_23,
  },
  description: {
    fontSize: 15,
    fontWeight: '400',
    color: colors.gray_80,
    marginTop: 4,
    lineHeight: 18,
  },
});
