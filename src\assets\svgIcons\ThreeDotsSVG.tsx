import React from "react"
import Svg, { Circle, SvgProps } from "react-native-svg"

interface IconProps extends SvgProps {
    size?: number;
    color?: string;

}



const ThreeDotsSVG: React.FC<IconProps> = ({ size = 20, color = "#fff", ...restProps }) => {

    const width = (size * 6) / 20 // maintain original width:height ratio

    return (
        <Svg
            width={width}
            height={size}
            viewBox="0 0 6 20"
            fill="none"
            {...restProps}
        >
            <Circle cx={3} cy={2.22} r={2.22} fill={color} />
            <Circle cx={3} cy={10} r={2.22} fill={color} />
            <Circle cx={3} cy={17.78} r={2.22} fill={color} />
        </Svg>
    )
}

export default ThreeDotsSVG
