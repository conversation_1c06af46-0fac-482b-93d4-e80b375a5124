import * as React from 'react';
import Svg, { Path, G, Mask, SvgProps } from 'react-native-svg';

const ReferSVG: React.FC<SvgProps> = (props) => (
  <Svg width={22} height={22} viewBox="0 0 22 22" fill="none" {...props}>
    <Mask id="mask0" maskUnits="userSpaceOnUse" x={0} y={0} width={22} height={22}>
      <Path d="M22 0H0V22H22V0Z" fill="#fff" />
    </Mask>
    <G mask="url(#mask0)">
      <Mask id="mask1" maskUnits="userSpaceOnUse" x={0} y={0} width={22} height={22}>
        <Path d="M0 0H22V22H0V0Z" fill="#fff" />
      </Mask>
      <G mask="url(#mask1)">
        <Path d="M5.5 6.35938C5.5 3.32183 7.96245 0.859376 11 0.859376C14.0375 0.859376 16.5 3.32183 16.5 6.35938C16.5 9.39692 14.0375 11.8594 11 11.8594C7.96245 11.8594 5.5 9.39692 5.5 6.35938Z" stroke="#232323" strokeWidth={1.71875} strokeMiterlimit={10} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M0.859375 22C0.859375 16.3995 5.3995 11.8594 11 11.8594" stroke="#232323" strokeWidth={1.71875} strokeMiterlimit={10} strokeLinecap="round" strokeLinejoin="round" />
        <Path d="M20.0234 15.7695H17.2745C14.7369 15.7695 12.6793 17.8255 12.6772 20.363L12.6758 22" stroke="#232323" strokeWidth={1.71875} strokeMiterlimit={10} strokeLinecap="round" />
        <Path d="M17.3594 12.375L20.7969 15.8125L17.3594 19.25" stroke="#232323" strokeWidth={1.71875} strokeMiterlimit={10} />
      </G>
    </G>
  </Svg>
);

export default ReferSVG; 