import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Switch,
} from 'react-native';
import CommonView from '../../../component/CommonView';
import Input from '../../../component/Input';
import ButtonPurple from '../../../component/ButtonPurple';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import { IMAGES } from '../../../assets/Images';
import { useTranslation } from 'react-i18next';
import ImageCropPicker from 'react-native-image-crop-picker';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import EditImageSVG from '../../../assets/svgIcons/EditImageSVG';
import { ChatService } from '../../../service/ChatService';
import { IChatScreenProps } from '../Chats/ChatSpecificScreen';
import { ICreateGroupForm, PickedImage } from './CreateGroupScreen';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { CreateGroupSchema } from '../../../yup/formSchemas/CreateGroupSchema';
import Toast from 'react-native-toast-message';
import { updateChatspaceInfo } from '../../../api/Chatspace/chatspace.api';
import useConversationInfo from '../../../device-storage/realm/hooks/useConversationInfo';
import { ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';

type EditGroupInfoParams = {
  EditGroupInfo: {
    userDetails: string;
  };
};

export interface IEditGroupForm {
  groupName: string;
  groupDescription: string;
  isPrivate?: boolean;
  imageUrl?: PickedImage | null;
}

const EditGroupInfo = () => {
  const route = useRoute<RouteProp<EditGroupInfoParams, 'EditGroupInfo'>>();
  const chatSpaceId = route.params.userDetails;

  const { t } = useTranslation();
  const navigation = useNavigation();

  const { conversationInfo } = useConversationInfo(chatSpaceId);

  const groupDescription = conversationInfo?.type === ConversationType.GROUP ? conversationInfo?.bio : '';
  const groupPrivate = conversationInfo?.type === ConversationType.GROUP ? conversationInfo?.isPrivate : false;

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<ICreateGroupForm>({
    resolver: yupResolver(CreateGroupSchema, { context: { isCreate: false } }),
    defaultValues: {
      groupName: conversationInfo?.displayName || '',
      groupDescription: groupDescription,
      isPrivate: groupPrivate,
      imageUrl: conversationInfo?.displayPic
        ? { uri: conversationInfo.displayPic, type: 'image/jpeg', name: 'group.jpg' }
        : null,
    },
  });

  const groupImage = watch('imageUrl');

  const onPressGallery = () => {
    ImageCropPicker.openPicker({
      cropping: true,
    }).then((image) => {
      const updatedImg: PickedImage = {
        uri: image.path,
        name: image.filename ?? image.path?.split('/')?.pop() ?? 'photo.jpg',
        type: image.mime,
      };
      setValue('imageUrl', updatedImg);
    });
  };

  const onSubmit: SubmitHandler<IEditGroupForm> = async (data: IEditGroupForm) => {
    try {
      const payload: any = {};

      if (data.groupName !== conversationInfo?.displayName) {
        payload.name = data.groupName;
      }
      if (data.groupDescription !== groupDescription) {
        payload.description = data.groupDescription;
      }
      if (data.isPrivate !== groupPrivate) {
        payload.isPrivate = data.isPrivate;
      }

      const isNewImage =
        groupImage &&
        typeof groupImage !== 'string' &&
        groupImage.uri !== conversationInfo?.displayPic;
      if (isNewImage) {
        payload.file = {
          uri: groupImage.uri,
          type: groupImage.type,
          name: groupImage.name,
        };
      }

      if (Object.keys(payload).length > 0) {
        const result = await updateChatspaceInfo(chatSpaceId, payload);
        if (result?.systemMessage) {
          ChatService.updateChatSpace(result?.systemMessage?.eventPayload);
          ChatService.onIncomingMessage(result?.systemMessage);
        }
      } else {
        console.log('No changes to submit.');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Failed to update group info:', error);
      Toast.show({
        type: 'error',
        text1: 'Failed to update group. Please try again.',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{ flex: 1 }}
    >
      <CommonView headerTitle={t('Edit group')}>
        <View style={styles.container}>
          {/* Group Image */}
          <View style={styles.imageContainer}>
            <Image
              source={groupImage?.uri ? { uri: groupImage.uri } : IMAGES.profile_image}
              style={styles.groupImage}
            />
            <TouchableOpacity onPress={onPressGallery} style={styles.galleryButton}>
              <EditImageSVG size={22} color={colors.gray_80} />
            </TouchableOpacity>
          </View>

          {/* Group Name */}
          <Controller
            control={control}
            name="groupName"
            render={({ field: { onChange, value } }) => (
              <Input
                title={t('Change group name')}
                value={value}
                onChangeText={onChange}
                inputContainer={{ backgroundColor: '#F9F9F9' }}
              />
            )}
          />
          {errors.groupName && <Text style={styles.errorText}>{errors.groupName.message}</Text>}

          {/* Group Description */}
          <Controller
            control={control}
            name="groupDescription"
            render={({ field: { onChange, value } }) => (
              <Input
                title={t('Change group description')}
                value={value}
                onChangeText={onChange}
                multiline
                inputContainer={{ height: hp(15), alignItems: 'flex-start' }}
              />
            )}
          />
          {errors.groupDescription && (
            <Text style={styles.errorText}>{errors.groupDescription.message}</Text>
          )}

          <View style={styles.container2}>
            <Text style={styles.label}>Group Private</Text>
            <Controller
              control={control}
              name="isPrivate"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                  trackColor={{ false: '#E9E9EA', true: colors.mainPurple }}
                  thumbColor={colors.white}
                />
              )}
            />
          </View>

          {/* Submit Button */}
          <ButtonPurple
            title={t('Save changes')}
            onPress={handleSubmit(onSubmit)}
            isLoading={isSubmitting}
            disabled={isSubmitting}
          />
        </View>
      </CommonView>
    </KeyboardAvoidingView>
  );
};

export default EditGroupInfo;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 24,
  },
  imageContainer: {
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  groupImage: {
    height: hp(15),
    width: hp(15),
    borderRadius: hp(15 / 2),
  },
  galleryButton: {
    position: 'absolute',
    bottom: -10,
    alignSelf: 'center',
    backgroundColor: colors.white,
    borderRadius: 100,
    padding: 6,
    elevation: 4,
  },
  galleryIcon: {
    height: hp(3),
    width: hp(3),
    tintColor: colors.gray_86,
  },
  container2: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  errorText: {
    color: colors.red_ff4444,
    fontSize: 12,
  },
});