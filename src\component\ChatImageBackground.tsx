import React from 'react';
import { ImageBackground, View, StyleProp, ViewStyle, StyleSheet } from 'react-native';

type Props = {
  color?: string;
  url?: string;
  defaultImage: any;
  opacity?: number;
  style?: StyleProp<ViewStyle>;
  children: React.ReactNode;
};

const ChatImageBackground: React.FC<Props> = ({
  color,
  url,
  defaultImage,
  opacity,
  style,
  children,
}) => {
  if (color) {
    return (
      <View style={[{ flex: 1 }, style]}>
        <View
          style={[
            StyleSheet.absoluteFill,
            { backgroundColor: color, opacity: opacity !== undefined ? opacity : 1 },
          ]}
          pointerEvents="none"
        />
        <View style={{ flex: 1 }}>{children}</View>
      </View>
    );
  }
  const source = url ? { uri: url } : defaultImage;
  return (
    <ImageBackground
      style={[{ flex: 1 }, style]}
      source={source}
      resizeMode="cover"
      imageStyle={opacity !== undefined ? { opacity } : undefined}
    >
      <View style={{ flex: 1 }}>{children}</View>
    </ImageBackground>
  );
};

export default ChatImageBackground;
