import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface ContactAvatarSVGProps {
  size?: number;
  color?: string;
}

const ContactTabSVG: React.FC<ContactAvatarSVGProps> = ({
  size = 23,
  color = "gray",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 23 23"
      fill="none"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.806 0h1.248c2.087 0 3.73 0 5.015.163 1.315.167 2.374.517 3.213 1.31.845.798 1.225 1.816 1.404 3.08.174 1.22.174 2.78.174 4.744v.063a.802.802 0 01-1.604 0c0-2.04-.002-3.486-.158-4.582-.152-1.067-.436-1.684-.918-2.14-.488-.46-1.16-.737-2.313-.883-1.172-.149-2.713-.15-4.87-.15H9.863c-2.157 0-3.7.001-4.87.15-1.153.146-1.826.423-2.314.884-.481.455-.766 1.072-.917 2.14-.156 1.095-.158 2.54-.158 4.581v4.28c0 2.04.002 3.486.158 4.582.151 1.067.436 1.684.917 2.139.488.461 1.16.738 2.313.884 1.172.149 2.714.15 4.87.15a.802.802 0 010 1.605h-.057c-2.087 0-3.73 0-5.015-.163-1.314-.167-2.374-.517-3.213-1.31-.845-.798-1.224-1.816-1.404-3.08C0 17.228 0 15.668 0 13.704V9.297c0-1.963 0-3.523.174-4.745.18-1.263.56-2.281 1.404-3.08C2.418.68 3.477.33 4.791.163 6.076 0 7.72 0 9.806 0z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.349 6.152c0-.443.359-.802.802-.802h8.558a.802.802 0 010 1.604H6.151a.802.802 0 01-.802-.802zM5.349 11.5c0-.444.359-.803.802-.803h6.418a.802.802 0 110 1.605H6.151a.802.802 0 01-.802-.802zM17.919 13.372c-.766 0-1.337.592-1.337 1.261 0 .67.57 1.261 1.337 1.261.766 0 1.337-.592 1.337-1.26 0-.67-.57-1.262-1.337-1.262zm-2.942 1.261c0-1.61 1.345-2.865 2.942-2.865 1.597 0 2.942 1.255 2.942 2.865 0 1.61-1.345 2.866-2.942 2.866-1.597 0-2.942-1.256-2.942-2.866zm6.548 8.002c-1.679-2.587-5.551-2.483-7.218.009a.802.802 0 01-1.334-.892c2.266-3.388 7.552-3.606 9.898.009a.802.802 0 01-1.346.874z"
        fill={color}
      />
    </Svg>
  );
};

export default ContactTabSVG;
