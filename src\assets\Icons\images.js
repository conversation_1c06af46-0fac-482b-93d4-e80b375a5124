const IMAGES = {
  introImage: require("./introImage.png"), // IMAGES.introImage. (no need to change)
  rightArrow: require("./rightArrow.png"), // IMAGES.rightArrow -done
  userInput: require("./userInput.png"), // IMAGES.userInput -- done
  bottomArrow: require("./bottomArrow.png"), // IMAGES.bottomArrow -done
  earphone: require("./earphone.png"), // IMAGES.earphone  -- EarPhoneSVG.tsx (In CallButtons.tsx)
  wrong: require("./wrong.png"), // IMAGES.wrong.  --(xmark icon in SetUsernameScreen.tsx)
  right: require("./right.png"), // IMAGES.right  --- (check icon in SetUsernameScreen.tsx & ProfileTab.tsx)
  updateImage: require("./updateImage.png"), // IMAGES.updateImage.( EditImageSVG.tsx)
  calender: require("./calender.png"), // IMAGES.calenders (used in SetProfileScreen.tsx)
  tabBG: require("./tabBG.png"), // IMAGES.tabBG. (no need to change)
  call: require("./call.png"), // IMAGES.call (CallTabSVG.tsx) -- done
  contacts: require("./contacts.png"), // IMAGES.contacts --- done
  homeTab: require("./homeTab.png"), // IMAGES.homeTab --- done
  chatIcon: require("./chatIcon.png"), // IMAGES.chatIcon -- done (chatSVG.tsx)
  userImage: require("./userImage.png"), // IMAGES.userImage (replaced with ContactAvatarSVG)
  moreMenu: require("./moreMenu.png"), // IMAGES.moreMenu.  (ThreeDots SVG)
  searchIcon: require("./searchIcon.png"), // IMAGES.searchIcon. (SearchSVG.tsx)
  deleteIcon: require("./deleteIcon.png"), // IMAGES.deleteIcon Done (DeleteSVG.tsx)
  Qr_Code: require("./Qr_Code.png"), //  ScannerSVG.tsx ---done
  view: require("./view.png"), // IMAGES.view. ion icon (used in Input.tsx)
  hide: require("./hide.png"), // IMAGES.hide ion icon (used in Input.tsx)
  check_single_tick: require("./check_single_tick.png"), // IMAGES.check_single_tick (SingleTickSVG.tsx)---done
  forward: require("./forward.png"), // IMAGES.forward --(return-up-forward-outline) --done
  sendButton: require("./sendButton.png"), // IMAGES.sendButton (sendSVG.tsx)---done
  trash: require("./trash.png"), // IMAGES.trash  (DeleteSVG.tsx) ---- (StoryEditModal.tsx)
  editSend: require("./editSend.png"), // IMAGES.editSend ---(only in storyeditmodal.tsx & ProfileScreen.tsx)
  checkPurple: require("./checkPurple.png"), // IMAGES.checkPurple (done pending in savepostscreen.tsx)
  play: require("./play.png"), // IMAGES.play (not used)
  pause: require("./pause.png"), // IMAGES.pause  (not used)
  UserPin: require("./UserPin.png"), // IMAGES.UserPin --done
  FilePin: require("./FilePin.png"), // IMAGES.FilePin  ---- done
  cropImage: require("./cropImage.png"), // IMAGES.cropImage (used in EditorIconList.tsx) no need
  HDImage: require("./HDImage.png"), // IMAGES.HDImage (used in EditorIconList.tsx)no need
  TImage: require("./TImage.png"), // IMAGES.TImage (used in EditorIconList.tsx) no need
  closeImage: require("./closeImage.png"), // IMAGES.closeImage --- done
  drawImage: require("./drawImage.png"), // IMAGES.drawImage (used in EditorIconList.tsx) no need
  editImage: require("./editImage.png"), // IMAGES.editImage (used in EditorIconList.tsx) no need
  accountSetting: require("./accountSetting.png"), // IMAGES.accountSetting --- done
  chatSetting: require("./chatSetting.png"), // IMAGES.chatSetting ---done
  notificationSetting: require("./notificationSetting.png"), // IMAGES.notificationSetting ---- done
  languageSetting: require("./languageSetting.png"), // IMAGES.languageSetting ---done
  privacySetting: require("./privacySetting.png"), // IMAGES.privacySetting ----done
  favouriteSetting: require("./favouriteSetting.png"), // IMAGES.favouriteSetting --done
  inArrow: require("./inArrow.png"), // IMAGES.inArrow. --done
  imageWallpaperPick: require("./imageWallpaperPick.png"), // IMAGES.imageWallpaperPick --done
  capture: require("./capture.png"), // IMAGES.capture  (used in CaptureButton.tsx --unused) --remove image
  maskImage: require("./maskImage.png"), // IMAGES.maskImage (used in FilterModal.tsx --unused) --remove image
  play_icon: require("./play_icon.png"), // IMAGES.play_icon (used in FilterModal.tsx --unused) --remove image
  pause_icon: require("./pause_icon.png"), // IMAGES.pause_icon (used in FilterModal.tsx --unused) --remove image
  time: require("./time.png"), // IMAGES.time. ---(in storypostmodal.tsx & storyeditmodal.tsx)
  lock_profile: require("./lock_profile.png"), // IMAGES.lock_profile ---(only in storypostmodal.tsx)---done (remove)
  messageTimer: require("./messageTimer.png"), // IMAGES.messageTimer ----done
  menuIcon: require("./menuIcon.png"), // IMAGES.menuIcon ----done
  f_image: require("./f_image.png"), // IMAGES.f_image. (used in scan contact as bgimage)
  post_icon: require("./post_icon.png"), // IMAGES.post_icon  ---(only in storyeditmodal.tsx)---done (remove)
  copyCode: require("./copyCode.png"), // IMAGES.copyCode ---- done
  gallery_icon: require("./gallery_icon.png"), // IMAGES.gallery_icon ----done
  gallery_gray: require("./GalleryGray.png"), // IMAGES.gallery_gray ----(setprofilescreen.tsx)
  down_arrow: require("./down_arrow.png"), // IMAGES.down_arrow ----not used
  chat_lock: require("./chat_lock.png"), // IMAGES.chat_lock.  ----done
  translate_msg: require("./translate_msg.png"), // IMAGES.translate_msg.  ----done
  pin: require("./pin.png"), // IMAGES.pin.  ---done
  menu_icon: require("./menu_icon.png"), // IMAGES.menu_icon. ----done
  profile_image: require("./profile_image.png"), // IMAGES.profile_image ---no need to change
  block: require("./block.png"), // IMAGES.block. ---done (except optionmodal.tsx)
  user_dp: require("./user_dp.png"), //  IMAGES.user_dp --done
  plus_icon: require("./plus_icon.png"), // IMAGES.plus_icon  ---done (except optionmodal.tsx)
  channel: require("./channel.png"), // IMAGES.channel. ----no need to change
  live_stream: require("./live_stream.png"), // IMAGES.live_stream (In EaringView.tsx &liveStreamModal.tsx)
  save: require("./save.png"), // IMAGES.save. ------done
  copy_link: require("./copy_link.png"), // IMAGES.copy_link -----done
  dollar_icon: require("./dollar_icon.png"), // IMAGES.dollar_icon --(in LiveScheduleScreen.tsx)
  share: require("./share.png"), // IMAGES.share. ---done
  ic_unSave: require("./ic_unSave.png"), // IMAGES.ic_unSave. (only used in savepostscreen)
  send: require("./send.png"), // IMAGES.send ----done
  group_icon: require("./group_icon.png"), // IMAGES.group_icon --no need to change.
  report: require("./report.png"), // IMAGES.report ---(OptionModal.tsx)
  videonew: require("./videonew.png"), // IMAGES.videonew ---(ongoingcall.tsx)
  mute: require("./mute.png"), // IMAGES.mute ---(In CallMembertile.tsx)
  mute_fill: require("./mute_fill.png"), // IMAGES.mute_fill  ---( In CallButtons)
  endcall: require("./endcall.png"), // IMAGES.endcall --CallButton.tsx & CallMemberModal.tsx
  chatList: require("./chatList.png"), // IMAGES.chatList ( In CallButtons)
  changeCamera: require("./changeCamera.png"), // IMAGES.changeCamera( In CallButtons)
  audio: require("./audio.png"), // IMAGES.audio ---done( In CallButtons)
  audio_selected: require("./audio_selected.png"), // IMAGES.audio_selected( In CallButtons)
  addUser: require("./addUser.png"), // IMAGES.addUser (ongoingcall.tsx)
  addCall: require("./addCall.png"), // IMAGES.addCall ( In CallButtons)
  more: require("./more.png"), // IMAGES.more  ---done
  addVideo: require("./addVideo.png"), // IMAGES.addVideo ( In CallButtons)
  callBg: require("./callBg.png"), // IMAGES.callBg ---done (pending in GroupCallScreen.tsx)
  videocall: require("./videocall.png"), // IMAGES.videocall --done
  redArrow: require("./redArrow.png"), // IMAGES.redArrow --done
  downarrow: require("./downarrow.png"), // IMAGES.downarrow ----done
  callIcon: require("./callIcon.png"), // IMAGES.callIcon ---not used
  addNewCall: require("./addNewCall.png"), // IMAGES.addNewCall ---done
  image1: require("./image1.png"), // IMAGES.image1 --not used
  image2: require("./image2.png"), // IMAGES.image2 ---not used
  rainingTeddy: require("./RainingTedday.png"), // IMAGES.rainingTeddy --- used in chatscreen(no need to change)
  cloudLock: require("./CloudLock.png"), // IMAGES.CloudLock. ----no need to change
  warning: require("./warning.png"), // IMAGES.warning (in SetUsernameScreen.tsx)
  callVerify: require("./call_verify.png"), // IMAGES.callVerify  ----MissCallScreen.tsx
  whatsApp: require("./whatsapp.png"), // IMAGES.whatsApp ---In misscall and scancontact.tsx(no need to change)
  message: require("./message.png"), // IMAGES.message ----MissCallScreen.tsx
  posterImg: require("./posterimg.png"), // IMAGES.posterImg. ----MissCallScreen.tsx
  blocked: require("./blocked.png"), // IMAGES.blocked ----MissCallScreen.tsx
  unSelect: require("./unselect.png"), // IMAGES.unSelect ----NewCallScreen.tsx
  selected: require("./selected.png"), // IMAGES.selected ----NewCallScreen.tsx
  videoImageWhite: require("./videoImageWhite.png"), // IMAGES.videoImageWhite.  ----NewCallScreen.tsx
  callImageWhite: require("./callImageWhite.png"), // IMAGES.callImageWhite  ----NewCallScreen.tsx
  upload: require("./upload.png"), // IMAGES.upload ---ScheduleCallScreen.tsx
  translation: require("./translation.png"), // IMAGES.translation. ----OptionsModal.tsx
  shareScreen: require("./shareScreen.png"), // IMAGES.shareScreen ----OptionsModal.tsx
  queenSymbol: require("./queensymbol.png"), // IMAGES.queenSymbol ---no need to change
  addPeople: require("./AddPeople.png"), // IMAGES.addPeople  ----done
  unselected_white: require("./unselected_white.png"), // IMAGES.unselected_white(AddPeopleModal.tsx,LanguageModal.tsx)
  selected_white: require("./selected_white.png"), // IMAGES.selected_white (AddPeopleModal.tsx,LanguageModal.tsx)
  search_white: require("./search_white.png"), // IMAGES.search_white  ----done
  mute_white: require("./mute_white.png"), // IMAGES.mute_white (unmute)(CallButton.tsx,CallMembersModal.tsx,CallMembertile.tsx)

  //=== unused files ====

  logoChat: require("./logoChat.png"),
  passwordInput: require("./passwordInput.png"),
  otpInput: require("./otpInput.png"),
  successInput: require("./successInput.png"),
  usernameInput: require("./usernameInput.png"),
  callingInput: require("./callingInput.png"),
  imagePlaceholder: require("./imagePlaceholder.png"),
  storiesIcon: require("./storiesIcon.png"),
  liveIcon: require("./liveIcon.png"),
  location: require("./location.png"),
  forgetPassInput: require("./forgetPassInput.png"),
  resetPassInput: require("./resetPassInput.png"),
  addStory: require("./addStory.png"),
  Videocamera: require("./Videocamera.png"),
  Phone: require("./Phone.png"),
  chatInputPin: require("./chatInputPin.png"),
  chatInputAdd: require("./chatInputAdd.png"),
  microphone: require("./microphone.png"),
  blurView: require("./blurView.png"),
  emoji: require("./emoji.png"),
  clock: require("./clock.png"),
  checkRead: require("./checkRead.png"),
  checkRead2: require("./CheckRead2.png"),
  deleteMessage: require("./deleteMessage.png"),
  pinMessage: require("./pinMessage.png"),
  addReaction: require("./addReaction.png"),
  copyMessage: require("./copyMessage.png"),
  editMessage: require("./editMessage.png"),
  replyMessage: require("./replyMessage.png"),
  silentMessage: require("./silentMessage.png"),
  sendNow: require("./sendNow.png"),
  recording: require("./recording.png"),
  retake: require("./retake.png"),
  pauseBold: require("./pauseBold.png"),
  MicrophonePin: require("./MicrophonePin.png"),
  locationPin: require("./locationPin.png"),
  GalleryPin: require("./GalleryPin.png"),
  CameraPin: require("./CameraPin.png"),
  wallpaper: require("./wallpaper.png"),
  dropper: require("./dropper.png"),
  opacity: require("./opacity.png"),
  addEmailSetting: require("./addEmailSetting.png"),
  nameColorSetting: require("./nameColorSetting.png"),
  voiceSetting: require("./voiceSetting.png"),
  switch: require("./switch.png"),
  back_Icon: require("./back_Icon.png"),
  flash_of: require("./flash_of.png"),
  flash_on: require("./flash_on.png"),
  filter_pen: require("./filter_pen.png"),
  deleteWhite: require("./deleteWhite.png"),
  more_icon: require("./more_icon.png"),
  bg_story: require("./bg_story.png"),
  two_up_arrow: require("./two_up_arrow.png"),
  heart: require("./heart.png"),
  heart_fill: require("./heart_fill.png"),
  bg_1: require("./bg_1.png"),
  bg_2: require("./bg_2.png"),
  uTurn: require("./uTurn.png"),
  file_Icon: require("./file_Icon.png"),
  archive: require("./archive.png"),
  clear_chat: require("./clear_chat.png"),
  folder: require("./folder.png"),
  unRead: require("./unRead.png"),
  drafts: require("./drafts.png"),
  unArchived: require("./unArchived.png"),
  backup: require("./backup.png"),
  clear_all_chat: require("./clear_all_chat.png"),
  app_lock: require("./app_lock.png"),
  chatLock: require("./chatLock.png"),
  location_1: require("./location_1.png"),
  location_2: require("./location_2.png"),
  slider_thumb: require("./slider_thumb.png"),
  slider_thumb_2: require("./slider_thumb_2.png"),
  setting: require("./setting.png"),
  current_location: require("./current_location.png"),
  profile_bg: require("./profile_bg.png"),
  filter_icon: require("./filter_icon.png"),
  tick_mark: require("./tick_mark.png"),
  ic_video: require("./ic_video.png"),
  ic_comment: require("./ic_comment.png"),
  tick: require("./tick.png"),
  like: require("./like.png"),
  sound: require("./sound.png"),
  line: require("./line.png"),
  musicIcon: require("./musicIcon.png"),
  currentLocation: require("./currentLocation.png"),
  layout: require("./layout.png"),
  addImageCall: require("./Add.png"),
  languageSelected: require("./languageSelected.png"),
  languageUnSelected: require("./languageUnselected.png"),
  messageWhile: require("./message-while.png"),
  soundWhite: require("./sound_white.png"),
  soundBlack: require("./sound_black.png"),
  onCall: require("./onCall.png"),
  videoCallBlack: require("./VideoCallBlack.png"),
  Options: require("./Options.png"),
  backArrow: require("./backArrow.png"),
  crownBronze: require("./crown_bronze.png"),
  Subtract: require("./Subtract.png"),
  standard: require("./Standard.png"),
  gold: require("./Gold.png"),
  silver: require("./Silver.png"),
  record: require("./record.png"),
  changeBackground: require("./changeBackground.png"),
  Recording_black: require("./Recording_black.png"),
  SpeakerOn: require("./Speaker.png"),
  nature: require("./lonely-young-woman-looking-through-concrete-window.webp"),
};

// 107 unused + 68 used
