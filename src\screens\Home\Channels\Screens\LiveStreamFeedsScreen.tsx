import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { FeatherIcons, EntypoIcons, MaterialIcons } from '../../../../utils/vectorIcons';
// import { FlatList } from 'react-native-gesture-handler';
import { hp } from '../../../../theme/fonts';
import { colors } from '../../../../theme/colors';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { navigationRef } from '../../../../navigation/RootContainer';
import HorizontalListView from '../../../../component/HorizontalListView';
import LiveStreamIconSvg from '../../../../assets/svgIcons/LiveStreamIconSvg';
import StreamCard from '../liveStreams/StreamFeedItemCard';
import { getLiveStreamFeed } from '../../../../api/Chatspace/chatspace.api';
import useConversations from '../../../../hooks/conversations/useConversations';
import { SCREENS } from '../../../../navigation/screenNames';
import { navigateTo } from '../../../../utils/commonFunction';

const ScreenWidth = Dimensions.get('window').width;

export interface StreamData {
  _id: string;
  totalLikes: number;
  totalShares: number;
  targetAmount: number;
  thumbnail: string;
  chatspaceId: string;
  views: number;
  chatspace: {
    name: string;
    displayPic: string;
  };
}

const LiveStreamsScreen: React.FC = () => {
  const { conversations } = useConversations();
  const followedChannelStreamCache = useRef<StreamData[]>([]);
  const [liveStreamFeed, setLiveStreamFeed] = React.useState<StreamData[]>([]);
  const [filteredFeed, setFilteredFeed] = useState<StreamData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const limit = useRef<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const topTabs = ['For you', 'Following', 'Favourites'];
  const [currentSelectedTab, setCurrentSelectedTab] = useState(topTabs[0]);

  async function fetchLiveStreamFeed(page: number = 1, limit: number = 10) {
    try {
      setLoading(true);
      const { results, pagination } = await getLiveStreamFeed({ page, limit });
      setLiveStreamFeed((prev) => (page === 1 ? results : [...prev, ...results]));
      setFilteredFeed((prev) => (page === 1 ? results : [...prev, ...results]));

      setTotalPages(pagination.totalPages);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch live stream feed:', error);
      setLoading(false);
    }
  }

  async function fetchFollowedChannelStreams() {
    try {
      const followedChannels = conversations.filter((c) => c.role === 'member');
      const followedChannelIds = followedChannels
        .map((c) => c.chatSpaceId)
        .filter((item) => {
          return item !== undefined;
        });

      const followedStreams = await getLiveStreamFeed({ chatSpaceIds: followedChannelIds });

      followedChannelStreamCache.current = followedStreams.results;
    } catch (error) {
      console.error('Failed to fetch followed channel streams:', error);
    }
  }

  useEffect(() => {
    fetchLiveStreamFeed(page, limit.current);
    fetchFollowedChannelStreams();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#7c3aed" barStyle="light-content" />
      <LiveStreamScreenHeader />

      <View style={styles.tabBarContainer}>
        <HorizontalListView
          data={topTabs}
          defaultSelectedItem={topTabs[0]}
          selectedItem={(item: any) => {
            setCurrentSelectedTab(item);
            if (item === 'Following') {
              setFilteredFeed(followedChannelStreamCache.current);
            } else if (item === 'Favourites') {
              setFilteredFeed([]);
            } else {
              setFilteredFeed(liveStreamFeed);
            }
          }}
          contentContainerStyle={{ gap: 10 }}
          bottomLine={false}
          styleViewTwo={true}
        />
        <TouchableOpacity style={styles.searchBox}>
          <FeatherIcons name="search" size={20} color="black" />
        </TouchableOpacity>
      </View>

      <FlatList
        refreshing={loading}
        onRefresh={() => {
          if (currentSelectedTab !== 'For you') return;
          console.log('pull to refresh triggered');
          setPage(1);
          fetchLiveStreamFeed(1, limit.current);
        }}
        data={filteredFeed}
        renderItem={({ item }) => {
          return <StreamCard item={item} />;
        }}
        keyExtractor={(item) => item._id}
        numColumns={2}
        contentContainerStyle={styles.streamGrid}
        showsVerticalScrollIndicator={false}
        onEndReached={() => {
          if (!loading && page < totalPages && currentSelectedTab === 'For you') {
            const nextPage = page + 1;
            setPage(nextPage);
            fetchLiveStreamFeed(nextPage, limit.current);
          }
        }}
        ListFooterComponent={
          <>
            {loading ? (
              <View
                style={{
                  width: ScreenWidth - 20,
                  paddingVertical: 10,
                  alignItems: 'center',
                }}
              >
                <ActivityIndicator size="large" color={colors.mainPurple} />
              </View>
            ) : null}
          </>
        }
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          <View
            style={{
              width: ScreenWidth - 20,
              paddingVertical: 10,
              alignItems: 'center',
            }}
          >
            {!loading && (
              <Text style={{ color: colors.gray_80, fontSize: 30 }}>No streams found</Text>
            )}
          </View>
        }
      />

      <TouchableOpacity
        style={styles.goLiveButton}
        onPress={() => {
          navigateTo(SCREENS.MyChannelsScreen);
        }}
      >
        <LiveStreamIconSvg />
        <Text style={styles.goLiveText}>Go live</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerMainView: {
    paddingHorizontal: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1.5),
    height: hp(13),
    backgroundColor: colors.mainPurple,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 30,
  },
  streamGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',

    paddingTop: 8,
  },
  goLiveButton: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  goLiveText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tabBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  searchBox: {
    borderRadius: 100,
    backgroundColor: colors._DADADA_gray,
    padding: 10,
  },
});

export default LiveStreamsScreen;

function LiveStreamScreenHeader() {
  return (
    <View style={styles.headerMainView}>
      <View style={styles.header}>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
          <TouchableOpacity onPress={() => navigationRef.goBack()}>
            <BackArrowSVG size={30} />
          </TouchableOpacity>
          <Text style={{ fontSize: 20, fontWeight: '500', color: colors.white }}>Live streams</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20 }}>
          <TouchableOpacity>
            <View style={{ transform: [{ scaleX: -1 }] }}>
              <MaterialIcons name="local-fire-department" size={25} color="white" />
            </View>
          </TouchableOpacity>
          <TouchableOpacity>
            <FeatherIcons name="message-square" size={25} color="white" />
          </TouchableOpacity>
          <TouchableOpacity>
            <EntypoIcons name="dots-three-vertical" size={25} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}
