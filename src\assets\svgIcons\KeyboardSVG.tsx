import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const KeyboardSVG: React.FC<IconProps> = ({
    size = 22,
    color = "#232323",
    ...restProps
}) => {
    const height = (17 / 22) * size;

    return (
        <Svg
            width={size}
            height={height}
            viewBox="0 0 22 17"
            fill="none"
            {...restProps}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.238 3.095A3.095 3.095 0 0018.143 0H3.095A3.095 3.095 0 000 3.095V13a3.095 3.095 0 003.095 3.095h15.048A3.095 3.095 0 0021.238 13V3.095zm-1.238 0V13a1.86 1.86 0 01-1.857 1.857H3.095a1.86 1.86 0 01-1.313-.543A1.86 1.86 0 011.238 13V3.095a1.86 1.86 0 011.857-1.857h15.048A1.86 1.86 0 0120 3.095z"
                fill={color}
            />
            <Path
                d="M5.57 6.81a1.238 1.238 0 100-2.477 1.238 1.238 0 000 2.476zM5.57 11.761a1.238 1.238 0 100-2.476 1.238 1.238 0 000 2.476zM10.523 6.81a1.238 1.238 0 100-2.477 1.238 1.238 0 000 2.476zM10.238 11.858a1.238 1.238 0 100-2.476 1.238 1.238 0 000 2.476zM15.476 6.81a1.238 1.238 0 100-2.477 1.238 1.238 0 000 2.476zM15.191 11.858a1.238 1.238 0 100-2.476 1.238 1.238 0 000 2.476z"
                fill={color}
            />
        </Svg>
    );
};

export default KeyboardSVG;

