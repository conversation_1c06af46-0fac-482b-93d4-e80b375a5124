MediaEditor Module Documentation
Prepared for : Client Review
Version : 1.0
Table of Contents
1. Introduction
 
1.1 Purpose
 
1.2 Target Audience
 
1.3 Key Features
 
1.4 Supported Platforms
2. System Architecture
 
2.1 High-Level Architecture
 
2.2 Technology Stack
 
2.3 Data Model
3. Functional Requirements
 
3.1 Live Photo Capture
 
3.2 Media Editing
 
3.3 Audio T rimming
 
3.4 Video Playback
4. Component Logic and Implementation
 
4.1 CameraScreen Component Logic
 
4.2 CanvasEditor Component Logic
 
4.3 FilteredV ideoV iew Component Logic
 
4.4 VidTrimmer Component Logic
 
4.5 GestureHandler Component Logic
 
4.6 AudioT rimmer Module Logic (iOS)
 
4.7 FilteredV ideoV iew Module Logic (Android)
 
4.8 ThumbnailModule Logic (Android)
5. Detailed W orkflows
 
5.1 Initialization W orkflow
 
5.2 Live Photo Capture W orkflow
 
5.3 Media Editing W orkflow
 
5.4 Audio T rimming W orkflow
 
5.5 Video Playback W orkflow
 
5.6 UI Flow
6. Flow Diagrams
 
6.1 Initialization
 
6.2 Live Photo Capture
 
6.3 Media Editing
 
6.4 Audio T rimming
 
6.5 Video Playback
 
6.6 Deletion
7. State Management
 
7.1 Transformation Storage in Image Array
8. Performance Optimization
9. Testing
10. Installation and Setup
 
10.1 Android Setup
 
10.2 iOS Setup
11. Troubleshooting
12. Future Enhancements
13. Conclusion
14. Appendix
 
14.1 Glossary
 
14.2 References
1. Introduction
The MediaEditor  module is a React Native application designed for capturing live photos (JPEGs with motion metadata) and editing media (images, videos, stickers, text,
audio) with customizable filters. All processing occurs on-device, ensuring privacy and performance. Compatible with Android 10 (API 29)  and iOS 9 , it targets content
creators, developers, and businesses needing robust media editing tools.
This documentation, prepared for client review , provides a comprehensive guide to the module’ s functionality , architecture, workflows, and UI flow . It emphasizes components
starting from CameraScreen.js , with detailed logic for GestureHandler.tsx ’s management of position, rotation, and resize transformations stored in the images  state.
The document includes six ASCII flow diagrams, excludes video capture, screenshots, video debugging, and export functionality , and expands on workflows, troubleshooting,
and future enhancements for thorough coverage.
1.1 Purpose
The MediaEditor module enables users to create and enhance media content with professional tools, including live photo capture, media editing (filters, stickers, text,
drawings), audio trimming, and filtered video playback. Its modular design supports seamless integration or standalone use, prioritizing cross-platform compatibility , privacy ,
and an intuitive user experience.
1.2 Target Audience
Content Creators : Individuals producing media for social platforms (e.g., Instagram, T ikTok), seeking user-friendly editing tools.
Developers : Engineers integrating media editing into React Native applications, requiring detailed technical insights.
Clients : Stakeholders evaluating the module’ s capabilities, needing a clear , accessible overview with technical depth.
1.3 Key Features
Live Photo Capture : Captures high-quality JPEGs with motion metadata.
Media Editing : Applies filters, stickers, text, and drawings, with precise positioning, rotation, and resizing via gestures.
Audio T rimming : Adds and trims music tracks, with waveform visualization.
Video Playback : Plays videos with real-time filters (e.g., sepia, grayscale).
Story Management : Creates one story per captured photo or uploaded media; supports multiple stories.
Z-Index Control : Ensures dragged elements or sketches have higher rendering priority .
1.4 Supported Platforms
Android : Version 10 (API 29) and above, using OpenGL ES 2.0 for video rendering.
iOS: Version 9 and above, leveraging A VFoundation for video playback and Core Image for filters.
2. System Architecture
The MediaEditor module processes all operations on-device for privacy and performance. This section outlines its architecture, technology stack, and data model, focusing
on components starting from CameraScreen.js .
2.1 High-Level Architecture
User Interface :
CameraScreen.js : Captures live photos with camera controls and preview .
CanvasEditor.tsx : Renders media with filters, stickers, and text.
FilteredVideoView.tsx : Plays videos with native filters.
VidTrimmer/index.tsx : Trims audio and video clips.
GestureHandler.tsx : Manages positioning, rotation, and resizing.
Rendering Engine :
@shopify/react-native-skia : Handles photo editing with hardware acceleration.
FilteredVideoView : Native video rendering (Android: OpenGL ES, iOS: A VFoundation).
Native Modules :
react-native-vision-camera : Captures live photos.
react-native-track-player : Manages audio playback.
AudioTrimmer  (iOS), AudioTrimModule  (Android): T rims audio.
ThumbnailModule  (Android): Generates video thumbnails.
Storage : react-native-fs  manages local file storage.
State Management : DataProvider  and TimeProvider  ensure consistent state, including transformation data.

Native Components :
Android: FilteredVideoView , ThumbnailModule  (OpenGL ES 2.0).
iOS: FilteredVideoView , AudioTrimmer  (AVFoundation, Core Image).
Development T ools: TypeScript (5.0.4), Jest (29.6.3), ESLint (8.19.0).
Requirements : Node >=18, Y arn 1.22.22.
2.3 Data Model
The MediaItem  object (story) represents a live photo or video:
id: Unique identifier (timestamp + random string).
uri: File path (e.g., file:///path/to/image.jpg ).
type : Media type (e.g., image/jpeg , video/mp4 ).
matrix : Filter transformation matrix.
audio : Audio metadata ( {url, duration} ).
stickers : Array of {id, uri, initialX, initialY, rotate, scale, zIndex} .
texts : Array of {id, text, color, fontFamily, initialX, initialY, rotate, scale, zIndex} .
additionalImages : Array of {id, uri, initialX, initialY, rotate, scale, zIndex} .
path : Drawing paths ( {points, color, strokeWidth} ). Managed by DataProvider , storing transformations via GestureHandler .
3. Functional Requirements
3.1 Live Photo Capture
Captures JPEGs with motion metadata using react-native-vision-camera .
Provides real-time camera preview , flash, and zoom controls.
Stores photos locally with unique IDs.
3.2 Media Editing
Applies filters using Skia-based rendering in CanvasEditor.tsx .
Supports adding, positioning, rotating, and resizing stickers, text, and images via GestureHandler.tsx .
Enables drag-and-drop and deletion of elements.
3.3 Audio Trimming
Adds music tracks using react-native-track-player  in VidTrimmer .
Trims audio with native modules, displaying waveforms.
3.4 Video Playback
Plays videos with real-time filters using FilteredVideoView.tsx .
Synchronizes audio with video playback.
Generates thumbnails for previews.
4. Component Logic and Implementation
4.1 CameraScreen Component Logic
Logic :
Initialization : Requests camera and microphone permissions, sets up react-native-vision-camera .
Camera Preview : Renders live feed with flash and zoom controls.
Photo Capture : Saves JPEGs to RNFS.PicturesDirectoryPath , creating a story .
Media Upload : Allows image/video uploads via react-native-image-picker ; videos < 60s.
Editing : Supports adding stickers, text, audio, and filters; drawing via PaintBrush .
State Management : Updates images  state for stories, using DataProvider  for transformations.
UI: Renders CanvasEditor  for stories, with modal for editing tools and Deletebtn  for deletions.

  
  return (
    <View style={{ flex: 1 }}>  
      {images.length === 0 && (  
        <Camera ref={cameraRef} style={{ flex: 1 }} isActive={true} photo={true} />  
      )}  
      {images.length > 0 && (  
        <CanvasEditor images={images.find(img => img.id === activeStoryIdx)} />  
      )}  
    </View>  
  ); 
}; 
Explanation : CameraScreen  initializes the camera, captures photos, and uploads media, creating stories in the images  state. It renders CanvasEditor  for editing and
manages UI interactions via modal and footer components.
4.2 CanvasEditor Component Logic
Logic :
Initializes a Skia canvas for rendering media.
Renders photos with filters using @shopify/react-native-skia .
Integrates FilteredVideoView  for video playback.
Manages stickers, text, and images via GestureHandler .
Synchronizes state with DataProvider  and handles app state changes.
Code Snippet :
import { Canvas, Image as SkiaImage } from '@shopify/react-native-skia';  
const CanvasEditor = ({ images }) => {  
  const videoRef = useRef(null);  
  useEffect(() => {  
    if (videoRef.current) {  
      videoRef.current.setSource(images?.uri);  
      videoRef.current.setFilter(images?.appliedFilter);  
    }
  }, [images]);  
  return (
    <View style={{ position: 'absolute', height: '100%', width: '100%' }}>  
      <Canvas style={{ width: ScreenWidth, height: ScreenHeight }}>  
        <SkiaImage image={images?.background} matrix={images.matrix || []} />  
      </Canvas>  
      {images.type === 'video' && <FilteredVideoView ref={videoRef} />}  
    </View>  
  ); 
}; 
Explanation : CanvasEditor  renders media with Skia for photos and FilteredVideoView  for videos, applying transformations via GestureHandler .
4.3 FilteredVideoView Component Logic
Logic :
Bridges React Native and native modules for video playback.
Applies filters (e.g., sepia) and synchronizes audio.
Generates thumbnails via native modules.
Code Snippet :
const FilteredVideoView = forwardRef((props, ref) => {  
  const nativeRef = useRef(null);  
  const { FilteredVideoViewManager } = NativeModules;  
  useImperativeHandle(ref, () => ({  
    setSource: uri => FilteredVideoViewManager.setSource(uri),  
    setFilter: filter => FilteredVideoViewManager.setFilter(filter)  
  })); 
  return <NativeFilteredVideoView ref={nativeRef} {...props} />;  
}); 
4.4 VidTrimmer Component Logic
Logic :
Initializes trimming times via TimeProvider .
Trims audio using native modules, saving to react-native-fs .
Displays waveforms for precise trimming.
Code Snippet :
const VidTrimmer = ({ audioUri, setAudioUri }) => {  
  const { setTrimEndTime } = useTimeData();  
  const trimAudio = async () => {  
    const trimmedFilePath = await AudioTrimmer.trimAudio(audioUri, 0, 15);  
    if (await RNFS.exists(trimmedFilePath)) setAudioUri(trimmedFilePath);  
    setTrimEndTime(15);  
  }; 
  return <View>{/* Trimming UI */}</View>;  
}; 
4.5 GestureHandler Component Logic
Logic :
Handles tap, pan, pinch, and rotation gestures using react-native-gesture-handler .
Updates initialX , initialY , rotate , and scale  in DataProvider .
Assigns highest zIndex  to dragged elements.
Code Snippet :
const panGesture = Gesture.Pan()  
  .onUpdate(event => {  
    translateX.value = context.value.x + event.translationX;  
    translateY.value = context.value.y + event.translationY;  
  }) 
  .onEnd(() => {  
    runOnJS(setMemorizedPosition)({ type, id, x: translateX.value, y: translateY.value, zIndex: getMaxZIndex() });  
  });
4.6 AudioTrimmer Module Logic (iOS)
Logic :
Trims audio using A VFoundation, saving as M4A.
Generates video thumbnails.
Code Snippet :
@objc func trimAudio(_ audioPath: String, startTime: NSNumber, endTime: NSNumber, resolve: @escaping RCTPromiseResolveBlock) {
  let asset = AVAsset(url: URL(fileURLWithPath: audioPath));  
  let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetAppleM4A);  
} 
4.7 FilteredVideoView Module Logic (Android)
Logic :
Uses GLSurfaceV iew with OpenGL ES 2.0 for video playback.
Applies filters and synchronizes audio.
Code Snippet :
public void setVideoPath(String path) {  
  renderer.setVideoPath(path);  
  requestRender();  
} 
4.8 ThumbnailModule Logic (Android)
Logic :
Extracts video frames using MediaMetadataRetriever .
Code Snippet :
@ReactMethod  
public void generateVideoThumbnail(String filePath, Promise promise) {  
  MediaMetadataRetriever retriever = new MediaMetadataRetriever();  
  retriever.setDataSource(filePath);  
  Bitmap bitmap = retriever.getFrameAtTime(1000000);  
} 
5. Detailed Workflows
5.1 Initialization Workflow
1. Launch app, initialize DataProvider  and GestureHandlerRootView .
2. Check permissions via requestPermissions .
3. Render loading screen until permissions are granted.
4. Display CameraScreen  after 300ms delay .
Code Snippet :
const App = () => {  
  const [hasPermission, setHasPermission] = useState(false);  
  const [showCamera, setShowCamera] = useState(false);  
  const checkPermissions = async () => {  
    const granted = await requestPermissions();  
    setHasPermission(granted);  
    if (granted) setTimeout(() => setShowCamera(true), 300);  
  }; 
  return (
    <DataProvider>  
      <GestureHandlerRootView>  
        {showCamera ? <CameraScreen /> : <ActivityIndicator />}  
      </GestureHandlerRootView>  
    </DataProvider>  
  ); 
}; 
5.2 Live Photo Capture Workflow
1. Open CameraScreen , request permissions.
2. Capture photo, save to RNFS.PicturesDirectoryPath .
3. Create story in images  state.
4. Update activeStoryIdx  and DataProvider .
Code Snippet :
const onStartRecording = async () => {  
  const photo = await cameraRef.current.takePhoto();  
  const filePath = `file://${photo.path}`;  
  const newImage = { id: `${Date.now()}-${Math.random()}`, uri: filePath, type: 'image/jpeg' };  
  setImages([newImage]);  
}; 
5.3 Media Editing Workflow
1. Load story in CanvasEditor .
2. Open modal to add stickers, text, or filters.
3. Apply transformations via GestureHandler .
4. Store changes in images  state.
Code Snippet :
const addStickerToImage = (unqId, stickerObj) => {  
  setImages(prevImages => prevImages.map(image => {  
    if (image.id === unqId) {  
      return { ...image, stickers: [...image.stickers, { ...stickerObj, zIndex: getMaxZIndex() }] };  
    }
    return image;  
  })); 
}; 
5.4 Audio Trimming Workflow
1. Select audio via DocumentPicker .
2. Trim audio to 15s for images or full duration for videos.
3. Save trimmed file and update images.audio .
Code Snippet :
const uploadMedia = async () => {  
  const res = await DocumentPicker.pickSingle({ type: [DocumentPicker.types.audio] });  
  await TrackPlayer.add({ url: res.uri });  
}; 
5.5 Video Playback Workflow
1. Load video in CanvasEditor .
2. Set source and filter in FilteredVideoView .
3. Play video with synchronized audio.
Code Snippet :
useEffect(() => {  
  videoRef.current.setSource(images?.uri);  
}, [images]);  
5.6 UI Flow
Components :
Header : Flash, sound, and modal controls.
Camera V iew: Camera feed or CanvasEditor .
Footer : Capture, flip camera, edit buttons.
Modal : Filters, Stickers, Music tabs.
PaintT ools: Paint and erase options.
Deletebtn : Appears when dragging elements.
Interactions :
1. App shows loading screen, then camera.
2. Capture photo or upload media to create stories.
3. Select story via AfterCapture  thumbnails.
4. Edit via modal (add stickers, text, audio).
5. Drag elements to transform or delete.
6. Draw sketches with highest z-index.
Example :
User captures photo → Story created → CanvasEditor  renders.
User adds sticker via modal → Drags to position.
User draws sketch → Saved with highest z-index.
6. Flow Diagrams
6.1 Initialization Flow Diagram
Launch App
   | 
Initialize DataProvider  
   | 
Check Permissions  
   | 
[Granted?] --> Yes --> Delay 300ms --> Render CameraScreen  
   |                        |  
   No --------------------- Show Loading Screen  
6.2 Live Photo Capture Flow Diagram
Open CameraScreen  
   | 
Capture Photo  
   | 
Save to FS
   | 
Create Story  
   | 
Update State  
6.3 Media Editing Flow Diagram
Select Story  
   | 
Open Modal
   | 
Add Element --> Sticker/Text/Filter  
   | 
Apply Transformations  
   | 
Store in images  
6.4 Audio Trimming Flow Diagram
Select Audio  
   | 
Trim Audio
   | 
Save File  
   | 
Update images.audio  
6.5 Video Playback Flow Diagram
Select Video  
   | 
Set Source
   | 
Play Video
   | 
Apply Filter  
6.6 Deletion Flow Diagram
Drag to Deletebtn  
   | 
Remove Element/Story  
   | 
Update images  
7. State Management
DataProvider : Manages images  state, storing stories with stickers , texts , additionalImages , and path .
TimeProvider : Tracks audio trimming times.
7.1 Transformation Storage in Image Array
Mechanism : GestureHandler  updates initialX , initialY , rotate , scale , and zIndex .
Example :
setMemorizedPosition({ type: 'sticker', id, x: 100, y: 150, scale: 1.5, zIndex: getMaxZIndex() });  
8. Performance Optimization
Skia Rendering : Efficient photo editing.
Native Rendering : Hardware-accelerated video playback.
Memoization : Minimizes re-renders in CanvasEditor .
Throttling : Limits gesture events.
9. Testing
Unit T ests: Jest for component logic.
Integration T ests: Native module interactions.
Platform T esting : Android 10 and iOS 9 compatibility .
10. Installation and Setup
10.1 Android Setup
Install: yarn install .
Build: cd android && ./gradlew clean .
Run: react-native run-android .
Permissions: Camera, READ_MEDIA_IMAGES .
10.2 iOS Setup
Install: yarn install .
Pods: cd ios && pod install .
Run: react-native run-ios .
Permissions: NSCameraUsageDescription .
11. Troubleshooting
Camera Failure : Verify permissions.
Transformation Issues : Check DataProvider  updates.
Video Playback : Ensure FilteredVideoView  compatibility .
Audio Sync : Validate VidTrimmer  inputs.
12. Future Enhancements
AI-based filters.
Cloud storage.
Multi-track audio editing.
Real-time collaboration.
13. Conclusion
This documentation provides a comprehensive guide to the MediaEditor module, detailing workflows, UI flow , and component logic for Android 10 and iOS 9.
14. Appendix
14.1 Glossary
Story : A single media item with elements.
Z-Index : Determines rendering order .
14.2 References
React Native  (https://reactnative.dev)
React Native V ision Camera  (https://react-native-vision-camera.com)
