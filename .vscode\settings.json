{"java.configuration.updateBuildConfiguration": "interactive", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}