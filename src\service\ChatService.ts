import { Socket } from 'socket.io-client';
import { Realm } from '@realm/react';
import {
  ChatSpace,
  IncomingDeliveredMessagesPayload,
  IncomingMessagePayload,
  IncomingSeenMessagesPayload,
  ServerMessage,
  PinMessageDto,
  PinMessageResponse,
  UnpinMessageDto,
  UnpinMessageResponse,
} from '../types/socketPayload.type';
import { MessageRepo } from '../device-storage/realm/repositories/MessageRepo';
import { ConversationRepo } from '../device-storage/realm/repositories/ConversationRepo';
import { ContactRepo } from '../device-storage/realm/repositories/ContactRepo';
import { getTranslationText } from '../utils/ApiService';
import { ComposedMessage, IUser, RemoteUser } from '../types/index.types';
import {
  ChannelType,
  ConversationType,
  IMessage,
  MessageEventType,
  MessageSchema,
  MessageStatus,
  MessageType,
  Reaction,
  ScheduleUpdateType,
} from '../device-storage/realm/schemas/MessageSchema';
import {
  ConversationSchema,
  IConversation,
  joinRequestStatus,
} from '../device-storage/realm/schemas/ConversationSchema';
import Toast from 'react-native-toast-message';
import { Notify } from '../firebase/notifications/displayNotifications';
import { ChatSocket, NewMessagePayload } from '../socket-client/ChatSocket';
import { generateLocalMessageId, safeRealmWrite } from '../device-storage/realm/lib';
import { Client } from '../lib/Client';
import { getChatspaces, getChatSpaceWithMembershipStatus } from './ChatSpacesService';
import { setChatSpaceRealm, setChatSpaceSocket } from './chatSpace.service';
import {
  defaultMemberPermissions,
  MemberPermissions,
} from '../screens/Home/Groups/GroupPermissions';
import { AdminPermissions, defaultAdminPermissions } from '../screens/Home/Groups/AdminPermissions';
import { UserSchema } from '../device-storage/realm/schemas/UserSchema';
import { realmSchemaNames } from '../device-storage/realm/schemas/schemaNames';
import { UserPreferencesService } from './UserService';
import CallService from './CallService';
import throttle from 'lodash.throttle';
import { resetNavigation, safeResetIfInsideDeletedChat } from '../utils/commonFunction';
import { IRequestData } from '../device-storage/realm/schemas/RequestSchema';
import { RequestRepo } from '../device-storage/realm/repositories/RequestRepo';
import { navigationRef } from '../navigation/RootContainer';
import { SCREENS } from '../navigation/screenNames';
import { ChatSpecificScreenParamsT } from '../screens/Home/Chats/ChatSpecificScreen';
import { PersonalProfileScreenScreenT } from '../screens/Home/Chats/Screens/PersonalProfileScreen';
import { InteractionManager } from 'react-native';
import { UserRepo } from '../device-storage/realm/repositories/UserRepo';
import { ChatSpaceRepo } from '../device-storage/realm/repositories/ChatSpaceRepo';
import { IChatSpace, RemoteChatSpace } from '../device-storage/realm/schemas/ChatSpaceSchema';
import { MembershipStatus } from '../types/chats.types';
import {
  getConversationDataFromLocalChatSpace,
  getConversationDataFromLocalUser,
  getConversationIdFromMessage,
  isChatSpace,
  isConversationMuted,
} from '../lib/chatLib';
import { getMe, getUser } from '../utils/userApiService';
import { getDateFromString, getDateInMillies } from '../lib/lib';
import { ConversationSettingsRepo } from '../device-storage/realm/repositories/ConversationSettingsRepo';

export type RealmChatSpace = Realm.Object<IConversation, never> & IConversation;

let socket: Socket | null = null;
let realm: Realm | null = null;

const validateResources = (strictSocket?: boolean): boolean => {
  if (!socket) {
    console.log('Socket not initialized or connected');
    if (strictSocket) {
      console.error('Strict socket validation failed, exiting function');
      return false;
    }
  }
  if (!realm) throw new Error('Realm not initialized');
  return true;
};

const setSocket = (s: Socket) => {
  if (socket) return;
  socket = s;
  setChatSpaceSocket(s);
  CallService.setSocket(s);
};

const setRealm = (r: Realm) => {
  if (!realm) realm = r;
  setChatSpaceRealm(r);
  CallService.setRealm(r);
};

const shouldDeleteMessage = (msg: ServerMessage, ownerId: string) =>
  msg.isDeleted || msg.deletedFor?.includes(ownerId);

const deleteMessage = (messageGlobalId: string) => {
  validateResources();
  const _realm = realm!;
  const message = _realm
    .objects<MessageSchema>('Message')
    .filtered('globalId == $0', messageGlobalId)[0];

  if (message) {
    safeRealmWrite(_realm, () => {
      const conversationId = message.conversationId;
      _realm.delete(message);
      ConversationRepo.updateLastMessageByGlobalId(realm!, conversationId, messageGlobalId);
    });
  }
};

// Utility to wrap async functions with try-catch
const safeAsync = <T extends (...args: any[]) => Promise<any>>(fn: T): T => {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error: any) {
      console.error(`Error in ${fn.name}:`, error);
      // Toast.show({ type: 'info', text1: error?.message ?? 'something went wrong' });
      // Optionally add more error handling here
    }
  }) as T;
};

/**
 * Builds a conversation object for a given conversationId.
 *
 * Resolution strategy (in order of priority):
 * 1. Check if the conversation already exists in Realm (fast local lookup).
 * 2. If not found:
 *    - For ChatSpaces (groups/channels):
 *        a. Check local Realm for ChatSpace.
 *        b. Otherwise, fetch from remote with membership status.
 *    - For P2P conversations (users):
 *        a. Check local Realm for User.
 *        b. Otherwise, fetch from remote API.
 *
 * @param {string} conversationId - The ID of the conversation (ChatSpace ID or User ID).
 * @returns {Promise<{ conversationData: IConversation | null; isValid: boolean }>}
 *          - conversationData: Resolved conversation object (or null if not found).
 *          - isValid: Whether the returned conversationData is valid.
 *
 * @example
 * const { conversationData, isValid } = await buildConversationData("abc123");
 * if (isValid) {
 *   console.log(conversationData?.name);
 * } else {
 *   console.log("Conversation not found");
 * }
 */
const buildConversationData = safeAsync(
  async (
    conversationId: string,
  ): Promise<{
    conversationData: IConversation | null;
    isValid: boolean;
    errorMessage?: string;
  }> => {
    // Fast path: return if conversation already exists in Realm
    const existing = ConversationRepo.findById(realm!, conversationId);
    if (existing) return { conversationData: existing, isValid: true };

    // Branch by type: ChatSpace (group/channel) or P2P (user)
    if (isChatSpace(conversationId)) {
      let conversationData: IConversation;

      const localChatSpace = ChatSpaceRepo.findRealmObjectById(realm!, conversationId);
      if (localChatSpace) {
        conversationData = getConversationDataFromLocalChatSpace(localChatSpace);
      } else {
        const remoteChatSpace = await getChatSpaceWithMembershipStatus(conversationId);
        if (!remoteChatSpace)
          return { conversationData: null, isValid: false, errorMessage: 'ChatSpace not found' };
        const chatSpaceObj = ChatSpaceRepo.createOrUpdate(
          realm!,
          remoteChatSpace,
          remoteChatSpace.membershipStatus,
        );
        conversationData = getConversationDataFromLocalChatSpace(chatSpaceObj);
      }

      return { conversationData, isValid: true };
    }

    // Fallback: treat as P2P conversation (user)
    const localUser = UserRepo.findRealmObjectByUserId(realm!, conversationId);
    let conversationData: IConversation | null = null;

    if (localUser) {
      conversationData = getConversationDataFromLocalUser(localUser);
    } else {
      const remoteUser = await getUser(conversationId);
      if (!remoteUser)
        return { conversationData: null, isValid: false, errorMessage: 'User not found' };
      const userObj = UserRepo.createOrUpdate(realm!, remoteUser, false);
      conversationData = getConversationDataFromLocalUser(userObj);
    }

    return { conversationData, isValid: true };
  },
);

export const buildUserData = async (userId: string): Promise<UserSchema | null> => {
  validateResources();
  const _realm = realm!;
  try {
    const user = UserRepo.findRealmObjectByUserId(_realm, userId);
    if (user) return user;
    const remoteUser = await getUser(userId);
    if (!remoteUser) return null;

    return UserRepo.createOrUpdate(_realm, remoteUser, false);
  } catch (error) {
    return null;
  }
};

const shouldShowIncomingMessageNotification = async (
  myUserId: string,
  message: MessageSchema,
  conversation: ConversationSchema,
): Promise<{ shouldShow: boolean; soundEnabled: boolean }> => {
  let showNotification = true;
  let soundEnabled = true;

  try {
    const userPrefs = await UserPreferencesService.getUserPref();

    if (userPrefs?.notifications) {
      showNotification = userPrefs.notifications.showNotification !== false;
      soundEnabled = userPrefs.notifications.notificationSound !== false;
    }
  } catch (error) {
    console.error('Error checking notification preferences, using defaults:', error);
  }

  const isNotFromMe =
    conversation.type === ConversationType.P2P
      ? myUserId !== message.senderId
      : myUserId !== message.senderId;

  const isCurrentlyMuted =
    isConversationMuted(conversation.conversationSettings.muteUntil) &&
    (conversation.muteUntil === null || Number(conversation.muteUntil) > Date.now());

  const shouldShow =
    showNotification &&
    !message.isSilent &&
    !message.isSeenByMe &&
    isNotFromMe &&
    !isCurrentlyMuted;

  return { shouldShow, soundEnabled };
};

const shouldNotifyServerAboutDelivery = (
  myUserId: string,
  message: MessageSchema,
  receivedMessage: ServerMessage,
): boolean => {
  if (isChatSpace(message.receiverId)) {
    return false;
  }
  const isAlreadyNotified =
    receivedMessage.status === MessageStatus.DELIVERED ||
    receivedMessage.status === MessageStatus.SEEN;
  const isMyMsg = myUserId === message.senderId;
  const shouldNotify = !isAlreadyNotified && !isMyMsg;
  return shouldNotify;
};

const handleEventMessage = safeAsync(async (eventMessage: ServerMessage) => {
  const me = await getMe();
  if (!me) {
    console.error('No user set on frontend, yet handling a message event');
    return;
  }
  const _realm = realm!;
  const chatSpaceId = eventMessage.receiverId;

  switch (eventMessage.eventType) {
    case MessageEventType.MEMBER_ADDED:
      MessageRepo.updateMemberCounts(eventMessage);
      // if (eventMessage.targetUserIds?.includes(me._id)) {
      //   ChatService.updateMemberPermissionOverride(
      //     eventMessage.receiverId,
      //     defaultMemberPermissions,
      //     null,
      //   );
      // }
      break;
    case MessageEventType.MEMBER_REMOVED:
      if (chatSpaceId && eventMessage.targetUserIds?.includes(me._id)) {
        InteractionManager.runAfterInteractions(() => {
          ChatService.deleteConversation(chatSpaceId);
        });
        safeResetIfInsideDeletedChat(chatSpaceId);
      } else {
        MessageRepo.updateMemberCounts(eventMessage);
      }
      break;
    case MessageEventType.JOIN_REQUEST_APPROVED:
      break;
    case MessageEventType.USER_JOINED:
      MessageRepo.updateMemberCounts(eventMessage);
      break;
    case MessageEventType.USER_LEFT:
      MessageRepo.updateMemberCounts(eventMessage);
      break;
    // case MessageEventType.ROLE_UPDATED:
    //   // if (chatSpaceId && eventMessage.targetUserIds?.includes(me._id)) {
    //   //   const { existingRole, updatedRole } = eventMessage.eventPayload || {};
    //   //   if (existingRole === 'admin' && updatedRole === 'member') {
    //   //     ChatService.updateMemberPermissionOverride(
    //   //       eventMessage.receiverId,
    //   //       defaultMemberPermissions,
    //   //       null,
    //   //     );
    //   //   }
    //   //   if (existingRole === 'member' && updatedRole === 'admin') {
    //   //     ChatService.updateMemberPermissionOverride(
    //   //       eventMessage.receiverId,
    //   //       null,
    //   //       defaultAdminPermissions,
    //   //     );
    //   //   }
    //   // }
    //   break;
    case MessageEventType.CHAT_SPACE_DELETED:
      InteractionManager.runAfterInteractions(() => {
        ChatService.deleteConversation(chatSpaceId);
      });
      safeResetIfInsideDeletedChat(chatSpaceId);
      break;
    case MessageEventType.CHAT_SPACE_INFO_UPDATED:
      const payloadData = eventMessage?.eventPayload;
      const chatSpaceUpdateData = {
        chatSpaceId: payloadData?.chatSpaceId,
        name: payloadData?.name,
        description: payloadData?.description,
        displayPic: payloadData?.displayPic,
      };

      ChatService.updateChatSpace(chatSpaceUpdateData);
      break;
    case MessageEventType.MEMBER_PRIVILEGES_UPDATED:
      if (eventMessage.targetUserIds?.includes(me._id)) {
        const memberPermissions = eventMessage.eventPayload?.updatedPrivileges;
        if (!memberPermissions) return;
        ChatService.updateMemberPermissionOverride(
          eventMessage.receiverId,
          memberPermissions,
          null,
        );
      }

      break;
    case MessageEventType.ADMIN_PRIVILEGES_UPDATED:
      break;
    case MessageEventType.ROLE_UPDATED:
      const updatedRole = eventMessage.eventPayload?.updatedRole;
      if (updatedRole && eventMessage.targetUserIds?.includes(me._id)) {
        ChatService.updateRoleofMember(eventMessage.receiverId, updatedRole);
      }
      break;
    case MessageEventType.CHAT_REACTION_ADDED:
      break;
    case MessageEventType.CHAT_REACTION_REMOVED:
      break;
    case MessageEventType.OWNERSHIP_TRANSFERRED:
      break;
    case MessageEventType.BLOCKED:
      break;
    case MessageEventType.UNBLOCKED:
      break;
  }
});

/**
 *
 */
/**
 * TODO: Accept an array of messages and process them in a single transaction
 * TODO: this is a more efficient way, and avoids quick ui updates
 */
const onIncomingMessage = safeAsync(async (data: ServerMessage) => {
  // !!TODO: Check what this if condition does
  // if (
  //   data.messageType?.toLowerCase?.() === 'event' &&
  //   data.eventType &&
  //   ['chat_reaction_added', 'chat_reaction_removed'].includes(data.eventType)
  // )
  validateResources();
  const _realm = realm!;
  const me = await getMe();
  if (!me) {
    console.error('No user set on frontend, yet handling an incoming message event');
    return;
  }
  const myId = me._id;

  const incomingMessage = data;
  const isChatSpaceMessage = isChatSpace(incomingMessage.receiverId);
  const conversationId = getConversationIdFromMessage(incomingMessage, myId);

  // !!TODO: why ?
  // if (incomingMessage.senderId && isGroup) {
  //   await ChatService.getOrCreateUser(incomingMessage.senderId);
  // }

  if (shouldDeleteMessage(incomingMessage, myId)) {
    // If the message is supposed to be deleted, delete it and exit the function
    deleteMessage(incomingMessage._id);
    return;
  }

  // TODO
  /** Now 2 cases after checking if the message already exists,
   * If exists follow case-2 and if not case-1
   *
   * (Write a utility function in ConversationRepo, to update the last message for a conversation)
   *
   * Case-1: new message:
   * -> Add it to the message table
   * -> Build conversation data
   * -> update conversation table
   *
   *
   *
   *
   * Case-2: updated message data (Existing message)
   * -> update the message data
   * -> upate conversation table with utility func
   *
   */
  // TODO

  const isEventMessage = incomingMessage.messageType === MessageType.EVENT;

  // Do nothing
  if (
    incomingMessage.eventType === MessageEventType.BLOCKED ||
    incomingMessage.eventType === MessageEventType.UNBLOCKED
  )
    return;
  const existingMessage = MessageRepo.findRealmObjectByGlobalId(realm!, incomingMessage._id);

  if (existingMessage && existingMessage.isCleared === true) {
    return;
  }

  // Update the existing message if it exists
  if (existingMessage) {
    const location = existingMessage.location;
    safeRealmWrite(_realm, () => {
      existingMessage.status = incomingMessage.status;
      existingMessage.text = incomingMessage.text;
      existingMessage.isPinned = incomingMessage.isPinned ? true : false;
      if (incomingMessage.isTextEdited) {
        existingMessage.isTextEdited = true;
      }
      if (existingMessage.messageType == MessageType.LOCATION && location?.type == 'live') {
        location.isStopped = incomingMessage.location?.isStopped as boolean;
      }
    });
    ConversationRepo.updateLastMessage(_realm, conversationId);
    Client.LastSyncedDate.set(incomingMessage.updatedAt);
    return;
  }

  // Create a new message and add it to the message table
  // Build conversation data if doesnt exist
  const { conversationData, isValid, errorMessage } = await buildConversationData(conversationId);
  if (!isValid || !conversationData) {
    console.error(errorMessage);
    return;
  }

  // let translatedText: string | undefined = undefined;

  // const shouldTranslate =
  //   conversationData.translatedLanguage &&
  //   (conversationData.type === ConversationType.P2P
  //     ? me._id === incomingMessage.receiverId
  //     : incomingMessage.senderId !== me._id);

  // if (shouldTranslate && !isEventMessage) {
  //   const { translatedText: tText } = await translateText(
  //     incomingMessage.text || '',
  //     conversationData.translatedLanguage || '',
  //   );
  //   translatedText = tText;
  // }

  // !!TODO: Implement text translation
  let message: MessageSchema | undefined;
  let conversation: ConversationSchema | undefined;
  let sender = await buildUserData(incomingMessage.senderId);
  // console.log(sender, 'incomingMessage.senderId');

  if (!sender) {
    console.error('Sender not found for incoming message', incomingMessage);
    return;
  }

  // If sender is not found, and its not an event message, we cannot proceed
  safeRealmWrite(realm!, async () => {
    message = MessageRepo.saveOrUpdateIncomingMessage(
      realm!,
      { ...incomingMessage, sender },
      conversationId,
    ) as MessageSchema;

    conversation = ConversationRepo.saveOrUpdateByMessage(realm!, conversationData, message);
  });

  if (isEventMessage) {
    handleEventMessage(incomingMessage);
  }

  if (!message || !conversation) {
    console.error('Failed to save message or conversation');
    return;
  }
  if (!isEventMessage) {
    const notificationSettings = await shouldShowIncomingMessageNotification(
      me._id,
      message,
      conversation,
    );
    if (notificationSettings.shouldShow) {
      Notify.incomingMessage(message, conversationData, notificationSettings.soundEnabled);
    }
    if (shouldNotifyServerAboutDelivery(me._id, message, incomingMessage)) {
      ChatSocket.emitReceivedMessages(socket!, [message.globalId!], [message.localId!]);
    }
  }
  Client.LastSyncedDate.set(incomingMessage.updatedAt.toString());
});

/**
 * Sends a message by performing the following steps:
 * 1. Validates necessary resources.
 * 2. Builds conversation data using the receiver's ID.
 * 3. Saves the new message to the Realm database.
 * 4. Updates the conversation with the new message.
 *
 * This function is wrapped in `safeAsync` to safely handle asynchronous errors.
 *
 * @param {ComposedMessage} composedMessage - The message object containing all necessary data for sending.
 * @returns {Promise<void>} A promise that resolves when the message is successfully processed.
 */
const sendMessage = safeAsync(async (composedMessage: ComposedMessage) => {
  validateResources();

  const conversationId = composedMessage.receiverId;
  const { conversationData, isValid } = await buildConversationData(conversationId);

  if (!isValid || !conversationData) {
    console.error(
      `[sendMessage] Could not build a valid conversation for ID: ${conversationId}. Message not sent.`,
    );
    return null;
  }

  const _realm = realm!;

  const disappearDuration = conversationData?.conversationSettings.disappearDuration;
  if (disappearDuration && !composedMessage.disappearAfter) {
    composedMessage.disappearAfter = Date.now() + disappearDuration;
  }

  //todo: fix updating the conversation for resolving dictionary error;
  let savedMessage: any = null;
  let messageToEmit: NewMessagePayload | null = null;
  safeRealmWrite(_realm, () => {
    if (composedMessage.messageType === MessageType.TEXT && composedMessage.localId) {
      // savedMessage = editMessage(composedMessage, conversationData);
      const editedMsg = editMessage(composedMessage, conversationData);
      // if (isEmit) {
      //   const messageToEmit = {
      //     ...editedMsg,
      //     senderLocalId: editedMsg.localId,
      //   };
      //   ChatSocket.emitNewMessage(socket!, messageToEmit);
      // }
      messageToEmit = {
        ...editedMsg,
        senderLocalId: editedMsg.localId,
      };
    } else {
      const newMessage = MessageRepo.saveOrUpdateSenderMessage(_realm, composedMessage);
      ConversationRepo.saveOrUpdateByMessage(_realm, conversationData, newMessage);
      savedMessage = newMessage;
      messageToEmit = {
        ...(newMessage.toJSON() as unknown as IMessage),
        senderLocalId: newMessage.localId,
      };
    }
  });

  // Emit socket notification AFTER safeRealmWrite completes
  if (messageToEmit && socket) {
    ChatSocket.emitNewMessage(socket, messageToEmit);
  }

  return savedMessage;
});

const sendScheduledMessageNow = (globalId: string) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findByGlobalId(_realm, globalId);

  safeRealmWrite(_realm, () => {
    if (message) {
      message.scheduledAt = Date.now();
    }
  });

  ChatSocket.sendScheduleMessagges(socket!, globalId);
};

export type ForwardMsgPayload = {
  receiverId: string;
  conversationType: ConversationType;
};

const createNewMessage = (msg: IMessage, newConversationId: ForwardMsgPayload): ComposedMessage => {
  if (
    msg.messageType === MessageType.IMAGE ||
    msg.messageType === MessageType.VIDEO ||
    msg.messageType === MessageType.AUDIO ||
    msg.messageType === MessageType.DOCUMENT ||
    msg.messageType === MessageType.VOICE
  ) {
    return {
      senderId: msg.senderId,
      receiverId: newConversationId.receiverId,
      conversationType: newConversationId.conversationType,
      messageType: msg.messageType,
      mediaUrl: msg.mediaUrl ?? '',
      text: msg.text ?? '',
      fileName: msg.fileName ?? '',
      fileSize: msg.fileSize ?? 0,
    };
  }

  if (msg.messageType === MessageType.TEXT) {
    return {
      senderId: msg.senderId,
      receiverId: newConversationId.receiverId,
      conversationType: newConversationId.conversationType,
      messageType: MessageType.TEXT,
      text: msg.text ?? '',
    };
  }

  if (msg.messageType === MessageType.LOCATION) {
    return {
      senderId: msg.senderId,
      receiverId: newConversationId.receiverId,
      conversationType: newConversationId.conversationType,
      messageType: MessageType.LOCATION,
      location: {
        latitude: (msg as any).location?.latitude,
        longitude: (msg as any).location?.longitude,
        type: msg.location?.type,
        expiresAt: msg.location?.expiresAt,
        isStopped: msg.location?.isStopped ?? false,
      },
    };
  }

  if (msg.messageType === MessageType.CONTACT) {
    return {
      senderId: msg.senderId,
      receiverId: newConversationId.receiverId,
      conversationType: newConversationId.conversationType,
      messageType: MessageType.CONTACT,
      contact: {
        name: (msg as any).contactName,
        phoneNumber: (msg as any).contactNumber,
      },
    };
  }

  if (msg.messageType === MessageType.EVENT) {
    return {
      senderId: msg.senderId,
      receiverId: newConversationId.receiverId,
      conversationType: newConversationId.conversationType,
      messageType: MessageType.EVENT,
      eventType: (msg as any).eventType,
      eventPayload: (msg as any).eventPayload,
    };
  }

  throw new Error('Unsupported message type for forwarding');
};

const forwardMessages = (messages: IMessage[], newConversations: ForwardMsgPayload[]) => {
  validateResources();
  const _realm = realm!;

  safeRealmWrite(_realm, () => {
    messages.forEach((msg) => {
      newConversations.forEach((newConversationId) => {
        const newMessage = createNewMessage(msg, newConversationId);
        sendMessage(newMessage);
      });
    });
  });
};

const updateConversation = (
  conversationId: string,
  updates: { status?: string; lastSeenAt?: number },
) => {
  validateResources();
  const _realm = realm!;

  ConversationRepo.updateConversation(_realm, conversationId, updates);
};

// !! This function must be called only inside sendMessage function
const editMessage = (composedMessage: ComposedMessage, conversationData: IConversation) => {
  const _realm = realm!;
  const editedMessage = MessageRepo.edit(_realm, composedMessage);
  if (!editedMessage) throw new Error('Tried to edit a non-existent message');

  if (editedMessage.localId === conversationData.lastMessage?.localId) {
    ConversationRepo.saveOrUpdateByMessage(_realm, conversationData, editedMessage);
  }
  return editedMessage.toJSON() as unknown as IMessage;
};

const getMessageByGlobalId = (globalId: string) => {
  const message = MessageRepo.findByGlobalId(realm!, globalId);
  return message;
};

const ackSentMessage = (message: ServerMessage) => {
  MessageRepo.ackSentMessage(realm!, message);
};

const ackSentScheduledMessages = (message: ServerMessage) => {
  MessageRepo.ackSentMessage(realm!, message);
};

const ackDeliveredMessages = (payload: IncomingDeliveredMessagesPayload) => {
  const messageGlobalIds = payload.body.messageIds;
  safeRealmWrite(realm!, () => {
    messageGlobalIds.forEach(({ globalId, localId }) => {
      MessageRepo.markMessageAsDelivered(realm!, localId);
    });
  });
};

const ackSeenMessages = (payload: IncomingSeenMessagesPayload) => {
  const messageGlobalIds = payload.body.messageIds;
  safeRealmWrite(realm!, () => {
    messageGlobalIds.forEach(({ globalId, localId }) => {
      MessageRepo.markMessageAsSeen(realm!, localId);
    });
  });
};

const sendPendingMessages = () => {
  const _realm = realm!;
  const pendingMessages = MessageRepo.findPendingMessages(_realm);
  if (pendingMessages.length === 0) return;
  console.log('Sending pending messages', pendingMessages.length);
  ChatSocket.emitPendingMessages(socket!, pendingMessages);
};

const onConversationOpen = async (conversationId: string) => {
  const _realm = realm!;
  const me = await getMe();
  if (!me) return;

  const Messages = MessageRepo.getMessagesNotSeenByMe(_realm, conversationId, me._id);
  const messageGlobalIds = Messages.map((message) => message.globalId!);
  const messageLocalIds = Messages.map((message) => message.localId!);
  console.log('userData.id', messageGlobalIds);

  const prefs = await UserPreferencesService.getUserPref();

  // !!TODOL: notify server about seen messages in chatspaces
  if (prefs.privacy.readReceipts && messageGlobalIds.length > 0 && !isChatSpace(conversationId)) {
    ChatSocket.emitMessagesSeenByMe(socket!, messageGlobalIds, messageLocalIds);
    MessageRepo.markMessagesAsSeenByMe(_realm, conversationId, me._id);
  }

  if (messageGlobalIds.length > 0) {
    MessageRepo.markMessagesAsSeenByMe(_realm, conversationId, me._id);
  }

  // ConversationRepo.updateLastMessage(_realm, conversationId);

  MessageRepo.findRecentMessageByConversationId(_realm, conversationId);
};

/**
 * Deletes all messages in a conversation identified by conversationId
 * Keeps the conversation alive but removes all messages
 * @param {string} conversationId
 */
const clearChat = (conversationId: string) => {
  validateResources();
  const _realm = realm!;
  MessageRepo.deleteAllByConversationId(_realm, conversationId);
  ConversationRepo.clearLastMessageData(_realm, conversationId);
};

/**
 * Clears all messages from all conversations
 * Keeps all conversations alive but removes all messages
 */
const clearAllChats = () => {
  validateResources();
  const _realm = realm!;
  MessageRepo.deleteAll(_realm);
  const allConversations = ConversationRepo.findAll(_realm);
  allConversations.forEach((conversation: any) => {
    ConversationRepo.clearLastMessageData(_realm, conversation.id);
  });
};

/**
 * Deletes all conversations and their messages
 * This will completely remove all conversations from the chat list
 */
const deleteAllConversations = () => {
  validateResources();
  const _realm = realm!;
  MessageRepo.deleteAll(_realm);
  ConversationRepo.deleteAll(_realm);
};

const toggleConversationMute = (conversationId: string, duration: string | undefined) => {
  const _realm = realm!;
  ConversationSettingsRepo.toggleMute(_realm, conversationId, duration);
};

const toggleConversationPin = (conversationId: string) => {
  const _realm = realm!;
  ConversationSettingsRepo.toggleConvSettPin(_realm, conversationId);
};

const toggleMessagesPin = (messages: IMessage[], unPinTime?: number) => {
  const _realm = realm!;

  safeRealmWrite(_realm, () => {
    messages.forEach((message) => {
      const realmMessage = MessageRepo.toggleMsgPin(_realm, message.localId, unPinTime);
    });
  });
};

const deleteConversation = (conversationId: string) => {
  const _realm = realm!;
  ChatSpaceRepo.delete(_realm, conversationId);
  MessageRepo.deleteAllByConversationId(_realm, conversationId);
  ConversationRepo.delete(_realm, conversationId);
};

const markChannelAsDeleted = (conversationId: string) => {
  validateResources();
  const _realm = realm!;

  const conversation = _realm
    .objects<IConversation>('Conversation')
    .filtered('id == $0', conversationId)[0];

  if (!conversation) return;

  safeRealmWrite(_realm, () => {
    (conversation as any).isDeleted = true;
  });
};

const toggleConversationArchive = (conversationId: string) => {
  const _realm = realm!;
  // ConversationRepo.toggleArchive(_realm, conversationId);
  ConversationSettingsRepo.toggleConvSettArchived(_realm, conversationId);
};

const toggleArchiveAllChats = (archive: boolean) => {
  const _realm = realm!;
  const allConversations = ConversationRepo.findAll(_realm);
  allConversations.forEach((conv: any) => {
    ConversationRepo.archiveUnarchiveConversation(_realm, conv.id, archive);
  });
};

const onQueuedMessages = (queuedMessages: ServerMessage[]) => {
  validateResources();
  const _realm = realm!;
  safeRealmWrite(_realm, () => {
    queuedMessages.forEach((message) => {
      ChatService.onIncomingMessage(message);
    });
  });
};

const deleteMessages = (
  messageLocalIds: string[],
  conversationId: string,
  forEveryone: boolean,
) => {
  console.log('[deleteMessages] called with:', {
    messageLocalIds,
    conversationId,
    forEveryone,
  });
  validateResources();
  const _realm = realm!;
  if (!forEveryone) {
    const messageGlobalIds = MessageRepo.deleteManyByLocalId(_realm, messageLocalIds);
    console.log('[deleteMessages] messageGlobalIds to delete:', messageGlobalIds);
    if (!messageGlobalIds || messageGlobalIds.length === 0) return;
    ChatSocket.emitDeleteMessages(socket!, messageGlobalIds, conversationId, forEveryone);
  } else {
    const messageGlobalIds = messageLocalIds
      .map((localId) => {
        const msg = MessageRepo.findByLocalId(_realm, localId);
        return msg?.globalId;
      })
      .filter((id): id is string => typeof id === 'string');
    console.log('[deleteMessages] (forEveryone) messageGlobalIds to delete:', messageGlobalIds);
    if (!messageGlobalIds || messageGlobalIds.length === 0) return;
    ChatSocket.emitDeleteMessages(socket!, messageGlobalIds, conversationId, forEveryone);
  }
};

const pinMessage = safeAsync(async (messageLocalId: string, receiverId: string, hours: number) => {
  console.log('[pinMessage] called with:', {
    messageLocalId,
    receiverId,
    hours,
  });
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findByLocalId(_realm, messageLocalId);

  if (!message || !message.globalId) {
    Toast.show({ type: 'error', text1: 'Cannot pin this message' });
    return;
  }

  // Don't update locally yet - wait for server confirmation
  const pinData: PinMessageDto = {
    messageId: message.globalId as string,
    receiverId,
    pinnedHours: hours,
  };

  ChatSocket.emitPinMessage(socket!, pinData);
});

const unpinMessage = safeAsync(async (messageLocalId: string, receiverId: string) => {
  console.log('[unpinMessage] called with:', {
    messageLocalId,
    receiverId,
  });
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findByLocalId(_realm, messageLocalId);

  if (!message || !message.globalId) {
    Toast.show({ type: 'error', text1: 'Cannot unpin this message' });
    return;
  }
  // const isPinned = Number(message.pinnedUntil) > Date.now()

  // // Check if message is actually pinned
  // if (!isPinned) {
  //   console.log('[unpinMessage] Message is not pinned:', message.globalId);
  //   Toast.show({ type: 'info', text1: 'Message is not pinned' });
  //   return;
  // }

  // Don't update locally yet - wait for server confirmation
  const unpinData: UnpinMessageDto = {
    messageId: message.globalId as string,
    receiverId,
  };

  console.log('[unpinMessage] Emitting unpin request:', unpinData);
  ChatSocket.emitUnpinMessage(socket!, unpinData);
});

const handlePinResponse = safeAsync(async (response: PinMessageResponse) => {
  console.log('[handlePinResponse] called with:', response);
  validateResources();
  const _realm = realm!;

  MessageRepo.updatePinStatus(_realm, response.messageId, {
    status: response.status,
    userId: response.userId,
    pinnedHours: response.pinnedHours,
  });
});

const handleUnpinResponse = safeAsync(async (response: UnpinMessageResponse) => {
  validateResources();
  const _realm = realm!;

  MessageRepo.updatePinStatus(_realm, response.messageId, {
    status: false, // Unpin means status is false
    userId: response.userId,
  });
});

const createChatSpace = (conversation: ChatSpace) => {
  validateResources();
  const _realm = realm!;

  const conversationData: IConversation = {
    id: conversation.chatSpaceId,
    displayName: conversation.name,
    displayPic: conversation.displayPic,
    unreadCount: 0,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    type: conversation.type,
    inviteCode: conversation.inviteCode,
    role: conversation.role,
    memberCount: conversation.memberCount,
    chatSpaceId: conversation.chatSpaceId,
    isLiveStreaming: false,
    isDeleted: conversation.isDeleted || false,
    isArchived: conversation.isArchived || false,
    isPrivate: conversation.isPrivate || false,
    groupMemberOverrides: JSON.stringify(conversation.groupMemberOverrides),
    lastMessageTimestamp: conversation.lastMessageTimestamp,
  };

  safeRealmWrite(_realm, () => {
    const createdConversation = ConversationRepo.createChatSpaceConversation(
      _realm,
      conversationData,
    );
  });

  return conversationData;
};

const createChatspaceBulk = (conversation: IConversation) => {
  validateResources();
  const _realm = realm!;

  safeRealmWrite(_realm, () => {
    const createdConversation = ConversationRepo.createChatSpaceConversation(_realm, conversation);
  });
};

const upsertConversation = (data: Partial<IConversation>) => {
  validateResources();
  const _realm = realm!;

  safeRealmWrite(_realm, () => {
    _realm.create(realmSchemaNames.conversation, data, Realm.UpdateMode.Modified);
  });
};

const updateChatSpace = (data: Partial<ChatSpace>) => {
  validateResources();
  const _realm = realm!;

  if (!data.chatSpaceId) {
    throw new Error('chatSpaceId is required to update a chat space');
  }

  const existingConversation = _realm
    .objects<IConversation>('Conversation')
    .filtered('id == $0', data.chatSpaceId)[0];

  if (!existingConversation) {
    throw new Error(`Conversation with chatSpaceId ${data.chatSpaceId} not found`);
  }

  safeRealmWrite(_realm, () => {
    if (data.name !== undefined) existingConversation.displayName = data.name;
    if (data.displayPic !== undefined) existingConversation.displayPic = data.displayPic;
    //   if (data.isLiveStreaming !== undefined)
    //     existingConversation.isLiveStreaming = data.isLiveStreaming;
    //   existingConversation.updatedAt = Date.now();
    // });
  });

  // return existingConversation;
  // ConversationRepo.editConversation(_realm, data?.chatSpaceId, {
  //   displayName: data.name as string,
  //   displayPic: data.displayPic,
  // });
  ChatSpaceRepo.updateChatSpace(_realm, data);
  return existingConversation;
};

const splitByEmojiAndSymbols = (text: string): string[] => {
  const regex = /(\p{Emoji_Presentation}|\p{Emoji}|\p{Symbol}|\p{Punctuation})/gu;
  return text.split(regex).filter(Boolean);
};

export const translateText = async (
  text: string,
  targetLang: string,
): Promise<{ translatedText: string }> => {
  try {
    const segments = splitByEmojiAndSymbols(text);

    const translatedSegments = await Promise.all(
      segments.map(async (segment) => {
        // Skip emojis or symbols from translation
        if (/^\p{Emoji}|\p{Symbol}|\p{Punctuation}/u.test(segment)) {
          return segment;
        }

        // Translate only if it's real text
        if (/\p{Letter}/u.test(segment)) {
          const result = await getTranslationText(segment, 'eng', targetLang);
          return result;
        }

        return segment;
      }),
    );

    return { translatedText: translatedSegments.join('') };
  } catch (e) {
    console.error('Translation failed', e);
    return { translatedText: '❌ Translation failed' };
  }
};

const rescheduleMessage = (globalId: string, scheduledAt: number) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findByGlobalId(_realm, globalId);
  if (!message) throw new Error('Message not found');

  safeRealmWrite(_realm, () => {
    message.scheduledAt = scheduledAt;
  });

  ChatSocket.sendScheduleMessagges(socket!, globalId);
};

const updateLocationStatus = (
  globalId: string,
  isStopped?: boolean,
  latitude?: number,
  longitude?: number,
) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findRealmObjectByGlobalId(_realm, globalId);
  const location = message.location;
  console.log(
    'updateLocationStatus onevent-----------------',
    globalId,
    isStopped,
    latitude,
    longitude,
    message,
  );
  if (!message) throw new Error('Message not found');
  if (!location) throw new Error('Message location not found');
  safeRealmWrite(_realm, () => {
    if (latitude !== undefined) location.latitude = latitude;
    if (longitude !== undefined) location.longitude = longitude;
    if (isStopped !== undefined) location.isStopped = isStopped;
  });
};

const updateThumbnail = (globalId: string, thumbnail: string) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findRealmObjectByGlobalId(_realm, globalId);
  if (!message) throw new Error('Message not found');
  console.log('thumbnail updatethumbnail', thumbnail);
  safeRealmWrite(_realm, () => {
    message.videoThumbnail = thumbnail;
  });
};

const updateLocalPath = (globalId: string, localPath: string) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findRealmObjectByGlobalId(_realm, globalId);
  if (!message) throw new Error('Message not found');
  console.log('localPath----------', localPath);
  safeRealmWrite(_realm, () => {
    message.localPath = localPath;
  });
};

const updateMediaMessage = async (
  localId?: string | null,
  mediaUrl?: string,
  fileSize?: number,
  thumbnail?: string,
) => {
  // console.log('update media====-----====', localId, mediaUrl, fileSize, thumbnail);
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findRealmObjectByLocalId(_realm, localId as string);
  if (!message) throw new Error('helloge not found');
  safeRealmWrite(_realm, () => {
    message.fileSize = fileSize;
    message.mediaUrl = mediaUrl;
    message.status = MessageStatus.PENDING;
    message.videoThumbnail = thumbnail ? thumbnail : '';
    const messageToEmit: NewMessagePayload = {
      ...(message.toJSON() as unknown as IMessage),
      senderLocalId: message.localId,
    };
    ChatSocket.emitNewMessage(socket!, messageToEmit);
  });
};

const deleteMediaMessage = async (localIds: string[]) => {
  console.log('kjadl akdjl sdf', localIds);
  const _realm = realm!;
  safeRealmWrite(_realm, () => {
    MessageRepo.deleteManyByLocalId(_realm, localIds);
  });
};

const updateScheduleMessage = (
  globalId: string,
  editType: string,
  scheduledAt?: number | null,
  localId?: string[],
) => {
  validateResources();
  const _realm = realm!;
  const message = MessageRepo.findRealmObjectByGlobalId(_realm, globalId);
  if (!message) throw new Error('Message not found');
  // console.log(
  //   'globalId, schedulesAt, editType',
  //   globalId,
  //   scheduledAt,
  //   editType,
  //   message.scheduledAt,
  // );
  safeRealmWrite(_realm, () => {
    if (editType == ScheduleUpdateType.RESCHEDULE) {
      message.scheduledAt = scheduledAt as number;
    } else if (editType == ScheduleUpdateType.EDIT) {
      // message.text = data?.text;
    } else if (editType == ScheduleUpdateType.DELETE) {
      MessageRepo.deleteManyByLocalId(_realm, localId as string[]);
    }
  });
  let payload: { messageId: string; type: string; rescheduleAt?: number | null } = {
    messageId: globalId,
    type: editType,
  };
  if (editType == ScheduleUpdateType.RESCHEDULE) {
    payload.rescheduleAt = scheduledAt;
  }
  ChatSocket.updateScheduleMessage(socket!, payload);
};

const updateTranslationTextLanguage = (conversationId: string, textLanguage: string) => {
  validateResources();
  const _realm = realm!;

  ConversationSettingsRepo.updateTextLanguage(_realm, conversationId, textLanguage);
};

const updateTranslationOptions = (
  conversationId: string,
  field: 'translateText' | 'translateVoiceMessages' | 'translateCalls',
  value: boolean,
) => {
  validateResources();
  const _realm = realm!;

  ConversationSettingsRepo.updateTranslationOptions(_realm, conversationId, field, value);
};

interface MessageDeletedBody {
  messageIds: string[];
  chatSpaceId?: string;
  initiatorId?: string;
}

interface MessageDeletedEvent {
  type: 'messages:status:deleted';
  body: string | MessageDeletedBody;
}

/**
 * Handles the server event for deleting messages locally.
 * This function is type-safe and handles both P2P, GROUPS and CHANNELS event structures.
 *
 * @param data - The event payload, which can be null or undefined.
 */
const handleMessagesDeletedEvent = (
  data: MessageDeletedEvent | MessageDeletedBody | null | undefined,
) => {
  if (!data) return;

  try {
    let body: MessageDeletedBody | null = null;

    if ('body' in data) {
      body = typeof data.body === 'string' ? JSON.parse(data.body) : data.body;
    } else {
      body = data;
    }

    if (body && Array.isArray(body.messageIds) && body.messageIds.length > 0) {
      MessageRepo.deleteManyByGlobalId(realm!, body.messageIds);
    } else {
      console.warn('Invalid MessageDeletedBody payload:', data);
    }
  } catch (error) {
    console.error('Failed to handle delete message event:', error, data);
  }
};

const updateMemberPermissionOverride = (
  conversationId: string,
  memberPermissions?: MemberPermissions | null,
  adminPermissions?: AdminPermissions | null,
) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.updateMemberPermissionsOverride(
    _realm,
    conversationId,
    memberPermissions,
    adminPermissions,
  );
};

const disappearingMessages = () => {
  validateResources();
  const _realm = realm!;
  MessageRepo.disappearMessagesAfetr(_realm);
};

const disappearDuration = (conversationId: string, disappearDuration: number | null) => {
  validateResources();
  const _realm = realm!;
  ConversationSettingsRepo.updateDisappearDuration(_realm, conversationId, disappearDuration);
};

const updateRoleofMember = (conversationId: string, role: ChannelType) => {
  validateResources();
  const _realm = realm!;
  ChatSpaceRepo.updateMemberRole(_realm, conversationId, role);
};

const deleteChatspace = (chatSpaceId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.deleteChatspace(_realm, chatSpaceId);
};

const generateRandomColor = (): string => {
  const hue = Math.floor(Math.random() * 360); // Full color wheel
  return `hsl(${hue}, 70%, 50%)`; // Bright and saturated
};

const getOrCreateUser = async (userId: string) => {
  validateResources();
  const _realm = realm!;

  const localUser = _realm.objectForPrimaryKey<any>(realmSchemaNames.user, userId);
  if (localUser) {
    // console.log('Returning localUser:', JSON.stringify(localUser, null, 2));
    return {
      id: localUser.id,
      displayName:
        localUser.displayName || localUser.name || localUser.username || localUser.contactName,
      userName: localUser.userName || localUser.username,
      phoneNumber: localUser.phoneNumber,
      displayPic: localUser.displayPic || localUser.image || localUser.profilePic,
      textColor: localUser.textColor,
      isBlocked: localUser.isBlocked,
    };
  }

  if (!localUser) {
    try {
      const profile = await getUser(userId);
      // console.log('==== Loaded profile:', JSON.stringify(profile, null, 2));
      if (!profile) return null;

      const randomColor = generateRandomColor();

      safeRealmWrite(_realm, () => {
        _realm.create(
          realmSchemaNames.user,
          {
            id: profile._id,
            displayName: profile.name || profile.username,
            userName: profile.username || profile.name,
            phoneNumber: profile.phoneNumber,
            displayPic: profile.image ?? '',
            textColor: randomColor,
            isBlocked: false,
          },
          Realm.UpdateMode.Modified,
        );
      });

      return profile;
    } catch (err) {
      console.error('Error in getOrCreateUser:', err);
      return null;
    }
  }
};

const onBlockedUser = (userId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.blockedUsers(_realm, userId);
};

const onUnBlockedUser = (userId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.unBlockedUsers(_realm, userId);
};

const onBlocked = (userId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.blocked(_realm, userId);
};

const addBulkUsers = async (userIds: string[]) => {
  validateResources();
  const _realm = realm!;
  const nonExistingUsers: string[] = [];

  safeRealmWrite(_realm, () => {
    userIds.forEach((userId) => {
      const localUser = _realm.objectForPrimaryKey<any>(realmSchemaNames.user, userId);
      if (!localUser) {
        nonExistingUsers.push(userId);
      }
    });
  });

  const profilesPrm = nonExistingUsers.map((userId) => getUser(userId));
  const profilesResp = await Promise.all(profilesPrm);
  const filteredProfiles = profilesResp.filter(
    (profile: any) => profile !== null && profile !== undefined,
  );

  safeRealmWrite(_realm, () => {
    filteredProfiles.forEach((profile: any) => {
      _realm.create(
        realmSchemaNames.user,
        {
          id: profile._id,
          displayName: profile.name,
          userName: profile.username,
          phoneNumber: profile.phoneNumber,
          displayPic: profile.image ?? '',
          textColor: generateRandomColor(),
          isBlocked: false,
        },
        Realm.UpdateMode.Modified,
      );
    });
  });

  return Promise.resolve();
};

const blockUsers = (userIds: string[]) => {
  validateResources();
  const _realm = realm!;
  safeRealmWrite(_realm, () => {
    userIds.forEach((userId) => {
      const localUser = _realm.objectForPrimaryKey<any>(realmSchemaNames.user, userId);
      if (localUser) {
        localUser.isBlocked = true;
      }
    });
  });
};

const onUnBlocked = (userId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.unBlocked(_realm, userId);
};

const getBlockedUsers = () => {
  validateResources();
  const _realm = realm!;
  return ConversationRepo.getBlockedUsers(_realm);
};

const getBlockedConversations = () => {
  validateResources();
  const _realm = realm!;
  return ConversationRepo.getBlockedConversations(_realm);
};

const getBlockedEntities = () => {
  validateResources();
  const _realm = realm!;

  // const blockedConversations = ConversationRepo.getBlockedConversations(_realm);
  // if (blockedConversations.length > 0) {
  //   return { data: blockedConversations };
  // }

  const blockedUsers = ConversationRepo.getBlockedUsers(_realm);

  return { data: blockedUsers };
};

const getUserData = (userId: string) => {
  validateResources();
  const _realm = realm!;

  const localUser = _realm.objectForPrimaryKey(UserSchema, userId);
  return localUser;
};

const getLiveConversation = (userId: string) => {
  validateResources();
  const _realm = realm!;

  return ConversationRepo.findRealmObjectById(_realm, userId);
};

const getConversation = (conversationId: string) => {
  validateResources();
  const _realm = realm!;
  return ConversationRepo.findById(_realm, conversationId);
};

const updateMemberCount = (conversationId: string, newMembers: number, removeMembers: number) => {
  validateResources();
  const _realm = realm!;
  ChatSpaceRepo.updateMemberCount(_realm, conversationId, newMembers, removeMembers);
};

const onTyping = throttle((conversationId: string) => {
  if (!validateResources(true)) return;
  ChatSocket.emitTyping(socket!, conversationId);
}, 3000);

const updateTypingStatus = (userId: string, conversationId: string) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.updateTypingStatus(_realm, userId, conversationId);
};

const fetchPresence = async (
  userId: string,
): Promise<{ userId: string; isUserOnline?: boolean; lastSeen?: number } | {}> => {
  validateResources();

  try {
    const lastSeen = await ChatSocket.getLastSeen(socket!, userId);
    return lastSeen || {};
  } catch (error) {
    return {};
  }
};

const getSavedMessages = (messageType: MessageType) => {
  validateResources();
  const _realm = realm!;
  return MessageRepo.getSavedMessages(_realm, messageType);
};

const handleProfileUpdate = (user: RemoteUser) => {
  validateResources();
  const _realm = realm!;
  ConversationRepo.handleProfileUpdate(_realm, user);
  // ContactRepo.handleProfileUpdate(_realm, user);
  UserRepo.handleProfileUpdate(_realm, user);
};

const sendMessages = (composedMessages: ComposedMessage[]) => {
  validateResources();
  const _realm = realm!;
  safeRealmWrite(_realm, () => {
    composedMessages.forEach((composedMessage) => {
      sendMessage(composedMessage);
    });
  });
};

// Group requests
const saveOrUpdateRequests = (requests: any) => {
  validateResources();

  const _realm = realm!;

  const existingRequests = _realm
    .objects<IRequestData>(realmSchemaNames.request)
    .filtered('chatSpaceId == $0', requests.chatSpaceId);

  const currentCount = existingRequests.length;

  const requestData: IRequestData = {
    ...requests,
    chatSpaceId: requests.chatSpaceId,
    requesterId: requests.requestedBy,
    requestName: requests.requestName,
    count: currentCount + 1,
  };

  safeRealmWrite(_realm, () => {
    _realm.create(realmSchemaNames.request, requestData, Realm.UpdateMode.Modified);
  });
};

const getLiveRequest = (chatSpaceId: string) => {
  validateResources();
  const _realm = realm!;

  return RequestRepo.findRealmObjectById(_realm, chatSpaceId);
};

const getLiveUserById = (userId: string) => {
  validateResources();
  const _realm = realm!;
  return UserRepo.findRealmObjectByUserId(_realm, userId);
};

const getLiveChatSpaceById = (chatSpaceId: string) => {
  validateResources();
  const _realm = realm!;
  return ChatSpaceRepo.findRealmObjectById(_realm, chatSpaceId);
};

const saveChatSpace = (
  chatSpace: RemoteChatSpace,
  membershipStatus: MembershipStatus,
  systemMessage: ServerMessage,
) => {
  validateResources();
  const _realm = realm!;
  const newChatSpace = ChatSpaceRepo.createOrUpdate(_realm, chatSpace, membershipStatus);
  if (systemMessage) {
    ChatService.onIncomingMessage(systemMessage);
  }
  return newChatSpace;
};

export const ChatService = {
  sendMessages,
  sendMessage,
  setSocket,
  setRealm,
  onIncomingMessage,
  ackSentMessage,
  ackDeliveredMessages,
  ackSeenMessages,
  sendPendingMessages,
  onConversationOpen,
  clearChat,
  deleteConversation,
  toggleConversationMute,
  toggleConversationPin,
  toggleConversationArchive,
  onQueuedMessages,
  deleteMessages,
  forwardMessages,
  createChatSpace,
  toggleMessagesPin,
  updateTranslationTextLanguage,
  updateTranslationOptions,
  sendScheduledMessageNow,
  updateLocationStatus,
  updateThumbnail,
  updateLocalPath,
  updateMediaMessage,
  deleteMediaMessage,
  updateScheduleMessage,
  getMessageByGlobalId,
  handleMessagesDeletedEvent,
  updateChatSpace,
  pinMessage,
  unpinMessage,
  handlePinResponse,
  handleUnpinResponse,
  updateMemberPermissionOverride,
  disappearingMessages,
  disappearDuration,
  updateRoleofMember,
  markChannelAsDeleted,
  deleteChatspace,
  getOrCreateUser,
  toggleArchiveAllChats,
  onBlockedUser,
  onUnBlockedUser,
  getBlockedUsers,
  onBlocked,
  onUnBlocked,
  getBlockedConversations,
  getBlockedEntities,
  updateConversation,
  getLiveConversation,
  clearAllChats,
  deleteAllConversations,
  updateMemberCount,
  getConversation,
  onTyping,
  updateTypingStatus,
  getSavedMessages,
  getUserData,
  handleProfileUpdate,
  ackSentScheduledMessages,
  fetchPresence,
  upsertConversation,
  saveOrUpdateRequests,
  getLiveRequest,
  createChatspaceBulk,
  addBulkUsers,
  blockUsers,
  getLiveUserById,
  getLiveChatSpaceById,
  saveChatSpace,
  buildUserData,
};
