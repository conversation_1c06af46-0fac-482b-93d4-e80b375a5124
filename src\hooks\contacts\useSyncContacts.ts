import { useEffect } from 'react';
import { getFormattedContacts } from '../../lib/contactsLib';
import Api from '../../utils/api';
import { useContacts } from './useContacts';
import { useRealm } from '../../device-storage/realm/realm';
import { IContact } from '../../device-storage/realm/schemas/ContactSchema';
import { safeRealmWrite } from '../../device-storage/realm/lib';
import { UserRepo } from '../../device-storage/realm/repositories/UserRepo';
import { ContactRepo } from '../../device-storage/realm/repositories/ContactRepo';
import { PhoneNumber } from 'libphonenumber-js';
import { AnyRealmObject, Results } from 'realm';
import { realmSchemaNames } from '../../device-storage/realm/schemas/schemaNames';
import Realm from 'realm';
import { InteractionManager } from 'react-native';
import useUsers from '../../device-storage/realm/hooks/useUsers';

type ClientContact = {
  name: string;
  phoneNumber: string;
};

export type ServerContact = {
  bio: string;
  name: string;
  username: string;
  userId: string;
  number: string;
  image: string;
};

/**
 * useSyncContacts
 *
 * Syncs the contacts from the device with the ones from the server.
 * Fetches the contacts from the device, saves them to the Realm DB
 * and then fetches the contacts from the server and updates/saves them
 * in the Realm DB as well.
 *
 * Triggers on mount and fetches the first 1000 contacts from the device.
 * If there are more than 1000 contacts, it will only sync the first 1000.
 *
 * @returns {void}
 */
export const useSyncContacts = () => {
  const realm = useRealm();
  const { saveOrUpdate, saveOrUpdateMany, pureSaveOrUpdate } = useContacts();
  const getServerContacts = async (contacts: ClientContact[]) => {
    try {
      let cleanContacts = contacts.map((contact) => ({
        name: contact.name,
        number: contact.phoneNumber,
      }));
      const contactsPayload = {
        contacts: cleanContacts,
      };
      const res = await Api.post('v1/users/contacts', contactsPayload);
      return res?.body?.data;
    } catch (error) {
      console.error('Error fetching contacts from server:', error);
      return null;
    }
  };

  const syncDeviceContacts = async (formattedContacts: ClientContact[]) => {
    try {
      const phoneSet = new Set(formattedContacts.map((c) => c.phoneNumber));

      realm.write(() => {
        const allContacts = realm.objects<IContact>('Contact'); // 👈 tell Realm what type
        allContacts.forEach((c) => {
          c.isInDevice = phoneSet.has(c.phoneNumber);
        });
      });
    } catch (err) {
      console.error('Failed to sync device contacts', err);
    }
  };

  const syncContacts = async () => {
    const clientContacts = await getFormattedContacts();
    await saveOrUpdateMany(clientContacts);
    await syncDeviceContacts(clientContacts);

    const serverContacts = await getServerContacts(clientContacts.slice(0, 1000));
    if (!serverContacts) {
      console.log('No server contacts found');
      return;
    }

    safeRealmWrite(realm, () => {
      // serverContacts.registered.forEach((element: ServerContact) => {
      //   ContactRepo.createOrUpdateRegisteredContact(realm, element);
      // });

      serverContacts.registered.forEach((element: ServerContact) => {
        UserRepo.createOrUpdate(
          realm,
          {
            ...element,
            _id: element.userId,
            phoneNumber: element.number,
            contactName: element.name,
          },
          true,
        );
        ContactRepo.deleteByPhoneNumber(realm, element.number);
      });

      serverContacts.unregistered.forEach((number: string) => {
        pureSaveOrUpdate({
          phoneNumber: number,
          isRegistered: false,
        });
      });
    });
  };
  useEffect(() => {
    syncContacts();
  }, []);
};

export const newSyncLib = () => {
  const realm = useRealm();
  const { saveOrUpdate, saveOrUpdateMany, pureSaveOrUpdate } = useContacts();

  const getAllRealmcontacts = () => {
    const allContacts = realm.objects<IContact>('Contact');
    return allContacts;
  };

  const getUnregisteredContacts = () => {
    const unregisteredContacts = realm
      .objects<IContact>('Contact')
      .filtered('isRegistered == false');
    return unregisteredContacts;
  };

  const getServerContacts = async (contacts: ClientContact[]) => {
    try {
      let cleanContacts = contacts.map((contact) => ({
        name: contact.name,
        number: contact.phoneNumber,
      }));

      const contactsPayload = {
        contacts: cleanContacts,
      };
      const res = await Api.post('v1/users/contacts', contactsPayload);
      return res?.body?.data;
    } catch (error) {
      console.error('Error fetching contacts from server:', error);
      return null;
    }
  };

  const deleteAllContacts = () => {
    console.log('Deleting all contacts');
    realm.write(() => {
      const allContacts = realm.objects<IContact>('Contact');
      realm.delete(allContacts);
    });
  };

  const startSync = async () => {
    console.log('Deleted all contacts');
    const clientContacts = await getFormattedContacts();
    console.log('Formatted contacts:', clientContacts);
    const allContacts = getAllRealmcontacts();
    console.log('All realm contacts:', allContacts);
    const allContactsSet = new Set(allContacts.map((c) => c.phoneNumber));
    const newContacts = clientContacts.filter((c) => !allContactsSet.has(c.phoneNumber));
    console.log('New contacts:', newContacts);

    const unregisteredContacts = getUnregisteredContacts();
    console.log('Unregistered contacts:', unregisteredContacts);
    const tobesenttoservercontacts = [...newContacts, ...unregisteredContacts];
    console.log('Contacts to send to server:', tobesenttoservercontacts);
    const serverContacts = await getServerContacts(tobesenttoservercontacts);
    if (!serverContacts) {
      console.log('No server contacts found');
      return;
    }

    const serverUnregisteredContacts = new Set(
      serverContacts.unregistered.map((num: string) => num),
    );
    console.log('writing registered contacts to realm', serverContacts);

    const chunkSize = 100; // tuned for smooth UI

    // --- Registered contacts ---
    for (let i = 0; i < serverContacts.registered.length; i += chunkSize) {
      const chunk = serverContacts.registered.slice(i, i + chunkSize);

      realm.write(() => {
        chunk.forEach((element: ServerContact) => {
          UserRepo.createOrUpdate(
            realm,
            {
              ...element,
              _id: element.userId,
              phoneNumber: element.number,
              contactName: element.name,
            },
            true,
          );
        });
      });

      await nextFrame(); // let UI breathe
    }

    // --- Unregistered contacts ---
    for (let i = 0; i < tobesenttoservercontacts.length; i += chunkSize) {
      const chunk = tobesenttoservercontacts.slice(i, i + chunkSize).filter((ctct) => {
        return serverUnregisteredContacts.has(ctct.phoneNumber);
      });

      realm.write(() => {
        chunk.forEach((element: ClientContact) => {
          realm.create(
            realmSchemaNames.contact,
            {
              name: element.name,
              phoneNumber: element.phoneNumber,
              isRegistered: false,
            },
            Realm.UpdateMode.Modified,
          );
        });
      });

      await nextFrame(); // also yield between unregistered chunks
    }

    console.log('Synced contacts ✅');
  };

  useEffect(() => {
    startSync();
  }, []);

  return {
    startSync,
    deleteAllContacts,
  };
};

function nextFrame() {
  return new Promise<void>((resolve) => {
    requestAnimationFrame(() => resolve());
  });
}

/*
// get all the contacts
// get all the local contacts
// get registered contacts
// get unregistered contacts
// get deleted contacts        // delete them from the local db
// get updated contacts (name only for now) //  save for later.
// get new contacts
// send all new contacts+unregestered to the server , server response = {registered:[] , unregistered:[] }
// save all the newcontacts in db
// save all the registered contacts in db
// filter out the newcontacts+updated contacts from the unregistered arr and save only new contacts excluding the new registered contacts (which will be saved in prev step). this will skip writing again.
*/

// start writing here the above logic. do not touch the existing code. and use the existing functions which are needed.

/**
 * useEnhancedSyncContacts
 *
 * Implements the new contact sync flow:
 * - get all the contacts (device)
 * - get all the local contacts
 * - get registered contacts (local Contact schema marked isRegistered, though usually Users)
 * - get unregistered contacts (local Contact schema where isRegistered == false)
 * - get deleted contacts (present locally but not on device) and delete them
 * - get updated contacts (name only) and update later
 * - get new contacts (present on device, not in local)
 * - send all new contacts + unregistered to server, expect { registered: ServerContact[], unregistered: string[] }
 * - save all the new contacts in db
 * - save all the registered contacts in db (as Users) and remove Contact duplicates
 * - from server.unregistered, save only the truly-new contacts (exclude those that became registered)
 */
export const useEnhancedSyncContacts = () => {
  const realm = useRealm();

  // Note: we have two schemas for contacts. one for registered and one for unregistered. unregistered = IContact , registered = IUser from UserSchema
  // update acc to it.
  const { createFilteredContactsQuery } = useUsers();
  const registeredContacts = createFilteredContactsQuery().get();

  const { saveOrUpdateMany, pureSaveOrUpdate } = useContacts();

  // --- Helpers to read local state all contacts from Icontacts are unregistered ---
  const getLocalUnregistered = () => Array.from(realm.objects<IContact>('Contact'));

  // Normalize to server payload
  const toClientContact = (c: { name?: string; phoneNumber: string }): ClientContact => ({
    name: c.name ?? '',
    phoneNumber: c.phoneNumber,
  });

  // Call server with contacts
  const postContactsToServer = async (contacts: ClientContact[]) => {
    try {
      const contactsPayload = {
        contacts: contacts.map((c) => ({ name: c.name, number: c.phoneNumber })),
      };
      const res = await Api.post('v1/users/contacts', contactsPayload);
      return res?.body?.data as { registered: ServerContact[]; unregistered: string[] };
    } catch (err) {
      console.error('useEnhancedSyncContacts: server sync failed', err);
      return { registered: [], unregistered: [] };
    }
  };

  // Compute diffs
  const diffWithDevice = (device: ClientContact[], local: IContact[]) => {
    const deviceByNumber = new Map(device.map((c) => [c.phoneNumber, c]));
    const localByNumber = new Map(local.map((c) => [c.phoneNumber, c]));

    const deletedLocal = local.filter((c) => !deviceByNumber.has(c.phoneNumber));
    const newOnDevice = device.filter((c) => !localByNumber.has(c.phoneNumber));
    const updatedName = device.filter((c) => {
      const existing = localByNumber.get(c.phoneNumber);
      if (existing) {
        const deviceName = parseContactName(c.name);
        const localName = parseContactName(existing.name);
        return deviceName !== localName;
      }
      return false;
    });

    return { deletedLocal, newOnDevice, updatedName };
  };

  // add diffWithDevice for registered contacts also.
  // For registered entries (Users), we only toggle isDeviceContact and update contactName.
  const diffWithDeviceRegistered = (
    device: ClientContact[],
    registered: Array<{
      phoneNumber?: string;
      contactName?: string;
      userId?: string;
      _id?: string;
    }>,
  ) => {
    const deviceByNumber = new Map(device.map((c) => [c.phoneNumber, c]));
    const withNumber = registered.filter((u) => !!u.phoneNumber) as Array<{
      phoneNumber: string;
      contactName?: string;
      userId?: string;
      _id?: string;
    }>;

    const onDevice = withNumber.filter((u) => deviceByNumber.has(u.phoneNumber));
    const nameChanges = onDevice.filter((u) => {
      const d = deviceByNumber.get(u.phoneNumber);
      return d && u.contactName !== d.name;
    });

    return { onDevice, nameChanges };
  };

  // Main entry point
  const syncContacts = async () => {
    // get all the contacts (device)
    const deviceContacts = await getFormattedContacts();

    // get unregistered contacts (IContact schema)
    const unregisteredLocal = getLocalUnregistered();

    // get deleted / new / updated for unregistered store
    const { deletedLocal, newOnDevice, updatedName } = diffWithDevice(
      deviceContacts,
      unregisteredLocal,
    );

    // diff for registered (Users)
    const regDiff = diffWithDeviceRegistered(deviceContacts, registeredContacts);

    // Build a unified local numbers set (registered + unregistered) to detect truly new device numbers
    const registeredNumbers = new Set(
      (registeredContacts || [])
        .map((u: any) => u?.phoneNumber)
        .filter((n: string | undefined): n is string => typeof n === 'string'),
    );
    const unregisteredNumbers = new Set(unregisteredLocal.map((c) => c.phoneNumber));
    const allLocalNumbers = new Set<string>([...registeredNumbers, ...unregisteredNumbers]);

    // New on device across both schemas
    const newOnDeviceAll = deviceContacts.filter((c) => !allLocalNumbers.has(c.phoneNumber));

    // delete handled inside the single write transaction below

    // save for later (we'll update names after server step)
    // updatedName: ClientContact[]

    // get new contacts: newOnDevice: ClientContact[]

    // send all new contacts + unregistered to the server
    const payloadForServer: ClientContact[] = [
      ...newOnDeviceAll,
      ...unregisteredLocal.map(toClientContact),
    ];

    const server = await postContactsToServer(payloadForServer);

    // Build sets and maps for a single-transaction write
    const deviceMap = new Map(deviceContacts.map((c) => [c.phoneNumber, c]));
    const newlyRegisteredNumbers = new Set(server.registered?.map((r) => r.number) ?? []);
    const newNumbers = new Set(newOnDeviceAll.map((c) => c.phoneNumber));
    const toPersistUnregisteredNumbers = new Set<string>();

    // Start with server-provided unregistered, exclude those that became registered
    (server.unregistered ?? []).forEach((num) => {
      if (!newlyRegisteredNumbers.has(num)) {
        // only persist if it came from newOnDevice (skip old unregistered to avoid re-writing)
        if (newNumbers.has(num)) toPersistUnregisteredNumbers.add(num);
      }
    });

    // Also include newOnDevice that server didn't mark as registered
    newOnDeviceAll.forEach((c) => {
      if (!newlyRegisteredNumbers.has(c.phoneNumber)) {
        toPersistUnregisteredNumbers.add(c.phoneNumber);
      }
    });

    // Perform a single Realm transaction covering:
    // - delete removed unregistered contacts
    // - upsert registered users returned by server
    // - upsert unregistered contacts
    // - update name changes for unregistered
    // - update registered contactName changes based on device diffs

    realm.write(() => {
      // Delete removed unregistered
      if (deletedLocal.length) {
        deletedLocal.forEach((c) => realm.delete(c));
      }

      // Upsert registered Users from server
      (server.registered ?? []).forEach((u) => {
        UserRepo.createOrUpdate(
          realm,
          {
            _id: u.userId,
            username: u.username ?? '',
            name: u.name ?? '',
            image: u.image ?? '',
            bio: u.bio ?? '',
            phoneNumber: u.number,
            contactName: u.name,
          },
          deviceMap.has(u.number),
        );
        // remove any Contact duplicate
        const existing = realm.objectForPrimaryKey<IContact>(realmSchemaNames.contact, u.number);
        if (existing) realm.delete(existing);
      });

      // Upsert unregistered contacts
      toPersistUnregisteredNumbers.forEach((num) => {
        const name = deviceMap.get(num)?.name ?? '';

        realm.create(
          realmSchemaNames.contact,
          {
            name,
            phoneNumber: num,
            isRegistered: false,
            isInDevice: true,
          },
          Realm.UpdateMode.Modified,
        );
      });

      // Apply updated names for existing unregistered contacts
      if (updatedName.length) {
        updatedName.forEach((c) => {
          // Only for unregistered store; registered name updates handled via server/user flow
          realm.create(
            realmSchemaNames.contact,
            {
              name: c.name,
              phoneNumber: c.phoneNumber,
              isRegistered: false,
              isInDevice: true,
            },
            Realm.UpdateMode.Modified,
          );
        });
      }

      // Update registered names where device name changed
      regDiff.nameChanges.forEach((u) => {
        const newName = deviceMap.get(u.phoneNumber)?.name ?? u.contactName ?? '';
        UserRepo.createOrUpdate(
          realm,
          {
            _id: (u as any)._id ?? (u as any).userId ?? '',
            username: '',
            name: '',
            image: '',
            bio: '',
            phoneNumber: u.phoneNumber,
            contactName: newName,
          },
          deviceMap.has(u.phoneNumber),
        );
      });
    });

    return { ok: true };
  };

  useEffect(() => {
    console.log('Syncing contacts...');
    syncContacts();
  }, []);
};

const parseContactName = (num: string) => num.replaceAll(' ', '');
