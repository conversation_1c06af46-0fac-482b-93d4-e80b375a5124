import React, { useState, useRef, useEffect } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    FlatList,
    Modal,
    TextInput,
    Keyboard,
} from 'react-native';

import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { _openAppSetting } from '../../utils/locationHandler';
import SearchSVG from '../../assets/svgIcons/SearchSVG';
import { colors } from '../../theme/colors';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle } from '../../theme/fonts';



type BLanguageModalProps = {
    languageModal: boolean;
    setLanguageModal: React.Dispatch<React.SetStateAction<boolean>>;
    filteredLanguages: (
        | {
            name: string;
            code: string;
            flag: null;
        }
        | {
            name: string;
            code: string;
            flag: string;
        }
    )[];
    setSearch: React.Dispatch<React.SetStateAction<string>>;
    search: string;
    selectedLanguage: string;
    setSelectedLanguage: React.Dispatch<React.SetStateAction<string>>;
};


import { Dimensions } from "react-native";
import ModalWrapper from '../../component/ModalWrapper';
import SelectionSVG from '../../assets/svgIcons/SelectionSVG';
import { useKeyboard } from '../../utils/useKeyboard';

const { height } = Dimensions.get("window");

const BLanguageModal = ({
    languageModal,
    setLanguageModal,
    setSearch,
    search,
    selectedLanguage,
    setSelectedLanguage,
    filteredLanguages,
}: BLanguageModalProps) => {
    const { keyboardHeight } = useKeyboard();

    return (
        <ModalWrapper isVisible={languageModal} onCloseModal={() => { Keyboard.dismiss(); setLanguageModal(false); }}>
            <View style={{
                height: height * 0.5,
                backgroundColor: colors.white,
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                padding: 15,
                marginBottom: keyboardHeight * 0.85,
            }}>
                <Text style={styles.title}>Select language</Text>

                <View
                    style={{
                        borderWidth: 0.5,
                        borderBottomColor: "#aaa",
                        marginVertical: 10,
                    }}
                />

                <View style={styles.searchContainer}>
                    <SearchSVG size={16} color={colors.black_23} />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Search languages"
                        placeholderTextColor={colors.gray_80}
                        onChangeText={setSearch}
                        value={search}
                    />
                </View>

                <FlatList
                    data={filteredLanguages}
                    keyExtractor={(item) => item.code}
                    style={{ marginTop: 10 }}
                    contentContainerStyle={{ paddingBottom: 20 }}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps='handled'
                    renderItem={({ item }) => (
                        <TouchableOpacity
                            style={styles.languageItem}
                            onPress={() => { Keyboard.dismiss(); setSelectedLanguage(item.code); setLanguageModal(false); }}
                        >
                            <Text style={styles.flag}>{item.flag}</Text>
                            <Text
                                style={[
                                    styles.languageName,
                                    {
                                        right: item.name === "Original language" ? 12 : 0,
                                    },
                                ]}
                            >
                                {item.name}
                            </Text>
                            <SelectionSVG isSelected={selectedLanguage === item.code} size={20} />
                        </TouchableOpacity>
                    )}
                />
            </View>
        </ModalWrapper>
    );
};


const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: "rgba(0,0,0,0.6)",
        justifyContent: "flex-end",
    },
    bottomSheetContainer: {
        height: height * 0.5, // 👈 half screen height
        backgroundColor: "rgba(0,0,0,0.8)",
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 15,
    },
    blurContainer: {
        ...StyleSheet.absoluteFillObject,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        overflow: "hidden",
    },
    title: {
        fontSize: 16,
        fontWeight: "600",
        color: colors.black_23,
    },
    searchContainer: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: colors.gray_f3,
        paddingVertical: 2,
        paddingHorizontal: 12,
        borderRadius: 12,
    },
    searchInput: {
        flex: 1,
        marginLeft: 8,
        height: 40,
        color: colors.black_23,
        fontSize: 14,
    },
    languageItem: {
        flexDirection: "row",
        alignItems: "center",
        paddingVertical: 12,
    },
    flag: {
        fontSize: 18,
        marginRight: 10,
    },
    languageName: {
        flex: 1,
        fontSize: 16,
        color: colors.black_23,
    },
});

//make this component available to the app
export default BLanguageModal;

