{"v": "5.7.1", "fr": 29.9700012207031, "ip": 0, "op": 177.000007209358, "w": 1920, "h": 1080, "nm": "1 UP", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "Controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [100]}], "ix": 2, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0002').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9760009765625, "s": [100]}], "ix": 3, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0003').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [50]}], "ix": 4, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0004').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745098039, 0.219607843137, 1], "ix": 7, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0007').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607843137, 0, 1], "ix": 8, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0008').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.78431372549, 0.262745098039, 1], "ix": 9, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0009').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843137, 0.807843137255, 0.486274509804, 1], "ix": 10, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0010').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960784314, 0.890196078431, 1], "ix": 11, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0011').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901960784, 0.227450980392, 0.701960784314, 1], "ix": 12, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0012').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0013').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('o 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0014').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "o 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-52.602, 0], [0, 52.602], [52.601, 0], [0, -52.602], [0, 0]], "o": [[0, 0], [0, 52.602], [52.601, 0], [0, -52.602], [-52.602, 0], [0, 0], [0, 0]], "v": [[-95.243, -299.26], [-95.243, 0], [0, 95.243], [95.243, 0], [0, -95.245], [-95.243, 0], [-95.243, 299.259]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 3');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [500, 568.406], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "o BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-52.602, 0], [0, 52.602], [52.601, 0], [0, -52.602], [0, 0]], "o": [[0, 0], [0, 52.602], [52.601, 0], [0, -52.602], [-52.602, 0], [0, 0], [0, 0]], "v": [[-95.243, -299.26], [-95.243, 0], [0, 95.243], [95.243, 0], [0, -95.245], [-95.243, 0], [-95.243, 299.259]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [500, 568.406], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "Controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [100]}], "ix": 2, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0002').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9760009765625, "s": [100]}], "ix": 3, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0003').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [50]}], "ix": 4, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0004').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745098039, 0.219607843137, 1], "ix": 7, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0007').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607843137, 0, 1], "ix": 8, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0008').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.78431372549, 0.262745098039, 1], "ix": 9, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0009').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843137, 0.807843137255, 0.486274509804, 1], "ix": 10, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0010').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960784314, 0.890196078431, 1], "ix": 11, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0011').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901960784, 0.227450980392, 0.701960784314, 1], "ix": 12, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0012').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0013').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 3 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0014').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "l 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -19.081], [0, 0], [0, 0], [-19.081, 0], [0, 0], [0, 0], [0, -19.081], [0, 0]], "o": [[0, 0], [19.081, 0], [0, 0], [0, 0], [0, 19.081], [0, 0], [0, 0], [19.082, 0], [0, 0], [0, 0]], "v": [[-152.04, -307.61], [-23.608, -307.61], [10.941, -273.061], [10.941, -237.061], [10.941, 110.08], [45.49, 144.629], [81.49, 144.629], [117.49, 144.629], [152.04, 179.179], [152.04, 307.61]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 5');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [471.784, 519.371], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "l BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -19.081], [0, 0], [0, 0], [-19.081, 0], [0, 0], [0, 0], [0, -19.081], [0, 0]], "o": [[0, 0], [19.081, 0], [0, 0], [0, 0], [0, 19.081], [0, 0], [0, 0], [19.082, 0], [0, 0], [0, 0]], "v": [[-152.04, -307.61], [-23.608, -307.61], [10.941, -273.061], [10.941, -237.061], [10.941, 110.08], [45.49, 144.629], [81.49, 144.629], [117.49, 144.629], [152.04, 179.179], [152.04, 307.61]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [471.784, 519.371], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "Controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [100]}], "ix": 2, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0002').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9760009765625, "s": [100]}], "ix": 3, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0003').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [50]}], "ix": 4, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0004').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745098039, 0.219607843137, 1], "ix": 7, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0007').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607843137, 0, 1], "ix": 8, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0008').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.78431372549, 0.262745098039, 1], "ix": 9, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0009').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843137, 0.807843137255, 0.486274509804, 1], "ix": 10, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0010').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960784314, 0.890196078431, 1], "ix": 11, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0011').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901960784, 0.227450980392, 0.701960784314, 1], "ix": 12, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0012').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0013').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('l 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0014').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "l 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -19.081], [0, 0], [0, 0], [-19.081, 0], [0, 0], [0, 0], [0, -19.081], [0, 0]], "o": [[0, 0], [19.081, 0], [0, 0], [0, 0], [0, 19.081], [0, 0], [0, 0], [19.082, 0], [0, 0], [0, 0]], "v": [[-152.04, -307.61], [-23.608, -307.61], [10.941, -273.061], [10.941, -237.061], [10.941, 110.08], [45.49, 144.629], [81.49, 144.629], [117.49, 144.629], [152.04, 179.179], [152.04, 307.61]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 5');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [471.784, 519.371], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "l BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -19.081], [0, 0], [0, 0], [-19.081, 0], [0, 0], [0, 0], [0, -19.081], [0, 0]], "o": [[0, 0], [19.081, 0], [0, 0], [0, 0], [0, 19.081], [0, 0], [0, 0], [19.082, 0], [0, 0], [0, 0]], "v": [[-152.04, -307.61], [-23.608, -307.61], [10.941, -273.061], [10.941, -237.061], [10.941, 110.08], [45.49, 144.629], [81.49, 144.629], [117.49, 144.629], [152.04, 179.179], [152.04, 307.61]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [471.784, 519.371], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "Controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [100]}], "ix": 2, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0002').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9760009765625, "s": [100]}], "ix": 3, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0003').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [50]}], "ix": 4, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0004').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745098039, 0.219607843137, 1], "ix": 7, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0007').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607843137, 0, 1], "ix": 8, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0008').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.78431372549, 0.262745098039, 1], "ix": 9, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0009').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843137, 0.807843137255, 0.486274509804, 1], "ix": 10, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0010').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960784314, 0.890196078431, 1], "ix": 11, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0011').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901960784, 0.227450980392, 0.701960784314, 1], "ix": 12, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0012').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0013').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('e 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0014').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "e 1 Matte", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [503, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.515, -24.861], [0, 0], [0, 0], [-2.056, -5.62], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[21.668, 13.982], [0, 0], [0, 0], [1.13, 2.682], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-215.032, 1.616], [-165.328, 60.828], [-165.245, 61.026], [-151.411, 94.282], [-146.383, 107.103], [-54.693, 285.57], [273.948, 157.502], [30.052, -285.57], [-273.948, -164.904]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "op", "nm": "Offset Paths 1", "a": {"a": 0, "k": -135, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), 0, 50, 0, -140);\n} catch (e) {\n    $bm_rt = value;\n}"}, "lj": 1, "ml": {"a": 0, "k": 4, "ix": 3}, "ix": 2, "mn": "ADBE Vector Filter - Offset", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5, 0.5, 0.5, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [783.052, 456.832], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [25.086, 22.856], [0, 37.297], [-31.033, 24.025], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-35.278, 0], [-26.867, -24.482], [0, -42.194], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[200.418, 54.667], [106.81, 19.223], [65.147, -76.577], [116.194, -180.403], [-3.085, -286.333], [-200.418, -267.998], [-200.418, 286.334], [156.248, 281.667]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5, 0.5, 0.5, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "op", "nm": "Offset Paths 1", "a": {"a": 0, "k": -135, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 0, -140, 0);\n} catch (e) {\n    $bm_rt = value;\n}"}, "lj": 1, "ml": {"a": 0, "k": 4, "ix": 3}, "ix": 3, "mn": "ADBE Vector Filter - Offset", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [300.418, 645.333], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "e 1", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [503, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-242.635, 100.205], [242.634, -100.205]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 6');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [496.526, 568.047], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "e 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [503, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [39.451, 0], [0, -52.601], [-52.601, 0], [-25.682, 21.851], [-32.496, 0], [0, -57.511], [0, 0]], "o": [[0, 0], [0, -17.479], [0, 0], [-14.458, -34.184], [-52.602, 0], [0, 57.512], [32.497, 0], [25.682, -21.852], [52.602, 0], [0, 0], [0, 0]], "v": [[29.876, 179.931], [29.876, -38.083], [16.009, -88.423], [2.141, -121.761], [-85.617, -179.931], [-180.861, -84.688], [-81.59, 10.556], [0, -22.616], [81.589, -55.788], [180.861, 39.455], [180.861, 66.27]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 3');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [582.426, 653.444], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "e BG Matte", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [503, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-10.515, -24.861], [0, 0], [0, 0], [-2.056, -5.62], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[21.668, 13.982], [0, 0], [0, 0], [1.13, 2.682], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-215.032, 1.616], [-165.328, 60.828], [-165.245, 61.026], [-151.411, 94.282], [-146.383, 107.103], [-54.693, 285.57], [273.948, 157.502], [30.052, -285.57], [-273.948, -164.904]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "op", "nm": "Offset Paths 1", "a": {"a": 0, "k": -135, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), 0, 50, 0, -140);\n} catch (e) {\n    $bm_rt = value;\n}"}, "lj": 1, "ml": {"a": 0, "k": 4, "ix": 3}, "ix": 2, "mn": "ADBE Vector Filter - Offset", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5, 0.5, 0.5, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [783.052, 456.832], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [25.086, 22.856], [0, 37.297], [-31.033, 24.025], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-35.278, 0], [-26.867, -24.482], [0, -42.194], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[200.418, 54.667], [106.81, 19.223], [65.147, -76.577], [116.194, -180.403], [-3.085, -286.333], [-200.418, -267.998], [-200.418, 286.334], [156.248, 281.667]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.5, 0.5, 0.5, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "op", "nm": "Offset Paths 1", "a": {"a": 0, "k": -135, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 0, -140, 0);\n} catch (e) {\n    $bm_rt = value;\n}"}, "lj": 1, "ml": {"a": 0, "k": 4, "ix": 3}, "ix": 3, "mn": "ADBE Vector Filter - Offset", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [300.418, 645.333], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "e BG", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [503, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-242.635, 100.205], [242.634, -100.205]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [496.526, 568.047], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [39.451, 0], [0, -52.601], [-52.601, 0], [-25.682, 21.851], [-32.496, 0], [0, -57.511], [0, 0]], "o": [[0, 0], [0, -17.479], [0, 0], [-14.458, -34.184], [-52.602, 0], [0, 57.512], [32.497, 0], [25.682, -21.852], [52.602, 0], [0, 0], [0, 0]], "v": [[29.876, 179.931], [29.876, -38.083], [16.009, -88.423], [2.141, -121.761], [-85.617, -179.931], [-180.861, -84.688], [-81.59, 10.556], [0, -22.616], [81.589, -55.788], [180.861, 39.455], [180.861, 66.27]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [582.426, 653.444], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 2, "ty": 3, "nm": "Controller", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [100]}], "ix": 2, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0002').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9760009765625, "s": [100]}], "ix": 3, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0003').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886004638672, "s": [50]}], "ix": 4, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0004').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745098039, 0.219607843137, 1], "ix": 7, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0007').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607843137, 0, 1], "ix": 8, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0008').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.78431372549, 0.262745098039, 1], "ix": 9, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0009').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843137, 0.807843137255, 0.486274509804, 1], "ix": 10, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0010').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960784314, 0.890196078431, 1], "ix": 11, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0011').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901960784, 0.227450980392, 0.701960784314, 1], "ix": 12, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0012').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0013').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar FM_controllerComp = comp('1 UP'), mValue = value;\ntry {\n    var FM_controllerLayer = FM_controllerComp.layer('h 2 1');\n    $bm_rt = mValue = FM_controllerLayer.effect(1)('Pseudo/0vbarur0o9-0014').valueAtTime($bm_sum(time, FM_controllerLayer.inPoint));\n} catch (err) {\n    $bm_rt = value;\n}\n;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "h 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -44.397], [0, 0], [44.396, 0], [0, 0]], "o": [[0, 0], [-44.397, 0], [0, 0], [0, 44.397], [0, 0], [0, 0]], "v": [[162.768, -289.057], [80.388, -289.057], [0, -208.669], [0, 208.67], [-80.387, 289.057], [-162.767, 289.057]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 2');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [419.612, 491.33], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "h 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-44.396, 0], [0, 44.397], [0, 0], [-44.397, 0], [0, -44.397], [0, 0], [-44.397, 0], [0, 44.397], [0, 0]], "o": [[0, 0], [0, 44.397], [44.397, 0], [0, 0], [0, -44.397], [44.397, 0], [0, 0], [0, 44.397], [44.397, 0], [0, 0], [0, 0]], "v": [[-241.163, 53.24], [-241.163, 73.049], [-160.776, 153.437], [-80.388, 73.049], [-80.388, -73.051], [0, -153.438], [80.388, -73.051], [80.388, 73.049], [160.775, 153.437], [241.163, 73.049], [241.163, 53.24]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 5');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 0;\n    } else {\n        $bm_rt = 100;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [500, 626.951], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 1}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "h BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 500, 0], "ix": 2}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -44.397], [0, 0], [44.396, 0], [0, 0]], "o": [[0, 0], [-44.397, 0], [0, 0], [0, 44.397], [0, 0], [0, 0]], "v": [[162.768, -289.057], [80.388, -289.057], [0, -208.669], [0, 208.67], [-80.387, 289.057], [-162.767, 289.057]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [419.612, 491.33], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-44.396, 0], [0, 44.397], [0, 0], [-44.397, 0], [0, -44.397], [0, 0], [-44.397, 0], [0, 44.397], [0, 0]], "o": [[0, 0], [0, 44.397], [44.397, 0], [0, 0], [0, -44.397], [44.397, 0], [0, 0], [0, 44.397], [44.397, 0], [0, 0], [0, 0]], "v": [[-241.163, 53.24], [-241.163, 73.049], [-160.776, 153.437], [-80.388, 73.049], [-80.388, -73.051], [0, -153.438], [80.388, -73.051], [80.388, 73.049], [160.775, 153.437], [241.163, 73.049], [241.163, 53.24]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [500, 626.951], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('Start'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = $bm_div(FM_controllerLayer.effect('Gilbert Controller')('End'), 2);\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 0, "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    $bm_rt = linear(FM_controllerLayer.effect('Gilbert Controller')('Offset'), -50, 50, 0, 180);\n} catch (e) {\n    $bm_rt = value;\n}"}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = FM_controllerLayer.effect('Gilbert Controller')('Color 1');\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4, "x": "var $bm_rt;\ntry {\n    var FM_controllerComp = thisComp;\n    var FM_controllerLayer = FM_controllerComp.layer('Controller');\n    if (FM_controllerLayer.effect('Gilbert Controller')('Use only Color 1') == 1) {\n        $bm_rt = 100;\n    } else {\n        $bm_rt = $bm_mul(FM_controllerLayer.effect('Gilbert Controller')('On white background'), 100);\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": {"a": 0, "k": 72, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 899.100036621094, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "hello [Controller]", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 540, 0], "ix": 2}, "a": {"a": 0, "k": [655.093, 415.206, 0], "ix": 1}, "s": {"a": 0, "k": [53.199, 53.199, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Font Controller", "np": 10, "mn": "Pseudo/Ib61aa4a42yOa", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "", "mn": "Pseudo/Ib61aa4a42yOa-0001", "ix": 1, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/Ib61aa4a42yOa-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "Tracking", "mn": "Pseudo/Ib61aa4a42yOa-0003", "ix": 3, "v": {"a": 0, "k": 50, "ix": 3}}, {"ty": 0, "nm": "Tracking center", "mn": "Pseudo/Ib61aa4a42yOa-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "Line spacing", "mn": "Pseudo/Ib61aa4a42yOa-0005", "ix": 5, "v": {"a": 0, "k": 331.334, "ix": 5}}, {"ty": 0, "nm": "Offset animation time", "mn": "Pseudo/Ib61aa4a42yOa-0006", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Y-Offset", "mn": "Pseudo/Ib61aa4a42yOa-0007", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "Z-Offset", "mn": "Pseudo/Ib61aa4a42yOa-0008", "ix": 8, "v": {"a": 0, "k": 0, "ix": 8}}]}], "ip": 0, "op": 177.000007209358, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "o 2 1", "parent": 1, "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [961.983, 207.603, 0], "ix": 2, "x": "var $bm_rt;\nvar coef, x, y, z;\ntry {\n    coef = 53.2;\n    var per = $bm_div(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking center'), 100 / 4);\n    x = $bm_sum(value[0], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking'), $bm_sub(4, per)));\n    $bm_rt = y = $bm_sum($bm_sum(value[1], $bm_mul($bm_div($bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Line spacing'), 100), coef), 0)), $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Y-Offset'), 4));\n    if (thisLayer.position.value.length > 2) {\n        z = $bm_sum(value[2], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Z-Offset'), 4));\n        $bm_rt = [\n            x,\n            y,\n            z\n        ];\n    } else {\n        $bm_rt = [\n            x,\n            y\n        ];\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 129.91, "s": [0]}, {"t": 153.886256267915, "s": [100]}], "ix": 2}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 40, "s": [0]}, {"t": 63.9762526058061, "s": [100]}], "ix": 3}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 40, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 63.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 129.91, "s": [0]}, {"t": 153.886256267915, "s": [50]}], "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745112181, 0.219607844949, 1], "ix": 7}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 8}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 9}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843831, 0.807843148708, 0.486274510622, 1], "ix": 10}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 11}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 12}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [0]}, {"t": 939.100038250327, "s": [29.933]}], "ix": 2, "x": "var $bm_rt;\nvar numChar, idx, offsetFrame;\ntry {\n    numChar = 5;\n    idx = $bm_mul($bm_sub($bm_sub(index, parent.index), numChar), -1);\n    offsetFrame = $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Offset animation time'), idx);\n    $bm_rt = $bm_sub(value, framesToTime(Math.round(offsetFrame)));\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": 1000, "h": 1000, "ip": 40.0000016292334, "op": 177.000007209358, "st": 40.0000016292334, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "l 3 1", "parent": 1, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [753.489, 207.603, 0], "ix": 2, "x": "var $bm_rt;\nvar coef, x, y, z;\ntry {\n    coef = 53.2;\n    var per = $bm_div(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking center'), 100 / 4);\n    x = $bm_sum(value[0], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking'), $bm_sub(3, per)));\n    $bm_rt = y = $bm_sum($bm_sum(value[1], $bm_mul($bm_div($bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Line spacing'), 100), coef), 0)), $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Y-Offset'), 3));\n    if (thisLayer.position.value.length > 2) {\n        z = $bm_sum(value[2], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Z-Offset'), 3));\n        $bm_rt = [\n            x,\n            y,\n            z\n        ];\n    } else {\n        $bm_rt = [\n            x,\n            y\n        ];\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 119.91, "s": [0]}, {"t": 143.886255860607, "s": [100]}], "ix": 2}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 30, "s": [0]}, {"t": 53.9762521984977, "s": [100]}], "ix": 3}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 30, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 53.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 119.91, "s": [0]}, {"t": 143.886255860607, "s": [50]}], "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745112181, 0.219607844949, 1], "ix": 7}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 8}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 9}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843831, 0.807843148708, 0.486274510622, 1], "ix": 10}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 11}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 12}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"t": 929.100037843019, "s": [29.933]}], "ix": 2, "x": "var $bm_rt;\nvar numChar, idx, offsetFrame;\ntry {\n    numChar = 5;\n    idx = $bm_mul($bm_sub($bm_sub(index, parent.index), numChar), -1);\n    offsetFrame = $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Offset animation time'), idx);\n    $bm_rt = $bm_sub(value, framesToTime(Math.round(offsetFrame)));\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": 1000, "h": 1000, "ip": 30.0000012219251, "op": 177.000007209358, "st": 30.0000012219251, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "l 2 1", "parent": 1, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [634.095, 207.603, 0], "ix": 2, "x": "var $bm_rt;\nvar coef, x, y, z;\ntry {\n    coef = 53.2;\n    var per = $bm_div(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking center'), 100 / 4);\n    x = $bm_sum(value[0], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking'), $bm_sub(2, per)));\n    $bm_rt = y = $bm_sum($bm_sum(value[1], $bm_mul($bm_div($bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Line spacing'), 100), coef), 0)), $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Y-Offset'), 2));\n    if (thisLayer.position.value.length > 2) {\n        z = $bm_sum(value[2], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Z-Offset'), 2));\n        $bm_rt = [\n            x,\n            y,\n            z\n        ];\n    } else {\n        $bm_rt = [\n            x,\n            y\n        ];\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 109.91, "s": [0]}, {"t": 133.886255453299, "s": [100]}], "ix": 2}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 20, "s": [0]}, {"t": 43.9762517911894, "s": [100]}], "ix": 3}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 20, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 43.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 109.91, "s": [0]}, {"t": 133.886255453299, "s": [50]}], "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745112181, 0.219607844949, 1], "ix": 7}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 8}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 9}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843831, 0.807843148708, 0.486274510622, 1], "ix": 10}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 11}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 12}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 919.100037435711, "s": [29.933]}], "ix": 2, "x": "var $bm_rt;\nvar numChar, idx, offsetFrame;\ntry {\n    numChar = 5;\n    idx = $bm_mul($bm_sub($bm_sub(index, parent.index), numChar), -1);\n    offsetFrame = $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Offset animation time'), idx);\n    $bm_rt = $bm_sub(value, framesToTime(Math.round(offsetFrame)));\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": 1000, "h": 1000, "ip": 20.0000008146167, "op": 177.000007209358, "st": 20.0000008146167, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "e 2 1", "parent": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [429.462, 207.603, 0], "ix": 2, "x": "var $bm_rt;\nvar coef, x, y, z;\ntry {\n    coef = 53.2;\n    var per = $bm_div(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking center'), 100 / 4);\n    x = $bm_sum(value[0], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking'), $bm_sub(1, per)));\n    $bm_rt = y = $bm_sum($bm_sum(value[1], $bm_mul($bm_div($bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Line spacing'), 100), coef), 0)), $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Y-Offset'), 1));\n    if (thisLayer.position.value.length > 2) {\n        z = $bm_sum(value[2], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Z-Offset'), 1));\n        $bm_rt = [\n            x,\n            y,\n            z\n        ];\n    } else {\n        $bm_rt = [\n            x,\n            y\n        ];\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 99.91, "s": [0]}, {"t": 123.88625504599, "s": [100]}], "ix": 2}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 10, "s": [0]}, {"t": 33.976251383881, "s": [100]}], "ix": 3}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 10, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 33.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 99.91, "s": [0]}, {"t": 123.88625504599, "s": [50]}], "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745112181, 0.219607844949, 1], "ix": 7}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 8}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 9}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843831, 0.807843148708, 0.486274510622, 1], "ix": 10}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 11}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 12}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"t": 909.100037028402, "s": [29.933]}], "ix": 2, "x": "var $bm_rt;\nvar numChar, idx, offsetFrame;\ntry {\n    numChar = 5;\n    idx = $bm_mul($bm_sub($bm_sub(index, parent.index), numChar), -1);\n    offsetFrame = $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Offset animation time'), idx);\n    $bm_rt = $bm_sub(value, framesToTime(Math.round(offsetFrame)));\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": 1000, "h": 1000, "ip": 10.0000004073083, "op": 177.000007209358, "st": 10.0000004073083, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "h 2 1", "parent": 1, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [139.887, 207.603, 0], "ix": 2, "x": "var $bm_rt;\nvar x, y, z;\ntry {\n    var per = $bm_div(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking center'), 100 / 4);\n    x = $bm_sum(value[0], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Tracking'), $bm_sub(0, per)));\n    $bm_rt = y = $bm_sum($bm_sum(value[1], $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Line spacing'), 0)), $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Y-Offset'), 0));\n    if (thisLayer.position.value.length > 2) {\n        z = $bm_sum(value[2], thisComp.layer('hello [Controller]').effect('Font Controller')('Z-Offset'));\n        $bm_rt = [\n            x,\n            y,\n            z\n        ];\n    } else {\n        $bm_rt = [\n            x,\n            y\n        ];\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [500, 500, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON>", "np": 17, "mn": "Pseudo/0vbarur0o9", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "Animation", "mn": "Pseudo/0vbarur0o9-0001", "ix": 1, "v": 0}, {"ty": 0, "nm": "Start", "mn": "Pseudo/0vbarur0o9-0002", "ix": 2, "v": {"a": 1, "k": [{"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886254638682, "s": [100]}], "ix": 2}}, {"ty": 0, "nm": "End", "mn": "Pseudo/0vbarur0o9-0003", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [0]}, {"t": 23.9762509765727, "s": [100]}], "ix": 3}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/0vbarur0o9-0004", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 0, "s": [-50]}, {"i": {"x": [0.25], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 23.976, "s": [0]}, {"i": {"x": [0.999], "y": [1]}, "o": {"x": [0.75], "y": [0]}, "t": 89.91, "s": [0]}, {"t": 113.886254638682, "s": [50]}], "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Color", "mn": "Pseudo/0vbarur0o9-0006", "ix": 6, "v": 0}, {"ty": 2, "nm": "Color 1", "mn": "Pseudo/0vbarur0o9-0007", "ix": 7, "v": {"a": 0, "k": [1, 0.262745112181, 0.219607844949, 1], "ix": 7}}, {"ty": 2, "nm": "Color 2", "mn": "Pseudo/0vbarur0o9-0008", "ix": 8, "v": {"a": 0, "k": [1, 0.419607847929, 0, 1], "ix": 8}}, {"ty": 2, "nm": "Color 3", "mn": "Pseudo/0vbarur0o9-0009", "ix": 9, "v": {"a": 0, "k": [1, 0.784313738346, 0.262745112181, 1], "ix": 9}}, {"ty": 2, "nm": "Color 4", "mn": "Pseudo/0vbarur0o9-0010", "ix": 10, "v": {"a": 0, "k": [0.019607843831, 0.807843148708, 0.486274510622, 1], "ix": 10}}, {"ty": 2, "nm": "Color 5", "mn": "Pseudo/0vbarur0o9-0011", "ix": 11, "v": {"a": 0, "k": [0, 0.701960802078, 0.890196084976, 1], "ix": 11}}, {"ty": 2, "nm": "Color 6", "mn": "Pseudo/0vbarur0o9-0012", "ix": 12, "v": {"a": 0, "k": [0.854901969433, 0.227450981736, 0.701960802078, 1], "ix": 12}}, {"ty": 7, "nm": "On white background", "mn": "Pseudo/0vbarur0o9-0013", "ix": 13, "v": {"a": 0, "k": 1, "ix": 13}}, {"ty": 7, "nm": "Use only Color 1", "mn": "Pseudo/0vbarur0o9-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 6, "nm": "", "mn": "Pseudo/0vbarur0o9-0015", "ix": 15, "v": 0}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 899.100036621094, "s": [29.933]}], "ix": 2, "x": "var $bm_rt;\nvar numChar, idx, offsetFrame;\ntry {\n    numChar = 5;\n    idx = $bm_mul($bm_sub($bm_sub(index, parent.index), numChar), -1);\n    offsetFrame = $bm_mul(thisComp.layer('hello [Controller]').effect('Font Controller')('Offset animation time'), idx);\n    $bm_rt = $bm_sub(value, framesToTime(Math.round(offsetFrame)));\n} catch (e) {\n    $bm_rt = value;\n}"}, "w": 1000, "h": 1000, "ip": 0, "op": 177.000007209358, "st": 0, "bm": 0}], "markers": []}