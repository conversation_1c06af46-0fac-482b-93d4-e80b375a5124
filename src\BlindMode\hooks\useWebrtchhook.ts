// useWebRTCStream.ts
import { useEffect, useRef, useState } from 'react';
import {
  mediaDevices,
  RTCPeerConnection,
  MediaStream,
  RTCRtpSender,
  RTCSessionDescription,
  registerGlobals,
} from 'react-native-webrtc';
import { Socket } from 'socket.io-client';
import { PermissionsAndroid } from 'react-native';
import { getVideoSummarySocket } from '../../socket-client/socket';
import RTCTrackEvent from 'react-native-webrtc/lib/typescript/RTCTrackEvent';
import InCallManager from 'react-native-incall-manager';

registerGlobals();

export type WebRTCStatus = 'Idle' | 'Connecting' | 'Connected' | 'Streaming' | 'Stopped' | 'Error';
export type GeneratedSummaryState = {
  summary: string;
  status: 'idle' | 'processing' | 'end';
};
export function useWebRTCStream() {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [status, setStatus] = useState<WebRTCStatus>('Idle');
  const [generatedSummaryState, setGeneratedSummaryState] = useState<GeneratedSummaryState>({
    summary: '',
    status: 'idle',
  });

  const summaryTextRef = useRef<string>('');
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const incoming_pc_ref = useRef<RTCPeerConnection | null>(null);
  const socketRef = useRef<Socket | null>(null);

  const logStatus = (s: WebRTCStatus) => {
    console.log(s);
    setStatus(s);
  };

  // 🔹 Request camera + mic permissions
  async function requestPermissions() {
    const granted = await PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.CAMERA,
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    ]);

    return (
      granted['android.permission.CAMERA'] === PermissionsAndroid.RESULTS.GRANTED &&
      granted['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED
    );
  }

  // 🔹 Setup local stream
  async function initStream() {
    const hasPerms = await requestPermissions();
    if (!hasPerms) {
      logStatus('Error');
      return;
    }

    const stream = await mediaDevices.getUserMedia({
      video: {
        facingMode: 'environment',
      },
      audio: false,
    });

    setLocalStream(stream);
  }

  function flipCamera() {
    if (localStream) {
      localStream.getVideoTracks()[0].applyConstraints({
        facingMode:
          localStream.getVideoTracks()[0].getSettings().facingMode === 'user'
            ? 'environment'
            : 'user',
      });
    }
  }

  function initSocketEvents(socket: Socket) {
    socket.on('summary_processing', () => {
      setGeneratedSummaryState((prev) => ({ ...prev, status: 'processing' }));
    });
    socket.on('summary_generated', (data) => {
      setGeneratedSummaryState({
        summary: summaryTextRef.current + data.summary,
        status: 'end',
      });
    });

    socket.on('summary_reading_ended', (data) => {
      setTimeout(() => {
        setGeneratedSummaryState({
          summary: '',
          status: 'idle',
        });
      }, 1000);
    });

    socket.on('renegotiation_offer', async (data) => {
      try {
        console.log('📡 Received renegotiation offer from server', data);

        // 1. Set server's offer as remote description
        await pcRef.current?.setRemoteDescription(new RTCSessionDescription(data));

        // 2. Create answer
        // const answer = await pcRef.current?.createAnswer();
        // await pcRef.current?.setLocalDescription(answer);

        // 3. Send answer back to server
        // socket.emit('renegotiation_answer', {
        //   sdp: answer.sdp,
        //   type: answer.type,
        // });

        console.log('✅ Renegotiation answer sent to server');
      } catch (err) {
        console.error('❌ Failed to handle renegotiation offer:', err);
      }
    });

    console.log('initialised socket events');
  }

  // 🔹 Connect socket
  function connectSocket() {
    const socket = getVideoSummarySocket();

    socket.on('connect', () => {
      logStatus('Connected');
      initSocketEvents(socket);
      socket.onAny((event, ...args) => {
        console.log(event, args, ' --------- incoming');
      });
      socket.onAnyOutgoing((event, ...args) => {
        console.log(event, args, ' --------- outgoing');
      });
    });

    socket.on('error', () => {
      logStatus('Error');
    });

    socket.on('answer', async (data: RTCSessionDescription) => {
      if (pcRef.current) {
        await pcRef.current.setRemoteDescription(data);
      }
    });

    socket.on('incoming_offer', async (data) => {
      try {
        if (data && data.replace_connection && incoming_pc_ref.current != null) {
          incoming_pc_ref.current.close();
          incoming_pc_ref.current = null;
        }
        const rtc_description = new RTCSessionDescription(data);
        incoming_pc_ref.current = new RTCPeerConnection();

        incoming_pc_ref.current.addEventListener('icecandidate', (event) => {
          if (event.candidate) {
            console.log('Local ICE candidate dfsff sdf', event.candidate);
          }
        });
        incoming_pc_ref.current.addEventListener('connectionstatechange', (event) => {
          console.log('connectionstatechangse', event.state);
        });

        // 👇 Handle incoming tracks (very important)
        incoming_pc_ref.current.addEventListener('track', (event) => {
          console.log('🎵 Got remote track', event.state);
        });

        await incoming_pc_ref.current.setRemoteDescription(rtc_description);

        const answer = await incoming_pc_ref.current.createAnswer();
        await incoming_pc_ref.current.setLocalDescription(answer);

        socket.emit('incoming_answer', {
          sdp: answer.sdp,
          type: answer.type,
        });
      } catch (err) {
        console.error('⚠️ Error handling incoming_offer:', err);
      }
    });

    socketRef.current = socket;
  }

  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  // 🔹 Start streaming
  async function startStreaming() {
    try {
      if (!socketRef.current || !socketRef.current.connected) {
        console.error('Socket not connected');
        return;
      }

      if (!localStream) {
        console.error('Local stream not ready');
        return;
      }

      pcRef.current = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      });

      pcRef.current.addEventListener('icecandidate', (event) => {
        if (event.candidate) {
          console.log('Local ICE candidate dfsff sdf', event.candidate);
        }
      });

      pcRef.current.addEventListener('track', (event: RTCTrackEvent<'track'>) => {
        console.log(' track recieved ==============', event.track?.kind);
        if (event.track?.kind === 'audio') {
          const remoteStream = new MediaStream();
          remoteStream.addTrack(event.track);
          setRemoteStream(remoteStream);
        }
        event.track?.addEventListener('ended', () => {
          console.log('Track ended');
        });
      });

      // Attach tracks
      for (const track of localStream.getTracks()) {
        const sender: RTCRtpSender = pcRef.current.addTrack(track, localStream);
        const params = sender.getParameters();
        sender.setParameters(params);
      }

      const offer = await pcRef.current.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true,
        voiceActivityDetection: true,
      });

      await pcRef.current.setLocalDescription(offer);

      socketRef.current?.emit('offer', {
        sdp: offer.sdp,
        type: offer.type,
      });

      logStatus('Streaming');
    } catch (e) {
      console.log(e);
    }
  }

  // 🔹 Stop streaming
  function stopStreaming() {
    if (pcRef.current) {
      pcRef.current.getSenders().forEach((sender: RTCRtpSender) => {
        pcRef.current?.removeTrack(sender);
      });
      pcRef.current.close();
      pcRef.current = null;
    }

    socketRef.current?.emit('stop_stream');
    logStatus('Stopped');
    setGeneratedSummaryState({
      summary: '',
      status: 'idle',
    });
  }

  // 🔹 Cleanup on unmount
  useEffect(() => {
    connectSocket();
    initStream();
    InCallManager.start({ media: 'video' });
    InCallManager.setSpeakerphoneOn(true);
    return () => {
      socketRef.current?.disconnect();
      stopStreaming();
    };
  }, []);

  return {
    localStream,
    status,
    startStreaming,
    stopStreaming,
    flipCamera,
    generatedSummaryState,
  };
}
