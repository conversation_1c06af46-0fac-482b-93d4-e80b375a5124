import * as React from "react";
import Svg, { Path } from "react-native-svg";
import { colors } from "../../theme/colors";
import { StyleSheet, View } from "react-native";

function StoryIconSVG({ width = 30, height = 29, color = colors.mainPurple, isAnyNewStory = false, containerStyle = {} }) {


    return (
        <View style={[styles.containerStyle, containerStyle ? containerStyle : null]}>
            <Svg
                width={width}
                height={height}
                viewBox="0 0 30 29"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <Path
                    d="M18.266 2.042c5.512 1.519 9.56 6.567 9.56 12.563 0 7.198-5.832 13.03-13.03 13.03-6.352 0-11.641-4.55-12.796-10.56M2.168 10.274c1.424-4.602 5.26-7.965 9.832-8.9"
                    stroke={color}
                    strokeWidth={2.53542}
                    strokeLinecap="round"
                />
            </Svg>

            {isAnyNewStory ? <View style={styles.dotView} /> : null}
        </View>
    );
}

export default React.memo(StoryIconSVG);

const styles = StyleSheet.create({
    containerStyle: {
        width: 50,
        height: 50,
        backgroundColor: colors.white,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 25,
        elevation: 4
    },
    dotView: {
        height: 8,
        width: 8,
        borderRadius: 4,
        backgroundColor: colors.mainPurple,
        position: 'absolute',
        top: 5,
        right: 2
    }
})
