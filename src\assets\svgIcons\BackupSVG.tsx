import React from 'react';
import { Svg, Path } from 'react-native-svg';

interface BackupSVGProps {
  size?: number;
  color?: string;
}

const BackupSVG: React.FC<BackupSVGProps> = ({ size = 20, color = '#232323' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 21 17" fill="none">
      <Path
        d="M17.1176 5.34396C16.168 1.56131 12.3319 -0.735363 8.54925 0.21417C5.59321 0.956231 3.44679 3.5111 3.22563 6.5508C1.12672 6.89693 -0.294211 8.87901 0.0519234 10.9779C0.359621 12.8439 1.97647 14.2104 3.86759 14.2029H7.07737V12.919H3.86759C2.44943 12.919 1.29977 11.7693 1.29977 10.3512C1.29977 8.93302 2.44943 7.78335 3.86759 7.78335C4.22215 7.78335 4.50954 7.49596 4.50954 7.1414C4.50633 3.95051 7.09049 1.36118 10.2814 1.35802C13.0435 1.35525 15.4213 3.308 15.9556 6.01797C16.0084 6.28856 16.228 6.49527 16.5013 6.53154C18.2563 6.78146 19.4764 8.40677 19.2265 10.1618C19.0021 11.7377 17.6566 12.9111 16.0648 12.919H13.4969V14.2029H16.0648C18.5466 14.1954 20.5523 12.1774 20.5448 9.69562C20.5385 7.62972 19.1245 5.83421 17.1176 5.34396Z"
        fill={color}
      />
      <Path
        d="M9.83198 7.96794L7.26416 10.5358L8.16932 11.4409L9.64582 9.97084V16.1272H10.9297V9.97084L12.3998 11.4409L13.305 10.5358L10.7371 7.96794C10.4867 7.71902 10.0824 7.71902 9.83198 7.96794Z"
        fill={color}
      />
    </Svg>
  );
};

export default BackupSVG; 