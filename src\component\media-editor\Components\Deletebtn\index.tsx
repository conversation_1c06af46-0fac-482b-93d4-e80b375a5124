import React, { FC } from 'react';
import { StyleSheet, View, Image, Text } from 'react-native';
import { hp, wp } from '../../utils/Constants/dimensionUtils';
interface DeletebtnProps {
    isDeleteable: boolean
}

const Deletebtn: FC<DeletebtnProps> = ({ isDeleteable }) => {
    const styles = myStyles(isDeleteable)
    return (
        <View style={styles.container}>
            <View style={styles.parent}>
                <Text style={styles.textStyle}>Drag to delete</Text>
                <View style={styles.btnView}>
                    <Image source={require('../../Assets/Images/Bin.png')} style={styles.binIcon} tintColor={isDeleteable ? 'red' : '#FFF'} />
                </View>
            </View>
        </View>
    );
};

const myStyles = (isDeleteable: boolean) => StyleSheet.create({
    container: {
        width: 100,
        position: 'absolute',
        bottom: 160,
        alignSelf: 'center',
        alignContent: 'center',
        zIndex:205,
    },
    parent: {
        alignItems: 'center',
        justifyContent: 'center',

    },
    textStyle: {
        color: isDeleteable ? 'red' : '#FFF'
    },
    btnView: {
        width: 50,
        height: 50,
        borderRadius: 25,
        borderWidth: 2,
        borderColor: isDeleteable ? 'red' : '#FFF',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10
    },
    binIcon: {
        width: wp(8),
        height: wp(8),
        tintColor: 'white',
    },

});

export default Deletebtn;
