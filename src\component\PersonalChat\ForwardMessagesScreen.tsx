import React, { useState } from 'react';
import {
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useContacts } from '../../hooks/contacts/useContacts';
import ContactItem from '../ContactItem';
import { ChatService, ForwardMsgPayload } from '../../service/ChatService';
import { ConversationType, IMessage } from '../../device-storage/realm/schemas/MessageSchema';
import SendSVG from '../../assets/svgIcons/SendSVG';
import { colors } from '../../theme/colors';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import SearchInput from '../SearchInput';
import { hp } from '../../theme/fonts';
import HeaderBackWithTitle from '../HeaderBackWithTitle';
import { zIndex } from '../../utils/Filters';
import { BlockedUserData } from '../../screens/Profile/Settings/Privacy/BlockedPeopleScreen';
import { IMAGES } from '../../assets/Images';
import SelectionSVG from '../../assets/svgIcons/SelectionSVG';
import { errorToast, showToast } from '../../utils/commonFunction';
import useChatspaces from '../../device-storage/realm/hooks/useChatspaces';

type ForwardMessagesScreenParams = {
  ForwardMessagesScreen: {
    messages: IMessage[];
    onForwardComplete?: () => void;
  };
};

const ForwardMessagesScreen = () => {
  const { registeredContacts } = useContacts();
  const { allGroups } = useChatspaces();
  const groups = allGroups?.filter((item) => item.type === ConversationType.GROUP);
  const route = useRoute<RouteProp<ForwardMessagesScreenParams, 'ForwardMessagesScreen'>>();
  const navigation = useNavigation();
  const messages = route.params.messages;
  const onForwardComplete = route.params.onForwardComplete;
  const blocked = ChatService.getBlockedEntities();
  const blockedUsers = (blocked.data as unknown as BlockedUserData[]) || [];
  const blockedSet = new Set(blockedUsers?.map((user) => user.id));
  const [activeTab, setActiveTab] = useState<'contacts' | 'groups'>('contacts');
  const [search, setSearch] = useState('');
  const [selectedRecipients, setSelectedRecipients] = useState<ForwardMsgPayload[]>([]);

  const handleSelect = (receiverId: string, conversationType: ConversationType) => {
    if (blockedSet.has(receiverId)) {
      errorToast('Cannot forward messages to this contact because they are blocked');
      return;
    }

    setSelectedRecipients((prev) => {
      const isSelected = prev.find((item) => item.receiverId === receiverId);
      if (isSelected) {
        return prev.filter((item) => item.receiverId !== receiverId);
      }
      if (prev.length >= 5) {
        showToast('You can only select up to 5 recipients');
        return prev;
      }
      return [...prev, { receiverId, conversationType }];
    });
  };

  const handleForwardMessages = () => {
    if (selectedRecipients.length > 0) {
      ChatService.forwardMessages(messages, selectedRecipients);
    }

    if (onForwardComplete) {
      onForwardComplete();
    }

    navigation.pop(2);
  };

  const filteredContactList = registeredContacts?.filter((contact) =>
    contact.name.toLowerCase().includes(search.toLowerCase()),
  );

  const filteredGroupsList = groups?.filter((group) =>
    group.name.toLowerCase().includes(search.toLowerCase()),
  );

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Forward To" />
      <View style={styles.whiteContainer}>
        <View style={styles.searchWrapper}>
          <SearchInput value={search} onChangeText={setSearch} />
        </View>

        {/* Tabs */}
        <View style={styles.tabsWrapper}>
          <TouchableOpacity onPress={() => setActiveTab('contacts')}>
            <Text style={[styles.tabText, activeTab === 'contacts' && styles.tabTextActive]}>
              Contacts
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setActiveTab('groups')}>
            <Text style={[styles.tabText, activeTab === 'groups' && styles.tabTextActive]}>
              Groups
            </Text>
          </TouchableOpacity>
        </View>

        {/* Contacts List */}
        <View style={{ flex: 1, display: activeTab === 'contacts' ? 'flex' : 'none' }}>
          <FlatList
            data={filteredContactList || []}
            keyExtractor={(item) => item.id || ''}
            style={styles.list}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            renderItem={({ item }) => {
              const isBlocked = blockedSet.has(item.id ?? '');
              return (
                <ContactItem
                  data={item}
                  onPress={() => item.id && handleSelect(item.id, ConversationType.P2P)}
                  isSelected={selectedRecipients.some(
                    (selected) => selected.receiverId === item.id,
                  )}
                  isSelectionMode={true}
                  isBlocked={isBlocked}
                />
              );
            }}
          />
        </View>

        {/* Groups List */}
        <View style={{ flex: 1, display: activeTab === 'groups' ? 'flex' : 'none' }}>
          <View style={{ flex: 1, display: activeTab === 'groups' ? 'flex' : 'none' }}>
            <FlatList
              data={filteredGroupsList}
              keyExtractor={(item) => item.id}
              style={styles.list}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContent}
              renderItem={({ item }) => {
                const isSelected = selectedRecipients.some(
                  (list) =>
                    list.receiverId === item.id && list.conversationType === ConversationType.GROUP,
                );
                return (
                  <TouchableOpacity
                    key={item.id}
                    activeOpacity={1}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'flex-start',
                      backgroundColor: colors.white,
                      paddingHorizontal: 20,
                      paddingVertical: 15,
                    }}
                    onPress={() => item.id && handleSelect(item.id, ConversationType.GROUP)}
                  >
                    {/* Avatar */}
                    <View style={{ position: 'relative' }}>
                      {item.displayPic ? (
                        <Image
                          source={{ uri: item.displayPic }}
                          style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                      ) : (
                        <Image
                          source={IMAGES.profile_image}
                          style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                      )}
                    </View>

                    <View style={{ flex: 1 }}>
                      <Text
                        numberOfLines={1}
                        style={{ color: colors.black, fontSize: 16, fontWeight: 'bold' }}
                      >
                        {item.name}
                      </Text>

                      <Text
                        numberOfLines={1}
                        style={{
                          color: colors.gray_80,
                          fontSize: 14,
                          fontWeight: '400',
                          marginTop: 5,
                        }}
                      >
                        {item?.description}
                      </Text>
                    </View>

                    {isSelected && (
                      <View style={styles.checkWrapper}>
                        {isSelected && <SelectionSVG size={24} isSelected={isSelected} />}
                      </View>
                    )}
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>

        {/* Floating Button */}
        {selectedRecipients.length >= 1 && (
          <View style={styles.floatingButtonWrapper}>
            <TouchableOpacity onPress={handleForwardMessages}>
              <SendSVG size={60} color={colors.white} backgroundColor={colors.mainPurple} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default ForwardMessagesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
  searchWrapper: {
    paddingHorizontal: hp(2),
    paddingVertical: hp(2),
  },
  tabsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  tabText: {
    fontWeight: '500',
    fontSize: 15,
    color: colors.gray_80,
    paddingBottom: 4,
  },
  tabTextActive: {
    fontWeight: '600',
    color: colors.mainPurple,
    borderBottomWidth: 2,
    borderBottomColor: colors.mainPurple,
  },
  list: {
    flex: 1,
    padding: hp(1),
  },
  listContent: {
    paddingBottom: 100,
  },
  checkWrapper: {
    position: 'absolute',
    right: hp(1),
    top: '80%',
    transform: [{ translateY: -18 }],
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  floatingButtonWrapper: {
    position: 'absolute',
    bottom: 50,
    right: 20,
    zIndex: zIndex.level_2,
  },
});
