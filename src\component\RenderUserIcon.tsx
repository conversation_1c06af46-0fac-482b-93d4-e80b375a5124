import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Image, View, ViewStyle, ImageStyle } from 'react-native';
import { IMAGES } from '../assets/Images';

type Props = {
  size: number;
  url?: string;
  activeOpacity?: number;
  userId?: string | number;
  image?: any;
  containerStyle?: ViewStyle;
  imageStyle?: ImageStyle;
};

const RenderUserIcon = ({
  size,
  url,
  activeOpacity,
  userId = undefined,
  image,
  containerStyle,
  imageStyle,
}: Props) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  const isUrl = !!url && typeof url === 'string' && url.includes('https');

  const styles = StyleSheet.create({
    userImage: {
      height: size,
      width: size,
      borderRadius: size / 2,
    },
  });

  const onOpenUserDetail = () => {};

  return (
    <TouchableOpacity
      disabled={!userId}
      activeOpacity={activeOpacity}
      onPress={onOpenUserDetail}
      style={{
        height: size,
        width: size,
        borderRadius: size / 2,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        ...containerStyle,
      }}
    >
      {/* Placeholder always visible behind */}
      <Image
        resizeMode="cover"
        source={image || IMAGES.profile_image}
        style={{ ...styles.userImage, ...imageStyle, position: 'absolute' }}
      />

      {/* URL Image — visible only after loaded */}
      {isUrl && (
        <Image
          resizeMode="cover"
          source={{ uri: url }}
          style={{ ...styles.userImage, ...imageStyle, opacity: isImageLoaded ? 1 : 0 }}
          onLoadEnd={() => setIsImageLoaded(true)}
        />
      )}
    </TouchableOpacity>
  );
};

export default RenderUserIcon;

const styless = StyleSheet.create({
  updateIcon: {
    height: 30,
    width: 30,
    bottom: -20,
    position: 'absolute',
    alignSelf: 'center',
    zIndex: 1000,
  },
});
