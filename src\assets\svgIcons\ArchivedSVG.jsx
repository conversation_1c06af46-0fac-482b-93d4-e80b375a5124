import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"
import { colors } from "../../theme/colors"


const ArchivedSVG = ({
    size = 24,
    color = colors.black_23,
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={(size * 19) / 21} // Maintain aspect ratio
            viewBox="0 0 21 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M9.021.864l.087.005a.71.71 0 01.25.079l.074.046a.71.71 0 01.13.116l.052.07 1.628 2.445h6.843l.267.014a2.695 2.695 0 011.636.776l.179.197c.393.479.61 1.081.61 1.706v9.843l-.013.265a2.696 2.696 0 01-.597 1.44l-.179.198a2.695 2.695 0 01-1.636.775l-.267.014H3.64a2.695 2.695 0 01-1.705-.61l-.199-.179a2.696 2.696 0 01-.789-1.903V3.556l.014-.265a2.696 2.696 0 01.775-1.638l.199-.179A2.695 2.695 0 013.64.864H9.02zM3.515 2.29c-.25.024-.487.123-.681.282l-.094.084a1.274 1.274 0 00-.373.9V16.16l.007.126c.03.29.158.564.366.773l.094.084c.226.186.51.289.806.29h14.445l.126-.008c.291-.029.565-.157.773-.366l.084-.094c.186-.226.29-.51.29-.805V6.318l-.007-.126a1.274 1.274 0 00-1.14-1.14l-.126-.007h-7.223A.71.71 0 0110.606 5l-.08-.037a.71.71 0 01-.204-.163l-.052-.069-1.628-2.446H3.64l-.125.007z"
                fill={color}
            />
            <Path
                d="M10.861 7.287a.71.71 0 01.502.208l.09.108a.71.71 0 01.118.394v4.749l1.55-1.55.108-.086a.711.711 0 01.392-.113l.137.014a.71.71 0 01.367 1.189v0l-2.762 2.762a.708.708 0 01-.231.153h0a.701.701 0 01-.41.042l-.132-.041a.71.71 0 01-.123-.066l-.108-.088L7.597 12.2a.71.71 0 01-.187-.362l-.013-.137a.71.71 0 01.208-.496l.108-.088a.71.71 0 01.389-.12l.137.012a.71.71 0 01.253.1l.109.087h0l1.55 1.55v-4.75l.014-.138a.71.71 0 01.194-.363l.108-.09a.711.711 0 01.394-.118z"
                fill={color}
            />
        </Svg>
    )
}

export default ArchivedSVG

