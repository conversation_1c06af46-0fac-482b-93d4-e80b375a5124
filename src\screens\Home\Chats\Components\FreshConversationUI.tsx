import { StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import { colors } from '../../../../theme/colors';
import { hexToRgba, hp } from '../../../../theme/fonts';
import LockSVG from '../../../../assets/svgIcons/LockSVG';
import LottieView from 'lottie-react-native';
import EndToEndEncryptedModal from '../ChatModals/EndToEndEncryptedModal';


interface IFreshProps {
}


const FreshConversationUI = ({ }: IFreshProps) => {

    const [learnMoreVisible, setLearnMoreVisible] = useState<boolean>(false);

    return (
        <View>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 20,
                    paddingVertical: 12,
                    backgroundColor: colors.white,
                    borderRadius: 10,
                    gap: 12,
                    margin: hp(2),
                }}
            >
                <LockSVG size={20} color={colors.black_23} />
                <Text
                    style={{
                        fontSize: 14,
                        color: colors.black,
                        fontWeight: '400',
                        flexShrink: 1,
                        lineHeight: 18,
                    }}
                >
                    Messages and call are end-to-end encrypted.
                    <Text onPress={() => setLearnMoreVisible(true)} style={{ color: colors.mainPurple }}>
                        {' '}
                        Tap to learn more.
                    </Text>
                </Text>
            </View>

            <View
                style={{
                    backgroundColor: hexToRgba(colors._6247AD_purple, 0.1),
                    height: 199,
                    width: 192,
                    borderRadius: 15,
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignSelf: 'center',
                    marginTop: 120,
                }}
            >
                <LottieView
                    source={require('../../../.././assets/animatedLottiImages/Friday_cocktail.json')}
                    autoPlay
                    loop
                    style={{ width: 150, height: 150 }}
                />

                <Text
                    style={{
                        color: colors.black,
                        fontSize: 16,
                        fontWeight: '600',
                        alignSelf: 'center',
                        marginTop: 12,
                    }}
                >
                    It's raining out..
                </Text>
            </View>

            <EndToEndEncryptedModal
                isVisible={learnMoreVisible}
                onClose={() => setLearnMoreVisible(false)}
            />
        </View>
    )
}

export default FreshConversationUI

const styles = StyleSheet.create({})