import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const BioSVG = ({ color = '#232323', ...props }) => (
  <Svg width={22} height={22} viewBox="0 0 19 16" fill="none" {...props}>
    <Path d="M12.3838 0.900391C12.9464 0.900513 13.486 1.12366 13.8838 1.52148C14.2816 1.91932 14.5048 2.45888 14.5049 3.02148V5.54688L14.5039 5.66211C14.4995 5.70056 14.4908 5.73849 14.4785 5.77539C14.454 5.84884 14.4144 5.91633 14.3623 5.97363C14.3102 6.03091 14.2466 6.07705 14.1758 6.1084C14.1048 6.13975 14.0277 6.15625 13.9502 6.15625C13.8728 6.15624 13.7964 6.13964 13.7256 6.1084C13.6547 6.07704 13.5903 6.03099 13.5381 5.97363C13.4861 5.91635 13.4464 5.8488 13.4219 5.77539C13.3974 5.70189 13.3882 5.62402 13.3955 5.54688V3.02148C13.3955 2.89054 13.3696 2.76058 13.3193 2.63965C13.2691 2.5187 13.1953 2.40886 13.1025 2.31641C13.0097 2.22392 12.8995 2.15042 12.7783 2.10059C12.6875 2.06325 12.5916 2.03957 12.4941 2.03027L12.3955 2.02637H3.02051C2.88903 2.02495 2.75863 2.04937 2.63672 2.09863C2.51471 2.14799 2.40317 2.22095 2.30957 2.31348C2.21592 2.4061 2.14156 2.51712 2.09082 2.63867C2.04023 2.75999 2.01372 2.89004 2.01367 3.02148V13.6924C2.01367 13.8241 2.04009 13.9546 2.09082 14.0762C2.14155 14.1977 2.21596 14.3078 2.30957 14.4004C2.40322 14.493 2.51462 14.5658 2.63672 14.6152C2.75867 14.6645 2.88898 14.6899 3.02051 14.6885H9.13574C9.28345 14.6885 9.42582 14.7471 9.53027 14.8516C9.63455 14.956 9.69334 15.0975 9.69336 15.2451C9.69336 15.3927 9.63452 15.5343 9.53027 15.6387C9.42583 15.7431 9.28346 15.8018 9.13574 15.8018H3.02148C2.45887 15.8016 1.91932 15.5785 1.52148 15.1807C1.12365 14.7828 0.900505 14.2433 0.900391 13.6807V3.02148C0.900502 2.45887 1.12365 1.91932 1.52148 1.52148C1.91932 1.12365 2.45887 0.900502 3.02148 0.900391H12.3838Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M11.5435 5.53906L11.645 5.55859C11.7435 5.5869 11.8329 5.64199 11.9028 5.71875C11.9962 5.82129 12.0483 5.9551 12.0483 6.09375C12.0483 6.23237 11.9961 6.36623 11.9028 6.46875C11.8095 6.57113 11.6814 6.63542 11.5435 6.64844L11.5347 6.64941V6.64844H3.85107V6.64746C3.77688 6.65323 3.702 6.64565 3.63135 6.62207C3.55794 6.59753 3.49036 6.55794 3.43311 6.50586C3.37583 6.45374 3.32969 6.39015 3.29834 6.31934C3.267 6.24843 3.25148 6.17128 3.25146 6.09375C3.25146 6.0162 3.26698 5.9391 3.29834 5.86816C3.32969 5.7973 3.37579 5.7338 3.43311 5.68164C3.49037 5.62954 3.55793 5.58999 3.63135 5.56543C3.702 5.54185 3.77687 5.53427 3.85107 5.54004V5.53906H11.5435Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M8.08887 9.08398C8.23657 9.08398 8.37797 9.14263 8.48242 9.24707C8.58687 9.35152 8.64551 9.49291 8.64551 9.64062C8.64549 9.78831 8.58685 9.92975 8.48242 10.0342C8.37798 10.1386 8.23653 10.1973 8.08887 10.1973H3.86328C3.71561 10.1973 3.57417 10.1386 3.46973 10.0342C3.3653 9.92975 3.30666 9.78831 3.30664 9.64062C3.30664 9.49291 3.36528 9.35152 3.46973 9.24707C3.57418 9.14262 3.71557 9.08398 3.86328 9.08398H8.08887Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M14.2837 11.9746C15.1964 11.9746 16.1479 12.2316 16.8735 12.7705C17.6022 13.3117 18.1001 14.1354 18.1001 15.2529V15.2617C18.087 15.4008 18.0218 15.53 17.9185 15.624C17.8152 15.718 17.6811 15.7704 17.5415 15.7705C17.4018 15.7705 17.2669 15.7181 17.1636 15.624C17.0604 15.53 16.996 15.4007 16.9829 15.2617L16.9819 15.2529L16.978 15.1172C16.9382 14.4528 16.6058 13.9628 16.1284 13.6299C15.6158 13.2724 14.9348 13.0957 14.2749 13.0918C13.6149 13.0879 12.917 13.2559 12.3843 13.6123C11.8549 13.9666 11.4876 14.5073 11.4761 15.2627C11.4734 15.4086 11.4142 15.5481 11.3101 15.6504C11.2059 15.7527 11.0654 15.8096 10.9194 15.8096C10.7732 15.8096 10.6326 15.7524 10.5278 15.6504C10.4232 15.5484 10.3628 15.4097 10.3589 15.2637V15.2588C10.3757 14.1761 10.8738 13.3519 11.6069 12.8008C12.3377 12.2515 13.2995 11.9746 14.2515 11.9746H14.2837Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M13.8032 6.83496C14.2424 6.74696 14.6978 6.79097 15.1118 6.96191C15.5259 7.13298 15.8803 7.4235 16.1294 7.7959C16.3783 8.16816 16.5112 8.60588 16.5112 9.05371C16.5112 9.65255 16.2737 10.2271 15.8511 10.6514C15.4284 11.0756 14.8552 11.3151 14.2563 11.3174C13.8084 11.3191 13.3699 11.1881 12.9966 10.9404C12.6232 10.6927 12.3323 10.3392 12.1597 9.92578C11.9872 9.51252 11.9406 9.0576 12.0269 8.61816C12.1132 8.17874 12.3281 7.77436 12.644 7.45703C12.9602 7.13965 13.3639 6.92299 13.8032 6.83496ZM14.1997 7.72656C13.9556 7.73543 13.7176 7.80824 13.5132 7.94336C13.2935 8.08871 13.1219 8.29612 13.02 8.53906C12.9181 8.78223 12.8904 9.05079 12.9409 9.30957C12.9915 9.56821 13.1184 9.80628 13.3042 9.99316C13.4901 10.1799 13.7275 10.3074 13.9858 10.3594C14.2441 10.4112 14.5119 10.3855 14.7554 10.2852C14.9989 10.1847 15.2074 10.0138 15.354 9.79492C15.5006 9.5758 15.5796 9.31738 15.5796 9.05371C15.5795 8.70252 15.4398 8.36593 15.1919 8.11719C14.9438 7.86835 14.6068 7.72756 14.2554 7.72656H14.1997Z" fill={color} stroke={color} strokeWidth={0.2}/>
  </Svg>
);

export default BioSVG; 