import React, { useEffect, useState } from 'react';
import { IUser } from '../types/index.types';
import { UserPreferencesState, useUserPreferences } from './UserPrefrences';
import { Client } from '../lib/Client';
import { UserPreferencesService } from '../service/UserService';
import { clearRealmData } from '../device-storage/realm/realm';
import { getMe } from '../utils/userApiService';
import { clearSocket } from '../socket-client/socket';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const USER_KEY = 'cb_user_me';

export type UserContextType = {
  user: IUser | null;
  updateUser: (user: IUser) => void;
  userPreferencesState: UserPreferencesState;
  refetchUser: () => void;
  clearUserData: () => Promise<void>;
  isAudioPlaying?: boolean,
  pauseAudioPlaying?: (value?: boolean) => void;
};
export const UserContext = React.createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<IUser | null>(null);
  const userPreferencesState = useUserPreferences(user);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState<boolean>(false);

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Fetches the user's data from the server, and updates the local
   * state with the result. If the fetch fails, the error is stored
   * in the local state.
   */
  /*******  8213b9f6-093d-4665-9607-327e95e140ac  *******/
  const fetchUser = async () => {
    setLoading(true);
    setError(null);
    try {
      const user = await getMe();
      setUser(user);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (updatedUser: IUser) => {
    setUser(updatedUser);
    await Client.User.set(updatedUser);
  };

  const refetchUser = async () => {
    setLoading(true);
    setError(null);
    try {
      const user = await getMe(true);
      setUser(user);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  const clearUserData = async () => {
    await Client.User.clear();
    await Client.AuthToken.clear();
    await Client.LastSyncedDate.clear();
    await UserPreferencesService.clearUserPref();
    userPreferencesState.setUserPreferences(null);
    await AsyncStorage.clear()
    // Realm data will be cleared in the signin page. (Find a way to clear it here)
    clearRealmData();
    setUser(null);
    clearSocket();
  };

  const pauseAudioPlaying = (value?: boolean) => {
    setIsAudioPlaying(value as boolean);
  }


  useEffect(() => {
    fetchUser();
  }, []);

  return (
    <UserContext.Provider
      value={{ user, updateUser, userPreferencesState, refetchUser, clearUserData, isAudioPlaying, pauseAudioPlaying }}
    >
      {children}
    </UserContext.Provider>
  );
};
