import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ButtonPurple from '../../../../component/ButtonPurple';
import ModalWrapper from '../../../../component/ModalWrapper';
import CustomDatePicker from '../../../../component/PersonalChat/CustomDatePicker';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import PinSVG from '../../../../assets/svgIcons/PinSVG';
import GoToSVG from '../../../../assets/svgIcons/GoToSVG';

type IProps = {
  isVisible: boolean;
  onClose: () => void;
  onUnPin?: (scheduledAt?: any) => void;
  onGoToMessage?: () => void;
  canUnpin?: boolean;
};

const UnPinModal = ({
  isVisible,
  onClose,
  onUnPin = () => {},
  onGoToMessage = () => {},
  canUnpin = true, 
}: IProps) => {
  return (
    <ModalWrapper
      isVisible={isVisible}
      onCloseModal={() => {
        onClose();
      }}
    >
      <View style={{ paddingHorizontal: hp(1) }}>
        <Text style={styles.unpin}>Options</Text>

        {canUnpin && (
          <TouchableOpacity
            onPress={() => {
              onUnPin();
            }}
            style={[styles.rowView]}
          >
            <PinSVG size={20} style={{ transform: [{ rotate: '-45deg' }], marginRight: -5 }} />
            <Text style={styles.titleItem}>Unpin</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={() => {
            onGoToMessage();
          }}
          style={styles.rowView}
        >
          <GoToSVG size={14} />
          <Text style={styles.titleItem}>Go to message</Text>
        </TouchableOpacity>
      </View>
    </ModalWrapper>
  );
};

export default UnPinModal;

const styles = StyleSheet.create({
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  unpin: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '600',
    marginBottom: 20,
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 16,
  },
  titleItem: {
    // ...commonFontStyle(400, 16, colors.black_23),
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
});
