import React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SingleTickSVGProps extends SvgProps {
    size?: number;
    color?: string;
}

const SingleTickSVG: React.FC<SingleTickSVGProps> = ({
    size = 12,
    color = "#B4ABC0",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={(size * 10) / 12} // maintain original aspect ratio 12:10
            viewBox="0 0 12 10"
            fill="none"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.194.175a.728.728 0 01.065 1.002l-7.183 8.52a.676.676 0 01-.517.244.676.676 0 01-.516-.243L.169 6.289a.728.728 0 01.066-*********** 0 01.967.067L3.56 8.151 10.226.243a.67.67 0 01.968-.068z"
                fill={color}
            />
        </Svg>
    );
};

export default SingleTickSVG;
