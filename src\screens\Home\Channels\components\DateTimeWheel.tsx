import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import TimeWheel from './TimeWheel';
import AmPmWheel from './AmPmWheel';

interface DateTimeWheelProps {
  date: Date;
  onChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  displayFormat?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  locale?: string;
  label?: string;
  showHeader?: boolean;
  isScheduling?: boolean;
  onScheduleValidation?: (isFuture: boolean, selected: Date) => void;
}

function getClockTime(date: Date) {
  const hours = date.getHours();
  if (hours === 0) {
    return { hours: 12, isAM: true };
  }
  if (hours === 12) {
    return { hours: 12, isAM: false };
  }
  if (hours > 12) {
    return { hours: hours - 12, isAM: false };
  }
  return { hours: hours, isAM: true };
}

const DateTimeWheel: React.FC<DateTimeWheelProps> = ({
  date,
  onChange,
  minimumDate,
  maximumDate,
  locale,
  label,
}) => {
  const initialDate = date || new Date();
  const initialClock = getClockTime(initialDate);
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);
  const [selectedHour, setSelectedHour] = useState<number>(
    initialClock.hours === 0 ? 12 : initialClock.hours,
  );
  const [selectedMinute, setSelectedMinute] = useState<number>(initialDate.getMinutes());
  const [amOrpm, setAmorPm] = useState<'AM' | 'PM'>(initialClock.isAM ? 'AM' : 'PM');
  const [showPicker, setShowPicker] = useState(false);

  const getCombinedDate = (baseDate: Date, hour: number, minute: number, ampm: 'AM' | 'PM') => {
    let h = hour;
    if (ampm === 'PM' && h !== 12) h += 12;
    if (ampm === 'AM' && h === 12) h = 0;
    const newDate = new Date(baseDate);
    newDate.setHours(h, minute, 0, 0);
    return newDate;
  };

  useEffect(() => {
    const combined = getCombinedDate(selectedDate, selectedHour, selectedMinute, amOrpm);
    onChange(combined);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDate, selectedHour, selectedMinute, amOrpm]);

  useEffect(() => {
    if (date) {
      setSelectedDate(date);
      const clock = getClockTime(date);
      setSelectedHour(clock.hours === 0 ? 12 : clock.hours);
      setSelectedMinute(date.getMinutes());
      setAmorPm(clock.isAM ? 'AM' : 'PM');
    }
  }, [date]);

  const handleDateChange = (event: DateTimePickerEvent, pickedDate?: Date) => {
    if (Platform.OS === 'android') setShowPicker(false);
    if (pickedDate) {
      setSelectedDate(pickedDate);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString(locale || 'en-GB', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <View style={styles.container}>
      {/* Label at the top */}
      <Text style={{ fontSize: 16, color: '#888', alignSelf: 'flex-start', marginBottom: 8 }}>
        {label || 'Set date and time'}
      </Text>
      <View style={{ flexDirection: 'row', gap: 10, marginBottom: 16 }}>
        {/* Single Date Dropdown (opens DateTimePicker) with up/down icons */}
        <DateButton
          label={formatDate(selectedDate)}
          onPress={() => setShowPicker(true)}
          style={{ flex: 1, height: 48, justifyContent: 'center' }}
          showArrow={true}
        />
      </View>
      <View
        style={{ alignItems: 'center', justifyContent: 'center', width: '100%', marginTop: -15 }}
      >
        <View
          style={{ flexDirection: 'row', gap: 10, alignItems: 'center', justifyContent: 'center' }}
        >
          <TimeWheel
            onValueChange={(val) => {
              setSelectedHour(val);
            }}
            type="hour"
            value={selectedHour}
            sideItemOpacity={0.3}
          />
          <View
            style={{
              height: '100%',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Text style={{ fontSize: 40, color: 'black', fontWeight: 700 }}>:</Text>
          </View>
          <TimeWheel
            onValueChange={(val) => {
              setSelectedMinute(val);
            }}
            type="minute"
            value={selectedMinute}
            sideItemOpacity={0.3}
          />
          <AmPmWheel selected={amOrpm} setSelected={setAmorPm} />
        </View>
      </View>
      {showPicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={minimumDate || new Date(2025, 0, 1)}
          maximumDate={maximumDate}
        />
      )}
    </View>
  );
};

export default DateTimeWheel;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: 24,
  },
  dropdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    width: '100%',
  },
  dropdown: {
    flex: 1,
    backgroundColor: '#F6F6F6',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 6,
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: 'black',
  },
  timeText: {
    fontSize: 36,
    fontWeight: '600',
    color: 'black',
  },
});

interface DateButtonProps {
  label: string;
  onPress?: () => void;
  showArrow?: boolean;
  style?: any;
  disabled?: boolean;
}

const DateButton: React.FC<DateButtonProps> = ({
  label,
  onPress,
  showArrow = false,
  style,
  disabled,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        {
          borderWidth: 0.5,
          borderColor: 'black',
          borderRadius: 10,
          padding: 10,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: disabled ? '#F0F0F0' : 'white',
          position: 'relative',
        },
        style,
      ]}
      disabled={disabled}
    >
      <Text
        style={{
          fontSize: 20,
          color: disabled ? '#AAA' : 'black',
          textAlign: 'center',
          flex: 1,
        }}
      >
        {label}
      </Text>
      {showArrow && (
        <Text
          style={{
            fontSize: 20,
            color: disabled ? '#AAA' : 'black',
            position: 'absolute',
            right: 14,
            top: '65%',
            transform: [{ translateY: -10 }],
          }}
        >
          {'>'}
        </Text>
      )}
    </TouchableOpacity>
  );
};
