import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const InvitationSVG = ({ size = 15, color = '#232323', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 15 15" fill="none" {...props}>
    <Path d="M8.68548 9.83316C8.31869 9.57724 7.92673 9.3687 7.5168 9.20938C8.06211 8.71368 8.42386 8.01928 8.48681 7.2419C9.31376 6.43603 10.3977 5.99423 11.5598 5.99423C12.467 5.99423 13.338 6.26744 14.0789 6.7842C14.3444 6.96939 14.7094 6.90438 14.8946 6.63884C15.0797 6.37342 15.0146 6.00819 14.7493 5.82312C14.3825 5.5672 13.9904 5.35878 13.5805 5.19934C14.1822 4.65236 14.5605 3.86365 14.5605 2.9883C14.5605 1.3405 13.2202 0 11.5726 0C9.9251 0 8.58477 1.3405 8.58477 2.9883C8.58477 3.86011 8.96002 4.64595 9.55763 5.1927C9.47581 5.22418 9.39444 5.25749 9.31387 5.29297C8.9472 5.45435 8.60251 5.65281 8.28196 5.88584C7.83987 4.78742 6.76355 4.01004 5.50893 4.01004C3.86144 4.01004 2.52099 5.35054 2.52099 6.99823C2.52099 7.86763 2.89418 8.6513 3.48859 9.19782C1.89934 9.79333 0.610732 11.0724 0.0861338 12.7036C-0.0878169 13.2445 0.00430831 13.8181 0.338934 14.2774C0.673561 14.7366 1.19129 15 1.75938 15H6.85648C7.18 15 7.4423 14.7377 7.4423 14.414C7.4423 14.0904 7.18 13.8281 6.85648 13.8281H1.75938C1.56998 13.8281 1.3974 13.7403 1.28582 13.5872C1.17424 13.434 1.14357 13.2428 1.20148 13.0624C1.78055 11.2618 3.54661 10.0043 5.49611 10.0043C6.40317 10.0043 7.2743 10.2775 8.01519 10.7942C8.28058 10.9794 8.64577 10.9143 8.83093 10.6489C9.01598 10.3835 8.95098 10.0182 8.68548 9.83316ZM11.5726 1.1719C12.5741 1.1719 13.3888 1.98671 13.3888 2.9883C13.3888 3.98978 12.5741 4.8047 11.5726 4.8047C10.5712 4.8047 9.75642 3.98978 9.75642 2.9883C9.75642 1.98671 10.5712 1.1719 11.5726 1.1719ZM5.50893 5.18183C6.51041 5.18183 7.32511 5.99675 7.32511 6.99823C7.32511 7.99982 6.51041 8.81463 5.50893 8.81463C4.50746 8.81463 3.69275 7.99982 3.69275 6.99823C3.69275 5.99675 4.50746 5.18183 5.50893 5.18183ZM15 12.2168C15 12.5404 14.7377 12.8027 14.4141 12.8027H12.803V14.414C12.803 14.7377 12.5407 15 12.2171 15C11.8935 15 11.6312 14.7377 11.6312 14.414V12.8027H10.0201C9.69656 12.8027 9.43427 12.5404 9.43427 12.2168C9.43427 11.8931 9.69656 11.6308 10.0201 11.6308H11.6312V10.0195C11.6312 9.69593 11.8935 9.4336 12.2171 9.4336C12.5407 9.4336 12.803 9.69593 12.803 10.0195V11.6308H14.4141C14.7377 11.6308 15 11.8931 15 12.2168Z" fill={color}/>
  </Svg>
);

export default InvitationSVG; 