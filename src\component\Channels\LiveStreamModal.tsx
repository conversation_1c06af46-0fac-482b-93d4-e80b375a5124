import { Image, StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import ModalWrapper from '../ModalWrapper';
import { useTranslation } from 'react-i18next';
import { IMAGES } from '../../assets/Images';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import ButtonPurple from '../ButtonPurple';
import { useNavigation } from '@react-navigation/native';
import { SCREENS } from '../../navigation/screenNames';
import LiveStreamIconSvg from '../../assets/svgIcons/LiveStreamIconSvg';

interface IProps {
  isVisible: boolean;
  onCloseModal: (value: boolean) => void;
}

const LiveStreamModal = ({ isVisible, onCloseModal }: IProps) => {
  const { navigate } = useNavigation();
  const { t } = useTranslation();
  const [_isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(isVisible);
  }, [isVisible]);

  const onClose = () => {
    setIsVisible(false);
    onCloseModal(!_isVisible);
  };

  const onPressLive = () => {
    onClose();
  };

  const onPressSchedule = () => {
    onClose();
    navigate(SCREENS.LiveScheduleScreen);
  };

  return (
    <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
      <View style={styles.row}>
        {/* <Image source={IMAGES.live_stream} style={styles.headerIcons} /> */}
        <LiveStreamIconSvg />
        <Text style={styles.liveText}>{t('Live stream')}</Text>
      </View>
      <View style={{ ...styles.row, paddingTop: hp(3) }}>
        <ButtonPurple
          title={t('Schedule')}
          type="gray"
          onPress={onPressSchedule}
          extraStyle={{ flex: 1 }}
        />
        <ButtonPurple title={t('Go live')} extraStyle={{ flex: 1 }} onPress={onPressLive} />
      </View>
    </ModalWrapper>
  );
};

export default LiveStreamModal;

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: hp(1),
  },
  headerIcons: {
    height: 23,
    width: 23,
    resizeMode: 'contain',
  },
  liveText: {
    ...commonFontStyle(600, 18, colors.black_23),
  },
});
