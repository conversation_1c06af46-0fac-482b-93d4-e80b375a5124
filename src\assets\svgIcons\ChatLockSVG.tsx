import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const ChatLockSVG = ({ size = 16, color = '#232323', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 16 16" fill="none" {...props}>
    <Path d="M7.75391 0.0996094C11.9773 0.0996094 15.408 3.47218 15.4082 7.63086V14.8311C15.4082 14.9911 15.2996 15.1004 15.1396 15.1006H7.75391C3.53058 15.1004 0.0996094 11.728 0.0996094 7.56934C0.0997659 3.41181 3.52961 0.0997865 7.75391 0.0996094ZM7.75391 0.638672C3.85422 0.638853 0.668945 3.75893 0.668945 7.60059C0.669053 11.4422 3.85429 14.5623 7.75391 14.5625H14.8389V7.60059C14.8389 3.75882 11.6537 0.638672 7.75391 0.638672Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M5.69287 7.20898H10.1851C10.5299 7.20898 10.7925 7.47259 10.7925 7.81738V10.125C10.7924 10.4698 10.5298 10.7324 10.1851 10.7324H5.69287C5.34811 10.7324 5.08452 10.4698 5.08447 10.125V7.81738C5.08447 7.47259 5.34808 7.20898 5.69287 7.20898ZM5.65381 10.1631H10.2231V7.74805H5.65381V10.1631Z" fill={color} stroke={color} strokeWidth={0.2}/>
    <Path d="M7.96924 4.07227C9.11402 4.07227 10.0541 5.01246 10.0542 6.15723V7.49805C10.0423 7.56405 10.0023 7.6351 9.94092 7.69043C9.87891 7.74624 9.80186 7.78027 9.72314 7.78027H6.15381C5.99917 7.78021 5.88451 7.64641 5.88428 7.51172V6.15723C5.88436 5.01252 6.82453 4.07235 7.96924 4.07227ZM7.93896 4.6416C7.11452 4.6416 6.42343 5.3328 6.42334 6.15723V7.21094H9.45361V6.15723C9.45353 5.33291 8.76326 4.64179 7.93896 4.6416Z" fill={color} stroke={color} strokeWidth={0.2}/>
  </Svg>
);

export default ChatLockSVG; 