// components/StickerHandler/StickerItem.tsx
import React from 'react';
import { StyleSheet } from 'react-native';
import GestureWrapper from '../Common/Gestrurewrapper';
import Sticker from './Sticker';

interface StickerItemProps {
  index: number;
  id: number | string;
  sticker: {
    image: any;
    id: string;
    initialX?: number;
    initialY?: number;
    rotate?: number;
    scale?: number;
    zIndex?: number;
  };
  onSelectSticker: () => void;
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number | string, type: string, value: boolean) => boolean;
  zIndex: number;
  topItemzIdx: any;
  deleteItem: (id: number | string, type: string) => void;
}

const StickerItem: React.FC<StickerItemProps> = ({
  id,
  sticker,
  onSelectSticker,
  itemDragging,
  isDeletable,
  zIndex,
  index,
  topItemzIdx,
  deleteItem,
}) => {
  return (
    <GestureWrapper
      id={id}
      itemDragging={(value) => itemDragging(value)}
      isDeletable={(id, type, bool) => isDeletable(id, type, bool)}
      deleteItem={deleteItem}
      xPos={sticker?.initialX || 0}
      yPos={sticker?.initialY || 0}
      rotate={sticker?.rotate || 0}
      scale={sticker?.scale || 1}
      onSelectSticker={onSelectSticker}
      index={index}
      zIndex={zIndex}
      topItemzIdx={topItemzIdx}
      type="sticker"
    >
      <Sticker
        id={id}
        sticker={{ id, image: sticker.image || sticker?.uri }}
        iconStyle={styles.stickerImage}
      />
    </GestureWrapper>
  );
};

const styles = StyleSheet.create({
  stickerImage: {
    width: 100,
    height: 100,
  },
});

export default React.memo(StickerItem);
