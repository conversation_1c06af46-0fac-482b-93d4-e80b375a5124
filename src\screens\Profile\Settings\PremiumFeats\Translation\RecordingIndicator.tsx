import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Animated,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';

import { RecordingState } from '../VoiceSampleScreen';
import { colors } from '../../../../../theme/colors';
import { FontAwesome5Icons, EntypoIcons } from '../../../../../utils/vectorIcons';

import Popover, { PopoverPlacement } from 'react-native-popover-view';
import { UseAudioRecorderReturn } from '../../../../../hooks/common/useAudioRecorder';

type RecordingIndicatorProps = {
  duration: number;
  minRequired: number;
  state: RecordingState;
  pauseRecording: () => void;
  resumeRecording: () => void;
  stopRecording: () => void;
  AudioRecorder: UseAudioRecorderReturn;
  retryRecording: () => void;
};

const RecordingIndicator = ({
  state,
  duration,
  minRequired,
  pauseRecording,
  resumeRecording,
  stopRecording,
  AudioRecorder,
  retryRecording,
}: RecordingIndicatorProps) => {
  const rippleAnim = useRef(new Animated.Value(0)).current;
  const popoverRef = useRef<Popover>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const loop = Animated.loop(
      Animated.sequence([
        Animated.timing(rippleAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(rippleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ]),
    );
    loop.start();
  }, []);

  const scale = rippleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 3],
  });

  const opacity = rippleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 0],
  });

  const [showPopover, setShowPopover] = useState(false);
  const touchableRef = useRef<any>(null);
  function handleStopRecording() {
    // console.log('popoverRef.current', popoverRef.current);
    if (AudioRecorder.recordTime < minRequired * 60) {
      setShowPopover(true);
      return;
    }
    stopRecording();
  }

  return (
    <View style={RecordingIndicatroStyles.container}>
      {/* Left side - Recording button and text */}
      <View style={RecordingIndicatroStyles.leftSection}>
        {/* Animated ripple ring */}

        {/* animation around this button. This is a circular button */}
        <View style={RecordingIndicatroStyles.recordButtonContainer}>
          <View style={RecordingIndicatroStyles.recordButton}>
            {state === 'recording' && (
              <>
                <TouchableOpacity ref={touchableRef} onPress={handleStopRecording}>
                  <FontAwesome5Icons name="square-full" size={12} color="white" />
                </TouchableOpacity>
                <Popover
                  isVisible={showPopover}
                  from={touchableRef}
                  onRequestClose={() => setShowPopover(false)}
                  placement={PopoverPlacement.TOP}
                >
                  <View style={{ backgroundColor: 'white', padding: 10 }}>
                    <Text style={{ color: 'black' }}>
                      Cannot stop recording before {minRequired} minutes.Try Refreshing
                    </Text>
                  </View>
                </Popover>
              </>
            )}

            {state === 'stopped' && (
              <TouchableOpacity onPress={retryRecording}>
                <EntypoIcons name="cross" size={24} color="white" />
              </TouchableOpacity>
            )}
          </View>
          {/* animation around this button. This is a circular button */}
        </View>
        <View style={RecordingIndicatroStyles.textContainer}>
          {!AudioRecorder.isPlaying && (
            <>
              <Text style={RecordingIndicatroStyles.recordingText}>
                {state === 'recording' && 'Recording..'}
                {state === 'paused' && 'Paused'}
                {state === 'stopped' && 'Stopped'}
              </Text>
              <Text style={RecordingIndicatroStyles.requirementText} numberOfLines={1}>
                Min.{minRequired} minutes required
              </Text>
            </>
          )}
          {AudioRecorder.isPlaying && (
            <Text style={RecordingIndicatroStyles.recordingText}>Playing Audio</Text>
          )}
        </View>
      </View>

      {/* Right side - Timer */}
      <>
        {state === 'recording' && (
          <View style={RecordingIndicatroStyles.rightSection}>
            <Text style={RecordingIndicatroStyles.timerText}>{formatTime(duration)}</Text>
          </View>
        )}

        {state === 'stopped' && (
          <View style={RecordingIndicatroStyles.rightSection}>
            <View style={RecordingIndicatroStyles.recordButton}>
              {!AudioRecorder.isPlaying && (
                <TouchableOpacity
                  onPress={() => {
                    AudioRecorder.playRecording(AudioRecorder.audioPath);
                  }}
                >
                  <FontAwesome5Icons name="play" size={12} color="white" />
                </TouchableOpacity>
              )}
              {AudioRecorder.isPlaybackPaused && (
                <TouchableOpacity
                  onPress={() => {
                    AudioRecorder.resumePlayback();
                  }}
                >
                  <FontAwesome5Icons name="play" size={12} color="white" />
                </TouchableOpacity>
              )}
              {AudioRecorder.isPlaying && !AudioRecorder.isPlaybackPaused && (
                <TouchableOpacity
                  onPress={() => {
                    AudioRecorder.pausePlayback();
                  }}
                >
                  <FontAwesome5Icons name="pause" size={12} color="white" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </>
    </View>
  );
};

export default RecordingIndicator;

const RecordingIndicatroStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors._F6F6F6_gray,
    borderRadius: 100,
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: colors.gray_80,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recordButtonContainer: {
    marginRight: 12,
    position: 'relative',
  },
  recordButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#8b5cf6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#8b5cf6',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  textContainer: {
    flex: 1,
  },
  recordingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  requirementText: {
    fontSize: 13,
    color: '#6b7280',
    fontWeight: '400',
  },
  rightSection: {
    left: 0,
    borderLeftWidth: 1,
    paddingLeft: 10,
    borderColor: colors.gray_80,
  },
  timerText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
    letterSpacing: 0.5,
  },
});

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
    .toString()
    .padStart(2, '0');
  const secs = (seconds % 60).toString().padStart(2, '0');
  return `${mins}:${secs}`;
};
