import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import { IMAGES } from '../../assets/Images';
import { useTranslation } from 'react-i18next';

interface IProps {
  onCapture: () => void;
  setIsCapture: (value: boolean) => void;
}
const CaptureButton = ({ onCapture = () => {}, setIsCapture }: IProps) => {
  const [swapCapture, setSwapCapture] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    setIsCapture(swapCapture);
  }, [swapCapture]);

  return (
    <View
      style={{
        ...styles.captureButtonContainer,
        flexDirection: !swapCapture ? 'column-reverse' : 'column',
      }}
    >
      <TouchableOpacity
        onPress={() => {
          if (swapCapture) {
            onCapture();
          } else {
            setSwapCapture(true);
          }
        }}
        style={{
          ...styles.captureButtonStyle,
          backgroundColor: swapCapture ? colors.mainPurple : colors.white,
          height: swapCapture ? 50 : 30,
          width: swapCapture ? 50 : 30,
        }}
      >
        <Image
          source={IMAGES.capture}
          style={styles.captureStyle}
          tintColor={swapCapture ? colors.white : colors.black}
        />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => setSwapCapture(false)}
        style={{
          ...styles.ATextContainer,
          backgroundColor: !swapCapture ? colors.mainPurple : colors.white,
          height: !swapCapture ? 50 : 30,
          width: !swapCapture ? 50 : 50,
        }}
      >
        <Text
          style={{
            ...styles.AText,
            color: !swapCapture ? colors.white : colors.black,
          }}
        >
          {t('Aa')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default CaptureButton;

const styles = StyleSheet.create({
  captureButtonContainer: {
    borderRadius: 50,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    padding: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(-9),
    marginTop: hp(-7.5),
    gap: 5,
    paddingBottom: 10,
  },
  captureButtonStyle: {
    height: 50,
    width: 50,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.mainPurple,
  },
  captureStyle: {
    height: 25,
    width: 25,
  },
  ATextContainer: {
    height: 50,
    width: 50,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.mainPurple,
  },
  AText: {
    ...commonFontStyle(700, 24, colors.black),
  },
});
