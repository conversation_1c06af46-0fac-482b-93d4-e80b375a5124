import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { commonFontStyle, hp } from '../theme/fonts';
import { colors } from '../theme/colors';
import { IMAGES } from '../assets/Images';
// import CountryPicker from "react-native-country-picker-modal";

interface IProps {
  country: any;
  onPress: () => void;
  countryList: any[];
  visible: boolean;
  onClose: () => void;
  onSelect: (country: any) => void;
}

const CustomCountryPicker = ({
  country,
  onPress,
  countryList,
  visible,
  onClose,
  onSelect,
}: IProps) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.rowView, { height: 55, marginBottom: hp(2) }]}
    >
      {/* <CountryPicker
        flatListProps={{
          data: countryList,
        }}
        withCallingCode
        visible={visible}
        onClose={onClose}
        withFlagButton={true}
        countryCode={country.cca2}
        withFilter
        withEmoji={false}
        filterProps={{}}
        onSelect={onSelect}
      /> */}
      <Text
        style={{
          ...commonFontStyle(400, 16, colors.black_23),
          flex: 1,
        }}
      >
        {country?.name}
      </Text>
    </TouchableOpacity>
  );
};

export default CustomCountryPicker;

const styles = StyleSheet.create({
  rowView: {
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 15,
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  rightArrow: {
    height: 15,
    width: 15,
    resizeMode: 'contain',
    tintColor: colors.mainPurple,
  },
});
