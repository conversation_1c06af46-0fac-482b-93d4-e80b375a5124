import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const SendArrowSVG: React.FC<IconProps> = ({
    size = 19,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 19 19"
            fill="none"
            {...restProps}
        >
            <Path
                d="M16.59.876a1.05 1.05 0 011.252 1.252l-.051.16-5.933 14.919c-.327.822-1.437.878-1.868.161l-.077-.153L7.6 11.53a.749.749 0 00-.279-.34l-.133-.07-5.684-2.315c-.874-.356-.87-1.597.008-1.946L16.43.927l.159-.051zm-7.994 9.976c.041.072.08.147.112.226l2.175 5.34 5.096-12.817-7.383 7.25zM2.299 7.835l5.34 2.175.071.032 7.459-7.325-12.87 5.118z"
                fill={color}
                stroke={color}
                strokeWidth={0.3}
            />
        </Svg>
    );
};

export default SendArrowSVG;

