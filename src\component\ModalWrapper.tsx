import { Dimensions, Platform, Pressable, StatusBar, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Modal, { ModalProps } from 'react-native-modal';
import { colors } from '../theme/colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { hp } from '../theme/fonts';
import { BlurView } from '@react-native-community/blur';

interface IModalProps extends ModalProps {
  isVisible: boolean;
  onCloseModal: () => void;
  children: any;
  [key: string]: any;
}

type Props = {
  isVisible: boolean;
  onCloseModal: () => void;
  onModelShow?: () => void;
  children: any;
  [key: string]: any;
};

const ModalWrapper = ({ isVisible, onCloseModal, children, onModelShow, ...props }: Props) => {
  const insets = useSafeAreaInsets();

  return (
    <>
      {/* {isVisible && (
        <>
          <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
          <Pressable style={StyleSheet.absoluteFill} onPress={onCloseModal}>
            <BlurView
              style={StyleSheet.absoluteFill}
              blurType='dark'
              blurAmount={1}
              reducedTransparencyFallbackColor="white"
            />
          </Pressable>
        </>
      )} */}
      <Modal
        onBackButtonPress={onCloseModal}
        onBackdropPress={onCloseModal}
        onSwipeComplete={onCloseModal}
        onModalShow={onModelShow}
        swipeThreshold={100}
        swipeDirection={['down']}
        avoidKeyboard={true}
        backdropOpacity={0.5}
        useNativeDriverForBackdrop
        isVisible={isVisible}
        animationInTiming={450}
        animationOutTiming={450}
        style={styles.container}
        // customBackdrop={
        //   <Pressable style={StyleSheet.absoluteFill} onPress={onCloseModal}>
        //     <BlurView
        //       style={styles.blurView}
        //       blurType={"light"} // or "dark" or "extraLight"
        //       blurAmount={10}
        //       reducedTransparencyFallbackColor={colors.white}
        //     />
        //   </Pressable>
        // }
        {...props}
      >
        <View
          style={[
            styles.innerContainer,
            // { paddingBottom: insets.bottom + hp(2) },
            { paddingBottom: hp(2) },
          ]}
        >
          <View style={styles.line} />
          {children}
        </View>
      </Modal>
    </>
  );
};

export default ModalWrapper;

const styles = StyleSheet.create({
  container: {
    margin: 0,
    flex: 1,
    justifyContent: 'flex-end',
  },
  innerContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.white,
    paddingHorizontal: hp(2),
  },
  line: {
    width: 45,
    height: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 13,
    borderRadius: 10,
    alignSelf: 'center',
    marginBottom: hp(2.5),
  },
  blurView: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
  },
});
