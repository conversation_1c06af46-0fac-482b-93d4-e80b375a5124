import * as React from "react";
import Svg, { Mask, Path, G, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const PinSVG: React.FC<IconProps> = ({
    size = 16,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 16 16"
            fill="none"
            {...restProps}
        >
            <Mask
                id="a"
                maskUnits="userSpaceOnUse"
                x={0}
                y={0}
                width={16}
                height={16}
            >
                <Path d="M0 0h15.367v15.367H0V0z" fill="#fff" />
            </Mask>
            <G
                mask="url(#a)"
                stroke={color}
                strokeWidth={0.900428}
                strokeMiterlimit={10}
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <Path d="M.45 14.918l5.192-5.192" />
                <Path d="M8.643 12.727L2.641 6.725l.562-.563a1.92 1.92 0 012.717 0l3.286 3.286c.75.75.75 1.967 0 2.717l-.563.563z" />
                <Path d="M13.477 5.972a1.43 1.43 0 01-1.019-.422L9.817 2.908A1.44 1.44 0 1111.854.871l2.642 2.641a1.44 1.44 0 01-1.019 2.46z" />
                <Path d="M8.883 9.125l3.097-4.05" />
                <Path d="M10.292 3.387l-4.05 3.097" />
            </G>
        </Svg>
    );
};

export default PinSVG;

