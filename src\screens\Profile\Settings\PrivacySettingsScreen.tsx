import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { colors } from '../../../theme/colors';
import { hp } from '../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import RightNavigationRow from '../components/RightNavigationRow';
import StreamToggleOption from '../../Home/Channels/components/StreamToggle';
// import ProfileSVG from '../../../assets/svgIcons/ProfileSVG';
import TimerSVG from '../../../assets/svgIcons/TimerSVG';
// import LockSVG from '../../../assets/svgIcons/LockSVG';
// import ChatSVG from '../../../assets/svgIcons/ChatSVG';
// import CallSVG from '../../../assets/svgIcons/CallSVG';
// import GalleryImgSvg from '../../../assets/svgIcons/GalleryImgSvg';
import DoubleTicksSVG from '../../../assets/svgIcons/DoubleTicksSVG';
// import BioSVG from '../../../assets/svgIcons/BioSVG';
import BlockSVG from '../../../assets/svgIcons/BlockSVG';
// import InvitationSVG from '../../../assets/svgIcons/InvitationSVG';
// import ChatLockSVG from '../../../assets/svgIcons/ChatLockSVG';
// import Documents2SVG from '../../../assets/svgIcons/Documents2SVG';
// import SendArrowSVG from '../../../assets/svgIcons/SendArrowSVG';
import { SCREENS } from '../../../navigation/screenNames';
import { errorToast, navigateTo } from '../../../utils/commonFunction';
import { useMe } from '../../../hooks/util/useMe';

const PrivacySettingsScreen = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();
  const { userPreferences, updatePreferences } = userPreferencesState;
  const readReceipts = userPreferences?.privacy?.readReceipts ?? true;

  const handleReadReceiptsToggle = async () => {
    try {
      await updatePreferences('privacy', { readReceipts: !readReceipts });
    } catch (error) {
      errorToast(error as string);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Privacy settings" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.sectionContainer}>
          <RightNavigationRow
            text="Last seen"
            onPress={() => navigateTo(SCREENS.LastSeenScreen)}
            icon={<TimerSVG size={20} />}
          />
          {/* <RightNavigationRow
            text="Profile photo"
            onPress={() => navigateTo(SCREENS.ProfilePhotoScreen)}
            icon={<ProfileSVG size={20} />}
          /> */}
          {/* <RightNavigationRow
            text="Bio"
            onPress={() => navigateTo(SCREENS.BioScreen)}
            icon={<BioSVG />}
          /> */}
          <RightNavigationRow
            text="Blocked people"
            onPress={() => navigateTo(SCREENS.BlockedPeopleScreen)}
            icon={<BlockSVG size={20} />}
          />
          <RightNavigationRow
            text="Default message timer"
            onPress={() => navigateTo(SCREENS.DefaultMessageTimerScreen)}
            icon={<TimerSVG size={20} />}
          />
          {/* <RightNavigationRow
            text="Phone number"
            onPress={() => navigateTo(SCREENS.PhoneNumberScreen)}
            icon={<CallSVG size={20} color={colors.black_23} />}
          /> */}
          {/* <RightNavigationRow
            text="Forward message"
            onPress={() => navigateTo(SCREENS.ForwardMessageScreen)}
            icon={<SendArrowSVG size={19} />}
          />
          <RightNavigationRow
            text="Group/Channel invitation"
            onPress={() => navigateTo(SCREENS.GroupChannelInvitationScreen)}
            icon={<InvitationSVG size={18} />}
          />
          <RightNavigationRow
            text="Messages"
            onPress={() => navigateTo(SCREENS.MessagesScreen)}
            icon={<ChatSVG size={20} />}
          />
          <RightNavigationRow
            text="Images"
            onPress={() => navigateTo(SCREENS.ImagesScreen)}
            icon={<GalleryImgSvg size={20} />}
          />
          <RightNavigationRow
            text="Documents"
            onPress={() => navigateTo(SCREENS.DocumentsScreen)}
            icon={<Documents2SVG size={20} />}
          /> */}
          <StreamToggleOption
            label="Read receipts"
            value={readReceipts}
            onToggle={handleReadReceiptsToggle}
            icon={<DoubleTicksSVG size={24} color={colors.black_23} />}
            switchStyle={styles.switchStyle}
          />
          {/* <StreamToggleOption
            label="App lock"
            value={appLock}
            onToggle={() => setAppLock(!appLock)}
            icon={<LockSVG size={20} />}
            switchStyle={styles.switchStyle}
          />
          <StreamToggleOption
            label="Chat lock"
            value={chatLock}
            onToggle={() => setChatLock(!chatLock)}
            icon={<ChatLockSVG size={20} color={colors.black_23} />}
            switchStyle={styles.switchStyle}
          /> */}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  sectionContainer: {
    paddingHorizontal: hp(2),
    paddingVertical: hp(1),
  },
  switchStyle: {
    marginRight: -6,
  },
});

export default PrivacySettingsScreen;
