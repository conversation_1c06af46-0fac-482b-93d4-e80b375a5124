import * as React from "react";
import Svg, { Path } from "react-native-svg";

function DocumentSVG({ width = 19, height = 25, color = "#6A4DBB", ...props }) {
    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 19 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M2.192 24.944h14.616A2.195 2.195 0 0019 22.75V7.308h-5.115a2.195 2.195 0 01-2.193-2.193V0h-9.5A2.195 2.195 0 000 2.192v20.56c0 1.208.983 2.192 2.192 2.192zM5.115 10.28h8.77a.73.73 0 110 1.461h-8.77a.73.73 0 110-1.461zm0 2.923h8.77a.73.73 0 110 1.461h-8.77a.73.73 0 110-1.461zm0 2.923h8.77a.73.73 0 110 1.461h-8.77a.73.73 0 110-1.461zm0 2.923h5.846a.73.73 0 110 1.461H5.115a.73.73 0 110-1.461z"
                fill={color}
            />
            <Path
                d="M13.885 5.846h4.687L13.154.428v4.687c0 .403.328.73.731.73z"
                fill={color}
            />
        </Svg>
    );
}

export default DocumentSVG;

