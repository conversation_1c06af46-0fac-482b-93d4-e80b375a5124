import { createContext, useContext, useEffect, useState } from 'react';
import { Socket } from 'socket.io-client';
import { connectToSocketServer } from './socket';
import SocketHandler from './SocketHandler';

import { authSocketEvents } from './socketEvents';

import { initDbListeners } from '../device-storage/realm/listeners/initDbListener';
import { getRealm, useRealm } from '../device-storage/realm/realm';
import { ChatService } from '../service/ChatService';
import { useMe } from '../hooks/util/useMe';
import Logger from '../lib/Logger';

type SocketConnectionStatus = 'connecting' | 'connected' | 'disconnected';

export type SocketContextType = {
  status: SocketConnectionStatus;
  socket: Socket | null;
  error: any;
  type: 'auth' | 'chat';
};

export const SocketContext = createContext<SocketContextType | null>(null);

type SocketProviderProps = {
  children: React.ReactNode;
};

const SocketProvider = ({ children }: SocketProviderProps) => {
  const { user } = useMe();
  const [socketState, setSocketState] = useState<SocketContextType>({
    status: 'disconnected',
    socket: null,
    error: null,
    type: 'auth',
  });

  const realm = useRealm();

  // Initialize all necessary connections here
  useEffect(() => {
    if (!user?._id) {
      Logger.warn('User ID is not available, skipping socket and realm initialization', true);
      return;
    }
    Logger.log('User id changed or exists, initializing realm and socket', true);
    const initApp = async () => {
      ChatService.setRealm(realm);
      const socket = await connectToSocketServer();
      setSocketState((prevState) => ({ ...prevState, socket }));
      // initDbListeners();
    };
    initApp();
    return () => {
      realm.removeAllListeners();
    };
  }, [user?._id]);

  return <SocketContext.Provider value={socketState}>{children}</SocketContext.Provider>;
};

export default SocketProvider;
