import React, { useState, useMemo } from 'react';
import { View, SafeAreaView, StyleSheet, FlatList } from 'react-native';
import HeaderBackWithTitle from '../../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../../theme/colors';
import { hp } from '../../../../../theme/fonts';
import SearchInput from '../../../../../component/SearchInput';
import { useContacts } from '../../../../../hooks/contacts/useContacts';
import { useFilteredContacts } from '../../../../../hooks/common/useFilterContacts';
import { useMe } from '../../../../../hooks/util/useMe';
import { useNavigation } from '@react-navigation/native';
import { PrivacyEnum } from '../../../../../device-storage/realm/schemas/UserPrefSchema';
import ButtonPurple from '../../../../../component/ButtonPurple';
import { IContact } from '../../../../../device-storage/realm/schemas/ContactSchema';
import { errorToast } from '../../../../../utils/commonFunction';
import ContactRow from '../../../components/ContactRow';

const ContactsExceptPhoneNumber = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();
  const { userPreferences, updatePreferences } = userPreferencesState;
  const phoneNumber = userPreferences?.privacy?.phoneNumber;
  const currentExcepted = useMemo(() => phoneNumber?.exceptedContacts || [], [phoneNumber]);
  const [selected, setSelected] = useState<Set<string>>(new Set(currentExcepted));
  const [isLoading, setIsLoading] = useState(false);
  const { registeredContacts } = useContacts();
  const [search, setSearch] = useState('');

  const validRegisteredContacts = useMemo(
    () => registeredContacts.filter((c) => c.userId !== undefined),
    [registeredContacts],
  );

  const { filteredRegistered } = useFilteredContacts(
    validRegisteredContacts.map((item) => item),
    [],
    search,
  );

  const handleToggle = (contact: IContact) => {
    const id = contact.userId;
    if (!id) return;
    setSelected((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleDone = async () => {
    setIsLoading(true);
    const newExcepted = Array.from(selected);
    const newPhoneNumber = {
      ...phoneNumber,
      privacy: PrivacyEnum.CONTACTS_EXCEPT,
      exceptedContacts: newExcepted,
      allowedContacts: phoneNumber?.allowedContacts || [],
    };
    try {
      await updatePreferences('privacy', { phoneNumber: newPhoneNumber });
      navigation.goBack();
    } catch (err) {
      errorToast('Failed to update phone number privacy');
    } finally {
      setIsLoading(false);
    }
  };

  const renderItem = ({ item }: any) => {
    const id = item.userId;
    if (!id) return null;
    return (
      <ContactRow
        contact={item}
        selected={selected.has(id)}
        onToggle={handleToggle}
        radioType="tick"
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="My contacts except" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.searchView}>
          <SearchInput value={search} onChangeText={setSearch} />
        </View>
        <FlatList
          data={filteredRegistered}
          renderItem={renderItem}
          keyExtractor={(item, idx) => item.userId || String(idx)}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
        <View style={styles.applyButtonContainer}>
          {selected.size > 0 && (
            <ButtonPurple
              title="Done"
              onPress={handleDone}
              extraStyle={styles.applyButtonStyle}
              disabled={isLoading}
              isLoading={isLoading}
            />
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
    paddingBottom: 16,
  },
  searchView: {
    paddingHorizontal: hp(2),
    marginBottom: hp(1),
    marginTop: hp(2),
  },
  listContainer: {
    paddingBottom: hp(10),
  },
  applyButtonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  applyButtonStyle: {
    marginTop: 15,
  },
  doneButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  infoText: {
    color: colors.gray_80,
    fontSize: 14,
    marginHorizontal: 20,
    marginBottom: 8,
    marginTop: 2,
  },
});

export default ContactsExceptPhoneNumber;
