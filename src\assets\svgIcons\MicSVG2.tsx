import * as React from "react";
import Svg, { Path } from "react-native-svg";
import { colors } from "../../theme/colors";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const MicSVG2: React.FC<SvgComponentProps> = ({ size = 24, color = colors.black_23, ...props }) => {


    return (
        <Svg
            width={size}
            height={(size * 14) / 11} // Maintain original aspect ratio (11x14)
            viewBox="0 0 11 14"
            fill="none"
            {...props}
        >
            <Path
                d="M5.5 10.502c-1.93 0-3.5-1.73-3.5-3.854V3.854C2 1.729 3.57 0 5.5 0s3.501 1.729 3.501 3.854v2.794c0 2.125-1.57 3.854-3.5 3.854zM5.5 1C4.123 1 3 2.28 3 3.854v2.794c0 1.573 1.122 2.853 2.5 2.853 1.38 0 2.5-1.28 2.5-2.853V3.854C8 2.28 6.88 1 5.5 1z"
                fill={color}
            />
            <Path
                d="M5.5 12.503a5.281 5.281 0 01-5.497-4.44.5.5 0 01.992-.125c.02.146.515 3.564 4.505 3.564 3.99 0 4.486-3.418 4.505-3.564a.5.5 0 01.992.126A5.281 5.281 0 015.5 12.503z"
                fill={color}
            />
            <Path
                d="M5.5 14.003a.5.5 0 01-.5-.5v-1a.5.5 0 011 0v1a.5.5 0 01-.5.5z"
                fill={color}
            />
        </Svg>
    );
};

export default MicSVG2;



