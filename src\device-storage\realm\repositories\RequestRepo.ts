import { Realm } from 'realm';
import { RequestSchema } from '../schemas/RequestSchema';
import { realmSchemaNames } from '../schemas/schemaNames';

export class RequestRepo {
  private static schemaName = realmSchemaNames.request;
  constructor() {}

  static findRealmObjectById(realm: Realm, chatSpaceId: string): RequestSchema | null {
    return realm.objectForPrimaryKey<RequestSchema>(RequestSchema.schema.name, chatSpaceId);
  }

}
