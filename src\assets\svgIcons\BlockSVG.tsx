import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const BlockSVG = ({ size = 15, color = '#232323', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 15 15" fill="none" {...props}>
    <Path d="M7.5 0.0996094C11.5802 0.0996096 14.9002 3.41972 14.9004 7.5C14.9004 11.5802 11.5803 14.9004 7.5 14.9004C3.42009 14.9004 0.0999528 11.58 0.0996094 7.5C0.0997798 3.41969 3.41971 0.0996094 7.5 0.0996094ZM12.2373 3.12695L3.12695 12.2383L3.05273 12.3125L3.13086 12.3828C4.29129 13.4221 5.8231 14.0557 7.5 14.0557C11.1148 14.0557 14.0557 11.1147 14.0557 7.5C14.0556 5.82306 13.4221 4.29124 12.3828 3.13086L12.3125 3.05273L12.2373 3.12695ZM7.5 0.944336C3.88519 0.944336 0.944469 3.88511 0.944336 7.5C0.944477 9.05677 1.49066 10.4887 2.40039 11.6143L2.46973 11.7002L2.54883 11.6221L11.6221 2.54883L11.7002 2.46973L11.6143 2.40039C10.4886 1.4905 9.05689 0.944336 7.5 0.944336Z" fill={color} stroke={color} strokeWidth={0.2}/>
  </Svg>
);

export default BlockSVG; 