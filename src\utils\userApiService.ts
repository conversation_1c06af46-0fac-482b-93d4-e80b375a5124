import { Client } from '../lib/Client';
import { IUser, RemoteUser } from '../types/index.types';
import Api from './api';

export const doesUsernameExist = async (username: string, interimToken: string) => {
  const res = await Api.post('v1/auth/verify_username', { username, token: interimToken });
  return {
    exists: res?.body.status === false,
    message: res?.body.message || 'Username not available',
  };
};

export const getBlockedUsers = async () => {
  const url = 'v1/users/blocked/users';
  const res = await Api.get(url);
  // console.log(res.body.data);
  return res?.body.data;
};

export const getMe = async (refetch = false): Promise<IUser | null> => {
  try {
    const cachedUser = await Client.User.get();
    if (cachedUser && !refetch) return cachedUser;
    const res = await Api.get('v1/users/get_profile');
    if (res.isNetworkError) {
      return cachedUser;
    }
    const profile: IUser = res?.body?.data?.user;
    if (profile) {
      await Client.User.set(profile);
      return profile;
    }
    return null;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
};

export const getUser = async (userId: string): Promise<RemoteUser | null> => {
  try {
    const res = await Api.get(`v1/users/${userId}`);
    if (res.isNetworkError) {
      return null;
    }
    const user: RemoteUser = res?.body?.data;
    return user || null;
  } catch (error) {
    return null;
  }
};
