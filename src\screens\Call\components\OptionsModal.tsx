import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TextInput,
  ToastAndroid,
} from 'react-native';

import { BlurView } from '@react-native-community/blur';
import { _openAppSetting } from '../../../utils/locationHandler';
import { useCallContext } from '../../../Context/CallProvider';
import { IMAGES } from '../../../assets/Images';
import { useMediasoup } from '../../../Context/RtcProvider';
import Toast from 'react-native-toast-message';

type OptionsModalProps = {
  optionsModal: boolean;
  setOptionsModal: React.Dispatch<React.SetStateAction<boolean>>;
  setLanguageModal: React.Dispatch<React.SetStateAction<boolean>>;
};

// create a component
const OptionsModal = ({ optionsModal, setOptionsModal, setLanguageModal }: OptionsModalProps) => {
  const { callDetails, initiateScreenShare } = useCallContext();
  const { mediaConsumers, mediaProducers } = useMediasoup();

  const isScreenSharingOngoing =
    mediaConsumers.screenSharers.length > 0 || mediaProducers.producers.isScreenSharing;

  return (
    <Modal
      transparent={true}
      visible={optionsModal}
      onRequestClose={() => {
        setOptionsModal(false);
      }}
    >
      <TouchableOpacity
        style={{
          flex: 1,
          justifyContent: 'flex-end',
        }}
        activeOpacity={0.6}
        onPress={() => {
          setOptionsModal(false);
        }}
      >
        <View
          style={{
            width: '100%',
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            overflow: 'hidden',
            alignSelf: 'center',
            paddingVertical: 10,
          }}
        >
          <BlurView
            style={[StyleSheet.absoluteFill]}
            blurType="dark"
            blurAmount={10}
            reducedTransparencyFallbackColor="rgba(0, 0, 0, 0.25)"
          />
          <View style={styles.overlay} />
          <View
            style={{
              marginHorizontal: 15,
            }}
          >
            <Text
              style={{
                marginTop: 15,
                fontWeight: '500',
                color: '#FFFFFF',
                fontSize: 16,
              }}
            >
              Options
            </Text>
            <View
              style={{
                borderWidth: 0.4,
                borderColor: '#4c555b',
                marginTop: 10,
              }}
            ></View>
            <View style={{ marginVertical: 6 }}>
              {/* <Pressable
                onPress={() => {
                  setOptionsModal(false);
                  setLanguageModal(true);
                }}
                style={{
                  alignItems: 'center',
                  flexDirection: 'row',
                  // marginTop: 10,
                  padding: 10,
                }}
              >
                <Image
                  source={IMAGES.translation}
                  style={{ height: 25, width: 25 }}
                  resizeMode="contain"
                />
                <Text style={{ color: '#FFFFFF', left: 10 }}>Translate voice</Text>
              </Pressable> */}

              <TouchableOpacity
                onPress={async (e) => {
                  if (isScreenSharingOngoing) {
                    const currentScreenSharer =
                      mediaConsumers.screenSharers.length > 0
                        ? mediaConsumers.screenSharers[0]
                        : undefined;
                    let errorText = '';
                    if (currentScreenSharer) {
                      const name =
                        callDetails.participants.find(
                          (p) => p.participantId === currentScreenSharer.id,
                        )?.client.name || '';
                      errorText = `${name} is already ScreenSharing.`;
                    } else {
                      errorText = 'You are already ScreenSharing.';
                    }

                    ToastAndroid.showWithGravity(
                      errorText,
                      ToastAndroid.SHORT,
                      ToastAndroid.BOTTOM,
                    );

                    return;
                  }
                  e.stopPropagation();

                  await initiateScreenShare();
                  setOptionsModal(false);
                }}
                style={{
                  alignItems: 'center',
                  flexDirection: 'row',
                  // marginTop: 12,
                  padding: 10,
                }}
              >
                <Image
                  source={IMAGES.shareScreen}
                  style={{ height: 25, width: 25, right: 4 }}
                  resizeMode="contain"
                />
                <Text style={{ color: '#FFF', left: 10 }}>Share screen</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

// define your styles
const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
});

//make this component available to the app
export default OptionsModal;
