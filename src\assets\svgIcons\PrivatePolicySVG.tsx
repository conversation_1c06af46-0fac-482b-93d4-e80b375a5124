import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const PrivatePolicySVG = (props: any) => (
  <Svg width={20} height={20} viewBox="0 0 16 16" fill="none" {...props}>
    <Path d="M3.8252 5.18165H11.4775C11.8053 5.18165 12.0712 4.91571 12.0712 4.5879C12.0712 4.26008 11.8053 3.99414 11.4775 3.99414H3.8252C3.49739 3.99414 3.23145 4.26008 3.23145 4.5879C3.23145 4.91571 3.49739 5.18165 3.8252 5.18165Z" fill="#232323"/>
    <Path d="M8.96056 8.28712C8.96056 7.9593 8.69462 7.69336 8.36681 7.69336H3.8252C3.49739 7.69336 3.23145 7.9593 3.23145 8.28712C3.23145 8.61493 3.49739 8.88087 3.8252 8.88087H8.36681C8.69462 8.88087 8.96056 8.61493 8.96056 8.28712Z" fill="#232323"/>
    <Path d="M15.2073 10.116C15.2711 9.41822 15.3139 8.60508 15.3148 7.65726C15.313 5.87381 15.1633 4.56786 15.0036 3.60816C14.7242 2.02689 13.2879 0.590631 11.7067 0.311253C10.747 0.151564 9.44103 0.00187502 7.65726 0C5.87381 0.00187502 4.56754 0.151564 3.60785 0.311253C2.02689 0.590631 0.590631 2.02689 0.311253 3.60816C0.151564 4.56786 0.00218752 5.87381 0 7.65726C0.00218752 9.44072 0.151564 10.747 0.311253 11.7067C0.590631 13.2879 2.02689 14.7242 3.60816 15.0036C4.56786 15.1633 5.87381 15.3126 7.65757 15.3148C8.60508 15.3139 9.41822 15.2711 10.1157 15.2073C10.7357 15.7033 11.522 16.0002 12.3776 16.0002C14.3783 16.0002 16.0005 14.3783 16.0005 12.3776C16.0005 11.522 15.7036 10.7357 15.2076 10.116H15.2073ZM7.65757 14.1273C6.25069 14.1258 4.99192 14.0295 3.80972 13.8333C3.28441 13.7392 2.75221 13.4445 2.31127 13.0036C1.87033 12.5626 1.57595 12.0307 1.48158 11.5051C1.28533 10.3242 1.18939 9.0654 1.18751 7.65726C1.18907 6.24975 1.28533 4.99099 1.48158 3.80941C1.57564 3.28409 1.87033 2.75221 2.31127 2.31127C2.75221 1.87033 3.28409 1.57595 3.80972 1.48158C4.9913 1.28533 6.25006 1.18939 7.65757 1.18751C9.06446 1.18907 10.3232 1.28533 11.5054 1.48158C12.0307 1.57564 12.5626 1.87033 13.0039 2.31127C13.4448 2.75221 13.7392 3.28409 13.8336 3.80972C14.0298 4.99192 14.1258 6.25037 14.1276 7.65757C14.127 8.18571 14.1126 8.69227 14.0851 9.18228C13.5764 8.90977 12.9951 8.7554 12.3776 8.7554C10.377 8.7554 8.75477 10.3773 8.75477 12.3779C8.75477 12.9954 8.90946 13.5764 9.18165 14.0851C8.69165 14.1126 8.18508 14.127 7.65726 14.1273H7.65757ZM12.3776 14.8126C11.0348 14.8126 9.94228 13.7204 9.94228 12.3776C9.94228 11.0348 11.0348 9.9426 12.3776 9.9426C13.7204 9.9426 14.813 11.0348 14.813 12.3776C14.813 13.7204 13.7204 14.8126 12.3776 14.8126Z" fill="#232323"/>
    <Path d="M12.696 13.0693C12.6204 13.0568 12.5175 13.0449 12.3772 13.0449C12.2369 13.0449 12.1341 13.0568 12.0585 13.0693C11.9341 13.0912 11.821 13.2043 11.7988 13.329C11.7863 13.4046 11.7744 13.5074 11.7744 13.6477C11.7744 13.7881 11.7863 13.8909 11.7988 13.9665C11.8207 14.0909 11.9338 14.204 12.0585 14.2262C12.1341 14.2387 12.2369 14.2506 12.3772 14.2506C12.5175 14.2506 12.6204 14.2387 12.696 14.2262C12.8204 14.2043 12.9335 14.0912 12.9557 13.9665C12.9682 13.8909 12.9801 13.7881 12.9801 13.6477C12.9801 13.5074 12.9682 13.4046 12.9557 13.329C12.9338 13.2046 12.8207 13.0915 12.696 13.0693Z" fill="#232323"/>
    <Path d="M12.3779 12.6762C12.7058 12.6762 12.9717 12.4103 12.9717 12.0824V11.0918C12.9717 10.764 12.7058 10.498 12.3779 10.498C12.0501 10.498 11.7842 10.764 11.7842 11.0918V12.0824C11.7842 12.4103 12.0501 12.6762 12.3779 12.6762Z" fill="#232323"/>
  </Svg>
);

export default PrivatePolicySVG; 