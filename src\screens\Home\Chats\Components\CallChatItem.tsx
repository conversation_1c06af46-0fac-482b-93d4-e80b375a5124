import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp, SCREEN_WIDTH } from '../../../../theme/fonts';

import { useMe } from '../../../../hooks/util/useMe';

import { ChatListProps } from './ChatListItem';
import { dayPipe } from '../../../../utils/commonFunction';

import CallService from '../../../../service/CallService';
import { useCallContext } from '../../../../Context/CallProvider';
import { safeJsonParse } from '../../../../common/utils';

interface SenderData {
  id: string;
  displayName: string;
  userName: string;
  phoneNumber: string;
  displayPic: string;
  textColor: string;
}

export type GroupCallInfo = {
  callType: string;
  callId: string;
  roomId: string;
  initiatorId: string;
  callState: 'incoming' | 'outgoing' | 'missed' | 'ended';
};

export default function CallChatItem({
  data,
  onCardPress = () => { },
  selectedMsgs = [],
  setSelectedMsgs = () => { },
  otheUserName = '',
  scrollToItem = () => { },
  isMsgHightlighted = false,
  unHightlightFunc = () => { },
  userData = {},
  type,
  onEditMessage = () => { },
}: ChatListProps) {
  const callDataRawStr = data.eventPayload;
  const parsedEventPayload = safeJsonParse<string>(callDataRawStr);
  const callData = parsedEventPayload ? safeJsonParse<GroupCallInfo>(parsedEventPayload) : null;
  const callHistoryData = callData?.callId ? CallService.getCallById(callData?.callId) : null;
  const { callDetails } = useCallContext();

  const { user } = useMe();
  const isMyData = data?.senderId === user?._id;

  return (
    <View
      style={[
        data?.replyToMessageId ? styles.replyContainer : styles.container,
        {
          alignSelf: isMyData ? 'flex-end' : 'flex-start',
          backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
          borderTopLeftRadius: isMyData ? 15 : 1,
          borderBottomRightRadius: isMyData ? 1 : 15,
        },
      ]}
    >
      <TouchableOpacity
        style={{ borderColor: colors.gray_f3, borderWidth: 1, padding: 8, borderRadius: 15 }}
      >
        <Text style={styles.text}>
          {callData?.initiatorId} started {callData?.callType} call
        </Text>
        <Text style={styles.timeText}>{dayPipe(data?.createdAt, 'time')}</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    paddingVertical: 10,
    paddingLeft: 10,
    paddingRight: 10,
    borderRadius: 15,
  },
  replyContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 15,
  },
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(38),
    padding: 8,
    borderRadius: 16,
  },
  imageContainer: {
    alignSelf: 'flex-start',
    maxWidth: hp(40),
  },
  text: {
    marginBottom: 8,
    ...commonFontStyle(700, 14, colors.black),
    alignSelf: 'flex-start',
  },
  timeText: {
    color: colors._7A6A90_purple,
    fontSize: 12,
    marginRight: 4,
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  smallText: {
    ...commonFontStyle(700, 12, colors._757575_gray),
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH * 0.4,
    justifyContent: 'flex-end',
  },
  contactView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedStyle: {
    paddingVertical: 6,
    backgroundColor: 'transperant',
  },
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  lastMessage: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
  },
  pinIconButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePic: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
});
