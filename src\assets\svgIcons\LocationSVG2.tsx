import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface LocationSVGProps {
    size?: number;
    color?: string;
}

const LocationSVG2: React.FC<LocationSVGProps & React.ComponentProps<typeof Svg>> = ({
    size = 22,
    color = "#B9B9B9",
    ...props
}) => {


    return (
        <Svg
            width={(size / 22) * 17}
            height={size}
            viewBox="0 0 17 22"
            fill="none"
            {...props}
        >
            <Path
                d="M8.473 0a8.22 8.22 0 00-8.22 8.22c0 4.801 5.082 9.313 7.254 11.022a1.562 1.562 0 001.932 0c2.172-1.71 7.254-6.22 7.254-11.022 0-4.54-3.68-8.22-8.22-8.22zm0 11.26a3.179 3.179 0 110-6.358 3.179 3.179 0 010 6.357z"
                fill={color}
            />
            <Path
                d="M12.053 18.875a31.553 31.553 0 01-1.738 1.478 2.999 2.999 0 01-1.841.638 2.99 2.99 0 01-1.84-.638 31.564 31.564 0 01-1.74-1.478c-1.922.299-3.206.828-3.206 1.434 0 .933 3.039 1.69 6.786 1.69 3.747 0 6.785-.756 6.785-1.69 0-.606-1.284-1.136-3.206-1.434z"
                fill={color}
            />
        </Svg>
    );
};

export default LocationSVG2;
