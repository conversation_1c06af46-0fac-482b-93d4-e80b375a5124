import Toast from 'react-native-toast-message';

import { initChatService } from './socketListeners';
import Logger from '../lib/Logger';
import { infoToast, successToast } from '../utils/commonFunction';
import { clearSessionCache } from '../lib/inMemoryCache';

const SocketHandler = {
  handleConnection: async () => {
    Logger.log("'Socket connection established with server, checking authorization...'");
  },

  handleAuthentication: async () => {
    Logger.log('Socket authentication successful, initializing chat service...');
    initChatService();
  },

  handleAuthenticationError: async (error: any) => {
    console.error('Socket authentication failed:', error);
  },

  /**
   * Global error handler for socket exceptions
   */
  handleErrors: async (error: any) => {
    console.error('Failed to establish socket connection:', error);
    // if (error.message) {
    //   Toast.show({
    //     type: 'error',
    //     text1: error.message,
    //   });
    //   // Toast.error(data.message);
    // }
  },

  handleDisconnection: async () => {
    console.log('============= socket disconnected =============');
    clearSessionCache();
  },
};

export default SocketHandler;
