import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

function ShareOutlineSvg(props) {
  return (
    <Svg
      width={23}
      height={23}
      viewBox="0 0 23 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M.717 22.088a.725.725 0 01-.71-.695C-.002 21.25-.575 7.242 11.381 5.315V.822c0-.292.168-.556.427-.672a.693.693 0 01.768.134l9.955 9.533a.745.745 0 01.227.538.745.745 0 01-.227.537l-9.955 9.533a.697.697 0 01-.768.135.734.734 0 01-.427-.672v-4.425h-.012c-1.984 0-7.797.5-9.993 6.164a.712.712 0 01-.66.46zM12.804 2.503v3.452c0 .372-.27.685-.628.728-8.014.972-10.08 7.684-10.595 11.678 3.768-4.853 10.492-4.344 10.57-4.337.37.032.653.35.653.73v3.452l8.199-7.851-8.199-7.852z"
        fill="#fff"
      />
    </Svg>
  );
}

export default ShareOutlineSvg;
