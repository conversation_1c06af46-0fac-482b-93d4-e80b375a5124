import { useQuery, useRealm } from '../../device-storage/realm/realm';
import { ConversationRepo } from '../../device-storage/realm/repositories/ConversationRepo';
import {
  ConversationSchema,
  IConversation,
} from '../../device-storage/realm/schemas/ConversationSchema';
import { ConversationType } from '../../device-storage/realm/schemas/MessageSchema';
import { realmSchemaNames } from '../../device-storage/realm/schemas/schemaNames';
import { MembershipStatus } from '../../types/chats.types';

export type SaveOrUpdateConversationParams = Partial<IConversation> & {
  id: string;
};

const useConversations = () => {
  const realm = useRealm();
  const conversations = useQuery(ConversationSchema).sorted([
    ['isPinned', true],
    ['lastMessageTimestamp', true],
  ]);
  const unArchivedConversations = conversations
    .filtered('conversationSettings.isArchived == false')
    .sorted([
      ['conversationSettings.isPinned', true],
      ['lastMessageTimestamp', true],
    ]);

  const archivedConversations = conversations
    .filtered('conversationSettings.isArchived == true')
    .sorted([
      ['conversationSettings.isPinned', true],
      ['lastMessageTimestamp', true],
    ]);

  const allUnarchivedChannels = useQuery(ConversationSchema).filtered(
    'conversationSettings.isArchived == false AND chatSpace.type == $0',
    ConversationType.CHANNEL,
  );
  const myUnarchivedChannels = useQuery(ConversationSchema).filtered(
    'conversationSettings.isArchived == false AND chatSpace.membershipStatus == $0 AND chatSpace.type == $1',
    MembershipStatus.OWNER,
    ConversationType.CHANNEL,
  );

  const unarchivedFollowingChannels = useQuery(ConversationSchema).filtered(
    'conversationSettings.isArchived == false AND chatSpace.membershipStatus IN $0 AND chatSpace.type == $1',
    [MembershipStatus.MEMBER, MembershipStatus.ADMIN],
    ConversationType.CHANNEL,
  );

  const unarchivedMyGroups = useQuery(ConversationSchema).filtered(
    'conversationSettings.isArchived == false AND chatSpace.membershipStatus == $0 AND chatSpace.type == $1',
    MembershipStatus.OWNER,
    ConversationType.GROUP,
  );

  const unarchivedGroups = useQuery(ConversationSchema).filtered(
    'conversationSettings.isArchived == false AND chatSpace.membershipStatus == $0 AND chatSpace.type == $1',
    [MembershipStatus.MEMBER, MembershipStatus.ADMIN],
    ConversationType.GROUP,
  );

  const getConversattionById = (id: string): any => {
    return realm.objects(realmSchemaNames.conversation).filtered('id == $0', id)[0];
  };

  const archiveUnarchiveConversation = (conversationId: string, value: boolean) => {
    ConversationRepo.archiveUnarchiveConversation(realm, conversationId, value);
  };

  return {
    conversations,
    unArchivedConversations,
    archivedConversations,
    archiveUnarchiveConversation,
    getConversattionById,
    allUnarchivedChannels,
    myUnarchivedChannels,
    unarchivedFollowingChannels,
    unarchivedMyGroups,
    unarchivedGroups,
  };
};

export default useConversations;
