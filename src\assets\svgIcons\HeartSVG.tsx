import * as React from "react";
import Svg, { Path } from "react-native-svg";
 
interface HeartSVGProps {
  size?: number;
  color?: string;
}
 
const HeartSVG: React.FC<HeartSVGProps> = ({
  size = 32,
  color = "#fff",
  ...props
}) => {
  return (
<Svg
      width={size}
      height={(size * 26) / 33} // maintain original aspect ratio (33x26)
      viewBox="0 0 33 26"
      fill="none"
      {...props}
>
<Path
        d="M32.034 5.407A10.259 10.259 0 0026.252.059a.906.906 0 00-.694.022.865.865 0 00-.466.51.912.912 0 00.542 1.17 8.407 8.407 0 014.762 4.405 8.39 8.39 0 01.26 6.476.86.86 0 00.033.694c.108.217.282.39.51.467a.77.77 0 00.314.054c.13 0 .26-.033.38-.087a.865.865 0 00.466-.51c.955-2.57.836-5.37-.314-7.864l-.01.01zM23.248 4.05a.908.908 0 00-1.172.543.912.912 0 00.543 1.172 5.481 5.481 0 013.113 2.874 5.532 5.532 0 01.173 4.242.912.912 0 00.543 1.171.77.77 0 00.314.054c.13 0 .26-.032.38-.086a.866.866 0 00.466-.51 7.297 7.297 0 00-.227-5.63 7.26 7.26 0 00-4.133-3.818V4.05zM21.035 16.135c-.369 0-.727-.043-1.074-.12-2.072 2.116-6.39 5.707-8.016 7.04-1.769-1.453-6.704-5.564-8.494-7.56-1.757-1.952-1.78-4.74-.043-6.487a4.586 4.586 0 016.443 0l1.323 1.335c.163.151.358.249.575.282.337.075.705-.022.966-.282l1.323-1.335a4.554 4.554 0 013.027-1.312 5.187 5.187 0 012.603-1.682 6.669 6.669 0 00-7.116 1.508l-.607.608-.597-.608c-2.603-2.603-6.834-2.603-9.427 0-2.549 2.55-2.56 6.585-.043 9.383 2.3 2.56 9.09 8.082 9.383 8.32.195.163.434.239.662.239h.021c.25.01.489-.076.695-.239.282-.238 7.072-5.76 9.372-8.32a7.83 7.83 0 00.825-1.095 5.246 5.246 0 01-1.801.325z"
        fill={color}
      />
<Path
        d="M21.035 9.084c-.152 0-.293.022-.434.054a1.894 1.894 0 00-1.464 1.844c0 1.053.846 1.899 1.898 1.899.25 0 .477-.044.694-.141a1.88 1.88 0 001.204-1.758 1.907 1.907 0 00-1.898-1.898z"
        fill={color}
      />
</Svg>
  );
};
 
export default HeartSVG;

