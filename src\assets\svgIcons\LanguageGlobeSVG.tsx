import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const LanguageGlobeSVG: React.FC<IconProps> = ({
  size = 24,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      {...props}
    >
      <Path
        d="M20.485 3.515A11.922 11.922 0 0012 0a11.922 11.922 0 00-8.485 3.515A11.922 11.922 0 000 12c0 3.205 1.248 6.219 3.515 8.485A11.921 11.921 0 0012 24c3.205 0 6.219-1.248 8.485-3.515A11.921 11.921 0 0024 12c0-3.205-1.248-6.219-3.515-8.485zm-.992.992c.64.641 1.189 1.35 1.638 2.11h-4.664a18.126 18.126 0 00-.795-2.854c-.3-.795-.642-1.473-1.017-2.025a10.53 10.53 0 014.838 2.77zM15.563 12c0 1.362-.093 2.684-.269 3.915H8.706A27.802 27.802 0 018.438 12c0-1.386.095-2.73.277-3.98h6.57c.182 1.25.277 2.594.277 3.98zM12 1.404c.802 0 1.684 1.067 2.359 2.855.268.71.494 1.503.676 2.358h-6.07c.182-.855.408-1.648.676-2.358.675-1.788 1.557-2.855 2.359-2.855zM4.507 4.507a10.53 10.53 0 014.839-2.77c-.376.553-.718 1.23-1.018 2.026a18.11 18.11 0 00-.795 2.854H2.87c.45-.76.998-1.469 1.638-2.11zM1.404 12c0-1.386.265-2.732.77-3.98h5.124A29.285 29.285 0 007.034 12c0 1.353.087 2.673.255 3.915H2.15A10.587 10.587 0 011.403 12zm3.103 7.492a10.651 10.651 0 01-1.676-2.174h4.69c.207 1.062.478 2.046.807 2.919.3.795.642 1.473 1.018 2.025a10.53 10.53 0 01-4.839-2.77zM12 22.596c-.802 0-1.684-1.067-2.359-2.855a16.418 16.418 0 01-.69-2.423h6.097c-.184.88-.415 1.695-.69 2.423-.674 1.788-1.556 2.855-2.358 2.855zm7.493-3.103a10.532 10.532 0 01-4.838 2.77c.375-.553.717-1.23 1.017-2.026.33-.873.6-1.857.807-2.918h4.69a10.66 10.66 0 01-1.676 2.174zm-2.782-3.578c.168-1.242.255-2.562.255-3.915 0-1.377-.09-2.719-.264-3.98h5.124c.505 1.248.77 2.594.77 3.98 0 1.363-.256 2.685-.744 3.915h-5.141z"
        fill={color}
      />
    </Svg>
  );
};

export default LanguageGlobeSVG;
