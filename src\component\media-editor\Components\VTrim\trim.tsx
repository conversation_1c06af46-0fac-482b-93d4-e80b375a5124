import React, { FC, useEffect, useRef, useState } from "react";
import { View, Text, StyleSheet, PanResponder, Dimensions, Animated } from "react-native";
import { useTimeData } from "../../utils/Context/TimeContext";
const { width: SCREEN_WIDTH } = Dimensions.get("window");

const THUMB_WIDTH = 20;
const SLIDER_WIDTH = SCREEN_WIDTH;

interface TrimmerProps {
  id: number;
  startTime: number;
  endTime: number;
  tStartTime: number;
  tEndTime: number;
  trimVideo: () => void;
  currentTime?: number;
}
const Trimmer: FC<TrimmerProps> = ({
  id,
  startTime,
  endTime,
  tStartTime,
  tEndTime,
  trimVideo,
  currentTime = 0,
}) => {
  const [sliderWidth] = useState(SLIDER_WIDTH);
  const animatedWidth = useState(new Animated.Value(0))[0];

  const TRACK_WIDTH = SLIDER_WIDTH; // 94.5% of screen width
  // const getPosition = (time: number) => Math.min((time / endTime || 0) * sliderWidth, sliderWidth - THUMB_WIDTH);
  // const getTimeFromPosition = (position: any) => (position / sliderWidth) * endTime;
  const getTimeFromPosition = (position: number) => (position / sliderWidth) * endTime;
  const getPosition = (time: number) => Math.min((time / endTime || 0) * TRACK_WIDTH, TRACK_WIDTH - THUMB_WIDTH);


  const { setTrimStartTime, setTrimEndTime } = useTimeData();

  useEffect(() => {
    const interval = setInterval(() => {
      if (currentTime?.current <= 0.2) {
        triggerAnimation();
      }
    }, 0);

    return () => clearInterval(interval);
  }, [tStartTime, tEndTime, currentTime]);

  const triggerAnimation = () => {
    const start = parseFloat(tStartTime.toString());
    const end = parseFloat(tEndTime.toString());
    const durationInSec = end - start;

    const pxPerSecond = SLIDER_WIDTH / endTime; // Consistent with track
    const targetWidth = getPosition(end) - getPosition(start);

    animatedWidth.setValue(0);

    Animated.timing(animatedWidth, {
      toValue: targetWidth,
      duration: durationInSec * 1000,
      useNativeDriver: false,
    }).start();

      //   animatedWidth.setValue(0);
  //   Animated.timing(animatedWidth, {
  //     toValue: targetWidth,
  //     duration: durationInSec * 1000,
  //     useNativeDriver: false,
  //   }).start();
  };


  // const triggerAnimation = () => {
  //   const start = parseFloat(tStartTime.toString());
  //   const end = parseFloat(tEndTime.toString());
  //   const targetWidth = getPosition(end) - getPosition(start);

  //   animatedWidth.setValue(0);
  //   Animated.timing(animatedWidth, {
  //     toValue: targetWidth,
  //     duration: Math.max(1000, 1000 * (end - start)),
  //     useNativeDriver: false,
  //   }).start();
  // };

  // PanResponder for Start Thumb
  const panResponderStart = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (event, gesture) => {
      const newPosition = getPosition(tStartTime) + gesture.dx;
      const newStartTime = getTimeFromPosition(
        Math.max(0, Math.min(newPosition, getPosition(tEndTime) - THUMB_WIDTH))
      );
      setTrimStartTime(newStartTime);
    },
    onPanResponderRelease: () => {
      trimVideo();
    },
  });

  // PanResponder for End Thumb
  const panResponderEnd = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (event, gesture) => {
      const newPosition = getPosition(tEndTime) + gesture.dx;
      const newEndTime = getTimeFromPosition(
        Math.min(sliderWidth, Math.max(newPosition, getPosition(tStartTime) + THUMB_WIDTH))
      );
      setTrimEndTime(newEndTime);
    },
    onPanResponderRelease: () => {
      trimVideo();
    },
  });

  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };
  return (
    <View id={id} style={styles.container}>
      <View style={styles.sliderContainer}>
        <View style={styles.track} />
        {/* Highlight Effect (white for Trimmed Area) */}
        <Animated.View
          style={[
            styles.highlight,
            {
              left: getPosition(tStartTime),
              width: animatedWidth,
            }
          ]}
        />
        {/* Start Thumb */}
        <View style={[styles.thumbWrapper, { left: getPosition(tStartTime) }]}>
          <Text style={styles.timeLabel}>{formatTime(tStartTime)}</Text>
          <View style={styles.thumb}
          // {...panResponderStart.panHandlers} 
          />
        </View>
        {/* End Thumb */}
        <View style={[styles.thumbWrapper, { left: getPosition(tEndTime)}]}>
          <Text style={styles.timeLabel}>{formatTime(tEndTime)}</Text>
          <View style={styles.thumb}
          //  {...panResponderEnd.panHandlers}
          />
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    bottom: 16,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 10,
  },
  sliderContainer: {
    width: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  track: {
    position: "absolute",
    height: 5,
    backgroundColor: 'rgb(171, 169, 169)',
    width: "94.5%",
    top: 16,
  },
  highlight: {
    position: "absolute",
    height: 5,
    marginLeft: 9,
    backgroundColor: "#fff",
    top: 16,
  },
  progressBar: {
    backgroundColor: "limegreen",
    top: 16,
  },
  thumb: {
    position: 'absolute',
    bottom: 5,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 15,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'white',
  },
  timeContainer: {
    width: '100%',
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
  },
  timeText: {
    color: "#fff",
    fontSize: 14,
  },
  thumbWrapper: {
    position: 'absolute',
    justifyContent: 'space-between',
  },
  timeLabel: {
    backgroundColor: '#fff',
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
    paddingHorizontal: 8,
    borderRadius: 2,
    bottom: 20,
    right: 11,
  },
});
export default Trimmer;