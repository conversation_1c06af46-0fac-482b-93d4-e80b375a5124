import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import ModalWrapper from '../../../../component/ModalWrapper'
import { hp } from '../../../../theme/fonts';
import KeyboardSVG from '../../../../assets/svgIcons/KeyboardSVG';
import { colors } from '../../../../theme/colors';
import TimerSVG from '../../../../assets/svgIcons/TimerSVG';
import CloseSVG from '../../../../assets/svgIcons/CloseSVG';
import MuteSVG from '../../../../assets/svgIcons/MuteSVG';
import { ComposedMessage } from '../../../../types/index.types';


interface IKeyboardMoadl {
    plusIconModal: boolean;
    setPlusIconModal: (value: boolean) => void;
    composerType?: string;
    switchToKeyboard: () => void;
    onSchedulePress: () => void;
    onSlientMessage: () => void;
    composedMessage: ComposedMessage
}


const KeyboardModal = ({ plusIconModal, setPlusIconModal = () => { }, composerType, composedMessage, switchToKeyboard, onSchedulePress, onSlientMessage }: IKeyboardMoadl) => {
    return (
        <ModalWrapper isVisible={plusIconModal} onCloseModal={() => setPlusIconModal(false)}>
            <View style={{ paddingHorizontal: hp(2) }}>
                <View>
                    {composerType == 'emojis' ? (
                        <TouchableOpacity
                            onPress={switchToKeyboard}
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 10,
                                marginBottom: 20,
                            }}
                        >
                            <KeyboardSVG color={colors.black_23} size={19} />
                            <Text style={styles.bottomText}>Switch to keyboard</Text>
                        </TouchableOpacity>
                    ) : (
                        <></>
                    )}

                    <Text
                        style={{
                            fontSize: 16,
                            color: colors.black_23,
                            fontWeight: '600',
                            marginBottom: 23,
                        }}
                    >
                        Options
                    </Text>

                    <TouchableOpacity
                        onPress={onSchedulePress}
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            marginBottom: 20,
                        }}
                    >
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                            <TimerSVG
                                color={composerType === 'schedule' ? colors.mainPurple : colors.black_23}
                                size={18}
                            />
                            <Text
                                style={[
                                    styles.bottomText,
                                    { color: composerType === 'schedule' ? colors.mainPurple : colors.black_23 },
                                ]}
                            >
                                Schedule
                            </Text>
                        </View>
                        {composerType === 'schedule' && <CloseSVG size={14} color={colors.mainPurple} />}
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={onSlientMessage}
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            marginBottom: 20,
                        }}
                    >
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                            <MuteSVG
                                color={composedMessage.isSilent ? colors.mainPurple : colors.black_23}
                                size={19}
                            />
                            <Text
                                style={[
                                    styles.bottomText,
                                    { color: composedMessage.isSilent ? colors.mainPurple : colors.black_23 },
                                ]}
                            >
                                Silent message
                            </Text>
                        </View>
                        {composedMessage.isSilent && <CloseSVG size={14} color={colors.mainPurple} />}
                    </TouchableOpacity>
                    {/* <TouchableOpacity
                          onPress={() => {
                            setComposerType('emojis');
                            setPlusIconModal(false);
                          }}
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 10,
                            marginBottom: 35,
                          }}
                        >
                          <EmojiSVG color={colors.black_23} size={18} />
                          <Text style={styles.bottomText}>Emojis</Text>
                        </TouchableOpacity> */}
                </View>
            </View>
        </ModalWrapper>
    )
}

export default KeyboardModal

const styles = StyleSheet.create({
    bottomText: {
        fontSize: 16,
        color: colors.black_23,
        fontWeight: '400',
    },
})