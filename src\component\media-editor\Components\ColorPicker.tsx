import React, { FC, memo, useState } from 'react';
import { StyleSheet, View, Image, TouchableOpacity, Platform } from 'react-native';
import ColorPicker, { Swatches, HueSlider } from 'reanimated-color-picker';
import { Images } from '../Assets/Images';
import { BlurView } from '@react-native-community/blur';
import { hp } from '../utils/Constants/dimensionUtils';
import Animated, { runOnJS, useSharedValue } from 'react-native-reanimated';
import { ColorFormatsObject } from 'reanimated-color-picker';
import SLider from './Sliderprogress';

interface TextColorPickerProps {
  selectedTextId: string | null;
  textStyles: Record<string, { color: string; font: string }>;
  setTextStyles: (styles: Record<string, { color: string; font: string }>) => void;
  isBgBlur?: boolean;
  isDrawing: boolean;
  onColorChange: any;
  strokeWidthFun: any;
  strokeWidth: any;
  onOkPress: () => void;
  activeTool: string | null;
}

const availableFonts = [
  'BadScript-Regular',
  'BreeSerif-Regular',
  'DancingScript-VariableFont_wght',
  'DMSerifDisplay-Italic',
  'DMSerifDisplay-Regular',
  'Inter_18pt-Bold',
  'Inter_18pt-Regular',
  'PinyonScript-Regular',
  'PlaywriteIN-Regular',
  'Roboto-Bold',
  'Roboto-Regular',
];

const TextColorPicker: FC<TextColorPickerProps> = memo(
  ({
    selectedTextId,
    textStyles,
    setTextStyles,
    isBgBlur = true,
    isDrawing = false,
    strokeWidthFun,
    strokeWidth,
    onColorChange,
    onOkPress,
    activeTool,
  }) => {
    const [showHueSlider, setShowHueSilder] = useState(false);
    const [colorSelected, setColorSelected] = useState(false);

    if (!selectedTextId && !isDrawing) return null; // Hide picker if no text is selected and not in drawing mode

    const selectedText = textStyles[selectedTextId] || {
      color: '#fff',
      font: 'DMSerifDisplay-Italic',
    };

    const ColorsArr = ['#CC4F49', '#309dba', '#ccb929'];
    const ColorsArr1 = ['#CC4F49', '#309dba']; // Limited colors for drawing mode

    const selectedColor = useSharedValue(isDrawing ? ColorsArr1[0] : ColorsArr[0]);

    // Create worklet functions for the color picker callbacks
    const onColorSelect = (color: ColorFormatsObject) => {
      'worklet';
      selectedColor.value = color.hex;
    };

    const handleColorComplete = (newColor: string) => {
      if (isDrawing) {
        onColorChange(newColor);
      } else {
        setTextStyles({
          ...textStyles,
          [selectedTextId as string]: { ...selectedText, color: newColor },
        });
      }
    };

    const handleFontChange = () => {
      if (isDrawing) return; // Prevent font change in drawing mode
      const currentIndex = availableFonts.indexOf(selectedText.font);
      const nextFont = availableFonts[(currentIndex + 1) % availableFonts.length];
      setTextStyles({
        ...textStyles,
        [selectedTextId]: { ...textStyles[selectedTextId], font: nextFont },
      });
    };

    const handleOkPress = () => {
      setShowHueSilder(false);
      onOkPress();
    };

    const renderColorView = () => (
      <ColorPicker
        value={selectedColor.value}
        onChange={onColorSelect}
        onComplete={(color) => {
          'worklet';
          runOnJS(handleColorComplete)(color.hex);
        }}
      >
        <Animated.View style={styles.colorPickerRow}>
          {/* Color Selection */}
          {activeTool !== 'erase' &&
            (showHueSlider ? (
              <HueSlider style={styles.hueSlider} sliderThickness={15} thumbSize={22} />
            ) : (
              <Swatches
                style={styles.swatchesContainer}
                swatchStyle={styles.swatchStyle}
                colors={isDrawing ? ColorsArr1 : ColorsArr}
              />
            ))}

          {/* Color Picker Button */}
          {activeTool !== 'erase' && (
            <TouchableOpacity
              style={styles.paintbtn}
              onPress={() => setShowHueSilder(!showHueSlider)}
            >
              <Image source={Images.Paint} style={styles.iconStyle} resizeMode="contain" />
            </TouchableOpacity>
          )}

          {/* Brush & Eraser Size Slider */}
          {isDrawing && !showHueSlider && (
            <SLider
              initialvalue={strokeWidth}
              animationEnabled={true}
              strokeWidthFun={strokeWidthFun}
            />
          )}

          <View style={{ flex: 1 }} />
          <TouchableOpacity
            style={styles.paintbtn}
            onPress={isDrawing ? handleOkPress : handleFontChange}
          >
            <Image
              source={isDrawing ? Images.Submit : Images.font}
              style={[styles.iconStyle, { width: isDrawing ? 16 : 20 }]}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </Animated.View>
      </ColorPicker>
    );

    return (
      <View style={styles.container}>
        {isBgBlur ? (
          <BlurView
            style={styles.blurView}
            blurAmount={100}
            reducedTransparencyFallbackColor="white"
            overlayColor="transparent"
            blurType={'ultraThinMaterialDark'}
          >
            {renderColorView()}
          </BlurView>
        ) : (
          renderColorView()
        )}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: { position: 'absolute', bottom: hp(8), width: '100%', height: '8%', zIndex: 100001 },
  sliderContainer: {
    width: 150,
    height: 40,
    justifyContent: 'center',
    // marginHorizontal:15,
    marginLeft: 25,
  },
  blurView: { flex: 1, justifyContent: 'center' },
  paintbtn: {
    // backgroundColor: 'rgba(255, 255, 255, 0.2)',
    backgroundColor: Platform.OS == 'android' ? '#FFFFFF33' : 'transparent',
    borderRadius: 25,
    height: 41,
    width: 41,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorPickerRow: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 8 },
  swatchesContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    paddingRight: 5,
  },
  swatchStyle: {
    borderRadius: 25,
    height: 41,
    width: 41,
    shadowColor: 'white',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,

    elevation: 0,
  },
  iconStyle: { width: 20, height: 20 },
  hueSliderContainer: { width: '70%' },
  hueSlider: { width: '70%', paddingHorizontal: 10 },
  knob: {
    width: 20,
    height: 20,
    borderRadius: 15,
    backgroundColor: 'white',
    position: 'absolute',
    top: '50%',
    marginTop: -9.8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
});

export default memo(TextColorPicker);
