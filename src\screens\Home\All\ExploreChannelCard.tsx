// components/ExploreChannelCard.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Button,
  ActivityIndicator,
} from 'react-native';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import {
  IConversation,
  IExploreChannelCardItem,
} from '../../../device-storage/realm/schemas/ConversationSchema';
import ButtonPurple from '../../../component/ButtonPurple';
import RenderUserIcon from '../../../component/RenderUserIcon';

export interface IExploreChannelCardProps {
  item: IExploreChannelCardItem;
  isFollowing: boolean;
  onFollowPress: () => void;
  onPress?: () => void;
  isLoading?: boolean;
}

const ExploreChannelCard = ({
  item,
  isFollowing,
  onFollowPress,
  onPress,
  isLoading,
}: IExploreChannelCardProps) => {
  const isDeleted = item?.isDeleted;
  return (
    <TouchableOpacity onPress={isDeleted ? undefined : onPress} disabled={isDeleted}>
      <View style={styles.cardContainer}>
        <RenderUserIcon size={50} url={item.displayPic} />
        {/* <Image source={{ uri: item.displayPic }} style={styles.avatar} /> */}
        <View style={styles.channelInfo}>
          <Text style={styles.name}>{item.displayName || item.name}</Text>
          <Text style={styles.description} numberOfLines={1}>
            {item.description || (isDeleted ? 'This channel has been deleted.' : '')}
          </Text>
        </View>
        {isDeleted ? (
          <Text style={styles.deletedLabel}></Text>
        ) : (
          <TouchableOpacity onPress={onFollowPress}>
            <View style={styles.followBtn}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.mainPurple} />
              ) : (
                <Text
                  style={{
                    color: colors.mainPurple,
                    fontSize: 15,
                    fontWeight: '500',
                  }}
                >
                  {isFollowing ? 'Following' : 'Follow'}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ExploreChannelCard;

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 14,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  channelInfo: {
    flex: 1,
  },
  name: {
    color: colors.black,
    fontSize: 16,
    fontWeight: 'bold',
  },
  description: {
    ...commonFontStyle(400, 13, colors.gray_80),
    marginTop: 2,
  },
  followBtn: {
    minWidth: 90,
    height: 34,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: colors.purple_1,
    paddingHorizontal: 16,
  },
  followText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 13,
  },
  followingBtn: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.mainPurple,
  },
  followingText: {
    color: colors.mainPurple,
  },
  btnStyle: {
    width: 90,
    height: 34,
    paddingHorizontal: 8,
    borderRadius: 20,
  },
  deletedContainer: {
    opacity: 0.5,
  },

  deletedLabel: {
    color: colors._DB1515_red,
    fontSize: 13,
    fontWeight: 'bold',
  },
});
