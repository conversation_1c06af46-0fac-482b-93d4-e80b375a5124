import {
  FlatList,
  Image,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { use, useEffect, useState } from 'react';
import GradientView from '../../component/GradientView';
import { AppStyles } from '../../theme/appStyles';
import HeaderBackWithTitle from '../../component/HeaderBackWithTitle';
import { IMAGES } from '../../assets/Images';
import { colors } from '../../theme/colors';
import RenderUserIcon from '../../component/RenderUserIcon';
import { androidVersion, commonFontStyle, hp, SCREEN_HEIGHT, wp } from '../../theme/fonts';
import SearchInput from '../../component/SearchInput';
import SettingTab from './SettingTab';
import ProfileTab from './ProfileTab';
import { useTranslation } from 'react-i18next';
import Profile1 from '../../assets/animated_profile/p_1.json';
import Profile2 from '../../assets/animated_profile/p_2.json';
import Profile3 from '../../assets/animated_profile/p_3.json';
import LottieView from 'lottie-react-native';
import Carousel from 'react-native-snap-carousel';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import ImageCropPicker from 'react-native-image-crop-picker';
import { uploadImage } from '../../utils/apiGlobal';
import CustomImage from '../../component/CustomImage';
import { navigateTo, resetNavigation } from '../../utils/commonFunction';
import { SCREENS } from '../../navigation/screenNames';
import { useMe } from '../../hooks/util/useMe';
import ModalWrapper from '../../component/ModalWrapper';
import PlusSVG from '../../assets/svgIcons/PlusSVG';
import InfoSVG from '../../assets/svgIcons/InfoSVG';
import ReferSVG from '../../assets/svgIcons/ReferSVG';
import LogoutSVG from '../../assets/svgIcons/LogoutSVG';
import { logout } from '../../utils/ApiService';
import OptionRow from '../../component/OptionRow';
import EditImageSVG from '../../assets/svgIcons/EditImageSVG';
import ThreeDotsSVG from '../../assets/svgIcons/ThreeDotsSVG';
import { Ionicons } from '../../utils/vectorIcons';
import { getSocket } from '../../socket-client/socket';

type Props = {};

type ImageData = {
  uri: string;
  type: string;
  fileName: string;
} | null;

const ProfileScreen = (props: Props) => {
  const { user, updateUser, userPreferencesState, clearUserData } = useMe();

  const { t } = useTranslation();
  const _carouselRef = React.useRef<Carousel<any>>(null);
  const isFocused = useIsFocused();

  const [imageData, setImageData] = useState<ImageData>(null);
  const [imageSource, setImageSource] = useState<string | null>(null);
  const [tab, setTab] = useState('Profile');
  const [isEdit, setIsEdit] = useState(false);
  const [_index, setIndex] = useState(0);
  const navigation = useNavigation();
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  const profileColor =
    userPreferencesState?.userPreferences?.account?.profileColors?.primary || colors.mainPurple;

  const ImagesList = [
    {
      id: 0,
      profile: null,
    },
    {
      id: 1,
      profile: Profile1,
    },
    {
      id: 2,
      profile: Profile2,
    },
    {
      id: 3,
      profile: Profile3,
    },
  ];

  const isUploadedImage = _index === 0;
  const lottieProfile = ImagesList[_index]?.profile;

  let tabs = [
    'Profile',
    // 'Wallet',
    // 'Stickers',
    'Settings',
  ];

  useEffect(() => {
    setImageSource(user?.image || null);
    setTab('Profile');
  }, [user?.image]);

  useEffect(() => {
    setIsEdit(false);
  }, [isFocused]);

  const openImagePicker = () => {
    ImageCropPicker.openPicker({ mediaType: 'photo' })
      .then((image: any) => {
        const imageData = {
          uri: image.path,
          type: image.mime || 'image/jpeg',
          fileName:
            image.filename ||
            image.path?.split('/')?.pop() ||
            image.sourceURL?.split('/')?.pop() ||
            'photo.jpg',
        };
        setImageData(imageData);
        setImageSource(imageData.uri || user?.image || null);
        setIndex(0);
      })
      .catch((error) => {
        console.error('Image picker error:', error);
      });
  };

  const RenderTab = () => {
    return (
      <View style={[styles.tabView, { justifyContent: 'center', alignItems: 'center' }]}>
        {tabs.map((item, index) => {
          return (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setIsEdit(false);
                setTab(item);
              }}
              style={styles.tabViewInner}
            >
              <Text
                style={
                  tab == item
                    ? { ...commonFontStyle(600, 15, colors.mainPurple) }
                    : { ...commonFontStyle(500, 15, colors.gray_80) }
                }
              >
                {t(item)}
              </Text>
              <View
                style={[
                  styles.lineBottom,
                  {
                    backgroundColor: tab == item ? colors.mainPurple : 'transparent',
                  },
                ]}
              />
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  useEffect(() => {
    if (Number(androidVersion) <= 13) {
      if (isFocused) {
        StatusBar.setBackgroundColor(profileColor);
      }
    }
    return () => {
      if (Number(androidVersion) <= 13) {
        StatusBar.setBackgroundColor(colors.mainPurple);
        StatusBar.setTranslucent(true);
        StatusBar.setBarStyle('light-content');
      }
    };
  }, [isFocused]);

  return (
    <View
      style={{
        backgroundColor: profileColor,
        flex: 1,
      }}
    >
      <SafeAreaView>
        {/* <StatusBar animated={true} backgroundColor={profileColor} barStyle={'default'} /> */}
        <HeaderBackWithTitle
          title={t('Account')}
          renderRight={() => {
            return (
              <View style={{ ...styles.row, gap: 20, marginEnd: 10 }}>
                {tab == 'Profile' && (
                  <CustomImage
                    source={isEdit ? IMAGES.wrong : IMAGES.editSend}
                    onPress={() => {
                      _carouselRef.current?.snapToItem(_index);
                      setIsEdit(!isEdit);
                    }}
                    size={20}
                  />
                )}
                {/* <TouchableOpacity
                  onPress={() => {
                    navigateTo('SavePostScreen');
                  }}
                >
                  <Ionicons name={'bookmark'} size={23} color={colors.white} />
                </TouchableOpacity> */}
                <TouchableOpacity
                  onPress={() => setShowMoreMenu(true)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <ThreeDotsSVG />
                </TouchableOpacity>
              </View>
            );
          }}
        />
      </SafeAreaView>
      <View style={AppStyles.flex}>
        {isEdit ? (
          <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            {_index === 0 && isEdit ? (
              <View style={styles.avatarContainer}>
                {/* Profile Image */}
                <Image
                  source={imageSource ? { uri: imageSource } : IMAGES.profile_image}
                  style={styles.avatar}
                />

                {/* Overlayed Edit Icon */}
                <TouchableOpacity style={styles.updateIcon} onPress={openImagePicker}>
                  <EditImageSVG size={22} />
                </TouchableOpacity>
              </View>
            ) : null}
            <Text style={styles.userName}>{user?.name}</Text>
          </View>
        ) : (
          <>
            <View style={styles.userIcon}>
              {isUploadedImage || !lottieProfile ? (
                <RenderUserIcon url={imageSource || ''} size={106} />
              ) : (
                <LottieView
                  source={lottieProfile}
                  autoPlay
                  loop
                  style={{ height: 106, width: 106 }}
                />
              )}
            </View>

            <Text style={styles.userName}>{user?.name}</Text>
          </>
        )}
        <View style={[AppStyles.bottomWhiteViewWithoutPadding, { flex: 1 }]}>
          <View>
            <RenderTab />
          </View>
          <View style={styles.padding}>
            {tab == 'Profile' && (
              <ProfileTab
                isEdit={isEdit}
                onPressUpdate={() => {
                  _carouselRef.current?.snapToItem(_index);
                  setIsEdit(!isEdit);
                }}
                imageSource={imageData}
                imageIndex={_index}
              />
            )}

            {tab == 'Settings' && <SettingTab />}
          </View>
        </View>
      </View>
      {/* Bottomsheet Menu Modal */}
      <ModalWrapper isVisible={showMoreMenu} onCloseModal={() => setShowMoreMenu(false)}>
        <View
          style={{
            paddingBottom: 10,
            backgroundColor: colors.white,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          }}
        >
          {[
            // {
            //   icon: PlusSVG,
            //   text: 'Add account',
            //   onPress: () => {},
            // },
            // {
            //   icon: ReferSVG,
            //   text: 'Refer and earn',
            //   onPress: () => {},
            // },
            {
              icon: InfoSVG,
              text: 'Help',
              onPress: () => {
                setShowMoreMenu(false);
                navigateTo(SCREENS.HelpScreen);
              },
            },
            {
              icon: LogoutSVG,
              text: 'Logout',
              textStyle: { color: 'red' },
              onPress: async () => {
                setShowMoreMenu(false);
                await clearUserData();
                resetNavigation(SCREENS.SignupScreen, {});
              },
            },
          ].map((opt, idx) => (
            <React.Fragment key={opt.text}>
              <OptionRow {...opt} />
              {idx === 0 && (
                <View style={{ height: 1, backgroundColor: '#eee', marginVertical: 2 }} />
              )}
            </React.Fragment>
          ))}
        </View>
      </ModalWrapper>
    </View>
  );
};

export default ProfileScreen;

const styles = StyleSheet.create({
  userIcon: {
    backgroundColor: colors.white,
    alignSelf: 'center',
    borderWidth: 3,
    borderRadius: 200,
    borderColor: colors.white,
    overflow: 'visible',
    // zIndex: 100,
  },
  userName: {
    textAlign: 'center',
    ...commonFontStyle(600, 20, colors.white),
    marginTop: 5,
    marginBottom: hp(3),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(1),
    // gap: -20,
  },
  moreMenu: {
    paddingHorizontal: hp(1),
  },
  moreMenuStyle: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  tabView: {
    flexDirection: 'row',
    paddingTop: hp(2),
  },
  tabViewInner: {
    paddingHorizontal: hp(2),
    paddingTop: hp(2),
  },
  lineBottom: {
    height: 3,
    width: 20,
    borderRadius: 50,
    alignSelf: 'center',
    marginBottom: hp(2),
    marginTop: 4,
  },
  padding: {
    paddingHorizontal: hp(2),
    flex: 1,
  },

  avatarContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },

  avatar: {
    width: 106,
    height: 106,
    borderRadius: 60, // Circular
    borderWidth: 3,
    borderColor: '#ffff',
  },

  updateIcon: {
    position: 'absolute',
    bottom: -10,
    alignSelf: 'center',
    borderColor: colors._5A419F_purple,
    borderWidth: 5,
    padding: 4,
    borderStyle: 'solid',
    borderRadius: 20,
    backgroundColor: colors.white,
    // elevation: 3, // for Android shadow
    // shadowColor: '#000', // for iOS shadow
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.3,
    // shadowRadius: 2,
  },
});

// 364
