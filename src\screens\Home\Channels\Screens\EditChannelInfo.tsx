import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ToastAndroid,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import ImageCropPicker from 'react-native-image-crop-picker';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { IChatScreenProps } from '../../Chats/ChatSpecificScreen';
import { ChatService } from '../../../../service/ChatService';
import { IMAGES } from '../../../../assets/Images';
import { updateChatspaceInfo } from '../../../../api/Chatspace/chatspace.api';
import CommonView from '../../../../component/CommonView';
import EditImageSVG from '../../../../assets/svgIcons/EditImageSVG';
import Input from '../../../../component/Input';
import { hp } from '../../../../theme/fonts';
import ButtonPurple from '../../../../component/ButtonPurple';
import { colors } from '../../../../theme/colors';
import { showToast } from '../../../../utils/commonFunction';

type EditChannelParams = {
  EditChannelInfo: {
    userDetails: IChatScreenProps;
  };
};

interface ChannelImageType {
  uri: string;
  type: string;
  name: string;
}

const MAX_CHANNEL_NAME_LENGTH = 30;
const MAX_CHANNEL_DESCRIPTION_LENGTH = 250;

const EditChannelInfo = () => {
  const { t } = useTranslation();
  const route = useRoute<RouteProp<EditChannelParams, 'EditChannelInfo'>>();
  const navigation = useNavigation();

  const user = route.params.userDetails;

  const liveConversationRealm = ChatService.getLiveConversation(user.id);
  const liveConversation = useMemo(() => {
    if (!liveConversationRealm) return null;
    return JSON.parse(JSON.stringify(liveConversationRealm));
  }, [liveConversationRealm]);

  const [channelName, setChannelName] = useState(liveConversation?.displayName || '');
  const [channelDescription, setChannelDescription] = useState(liveConversation?.description || '');
  const [channelImage, setChannelImage] = useState<ChannelImageType | string>(
    liveConversation?.displayPic || IMAGES.channel,
  );
  const [isUpdating, setIsUpdating] = useState(false);


  const onPressGallery = () => {
    ImageCropPicker.openPicker({ cropping: true }).then((image) => {
      const updatedImg = {
        uri: image.path,
        name: image.filename ?? image.path?.split('/')?.pop() ?? 'photo.jpg',
        type: image.mime,
      };
      setChannelImage(updatedImg);
    });
  };

  const onSubmit = async (chatspaceId: string) => {
    if (!channelName.trim()) {
      return showToast(t('Channel name is required'));
    }
    if (channelName.length > MAX_CHANNEL_NAME_LENGTH) {
      return showToast(t(`Channel name cannot exceed ${MAX_CHANNEL_NAME_LENGTH} characters`));
    }
    if (channelDescription.length > MAX_CHANNEL_DESCRIPTION_LENGTH) {
      return showToast(
        t(`Channel description cannot exceed ${MAX_CHANNEL_DESCRIPTION_LENGTH} characters`),
      );
    }

    setIsUpdating(true);

    try {
      const payload: any = {};
      if (channelName !== user.displayName) payload.name = channelName;
      if (channelDescription !== user.conversation?.description)
        payload.description = channelDescription;

      if (channelImage && typeof channelImage !== 'string') {
        payload.file = {
          uri: channelImage.uri,
          type: channelImage.type || 'image/jpeg',
          name: channelImage.name || 'photo.jpg',
        };
      }

      if (Object.keys(payload).length > 0) {
        const result = await updateChatspaceInfo(chatspaceId, payload);
        if (result?.systemMessage) {
          ChatService.updateChatSpace(result?.systemMessage?.eventPayload);
        }
      }

      navigation.pop(2);
    } catch (err) {
      console.error('Failed to update channel info:', err);
      showToast('Failed to update channel info');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{ flex: 1 }}
    >
      <CommonView headerTitle={t('Edit Channel')}>
        <View style={styles.container}>
          {/* Channel Image */}
          <View style={styles.imageContainer}>
            <Image
              source={
                typeof channelImage === 'string'
                  ? { uri: channelImage }
                  : channelImage?.uri
                  ? { uri: channelImage.uri }
                  : IMAGES.profile_image
              }
              style={styles.channelImage}
            />
            <TouchableOpacity onPress={onPressGallery} style={styles.galleryButton}>
              <EditImageSVG size={22} color={colors.gray_80} />
            </TouchableOpacity>
          </View>

          {/* Channel Name */}
          <Input
            title={t('Channel name')}
            value={channelName}
            onChangeText={(text: string) => {
              if (text.length > MAX_CHANNEL_NAME_LENGTH) {
                showToast(t(`Channel name cannot exceed ${MAX_CHANNEL_NAME_LENGTH} characters`));
                return;
              }
              setChannelName(text);
            }}
            inputContainer={{ backgroundColor: '#F9F9F9' }}
          />

          {/* Channel Description */}
          <Input
            title={t('Channel description')}
            value={channelDescription}
            onChangeText={(text: string) => {
              if (text.length > MAX_CHANNEL_DESCRIPTION_LENGTH) {
                showToast(t(`Channel description cannot exceed ${MAX_CHANNEL_DESCRIPTION_LENGTH} characters`));
                return;
              }
              setChannelDescription(text);
            }}
            multiline
            inputContainer={{ height: hp(15), alignItems: 'flex-start' }}
          />

          {/* Submit Button */}
          <ButtonPurple
            title={t('Done')}
            onPress={() => onSubmit(user.id)}
            isLoading={isUpdating}
            disabled={isUpdating}
          />
        </View>
      </CommonView>
    </KeyboardAvoidingView>
  );
};

export default EditChannelInfo;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 24,
  },
  imageContainer: {
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  channelImage: {
    height: hp(15),
    width: hp(15),
    borderRadius: hp(15 / 2),
    borderColor: colors.gray_80,
  },
  galleryButton: {
    position: 'absolute',
    bottom: -10,
    alignSelf: 'center',
    backgroundColor: colors.white,
    borderRadius: 100,
    padding: 6,
    elevation: 4,
  },
});
