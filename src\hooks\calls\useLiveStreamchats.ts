import {
  useState,
  useEffect,
  useRef,
  useCallback,
  SetStateAction,
  Dispatch,
  RefObject,
} from 'react';

import { getStreamChatMessagesApi } from '../../api/Chatspace/chatspace.api';
import { Comment } from '../../screens/Home/Channels/liveStreams/CommentContainer';
import useSocket from '../../socket-client/useSocket';

interface UsePaginatedChatOptions {
  liveStreamId: string;
  limit?: number;
  initialChatRef: RefObject<Comment[] | null>;
  loadInitialChats?: boolean;
}

export interface IPaginatedChatController {
  messages: any[];
  loadInitialMessages: () => Promise<void>;
  loadOlderMessages: () => Promise<void>;
  loadingOlder: boolean;
  hasMore: boolean;
  setMessages: Dispatch<SetStateAction<Comment[]>>;
  loadingIntialChats: boolean;
}

export function usePaginatedChatMessages({
  liveStreamId,
  limit = 20,
  initialChatRef,
  loadInitialChats = true,
}: UsePaginatedChatOptions): IPaginatedChatController {
  const { socket } = useSocket();
  const [messages, setMessages] = useState<Comment[]>([]);
  const [loadingOlder, setLoadingOlder] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const paginationAnchor = useRef<string | null>(null);
  const [loadingIntialChats, setLoadingInitialChats] = useState(loadInitialChats);

  const loadInitialMessages = useCallback(async () => {
    try {
      const now = new Date().toISOString();
      paginationAnchor.current = now;

      const res = await getStreamChatMessagesApi({
        liveStreamId,
        paginationAnchor: now,
        limit,
      });

      const initialChat = initialChatRef?.current;
      const combinedChats = [...res.messages];

      if (initialChat && initialChat.length > 0) {
        combinedChats.push(...initialChat);
        initialChatRef.current = null;
      }

      setMessages(combinedChats || []);
      setHasMore(res.hasMore);
    } catch (err) {
      console.error('📦 [PaginatedChat] ❌ Failed to load initial messages', err);
    } finally {
      setLoadingInitialChats(false);
    }
  }, [liveStreamId, limit]);

  const loadOlderMessages = useCallback(async () => {
    if (loadInitialChats === false) return;
    if (loadingOlder) {
      return;
    }
    if (!hasMore) {
      return;
    }

    setLoadingOlder(true);

    try {
      const lastMessage = messages[0];
      const lastCreatedAt = lastMessage?.createdAt;

      const res = await getStreamChatMessagesApi({
        liveStreamId,
        paginationAnchor: paginationAnchor.current!,
        lastCreatedAt,
        limit,
      });

      // ⬅️ Prepend older messages to the front
      setMessages((prev) => [...(res.messages || []), ...prev]);
      setHasMore(res.hasMore);
    } catch (err) {
      console.error('📦 [PaginatedChat] ❌ Failed to load older messages', err);
    } finally {
      setLoadingOlder(false);
    }
  }, [loadingOlder, hasMore, messages, liveStreamId, limit]);

  return {
    messages,
    loadInitialMessages,
    loadOlderMessages,
    loadingOlder,
    hasMore,
    setMessages,
    loadingIntialChats,
  };
}
