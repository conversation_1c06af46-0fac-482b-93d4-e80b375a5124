import Api from '../../utils/api';

const apiPaths = {
  getEmbeddingDocument: 'v1/calls/embedding-document',
  embedAudio: 'v1/calls/embed_voice',
};

export enum EmbeddingStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
export interface EmbeddingDoc {
  userId: string;
  fileUrl: string;
  embeddingId?: string;
  status?: EmbeddingStatus;
  task_id?: string;
  error?: string;
}

export interface EmbeddingDocResponse {
  status: boolean;
  data: EmbeddingDoc;
  status_code: number;
}

export const getEmbeddingDocumentApi = async (): Promise<EmbeddingDocResponse> => {
  try {
    const response = await Api.get(apiPaths.getEmbeddingDocument);
    const data = await response.body;
    if (!data) throw new Error('No data found');
    return data;
  } catch (error) {
    console.error(error);
    throw new Error();
  }
};

export const embedAudioApi = async (payload: { fileUrl: string; fileName: string }) => {
  try {
    const response = await Api.post(apiPaths.embedAudio, payload);
    const data = await response.body;

    return data;
  } catch (error) {
    console.error(error);
  }
};

type CallHisoryParams = {
  page?: number;
  perPage?: number;
  search?: string;
  callStatus?: string[];
  callType?: string[];
};

export const callHistoryAPI = async (
  params: CallHisoryParams = { page: 1, perPage: 50, search: '', callStatus: [], callType: [] },
) => {
  let { page, perPage, search = '', callStatus = [], callType = [] } = params;
  const searchQuery = search && `&search=${search}`;
  const callStatusQuery = callStatus.map((item) => `&callStatus=${item}`).join('') ?? '';
  const callTypeQuery = callType.map((item) => `&callType=${item}`).join('') ?? '';

  let queryParam = `?page=${page}&perPage=${perPage}${
    searchQuery + callStatusQuery + callTypeQuery
  }`;

  try {
    const response = await Api.get(`v1/calls/call_history${queryParam}`);
    if (response?.body && response?.body?.status) {
      return response.body;
    }
  } catch (error) {
    throw error;
  }
};
export interface CallUser {
  _id: string;
  username: string;
  name: string;
  image: string;
  e164CompliantNumber?: string;
}

export interface CallResponse {
  _id: string;
  initiatorId: string;
  callType: 'audio' | 'video'; // or use your CallType enum
  originType: 'directConversation' | 'groupConversation' | 'contacts' | 'callHistory';
  invitedUserIds: CallUser[];
  participants: CallUser[];
  initiatorDetails: CallUser;
  scheduledAt: string | null;
  createdAt: string;
  updatedAt: string;
  endedAt?: string;
  status: 'ongoing' | 'ended';
  __v: number;
}

export const getCallInfoApi = async (callId: string): Promise<CallResponse | undefined> => {
  try {
    const response = await Api.get(`v1/calls/get_call_info/${callId}`);
    if (response?.body && response?.body?.status) {
      return response.body.data;
    }
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
