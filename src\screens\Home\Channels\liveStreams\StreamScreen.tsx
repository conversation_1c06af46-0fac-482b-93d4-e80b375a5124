// StreamScreen.tsx

import React, { useCallback, useEffect, useRef, useState, forwardRef, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Animated,
  Alert,
} from 'react-native';
import {
  ApiVideoLiveStreamMethods,
  ApiVideoLiveStreamView,
} from '@cbucket/react-native-livestream';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  StreamerLiveStreamSession,
  publishStreamFailedApi,
  publishStreamSuccessApi,
  startStreamApi,
  stopStreamApi,
  streamPublishDisconnectApi,
} from '../../../../api/Chatspace/chatspace.api';
import { getStreamToken } from '../../../../utils/asyncStorage';
import { navigationRef } from '../../../../navigation/RootContainer';

import LiveStreamHeader from './LiveStreamHeader';
import OverlayComponent from './OverlayComponent';

import useLivestreamSpace, { UserRole } from '../../../../hooks/channels/useLivestreamSpace';

import { DummyScreenStyles } from '../Screens/DummyScreen';
import { useOverlayVisibility } from './ViewerStreamScreen';

import CustomAlert from '../../../../component/Common/CustomAlert';
import { useMe } from '../../../../hooks/util/useMe';

type StreamScreenParams = {
  screenprops: {
    liveStreamInfo: StreamerLiveStreamSession;
  };
};

type CameraSettings = {
  facingMode: 'front' | 'back';
};

const StreamScreen = () => {
  const [isStreaming, setIsStreaming] = useState(false);

  const ref = useRef<ApiVideoLiveStreamMethods>(null);
  const statusRef = useRef(false);
  const { params } = useRoute<RouteProp<StreamScreenParams, 'screenprops'>>();
  const { user } = useMe();

  const [cameraSettings, setCameraSettings] = useState<CameraSettings>({
    facingMode: 'front',
  });

  const [showAlert, setShowAlert] = useState(false);

  // Toggle state every 2 seconds (likely for UI indicator)

  const startStream = async () => {
    const token = await getStreamToken();
    const streamUrl = `${params.liveStreamInfo.rtmpIngestUrl}?token=${token}`;
    console.log('streamUrl', streamUrl);
    if (ref.current) {
      ref.current.startStreaming(params.liveStreamInfo.streamKey, streamUrl);
      await startStreamApi(params.liveStreamInfo);
      setIsStreaming(true);
    }
  };

  const stopStream = async () => {
    if (ref.current) {
      ref.current.stopStreaming();
      await stopStreamApi(params.liveStreamInfo);
      setIsStreaming(false);
    }
  };

  const tryStartStream = () => {
    if (ref.current && !statusRef.current) {
      statusRef.current = true;
      startStream();
    } else {
      setTimeout(tryStartStream, 1000);
    }
  };

  useEffect(() => {
    const id = setTimeout(tryStartStream, 1000);
    return () => {
      if (isStreaming) stopStream();
      clearTimeout(id);
    };
  }, []);

  const handleConnectionSuccess = useCallback(() => {
    publishStreamSuccessApi(params.liveStreamInfo);
  }, [params.liveStreamInfo]);

  const handleConnectionFailed = useCallback((e: string) => {
    publishStreamFailedApi(params.liveStreamInfo);
    console.error('❌ Stream failed:', e);
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log('🔌 Stream disconnected.');
    streamPublishDisconnectApi(params.liveStreamInfo);
  }, []);

  function switchCamera() {}

  const liveStreamSpace = useLivestreamSpace({
    chatSpaceId: params.liveStreamInfo.chatspaceId,
    liveStreamId: params.liveStreamInfo._id,
    userRole: UserRole.STREAMER,
  });

  const safeAreaInsets = useSafeAreaInsets();
  const { toggleOverlay, translateY, opacity, bottomHidden, bottomTranslateY, bottomOpacity } =
    useOverlayVisibility();

  function handleCancelStream() {
    setShowAlert(false);
    stopStream();
    liveStreamSpace.leaveLiveStream();
    liveStreamSpace.stopStream();
    navigationRef.goBack();
  }
  const handleSendComment = (comment: string) => {
    if (!user) {
      return;
    }
    liveStreamSpace.sendMessageToStream({
      liveStreamId: params.liveStreamInfo._id,
      senderId: user._id,
      message: comment,
      displayName: user.name,
      displayImageUrl: user.image,
      chatRank: 'streamer',
    });
  };

  return (
    <SafeAreaView
      style={{
        ...DummyScreenStyles.container,
        paddingTop: safeAreaInsets.top,
        paddingBottom: safeAreaInsets.bottom,
      }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <MemoLiveStreamPlayer
        ref={ref}
        cameraSettings={cameraSettings}
        onConnectionSuccess={handleConnectionSuccess}
        onConnectionFailed={handleConnectionFailed}
        onDisconnect={handleDisconnect}
      />
      <Animated.View style={{ transform: [{ translateY }], opacity }}>
        <LiveStreamHeader
          title="Live Now"
          coins={500}
          viewers={120}
          onBackPress={() => {
            setShowAlert(true);
          }}
          elapsedTime={liveStreamSpace.elapsedTime}
        />
      </Animated.View>
      <TouchableOpacity
        onPress={() => {
          toggleOverlay();
        }}
        style={{
          flex: 1,
          //   backgroundColor: 'white',
        }}
      ></TouchableOpacity>

      {!bottomHidden && (
        <OverlayComponent
          bottomTranslateY={bottomTranslateY}
          bottomOpacity={bottomOpacity}
          mockComments={liveStreamSpace.streamChat}
          handleLike={liveStreamSpace.likeStream}
          handleShare={liveStreamSpace.shareStream}
          liveStreamSpace={liveStreamSpace}
          handleSendComment={handleSendComment}
          handleStopStream={() => {
            setShowAlert(true);
          }}
        />
      )}
      {showAlert && (
        <CustomAlert
          visible={showAlert}
          onCancel={() => setShowAlert(false)}
          onConfirm={handleCancelStream}
          title="End Stream"
          message="Are you sure you want to end the stream?"
          confirmText="Stop Stream"
          cancelText="Cancel"
        />
      )}
    </SafeAreaView>
  );
};

type OverlayProps = {
  stopStream(): Promise<void>;
};

const StreamOverlay = ({ stopStream }: OverlayProps) => (
  <View style={styles.overlayContainer}>
    <View style={{ top: 100 }}>
      <TouchableOpacity
        onPress={async () => {
          await stopStream();
          navigationRef.goBack();
        }}
        style={styles.stopButton}
      >
        <Text style={styles.stopButtonText}>Stop</Text>
      </TouchableOpacity>
    </View>
  </View>
);

type PlayerProps = {
  onConnectionSuccess: () => void;
  onConnectionFailed: (error: string) => void;
  onDisconnect: () => void;
  cameraSettings: CameraSettings;
};

const LiveStreamPlayer = forwardRef<ApiVideoLiveStreamMethods, PlayerProps>(
  ({ onConnectionSuccess, onConnectionFailed, onDisconnect, cameraSettings }, ref: any) => (
    <ApiVideoLiveStreamView
      ref={ref}
      style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
      camera={cameraSettings.facingMode}
      video={{
        fps: 30,
        resolution: '720p',
        bitrate: 2000000,
        gopDuration: 1,
      }}
      audio={{
        sampleRate: 44100,
        isStereo: true,
        bitrate: 128000,
      }}
      // onConnectionSuccess={onConnectionSuccess}
      // onConnectionFailed={onConnectionFailed}
      // onDisconnect={onDisconnect}

      // add onpermission denied event
    />
  ),
);

const MemoLiveStreamPlayer = memo(LiveStreamPlayer);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    width: '100%',
    height: '100%',
  },
  stopButton: {
    backgroundColor: 'red',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    alignItems: 'center',
  },
  stopButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 20,
  },
});

export default StreamScreen;

// export function useOverlayVisibility({
//   headerOffset = -100,
//   bottomOffset = 200,
//   duration = 300,
// } = {}) {
//   const [hide, setHide] = useState(false);

//   const translateY = useRef(new Animated.Value(0)).current;
//   const opacity = useRef(new Animated.Value(1)).current;

//   const [bottomHidden, setBottomHidden] = useState(false);
//   const bottomTranslateY = useRef(new Animated.Value(0)).current;
//   const bottomOpacity = useRef(new Animated.Value(1)).current;

//   const hideHeader = () => {
//     Animated.parallel([
//       Animated.timing(translateY, {
//         toValue: -100,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//       Animated.timing(opacity, {
//         toValue: 0,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//     ]).start();
//   };

//   const showHeader = () => {
//     translateY.setValue(-100);
//     opacity.setValue(0);

//     Animated.parallel([
//       Animated.timing(translateY, {
//         toValue: 0,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//       Animated.timing(opacity, {
//         toValue: 1,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//     ]).start();
//   };

//   const hideBottomView = () => {
//     Animated.parallel([
//       Animated.timing(bottomTranslateY, {
//         toValue: 200, // push it down
//         duration: 300,
//         useNativeDriver: true,
//       }),
//       Animated.timing(bottomOpacity, {
//         toValue: 0,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//     ]).start(() => setBottomHidden(true));
//   };

//   const showBottomView = () => {
//     setBottomHidden(false);
//     bottomTranslateY.setValue(200);
//     bottomOpacity.setValue(0);
//     Animated.parallel([
//       Animated.timing(bottomTranslateY, {
//         toValue: 0,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//       Animated.timing(bottomOpacity, {
//         toValue: 1,
//         duration: 300,
//         useNativeDriver: true,
//       }),
//     ]).start();
//   };

//   function toggleOverlay() {
//     if (hide) {
//       showHeader();
//       showBottomView();
//     } else {
//       hideHeader();
//       hideBottomView();
//     }
//     setHide((p) => !p);
//   }

//   return {
//     toggleOverlay,
//     bottomHidden,
//     bottomTranslateY,
//     bottomOpacity,
//     translateY,
//     opacity,
//   };
// }
