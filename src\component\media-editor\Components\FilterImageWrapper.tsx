import React from 'react';
import { Canvas, Image as SkiaImage, ColorMatrix, useImage } from '@shopify/react-native-skia';
import { ScreenHeight } from '../utils/Constants/dimensionUtils';

interface FilterImageWrapperProps {
  matrix: number[];
  imageUri: string;
}

const FilterImageWrapper: React.FC<FilterImageWrapperProps> = ({ matrix, imageUri }) => {
  const image = useImage(imageUri);
  if (!image) {
    return null;
  }

  return (
    <Canvas style={{ flex: 1, position: 'absolute', width: '100%', height: '100%' }}>
      <SkiaImage x={0} y={0} width={430} height={ScreenHeight} image={image} fit="cover">
        <ColorMatrix matrix={matrix} />
      </SkiaImage>
    </Canvas>
  );
};

export default FilterImageWrapper;
