import { useContext } from 'react';
import { SocketContext, SocketContextType } from './SocketProvider';

type NonNullableSocketContextType = {
  status: SocketContextType['status'];
  socket: NonNullable<SocketContextType['socket']>;
  error: SocketContextType['error'];
};
const useSocket = (): NonNullableSocketContextType => {
  const socketState = useContext(SocketContext);
  if (!socketState) {
    throw new Error('No socket connection');
  }
  return socketState as NonNullableSocketContextType;
};

export default useSocket;
