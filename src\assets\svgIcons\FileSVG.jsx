import * as React from "react";
import Svg, { Path } from "react-native-svg";

function FileSVG({ width = 20, height = 22, color = "#6A4DBB" }) {
    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 20 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 22H8c-3.771 0-5.657 0-6.828-1.289C0 19.423 0 17.348 0 13.2V8.8c0-4.148 0-6.223 1.172-7.511C2.343 0 4.239 0 8.03 0c.606 0 1.091 0 1.5.018-.013.088-.02.178-.02.269L9.5 3.404c0 1.207 0 2.274.105 3.133.114.932.375 1.864 1.067 2.624.69.76 1.538 1.048 2.385 1.174.781.115 1.751.115 2.848.115h4.052c.043.588.043 1.31.043 2.27v.48c0 4.148 0 6.223-1.172 7.511C17.657 22 15.771 22 12 22zm-8.75-8.25c0-.456.336-.825.75-.825h8c.414 0 .75.37.75.825 0 .456-.336.825-.75.825H4c-.414 0-.75-.37-.75-.825zm0 3.85c0-.456.336-.825.75-.825h5.5c.414 0 .75.37.75.825 0 .456-.336.825-.75.825H4c-.414 0-.75-.37-.75-.825z"
                fill={color}
            />
            <Path
                d="M17.352 6.178l-3.96-3.919C12.266 1.143 11.703.584 11.01.292L11 3.3c0 2.593 0 3.89.732 4.695.732.805 1.911.805 4.268.805h3.58c-.362-.775-1.012-1.417-2.228-2.622z"
                fill={color}
            />
        </Svg>
    );
}

export default FileSVG;


