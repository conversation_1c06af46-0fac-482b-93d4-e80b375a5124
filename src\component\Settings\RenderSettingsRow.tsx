// import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import React from 'react';
// import { IMAGES } from '../../assets/Images';
// import { SCREENS } from '../../navigation/screenNames';
// import { commonFontStyle, hp } from '../../theme/fonts';
// import { colors } from '../../theme/colors';
// import CustomSwitch from '../CommonSwitch';

// type Props = {
//   icon: any;
//   title: any;
//   onPress?: () => void;
//   isToggle?: boolean;
//   isVisible?: boolean;
//   onToggleSwitch?: () => void;
//   rightIcon?: boolean;
// };

// const RenderSettingsRow = ({
//   icon,
//   title,
//   onPress,
//   isToggle,
//   isVisible,
//   onToggleSwitch,
//   rightIcon = true,
// }: Props) => {
//   return (
//     <TouchableOpacity onPress={() => (onPress ? onPress() : {})} style={styles.rowView}>
//       <Image source={icon} style={styles.rowIcon} tintColor={colors.black} />
//       <Text style={styles.rowText}>{title}</Text>

//       {isToggle ? (
//         <>
//           <CustomSwitch isToggleOn={isVisible} onToggleSwitch={onToggleSwitch} />
//         </>
//       ) : (
//         rightIcon && <Image source={IMAGES.inArrow} style={styles.inIcon} />
//       )}
//     </TouchableOpacity>
//   );
// };

// export default RenderSettingsRow;

// const styles = StyleSheet.create({
//   rowIcon: {
//     width: 20,
//     height: 20,
//     resizeMode: 'contain',
//   },
//   rowText: {
//     ...commonFontStyle(400, 16, colors.black_23),
//     flex: 1,
//   },
//   inIcon: {
//     width: 12,
//     height: 12,
//     resizeMode: 'contain',
//   },
//   rowView: {
//     flexDirection: 'row',
//     paddingHorizontal: hp(3),
//     paddingVertical: hp(2),
//     alignItems: 'center',
//     gap: hp(1.5),
//   },
// });
