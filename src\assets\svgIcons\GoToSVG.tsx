import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}


const GoToSVG: React.FC<IconProps> = ({
    size = 20,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={(size * 17) / 14} // Maintain aspect ratio based on original 14x17 viewBox
            viewBox="0 0 14 17"
            fill="none"
            {...props}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M.212 12.842a.72.72 0 011.024 0l1.66 1.67V5.586C2.897 2.5 5.383 0 8.449 0 11.514 0 14 2.5 14 5.586v6.8a.726.726 0 01-.724.728.726.726 0 01-.724-.728v-6.8c0-2.28-1.838-4.129-4.104-4.129S4.345 3.306 4.345 5.586v8.926l1.66-1.67a.72.72 0 011.024 0 .732.732 0 010 1.03l-2.896 2.915a.721.721 0 01-1.024 0L.212 13.872a.732.732 0 010-1.03z"
                fill={color}
            />
        </Svg>
    );
};

export default GoToSVG;



