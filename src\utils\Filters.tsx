// import {
//   AdenCompat,
//   _1977Compat,
//   <PERSON><PERSON><PERSON>ompat,
//   BrooklynCompat,
//   ClarendonCompat,
//   EarlybirdCompat,
//   GinghamCompat,
//   HudsonCompat,
//   InkwellCompat,
//   KelvinCompat,
//   LarkCompat,
//   LofiCompat,
//   MavenCompat,
//   MayfairCompat,
//   MoonCompat,
//   NashvilleCompat,
//   PerpetuaCompat,
//   ReyesCompat,
//   RiseCompat,
//   SlumberCompat,
//   StinsonCompat,
//   ToasterCompat,
//   ValenciaCompat,
//   WaldenCompat,
//   WillowCompat,
//   Xpro2Compat,
// } from "react-native-image-filter-kit";
// interface IFilter {
//   title: string;
//   filterComponent: any;
// }

export const FILTERS = [
  {
    title: 'Normal',
    // filterComponent: AdenCompat,
  },
  {
    title: 'Maven',
    // filterComponent: <PERSON><PERSON>Compat,
  },
  {
    title: 'Mayfair',
    // filterComponent: MayfairCompat,
  },
  {
    title: 'Moon',
    // filterComponent: MoonCompat,
  },
  {
    title: 'Nashville',
    // filterComponent: NashvilleCompat,
  },
  {
    title: 'Perpetua',
    // filterComponent: PerpetuaCompat,
  },
  {
    title: 'Reyes',
    // filterComponent: ReyesCompat,
  },
  {
    title: 'Rise',
    // filterComponent: RiseCompat,
  },
  {
    title: 'Slumber',
    // filterComponent: SlumberCompat,
  },
  {
    title: 'Stinson',
    // filterComponent: StinsonCompat,
  },
  {
    title: 'Brooklyn',
    // filterComponent: BrooklynCompat,
  },
  {
    title: 'Earlybird',
    // filterComponent: EarlybirdCompat,
  },
  {
    title: 'Clarendon',
    // filterComponent: ClarendonCompat,
  },
  {
    title: 'Gingham',
    // filterComponent: GinghamCompat,
  },
  {
    title: 'Hudson',
    // filterComponent: HudsonCompat,
  },
  {
    title: 'Inkwell',
    // filterComponent: InkwellCompat,
  },
  {
    title: 'Kelvin',
    // filterComponent: KelvinCompat,
  },
  {
    title: 'Lark',
    // filterComponent: LarkCompat,
  },
  {
    title: 'Lofi',
    // filterComponent: LofiCompat,
  },
  {
    title: 'Toaster',
    // filterComponent: ToasterCompat,
  },
  {
    title: 'Valencia',
    // filterComponent: ValenciaCompat,
  },
  {
    title: 'Walden',
    // filterComponent: WaldenCompat,
  },
  {
    title: 'Willow',
    // filterComponent: WillowCompat,
  },
  {
    title: 'Xpro2',
    // filterComponent: Xpro2Compat,
  },
  {
    title: 'Aden',
    // filterComponent: AdenCompat,
  },
  {
    title: '_1977',
    // filterComponent: _1977Compat,
  },
  {
    title: 'Brannan',
    // filterComponent: BrannanCompat,
  },
];

export const zIndex = {
  level_1: 100,
  level_2: 200,
  level_3: 300,
  level_4: 400,
  level_5: 500,
  level_6: 600,
  level_7: 700,
  level_8: 800,
  level_9: 900,
  level_10: 1000,
};
