import * as React from "react";
import Svg, { Path } from "react-native-svg";

function GallerySVG({ size = 11, color = "#fff" }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 11 11"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <Path
                d="M.298 8.751l-.01.01a3.183 3.183 0 01-.263-1.029c.036.376.134.716.273 1.02zM3.601 4.311a1.224 1.224 0 100-2.449 1.224 1.224 0 000 2.45z"
                fill={color}
            />
            <Path
                d="M7.3 0H2.99C1.115 0 0 1.116 0 2.99V7.3c0 .562.098 1.05.288 1.462.443.977 1.39 1.528 2.701 1.528h4.312c1.873 0 2.989-1.116 2.989-2.99V2.99C10.29 1.116 9.174 0 7.3 0zm2.151 5.402a1.147 1.147 0 00-1.45 0L5.86 7.24a1.147 1.147 0 01-1.452 0l-.175-.144c-.365-.319-.946-.35-1.358-.072L.952 8.314a2.744 2.744 0 01-.18-1.013V2.989c0-1.45.766-2.217 2.217-2.217h4.312c1.45 0 2.217.766 2.217 2.217v2.47l-.067-.057z"
                fill={color}
            />
        </Svg>
    );
}

export default GallerySVG;
