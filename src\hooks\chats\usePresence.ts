import { useEffect, useRef, useState } from 'react';
import { InteractionManager } from 'react-native';
import useSocket from '../../socket-client/useSocket';
import { ChatService } from '../../service/ChatService';
import { isChatSpace } from '../../lib/chatLib';

type PresenceState = {
  isUserOnline?: boolean;
  lastSeen?: number;
} | null;

export const usePresence = (userId?: string): PresenceState => {
  const { socket } = useSocket();
  const [presence, setPresence] = useState<PresenceState>(null);
  const mountedRef = useRef<boolean>(false);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!userId || isChatSpace(userId)) return;

    const task = InteractionManager.runAfterInteractions(() => {
      (async () => {
        try {
          const snap: any = await ChatService.fetchPresence(userId);
          if (!mountedRef.current) return;
          if (snap && (snap.isUserOnline === true || typeof snap.lastSeen === 'number')) {
            setPresence({ isUserOnline: snap.isUserOnline, lastSeen: snap.lastSeen });
          } else {
            setPresence(null);
          }
        } catch {}
      })();
    });

    return () => {
      // Cancel deferred task if still pending
      // @ts-ignore runAfterInteractions returns Cancellable
      task?.cancel?.();
    };
  }, [userId]);

  useEffect(() => {
    if (!socket || !userId) return;

    const handleOnline = (payload: any) => {
      if (!mountedRef.current) return;
      if (payload?.userId !== userId) return;
      setPresence((prev) => ({ ...(prev || {}), isUserOnline: true }));
    };

    const handleOffline = (payload: any) => {
      if (!mountedRef.current) return;
      if (payload?.userId !== userId) return;
      const lastActive = typeof payload?.lastActive === 'number' ? payload.lastActive : undefined;
      setPresence((prev) => ({
        ...(prev || {}),
        isUserOnline: false,
        lastSeen: lastActive ?? (prev?.lastSeen as number | undefined),
      }));
    };

    socket.on('user:online', handleOnline);
    socket.on('user:offline', handleOffline);

    return () => {
      socket.off('user:online', handleOnline);
      socket.off('user:offline', handleOffline);
    };
  }, [socket, userId]);

  return presence;
};

export default usePresence;
