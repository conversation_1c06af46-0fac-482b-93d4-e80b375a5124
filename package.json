{"name": "chatbucket", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "format": "prettier --write .", "check-format": "prettier --check ."}, "dependencies": {"@cbucket/react-native-livestream": "^2.0.2", "@gorhom/bottom-sheet": "^5.1.2", "@hookform/resolvers": "^5.2.1", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-camera-roll/camera-roll": "^7.10.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.6", "@react-native-documents/picker": "^10.1.5", "@react-native-firebase/app": "^22.1.0", "@react-native-firebase/messaging": "^22.1.0", "@react-native-masked-view/masked-view": "^0.2.9", "@react-native-voice/voice": "^3.2.4", "@react-native/gradle-plugin": "^0.78.2", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.26", "@react-navigation/stack": "^7.2.3", "@realm/react": "^0.11.0", "@reduxjs/toolkit": "^2.6.1", "@shopify/react-native-skia": "^2.2.4", "@welldone-software/why-did-you-render": "^10.0.1", "axios": "^1.8.4", "i18next": "^24.2.3", "libphonenumber-js": "^1.12.7", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lottie-react-native": "^7.2.2", "mediasoup-client": "^3.9.5", "metro-react-native-babel-preset": "^0.77.0", "moment": "^2.30.1", "nanoid": "^5.1.5", "react": "19.0.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.4.1", "react-native": "0.78.1", "react-native-android-location-enabler": "^2.0.1", "react-native-audio-recorder-player": "^3.6.12", "react-native-blob-util": "^0.22.2", "react-native-confirmation-code-field": "^7.4.0", "react-native-contacts": "^8.0.4", "react-native-countdown-circle-timer": "^3.2.1", "react-native-country-codes-picker": "^2.3.5", "react-native-create-thumbnail": "^2.1.1", "react-native-date-picker": "^5.0.11", "react-native-device-info": "^14.0.4", "react-native-element-dropdown": "^2.12.4", "react-native-emoji-modal": "^0.2.4", "react-native-emoji-selector": "^0.2.0", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-image-crop-picker": "^0.50.1", "react-native-image-picker": "^8.2.1", "react-native-incall-manager": "^4.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.17.3", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-maps": "^1.20.0", "react-native-material-menu": "^2.0.0", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-otp-entry": "^1.8.4", "react-native-otp-textinput": "^1.1.6", "react-native-pdf": "^6.7.7", "react-native-popover-view": "^6.1.0", "react-native-popup-menu": "^0.18.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "3.18.0", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-svg": "^15.11.2", "react-native-swipe-list-view": "^3.2.9", "react-native-toast-message": "^2.2.1", "react-native-track-player": "^4.1.2", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.11.0", "react-native-video-trim": "^5.1.1", "react-native-view-shot": "^4.0.3", "react-native-volume-manager": "^2.0.8", "react-native-webrtc": "^124.0.5", "react-native-webview": "^13.15.0", "realm": "^20.1.0", "reanimated-color-picker": "^4.1.0", "socket.io-client": "^4.8.1", "use-debounce": "^10.0.5", "validate-country-mobile": "^1.6.0", "yup": "^1.7.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@simbathesailor/use-what-changed": "^2.0.0", "@types/jest": "^29.5.13", "@types/lodash.debounce": "^4.0.9", "@types/lodash.throttle": "^4.1.9", "@types/react": "^19.0.0", "@types/react-native-snap-carousel": "^3.8.11", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "^2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.9.2+sha512.1fc009bc09d13cfd0e19efa44cbfc2b9cf6ca61482725eb35bbc5e257e093ebf4130db6dfe15d604ff4b79efd8e1e8e99b25fa7d0a6197c9f9826358d4d65c3c"}