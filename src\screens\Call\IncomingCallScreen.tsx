// App.tsx
import { RouteProp, useRoute } from '@react-navigation/native';

import React, { useEffect, useState, useRef } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import { useCallContext } from '../../Context/CallProvider';
import { CallButtons } from './components/CallButtons';
import FileSVG from '../../assets/svgIcons/FileSVG';
import { IMAGES } from '../../assets/Images';
import useSocket from '../../socket-client/useSocket';
import { callSocketEvents } from '../../socket-client/socketEvents';
import { CallDetails, incomingCallData } from '../../types/calls.types';
import Toast from 'react-native-toast-message';
import { Socket } from 'socket.io-client';
import { connectToSocketServer } from '../../socket-client/socket';

type LinkingPayload = {
  roomId: string;
  callerName: string;
  callType: string;
};

export type IncomingCallScreenParams = {
  IncomingCallScreen: LinkingPayload;
};

export default IncomingCallScreen;

function IncomingCallScreen() {
  const route = useRoute<RouteProp<IncomingCallScreenParams, 'IncomingCallScreen'>>();
  const stateInitialisedRef = useRef(false);
  const { updateCallDetails } = useCallContext();

  const [loadedData, setLoadedData] = useState(false);

  async function handleInitialisation() {
    let socketInstance: Socket | null = await connectToSocketServer();

    if (stateInitialisedRef.current === false) {
      stateInitialisedRef.current = true;

      socketInstance.emit(
        callSocketEvents.getCallDetails,
        { roomId: route.params.roomId },
        (response: incomingCallData) => {
          console.log('[Notif-Log]', response);
          if (response.status === false) {
            console.log('[Notif-Log] got error', response.error);
            Toast.show({ type: 'error', text1: response.error });
            return;
          }

          updateCallDetails({
            state: 'incoming',
            type: response.callType,
            recipients: response.recipents,
            callId: response.callId,
            initialCallType: response.initialCallType,
            isGroupCall: response.isGroupCall,
            roomId: response.roomId,
            recipentList: response.recipentList,
            origin: response.origin,
            initiatorDetails: response.initiatorDetails,
          });
          setLoadedData(true);
        },
      );
    }
  }

  useEffect(() => {
    handleInitialisation();
  }, []);

  const [_som, setOptionsModal] = useState(false);
  const [_cmm, setCallMembersModal] = useState(false);

  return (
    <LinearGradient
      locations={[0, 0.35, 1]}
      colors={['#e09e61', '#5b2e15', '#000000']}
      style={{ flex: 1, justifyContent: 'center' }}
    >
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        <View style={newStyles.profileContainer}>
          <View style={[newStyles.profileWrapper, { borderColor: '#EF924F' }]}>
            <RenderImages payload={route.params} loadedData={loadedData} />
          </View>
        </View>
      </View>
      {loadedData && (
        <CallButtons setCallMembersModal={setCallMembersModal} setOptionsModal={setOptionsModal} />
      )}
    </LinearGradient>
  );
}

const newStyles = StyleSheet.create({
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },

  textContainer: {
    marginTop: 60,
    alignSelf: 'center',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    color: '#bbb',
    fontSize: 14,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  extraText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  goldBadge: {
    position: 'absolute',
    top: -6,
    right: 6,
    borderRadius: 10,
    padding: 3,
  },
  profileContainer: {
    alignItems: 'center',
    position: 'absolute',
    bottom: '50%',
    width: '100%',
  },
  profileWrapper: {
    width: '100%',
    height: '100%',
  },

  singleCallContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarContainer: {
    position: 'absolute',
    zIndex: 1,
    // backgroundColor: 'red',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  singleAvatar: {
    width: 120,
    height: 120,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
});

interface RenderImagesProps {
  loadedData: boolean;
  payload: LinkingPayload;
}
const RenderImages = ({ loadedData, payload }: RenderImagesProps) => {
  // For a 1:1 call, show only one participant
  const { callDetails } = useCallContext();
  return (
    <View style={newStyles.singleCallContainer}>
      <Image
        source={
          loadedData && callDetails.initiatorDetails?.image
            ? { uri: callDetails.initiatorDetails?.image }
            : require('./UserAvatar.png')
        }
        style={newStyles.singleAvatar}
      />
      <View style={{ alignItems: 'center', marginTop: 10 }}>
        <Text style={newStyles.title}>{payload.callerName}</Text>
        <Text style={newStyles.subtitle}>Incoming {payload.callType} call</Text>
      </View>
    </View>
  );
};
