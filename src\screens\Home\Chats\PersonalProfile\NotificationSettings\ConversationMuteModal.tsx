import { View, Text, Switch, StyleSheet } from 'react-native';
import ModalWrapper from '../../../../../component/ModalWrapper';
import { colors } from '../../../../../theme/colors';
import { useState } from 'react';
import { ConversationInfo } from '../../../../../device-storage/realm/hooks/useConversationInfo';
import { ChatService } from '../../../../../service/ChatService';
import { isConversationMuted } from '../../../../../lib/chatLib';
import ConversationMuteTimerModal from './ConversationMuteTimerModal';

type ConversationMuteModalProps = {
  conversationInfo: ConversationInfo | null;
  isVisible: boolean;
  onClose: () => void;
};

const ConversationMuteModal: React.FC<ConversationMuteModalProps> = ({
  conversationInfo,
  isVisible,
  onClose,
}) => {
  const [isMuteTimeModal, setIsMuteTimeModal] = useState(false);

  const isMuted = conversationInfo?.conversationSettings?.muteUntil;
  const isMutedBool = !!isMuted;

  const handleMuteWithDuration = (duration: '8_hours' | '1_week' | 'always' | 'unmute') => {
    console.log('handleMuteWithDuration -> duration', duration);

    if (conversationInfo?.conversationSettings?.id) {
      const isMuted = isConversationMuted(conversationInfo?.conversationSettings.muteUntil);
      ChatService.toggleConversationMute(conversationInfo.id, isMuted ? undefined : duration);
    }
    onClose();
    setIsMuteTimeModal(false);
  };

  return (
    <>
      <ModalWrapper isVisible={isVisible} onCloseModal={onClose}>
        <View style={styles.modalContent}>
          <View style={styles.muteRow}>
            <Text style={styles.muteLabel}>Mute notifications</Text>
            <Switch
              value={isMutedBool}
              onValueChange={(isMuting) => {
                if (isMuting) {
                  setIsMuteTimeModal(true);
                } else {
                  handleMuteWithDuration('unmute');
                  onClose();
                }
              }}
              trackColor={{ false: '#E9E9EA', true: colors.mainPurple }}
              thumbColor={colors.white}
            />
          </View>
        </View>
      </ModalWrapper>
      <ConversationMuteTimerModal
        isVisible={isMuteTimeModal}
        onClose={() => setIsMuteTimeModal(false)}
        onSelectDuration={handleMuteWithDuration}
      />
    </>
  );
};

export default ConversationMuteModal;

const styles = StyleSheet.create({
  modalContent: {
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
    marginBottom: 20,
  },
  muteRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  muteLabel: {
    fontSize: 16,
    color: colors.black,
  },
});
