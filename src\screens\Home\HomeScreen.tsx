import { <PERSON><PERSON><PERSON><PERSON>, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState, JSX } from 'react';
import GradientView from '../../component/GradientView';
import { AppStyles } from '../../theme/appStyles';
import HomeHeader from '../../component/HomeHeader';
import SearchInput from '../../component/SearchInput';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';

import { navigateTo } from '../../utils/commonFunction';
import { SCREENS } from '../../navigation/screenNames';

import HorizontalListView from '../../component/HorizontalListView';
import ChannelsScreen from './Channels/ChannelsScreen';
import GroupsScreen from './Groups/GroupsScreen';
import ChatsScreen from './Chats/ChatsScreen';

import { useEnhancedSyncContacts, useSyncContacts } from '../../hooks/contacts/useSyncContacts';

import AllChatScreen from './All/AllChatScreen';

import useConversations from '../../hooks/conversations/useConversations';
import PinSVG from '../../assets/svgIcons/PinSVG';
import UnpinSVG from '../../assets/svgIcons/UnpinSVG';

import BackArrowSVG from '../../assets/svgIcons/BackArrowSVG';
import { ChatService } from '../../service/ChatService';
import { HomeMenuModal } from './components/HomeMenuModal';
import { initiailizeUsers, initializeApp } from '../../utils/initializeApp';
import { useMe } from '../../hooks/util/useMe';

import { OcticonsIcons } from '../../utils/vectorIcons';
import { IConversation } from '../../device-storage/realm/schemas/ConversationSchema';
import { isConversationMuted } from '../../lib/chatLib';
import { attachWriteListeners } from '../../device-storage/realm/realmlisteners';

export type ConversationTab = 'All' | 'Chats' | 'Groups' | 'Channels' | 'My Family';

const HomeScreen = () => {
  initializeApp();

  useEffect(() => {
    // attachWriteListeners();
  }, []);

  useEnhancedSyncContacts();

  const [_storyVisible, _setStoryVisible] = useState(false);

  const tabs: ConversationTab[] = ['All', 'Chats', 'Groups', 'Channels'];
  const [searchText, setSearchText] = useState('');
  const [selectedItem, setSelectedItem] = useState<ConversationTab>('All');

  const [menuVisible, setMenuVisible] = useState(false);

  const [selectedUsers, setSelectedUsers] = useState<IConversation[]>([]);
  const [clearSelectionTrigger, setClearSelectionTrigger] = useState(0);

  const onMenuFilterPress = (value: string) => {
    setMenuVisible(false);
    if (value == 'newMessage') {
      navigateTo(SCREENS.NewMessageScreen);
    } else if (value == 'chatFolders') {
    } else if (value == 'archivedChats') {
      navigateTo(SCREENS.ArchiveScreen);
    } else if (value == 'unread') {
    } else {
      console.log('unknown value');
    }
  };

  const TabScreens: Record<ConversationTab, JSX.Element> = {
    All: (
      <AllChatScreen
        onSelectionChange={setSelectedUsers}
        clearSelectionTrigger={clearSelectionTrigger}
        searchText={searchText}
      />
    ),
    Chats: <ChatsScreen searchText={searchText} />,
    Channels: <ChannelsScreen searchText={searchText} />,
    Groups: <GroupsScreen searchText={searchText} />,
    'My Family': <></>, // Add actual component if available
  };

  useEffect(() => {
    const backAction = () => {
      if (selectedUsers.length) {
        setSelectedUsers([]);
        setClearSelectionTrigger(0);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [selectedUsers]);

  useEffect(() => {
    if (searchText) {
      setSearchText('');
    }
    if (selectedUsers.length > 0) {
      setSelectedUsers([]);
      setClearSelectionTrigger((prev) => prev + 1);
    }
  }, [selectedItem]);

  return (
    <GradientView>
      <SafeAreaView style={[AppStyles.flex]}>
        <View style={{ height: hp(13), justifyContent: 'center' }}>
          {selectedUsers?.length == 0 ? (
            <HomeHeader type={'home'} onPressMenu={() => setMenuVisible(true)} />
          ) : (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                flex: 1,
                paddingHorizontal: 22,
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 15 }}>
                <BackArrowSVG
                  size={20}
                  color={colors.white}
                  onPress={() => {
                    setSelectedUsers([]);
                    setClearSelectionTrigger((prev) => prev + 1);
                  }}
                />
                <Text
                  style={{
                    fontSize: 14,
                    color: colors.white,
                    fontWeight: '500',
                  }}
                >
                  {selectedUsers?.length}
                </Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 32 }}>
                {/* Mute/Unmute icon logic */}

                {selectedUsers.length > 0 &&
                  (selectedUsers.every(
                    (user) => user.conversationSettings.muteUntil == 'always',
                  ) ? (
                    <TouchableOpacity
                      onPress={() => {
                        selectedUsers.forEach((conversation) => {
                          if (conversation.id) {
                            const isMuted = isConversationMuted(
                              conversation.conversationSettings.muteUntil,
                            );
                            ChatService.toggleConversationMute(
                              conversation.id,
                              isMuted ? undefined : 'always',
                            );
                          }
                        });
                        setSelectedUsers([]);
                        setClearSelectionTrigger((prev) => prev + 1);
                      }}
                    >
                      <OcticonsIcons name="unmute" size={20} color={colors.white} />
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      onPress={() => {
                        selectedUsers.forEach((conversation) => {
                          if (conversation?.id) {
                            const isMuted = isConversationMuted(
                              conversation.conversationSettings.muteUntil,
                            );
                            ChatService.toggleConversationMute(
                              conversation.id,
                              isMuted ? undefined : 'always',
                            );
                          }
                        });
                        setSelectedUsers([]);
                        setClearSelectionTrigger((prev) => prev + 1);
                      }}
                    >
                      <OcticonsIcons name="mute" size={20} color={colors.white} />
                    </TouchableOpacity>
                  ))}
                {/* Pin/Unpin icon logic */}
                {selectedUsers.length > 0 &&
                selectedUsers.every((u) => u.conversationSettings?.isPinned) ? (
                  <UnpinSVG
                    color={colors.white}
                    size={20}
                    onPress={() => {
                      selectedUsers.forEach((user) => {
                        if (user?.id) {
                          ChatService.toggleConversationPin(user.id);
                        }
                      });
                      setSelectedUsers([]);
                      setClearSelectionTrigger((prev) => prev + 1);
                    }}
                  />
                ) : (
                  <PinSVG
                    color={colors.white}
                    size={20}
                    onPress={() => {
                      selectedUsers.forEach((user) => {
                        if (user?.id) {
                          ChatService.toggleConversationPin(user.id);
                        }
                      });
                      setSelectedUsers([]);
                      setClearSelectionTrigger((prev) => prev + 1);
                    }}
                  />
                )}
                {/* "todo: enabel chat lock " */}
                {/* <LockSVG
                  color={colors.white}
                  size={18}
                  onPress={() => {
                    console.log('lock icon clicked');
                  }}
                /> */}
              </View>
            </View>
          )}
        </View>
        <View
          style={[AppStyles.bottomWhiteViewWithoutPadding, { flex: 1, paddingVertical: hp(2) }]}
        >
          <View style={styles.searchView}>
            <SearchInput value={searchText} onChangeText={setSearchText} activeTab={selectedItem} />
          </View>
          {/*todo: blind mode button  */}
          {/* <TouchableOpacity
            onPress={() => navigateTo('BlindHomeScreen')}
            style={{
              backgroundColor: colors.mainPurple,
              padding: 10,
              borderRadius: 5,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Text>Blind h Mode</Text>
          </TouchableOpacity> */}
          <HorizontalListView
            data={tabs}
            defaultSelectedItem="All"
            selectedItem={(tab: ConversationTab) => setSelectedItem(tab)}
            contentContainerStyle={{
              paddingLeft: '5%',
              paddingRight: '2%',
              marginTop: hp(2),
              marginBottom: hp(2),
              gap: 10,
            }}
            bottomLine={false}
            styleViewTwo={true}
          />
          {TabScreens[selectedItem]}
        </View>

        {menuVisible && (
          <HomeMenuModal
            isVisible={menuVisible}
            onCloseModal={() => {
              setMenuVisible(false);
            }}
            onMenuFilterPress={onMenuFilterPress}
            selectedItem={selectedItem}
            setSelectedItem={setSelectedItem}
          />
        )}
      </SafeAreaView>
    </GradientView>
  );
};

export default HomeScreen;
const styles = StyleSheet.create({
  searchView: {
    paddingHorizontal: hp(2),
  },
  hiddenRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',

    width: 140,
    alignSelf: 'flex-end',
  },
  deleteButton: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors._F11010_red,
    width: 70,
  },
  deleetIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  menuIcon: {
    height: 18,
    width: 18,
    resizeMode: 'contain',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  localVideo: {
    width: 200,
    height: 200,
    backgroundColor: 'gray',
    marginBottom: 10,
  },
  remoteVideo: {
    width: 300,
    height: 300,
    backgroundColor: 'gray',
  },
  subText: {
    ...commonFontStyle(500, 16, colors.black),
    fontWeight: '500',
  },
  headText: {
    ...commonFontStyle(600, 16, colors.black),
    fontWeight: '600',
    marginBottom: 14,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 20,
  },
});
