import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import ButtonPurple from '../../../../component/ButtonPurple';
import ModalWrapper from '../../../../component/ModalWrapper';
import CustomDatePicker from '../../../../component/PersonalChat/CustomDatePicker';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';

type IProps = {
  isVisible: boolean;
  onClose: () => void;
  setscheduleDate: (scheduledAt: any) => void;
  scheduleDate: any;
  onClickSchedule: () => void;
};

const ScheduleMessageModal = ({
  isVisible,
  onClose,
  setscheduleDate = () => {},
  scheduleDate,
  onClickSchedule = () => {},
}: IProps) => {
  return (
    <ModalWrapper
      isVisible={isVisible}
      onCloseModal={() => {
        onClose();
      }}
    >
      <View style={{ paddingHorizontal: hp(1) }}>
        <Text
          style={{
            fontSize: 16,
            color: colors.black_23,
            fontWeight: '600',
            marginBottom: 23,
          }}
        >
          Schedule message
        </Text>

        <CustomDatePicker scheduleDate={scheduleDate} setScheduleDate={setscheduleDate} />

        <ButtonPurple
          extraStyle={{ marginBottom: 30, marginHorizontal: hp(2) }}
          onPress={onClickSchedule}
          textStyle={{ fontSize: 16, fontWeight: '600' }}
          title={'Schedule'}
        />
      </View> 
    </ModalWrapper>
  );
};

export default ScheduleMessageModal;

const styles = StyleSheet.create({
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
});
