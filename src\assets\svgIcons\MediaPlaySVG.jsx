import * as React from "react";
import Svg, { Circle, Path } from "react-native-svg";

function MediaPlaySVG({ size = 28, color = "#fff", backgroundColor = "#6A4DBB" }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <Circle cx={14} cy={14} r={14} fill={backgroundColor} />
            <Path
                d="M19.264 15.91l-6.199 3.543c-1.467.838-3.292-.22-3.292-1.91v-7.085c0-1.69 1.825-2.748 3.292-1.91l6.2 3.542c1.477.845 1.477 2.976 0 3.82z"
                fill={color}
            />
        </Svg>
    );
}

export default MediaPlaySVG;

