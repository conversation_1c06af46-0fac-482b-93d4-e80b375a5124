export const ArrStickers = [
  { id: 1, image: require('../../Assets/Stickers/balloons.png') },
  { id: 2, image: require('../../Assets/Stickers/creative.png') },
  { id: 3, image: require('../../Assets/Stickers/creativity.png') },
  { id: 4, image: require('../../Assets/Stickers/getidea.png') },
  { id: 5, image: require('../../Assets/Stickers/idea.png') },
  { id: 6, image: require('../../Assets/Stickers/panda-bear.png') },
  { id: 7, image: require('../../Assets/Stickers/reading-book.png') },
  { id: 8, image: require('../../Assets/Stickers/skipping.gif') },
  { id: 9, image: require('../../Assets/Stickers/laugh.gif') },
  { id: 12, image: require('../../Assets/Stickers/dance.gif') },
  { id: 16, image: require('../../Assets/Stickers/dogdance.gif') },
  { id: 17, image: require('../../Assets/Stickers/love-need.gif') },
  { id: 17, image: require('../../Assets/Stickers/love-couple.gif') },
];

export const arrMusic = [
  {
    "id": "1",
    "title": "Acoustic Breeze",
    "artist": "Bensound",
    "url": "https://ravalwebs.github.io/hanumanchalisa/mp3/Super%20Fast%20Shri%20Hanuman%20Chalisa.mp3",
    // "url": "https://download.samplelib.com/mp3/sample-15s.mp3",
    // "url": "https://www.bensound.com/bensound-music/bensound-acousticbreeze.mp3"
    // "url": "https://github.com/rafaelreis-hotmart/Audio-Sample-files/blob/master/sample.m4a"
    // "url": "https://sample-files-online.com/samples/countdownload/122"
    // "url": "https://www.filesampleshub.com/download/audio/aac/sample2.AAC"
    // "url": "https://github.com/rafaelreis-hotmart/Audio-Sample-files/raw/master/sample.mp4"
    // "url": "https://getsamplefiles.com/download/aac/sample-5.aac"
  },
  {
    "id": "2",
    "title": "Creative Minds",
    "artist": "Bensound",
    // "url": "https://www.bensound.com/bensound-music/bensound-creativeminds.mp3"
    "url": "https://sample-files-online.com/samples/countdownload/121"
  },
  {
    "id": "3",
    "title": "Sunny",
    "artist": "Bensound",
    // "url": "https://www.bensound.com/bensound-music/bensound-sunny.mp3"
    "url": "https://dl.espressif.com/dl/audio/ff-16b-2c-44100hz.m4a"
  },
  {
    "id": "4",
    "title": "Jazz Comedy",
    "artist": "Bensound",
    // "url": "https://www.bensound.com/bensound-music/bensound-jazzcomedy.mp3"
    "url": "https://sample-files-online.com/samples/countdownload/123"
  },
  {
    "id": "5",
    "title": "Better Days",
    "artist": "Bensound",
    "url": "https://sample-files-online.com/samples/countdownload/124 "
  }
]

// [
//   {
//     "title": "Death Bed",
//     "artist": "Powfu",
//     "artwork": "https://samplesongs.netlify.app/album-arts/death-bed.jpg",
//     "url": "https://samplesongs.netlify.app/Death%20Bed.mp3",
//     // "url": "https://ravalwebs.github.io/hanumanchalisa/mp3/Super%20Fast%20Shri%20Hanuman%20Chalisa.mp3",
//     "id": "1"
//   },
//   {
//     "title": "Bad Liar",
//     "artist": "Imagine Dragons",
//     "artwork": "https://samplesongs.netlify.app/album-arts/bad-liar.jpg",
//     "url": "https://samplesongs.netlify.app/Bad%20Liar.mp3",
//     // "url": "https://files.freemusicarchive.org/storage-freemusicarchive-org/music/no_curator/Gargoyles_Numb_End/Arp_220__Arp_299/Gargoyles_Numb_End_-_01_-_Arp_220.mp3",
//     "id": "2"
//   },
//   {
//     "title": "Faded",
//     "artist": "Alan Walker",
//     "artwork": "https://samplesongs.netlify.app/album-arts/faded.jpg",
//     "url": "https://samplesongs.netlify.app/Faded.mp3",
//     "id": "3"
//   },
//   {
//     "title": "Hate Me",
//     "artist": "Ellie Goulding",
//     "artwork": "https://samplesongs.netlify.app/album-arts/hate-me.jpg",
//     "url": "https://samplesongs.netlify.app/Hate%20Me.mp3",
//     "id": "4"
//   },
//   {
//     "title": "Solo",
//     "artist": "Clean Bandit",
//     "artwork": "https://samplesongs.netlify.app/album-arts/solo.jpg",
//     "url": "https://samplesongs.netlify.app/Solo.mp3",
//     "id": "5"
//   },
//   {
//     "title": "Without Me",
//     "artist": "Halsey",
//     "artwork": "https://samplesongs.netlify.app/album-arts/without-me.jpg",
//     "url": "https://samplesongs.netlify.app/Without%20Me.mp3",
//     "id": "6"
//   }
// ]

export const filters = [
  { id: 'default', name: 'None', matrix: [] },
  {
    id: 'grayscale',
    name: 'Grayscale',
    matrix: [
      0.2126, 0.7152, 0.0722, 0, 0, 0.2126, 0.7152, 0.0722, 0, 0, 0.2126,
      0.7152, 0.0722, 0, 0, 0, 0, 0, 1, 0,
    ],
  },
  {
    id: 'sepia',
    name: 'Sepia',
    matrix: [
      0.393, 0.769, 0.189, 0, 0, 0.349, 0.686, 0.168, 0, 0, 0.272, 0.534, 0.131,
      0, 0, 0, 0, 0, 1, 0,
    ],
  },
  {
    id: 'brightness',
    name: 'Brightness',
    matrix: [1.2, 0, 0, 0, 0, 0, 1.2, 0, 0, 0, 0, 0, 1.2, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'contrast',
    name: 'Contrast',
    matrix: [1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'blur',
    name: 'Blur',
    matrix: [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0],
    extra: 'blur(5px)',
  },
  {
    id: 'sharpen',
    name: 'Sharpen',
    matrix: [1, -1, 1, 0, 0, -1, 5, -1, 0, 0, 1, -1, 1, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'emboss',
    name: 'Emboss',
    matrix: [-2, -1, 0, 0, 0, -1, 1, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'saturation-decrease',
    name: 'Saturation Decrease',
    matrix: [0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'vintage',
    name: 'Vintage',
    matrix: [
      0.393, 0.769, 0.189, 0, 0, 0.349, 0.686, 0.168, 0, 0, 0.272, 0.534, 0.131,
      0, 0, 0, 0, 0, 1, 0,
    ],
  },
  {
    id: 'thermal',
    name: 'Thermal',
    matrix: [1, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 1, 0],
  },
  {
    id: 'highlight',
    name: 'Highlight',
    matrix: [1, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1, 0],
  },
];

export const filterNm = [
  {
    id: '0',
    name: 'Grayscale',
    matrix: [
      0.2126, 0.7152, 0.0722, 0, 0, 0.2126, 0.7152, 0.0722, 0, 0, 0.2126,
      0.7152, 0.0722, 0, 0, 0, 0, 0, 1, 0,
    ],
    command: 'colorchannelmixer=.2126:.7152:.0722:0:.2126:.7152:.0722:0:.2126:.7152:.0722',
  },
  {
    id: '1',
    name: 'Sepia',
    matrix: [
      0.393, 0.769, 0.189, 0, 0, 0.349, 0.686, 0.168, 0, 0, 0.272, 0.534, 0.131,
      0, 0, 0, 0, 0, 1, 0,
    ],
    command: 'colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131',
  },
  // {
  //   id: 'brightness',
  //   name: 'Brightness',
  //   matrix: [1.2, 0, 0, 0, 0, 0, 1.2, 0, 0, 0, 0, 0, 1.2, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'eq=brightness=0.2',
  // },
  // {
  //   id: 'contrast',
  //   name: 'Contrast',
  //   matrix: [1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'eq=contrast=1.5',
  // },
  // {
  //   id: 'blur',
  //   name: 'Blur',
  //   matrix: [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'boxblur=5',
  // },
  // {
  //   id: 'sharpen',
  //   name: 'Sharpen',
  //   matrix: [1, -1, 1, 0, 0, -1, 5, -1, 0, 0, 1, -1, 1, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'unsharp=5:5:1.0+0.0+0.0',
  // },
  // {
  //   id: 'emboss',
  //   name: 'Emboss',
  //   matrix: [-2, -1, 0, 0, 0, -1, 1, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'convolution="0 -1 -2 1 1 -1 2 1 0"',
  // },
  // {
  //   id: 'saturation-decrease',
  //   name: 'Saturation Decrease',
  //   matrix: [0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'eq=saturation=0.5',
  // },
  // {
  //   id: 'vintage',
  //   name: 'Vintage',
  //   matrix: [
  //     0.393, 0.769, 0.189, 0, 0, 0.349, 0.686, 0.168, 0, 0, 0.272, 0.534, 0.131,
  //     0, 0, 0, 0, 0, 1, 0,
  //   ],
  //   command: 'colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131',
  // },
  // {
  //   id: 'thermal',
  //   name: 'Thermal',
  //   matrix: [1, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'colorchannelmixer=1:0:0:0:0:0.5:0:0:0:0:0.5',
  // },
  // {
  //   id: 'highlight',
  //   name: 'Highlight',
  //   matrix: [1, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1.5, 0, 0, 0, 0, 0, 1, 0],
  //   command: 'eq=gamma=1.5',
  // },
];

export default { arrMusic, ArrStickers };
