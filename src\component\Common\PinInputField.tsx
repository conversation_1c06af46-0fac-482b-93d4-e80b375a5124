import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Platform,
  ViewStyle,
  TextStyle,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
} from 'react-native';
import { colors } from '../../theme/colors';

interface PinInputFieldProps {
  length: number;
  value: string[];
  onChange: (value: string[]) => void;
  onComplete?: (value: string) => void;
  autoFocus?: boolean;
  secureTextEntry?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  focusedStyle?: ViewStyle;
  inputTextStyle?: TextStyle;
  keyboardType?:
    | 'default'
    | 'number-pad'
    | 'decimal-pad'
    | 'numeric'
    | 'email-address'
    | 'phone-pad';
  showCursor?: boolean;
  showFocusedStyle?: boolean;
}

const PinInputField: React.FC<PinInputFieldProps> = ({
  length = 4,
  value,
  onChange,
  onComplete,
  autoFocus = false,
  secureTextEntry = false,
  containerStyle,
  inputStyle,
  focusedStyle,
  inputTextStyle,
  keyboardType = 'number-pad',
  showCursor = true,
  showFocusedStyle = true,
}) => {
  const [focusedIndex, setFocusedIndex] = useState(0);
  const inputRefs = useRef<Array<TextInput | null>>([]);

  useEffect(() => {
    if (value.length !== length) {
      onChange(Array(length).fill(''));
    }
  }, [length]);

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }
  }, [autoFocus]);

  const handleChange = (text: string, index: number) => {
    if ((keyboardType === 'number-pad' || keyboardType === 'numeric') && !/^\d*$/.test(text)) {
      return;
    }

    const newValue = [...value];
    newValue[index] = text.slice(0, 1);
    onChange(newValue);

    if (text && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Check if code is complete
    const isComplete = newValue.every((digit) => digit !== '') && newValue.length === length;
    if (isComplete && onComplete) {
      onComplete(newValue.join(''));
    }
  };

  const handleKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>, index: number) => {
    if (e.nativeEvent.key === 'Backspace') {
      const newValue = [...value];

      if (value[index]) {
        newValue[index] = '';
        onChange(newValue);
      }
      else if (index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handleAndroidBackspace = (text: string, index: number) => {
    if (text === '' && value[index] !== '') {
      const newValue = [...value];
      newValue[index] = '';
      onChange(newValue);

      if (index > 0) {
        setTimeout(() => {
          inputRefs.current[index - 1]?.focus();
        }, 10);
      }
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {Array.from({ length }).map((_, index) => (
        <TextInput
          key={index}
          ref={(ref) => {
            inputRefs.current[index] = ref;
          }}
          style={[
            styles.input,
            inputStyle,
            showFocusedStyle && focusedIndex === index && (focusedStyle || styles.inputFocused),
            inputTextStyle,
          ]}
          value={value[index] || ''}
          onChangeText={(text) => {
            handleChange(text, index);
            if (Platform.OS === 'android') {
              handleAndroidBackspace(text, index);
            }
          }}
          onKeyPress={(e) => handleKeyPress(e, index)}
          keyboardType={keyboardType}
          maxLength={1}
          secureTextEntry={secureTextEntry}
          onFocus={() => handleFocus(index)}
          textAlign="center"
          selectTextOnFocus
          autoCorrect={false}
          caretHidden={!showCursor}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  input: {
    width: 45,
    height: 50,
    borderRadius: 8,
    borderWidth: 1.5,
    borderColor: colors.gray_f3,
    backgroundColor: colors.white,
    fontSize: 20,
    color: colors.black_23,
    marginHorizontal: 5,
  },
  inputFocused: {
    borderColor: colors.mainPurple,
  },
});

export default PinInputField;
