import React from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import StreamToggleOption from '../../Home/Channels/components/StreamToggle';
import { useNavigation } from '@react-navigation/native';
import BellSVG from '../../../assets/svgIcons/BellSVG';
import SoundSVG from '../../../assets/svgIcons/SoundSVG';
import ThumbsUpSVG from '../../../assets/svgIcons/ThumbsUpSVG';
import { useMe } from '../../../hooks/util/useMe';
import { INotificationPreferences } from '../../../device-storage/realm/schemas/UserPrefSchema';

const NotificationScreen = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();

  const handleNotificationToggle = (key: keyof INotificationPreferences) => {
    const prevValue = userPreferencesState.userPreferences?.notifications[key];
    userPreferencesState.updatePreferences('notifications', {
      [key]: !prevValue,
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Notifications" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.sectionContainer}>
          <StreamToggleOption
            label="Show notification"
            value={userPreferencesState.userPreferences?.notifications.showNotification}
            onToggle={() => {
              handleNotificationToggle('showNotification');
            }}
            icon={<BellSVG />}
          />
          <StreamToggleOption
            label="Sound"
            value={userPreferencesState.userPreferences?.notifications.notificationSound}
            onToggle={() => handleNotificationToggle('notificationSound')}
            icon={<SoundSVG size={20} />}
          />
          {/* <StreamToggleOption
            label="Reaction Notification"
            value={userPreferencesState.userPreferences?.notifications.reactionNotification}
            onToggle={() => handleNotificationToggle('reactionNotification')}
            icon={<ThumbsUpSVG />}
          /> */}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  sectionContainer: {
    paddingHorizontal: hp(2),
    paddingVertical: hp(1),
  },
  sectionTitle: {
    ...commonFontStyle(600, 18, colors.black_23),
    marginBottom: hp(2),
    marginLeft: hp(0.5),
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  rowText: {
    flex: 1,
    ...commonFontStyle(400, 16, colors.black_23),
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginRight: 10,
  },
});

export default NotificationScreen;
