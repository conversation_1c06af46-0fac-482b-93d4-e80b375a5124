const fs = require("fs");

// // Read the content of the file
// const content = fs.readFileSync("images.js", "utf-8");

// const used = [];
// const unused = [];

// let inUnusedSection = false;

// for (const line of content.split("\n")) {
//   const trimmed = line.trim();

//   // Check if we're entering the unused section
//   if (trimmed.includes("//=== unused files")) {
//     inUnusedSection = true;
//     continue;
//   }

//   // Match lines like: key: require("./filename.png"),
//   const match = trimmed.match(
//     /require\(["']\.\/(.+?\.(png|jpg|jpeg|svg|webp))["']\)/i
//   );

//   if (match) {
//     const filename = match[1];
//     if (inUnusedSection) {
//       unused.push(filename);
//     } else {
//       used.push(filename);
//     }
//   }
// }

// // Save result to JSON
// const result = {
//   used,
//   unused,
// };

// fs.writeFileSync("image-usage.json", JSON.stringify(result, null, 2));
// console.log("✅ Done. Written to image-usage.json");

const unUsed = [
  "logoChat.png",
  "passwordInput.png",
  "otpInput.png",
  "successInput.png",
  "usernameInput.png",
  "callingInput.png",
  "imagePlaceholder.png",
  "storiesIcon.png",
  "liveIcon.png",
  "location.png",
  "forgetPassInput.png",
  "resetPassInput.png",
  "addStory.png",
  "Videocamera.png",
  "Phone.png",
  "chatInputPin.png",
  "chatInputAdd.png",
  "microphone.png",
  "blurView.png",
  "emoji.png",
  "clock.png",
  "checkRead.png",
  "CheckRead2.png",
  "deleteMessage.png",
  "pinMessage.png",
  "addReaction.png",
  "copyMessage.png",
  "editMessage.png",
  "replyMessage.png",
  "silentMessage.png",
  "sendNow.png",
  "recording.png",
  "retake.png",
  "pauseBold.png",
  "MicrophonePin.png",
  "locationPin.png",
  "GalleryPin.png",
  "CameraPin.png",
  "wallpaper.png",
  "dropper.png",
  "opacity.png",
  "addEmailSetting.png",
  "nameColorSetting.png",
  "voiceSetting.png",
  "switch.png",
  "back_Icon.png",
  "flash_of.png",
  "flash_on.png",
  "filter_pen.png",
  "deleteWhite.png",
  "more_icon.png",
  "bg_story.png",
  "two_up_arrow.png",
  "heart.png",
  "heart_fill.png",
  "bg_1.png",
  "bg_2.png",
  "uTurn.png",
  "file_Icon.png",
  "archive.png",
  "clear_chat.png",
  "folder.png",
  "unRead.png",
  "drafts.png",
  "unArchived.png",
  "backup.png",
  "clear_all_chat.png",
  "app_lock.png",
  "chatLock.png",
  "location_1.png",
  "location_2.png",
  "slider_thumb.png",
  "slider_thumb_2.png",
  "setting.png",
  "current_location.png",
  "profile_bg.png",
  "filter_icon.png",
  "tick_mark.png",
  "ic_video.png",
  "ic_comment.png",
  "tick.png",
  "like.png",
  "sound.png",
  "line.png",
  "musicIcon.png",
  "currentLocation.png",
  "layout.png",
  "Add.png",
  "languageSelected.png",
  "languageUnselected.png",
  "message-while.png",
  "sound_white.png",
  "sound_black.png",
  "onCall.png",
  "VideoCallBlack.png",
  "Options.png",
  "backArrow.png",
  "crown_bronze.png",
  "Subtract.png",
  "Standard.png",
  "Gold.png",
  "Silver.png",
  "record.png",
  "changeBackground.png",
  "Recording_black.png",
  "Speaker.png",
  "lonely-young-woman-looking-through-concrete-window.webp",
];

const used = [
  "rightArrow.png",
  "userInput.png",
  "bottomArrow.png",

  "call.png",
  "contacts.png",
  "homeTab.png",
  "chatIcon.png",
  "userImage.png",
  "moreMenu.png",
  "searchIcon.png",
  "deleteIcon.png",
  "Qr_Code.png",

  "check_single_tick.png",
  "forward.png",
  "sendButton.png",
  "trash.png",

  "checkPurple.png",
  "play.png",
  "pause.png",
  "UserPin.png",
  "FilePin.png",
  "cropImage.png",
  "HDImage.png",
  "TImage.png",
  "closeImage.png",
  "drawImage.png",
  "editImage.png",
  "accountSetting.png",
  "chatSetting.png",
  "notificationSetting.png",
  "languageSetting.png",
  "privacySetting.png",
  "favouriteSetting.png",
  "inArrow.png",
  "imageWallpaperPick.png",
  "capture.png",
  "maskImage.png",
  "play_icon.png",
  "pause_icon.png",

  "messageTimer.png",
  "menuIcon.png",

  "post_icon.png",
  "copyCode.png",
  "gallery_icon.png",

  "down_arrow.png",
  "chat_lock.png",
  "translate_msg.png",
  "pin.png",
  "menu_icon.png",
  "profile_image.png",
  "block.png",
  "user_dp.png",
  "plus_icon.png",
  "channel.png",

  "save.png",
  "copy_link.png",

  "share.png",

  "send.png",
  "group_icon.png",

  "audio.png",

  "more.png",

  "videocall.png",
  "redArrow.png",
  "downarrow.png",
  "callIcon.png",
  "addNewCall.png",
  "image1.png",
  "image2.png",

  "CloudLock.png",

  "AddPeople.png",

  "search_white.png",
];
let count = 0;
for (const unused of used) {
  fs.unlink(`./${unused}`, (err) => {
    if (err) {
      console.log(err);
    } else {
      count++;
      console.log(count);
    }
  });
}

console.log("Done");
