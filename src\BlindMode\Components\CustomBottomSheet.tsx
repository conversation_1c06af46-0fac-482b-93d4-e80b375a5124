
import React, { useRef, useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
} from 'react-native';
import BottomSheet, {
    BottomSheetView,
    BottomSheetBackdrop,
    BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { colors } from '../../theme/colors';

export interface CustomBottomSheetRef {
    expand: () => void;
    collapse: () => void;
}
export interface ContentItem {
    id: string;
    title: string;
    description: string;
    category: string;
    createdAt: string;
}

export interface BottomSheetProps {
    isVisible: boolean;
    onClose: () => void;
    onSubmit: (content: Omit<ContentItem, 'id' | 'createdAt'>) => void;
}

const CustomBottomSheet = forwardRef<CustomBottomSheetRef, BottomSheetProps>(
    ({ isVisible, onClose, onSubmit }, ref) => {
        const bottomSheetRef = useRef<BottomSheetModal>(null);
        const [title, setTitle] = useState<string>('');
        const [description, setDescription] = useState<string>('');
        const [category, setCategory] = useState<string>('');

        // Snap points for the bottom sheet
        const snapPoints = useMemo(() => ['50%', '90%'], []);

        // Expose methods to parent component
        useImperativeHandle(ref, () => ({
            expand: () => {
                bottomSheetRef.current?.expand();
            },
            collapse: () => {
                bottomSheetRef.current?.collapse();
            },
        }));

        // Handle sheet changes
        const handleSheetChanges = (index: number) => {
            if (index === -1) {
                onClose();
                // Reset form when sheet is closed
                setTitle('');
                setDescription('');
                setCategory('');
            }
        };

        // Handle submit
        const handleSubmit = () => {
            if (title.trim() && description.trim()) {
                onSubmit({
                    title: title.trim(),
                    description: description.trim(),
                    category: category.trim() || 'General',
                });
                setTitle('');
                setDescription('');
                setCategory('');
                onClose();
            }
        };

        // Render backdrop
        const renderBackdrop = (props: any) => (
            <BottomSheetBackdrop
                {...props}
                style={{ backgroundColor: colors.black_23 }}
                disappearsOnIndex={-1}
                appearsOnIndex={0}
                opacity={0.3}
            />
        );

        if (!isVisible) return null;

        return (
            <BottomSheet
                ref={bottomSheetRef}
                index={0}
                snapPoints={snapPoints}
                onChange={handleSheetChanges}
                backdropComponent={renderBackdrop}
                enablePanDownToClose={true}
                backgroundStyle={styles.background}
                handleIndicatorStyle={styles.handleIndicator}
            >
                <BottomSheetView style={styles.contentContainer}>
                    <ScrollView style={{ flex: 1 }}>
                        {/* Header */}
                        <View style={styles.header}>
                            <Text style={styles.title}>Add New Content</Text>
                            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                                <Text style={styles.closeText}>×</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Form */}
                        <View style={styles.form}>
                            <TextInput
                                style={styles.input}
                                placeholder="Title"
                                value={title}
                                onChangeText={setTitle}
                                placeholderTextColor="#999"
                            />

                            <TextInput
                                style={[styles.input, styles.textArea]}
                                placeholder="Description"
                                value={description}
                                onChangeText={setDescription}
                                multiline
                                numberOfLines={4}
                                placeholderTextColor="#999"
                                textAlignVertical="top"
                            />

                            <TextInput
                                style={styles.input}
                                placeholder="Category (optional)"
                                value={category}
                                onChangeText={setCategory}
                                placeholderTextColor="#999"
                            />

                            <TouchableOpacity
                                style={[
                                    styles.submitButton,
                                    (!title.trim() || !description.trim()) && styles.submitButtonDisabled
                                ]}
                                onPress={handleSubmit}
                                disabled={!title.trim() || !description.trim()}
                            >
                                <Text style={styles.submitText}>Add Content</Text>
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                </BottomSheetView>
            </BottomSheet>
        );
    }
);

const styles = StyleSheet.create({
    background: {
        backgroundColor: '#fff',
        borderRadius: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    handleIndicator: {
        backgroundColor: '#ccc',
        width: 40,
        height: 4,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 16,
    },
    keyboardAvoidingView: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        paddingTop: 10,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    closeButton: {
        padding: 5,
    },
    closeText: {
        fontSize: 24,
        color: '#666',
    },
    form: {
        flex: 1,
    },
    input: {
        backgroundColor: '#f5f5f5',
        borderRadius: 10,
        padding: 15,
        marginBottom: 15,
        fontSize: 16,
        borderWidth: 1,
        borderColor: '#eee',
    },
    textArea: {
        minHeight: 100,
        textAlignVertical: 'top',
    },
    submitButton: {
        backgroundColor: '#007AFF',
        borderRadius: 10,
        padding: 15,
        alignItems: 'center',
        marginTop: 10,
    },
    submitButtonDisabled: {
        backgroundColor: '#ccc',
    },
    submitText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default CustomBottomSheet;