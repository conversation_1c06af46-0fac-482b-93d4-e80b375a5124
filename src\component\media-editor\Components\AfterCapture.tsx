import React, { useCallback, useState } from 'react';
import { FlatList, TouchableOpacity, Image, View } from 'react-native';
import styles from './styles';
import { wp } from '../utils/Constants/dimensionUtils';
import PaintColorPicker from './PaintColorPicker';
import PaintBrush from './PaintBrush';
interface AfterCaptureProps {
  images: { uri: string }[];
  activeStoryIdx: number;
  backgroundImage: string | null;
  deleteImage: (id: string) => void;
  setBackgroundImage: (uri: string) => void;
  isDrawing: boolean;
  onColorChange: any;
  strokeWidthFun: any;
  childRef: any;
  datas: any;
}

const AfterCapture: React.FC<AfterCaptureProps> = React.memo(
  ({ images, deleteImage, setBackgroundImage, activeStoryIdx, isDrawing, childRef, datas }) => {
    const renderImageItem = useCallback(
      ({ item }: { item: any }) => {
        return (
          <ImageItem
            item={item}
            isSelected={activeStoryIdx === item.id}
            onSelect={() => setBackgroundImage(item)}
            onDelete={() => deleteImage(item.id)}
          />
        );
      },
      [activeStoryIdx, deleteImage, setBackgroundImage],
    );

    return (
      <>
        {!isDrawing && (
          <FlatList
            bounces={false}
            data={images}
            keyExtractor={(item) => item.uri} // Ensure unique key
            extraData={images}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 10,
              marginBottom: 20,
              width: '100%',
            }}
            renderItem={renderImageItem}
          />
        )}
      </>
    );
  },
);

// Memoized ImageItem component to prevent re-renders
const ImageItem = React.memo(({ item, isSelected, onSelect, onDelete }) => {
  return (
    <TouchableOpacity style={styles.imageItem} onPress={onSelect}>
      <Image source={{ uri: item.uri }} style={styles.listImage} />
      {item?.type.includes('video') && !isSelected && (
        <View style={styles.deleteImageOverlay}>
          <Image
            source={require('../Assets/Images/Video/play.png')}
            style={{ height: wp(3.5), width: wp(3.5), resizeMode: 'contain' }}
          />
        </View>
      )}
      {isSelected && (
        <TouchableOpacity onPress={onDelete} style={styles.deleteImageOverlay}>
          <Image source={require('../Assets/Images/Bin.png')} style={styles.binIcon} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
});

export default React.memo(AfterCapture);
