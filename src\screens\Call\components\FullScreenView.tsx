import React, { useState, useRef, useEffect } from 'react';
import { colors } from '../../../theme/colors';
import { View } from 'react-native';

import { RTCView } from 'react-native-webrtc';

import CallMemberTile from './CallMembertile';
import { _openAppSetting } from '../../../utils/locationHandler';

import { CallDetails, Participant, producerT } from '../../../types/calls.types';
import { hp, wp } from '../../../theme/fonts';
import { Text } from 'react-native-gesture-handler';

type FullScreenViewProps = {
  selectedUser: {
    participant: Participant | null;
    idx: number | null;
  };
  selectedScreenSharer: {
    participant: Participant | null;
    idx: number | null;
  };
  resetTimer: () => void;
  producers: producerT;
  callDetails: CallDetails;
  showScreenSharingBanner: {
    visible: boolean;
    participant: Participant | null;
  };
  fullScreenMode: boolean;
  selectUser: (participant: Participant, idx: number) => void;
};

const FullScreenView: React.FC<FullScreenViewProps> = ({
  selectedUser,
  resetTimer,
  producers,
  callDetails,
  showScreenSharingBanner,
  fullScreenMode,
  selectUser,
  selectedScreenSharer,
}) => {
  const selectedParticipant = selectedUser.participant;
  if (selectedUser.idx !== undefined && selectedUser.idx !== null && selectedParticipant) {
    if (showScreenSharingBanner.visible) {
      return (
        <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
          <View
            style={{
              width: wp(90),
              height: wp(90),
              backgroundColor: colors._7A5DCB_purple,
              borderRadius: 20,
              position: 'relative',
              marginHorizontal: 'auto',
              top: hp(10),
              justifyContent: 'center',
              alignItems: 'center',
              gap: 10,
            }}
          >
            <Text style={{ color: 'white', fontWeight: '400', fontSize: 20 }}>
              {selectedParticipant.client.name || selectedParticipant.client.username} Started
              screen sharing.
            </Text>
          </View>
        </View>
      );
    } else {
      return (
        <CallMemberTile
          onClick={() => {
            resetTimer();
          }}
          callDetails={callDetails}
          participent={selectedParticipant}
          idx={selectedUser.idx}
          isScreenSharing={selectedParticipant.isScreenSharing}
          selectUser={selectUser}
          fullScreenMode={fullScreenMode}
        />
      );
    }
  }

  const selectedScreenSharingParticipant = selectedScreenSharer.participant;

  if (
    selectedScreenSharer.idx !== undefined &&
    selectedScreenSharer.idx !== null &&
    selectedScreenSharingParticipant
  ) {
    if (showScreenSharingBanner.visible) {
      return (
        <View style={{ flex: 1, backgroundColor: colors.mainPurple }}>
          <View
            style={{
              width: wp(90),
              height: wp(90),
              backgroundColor: colors._7A5DCB_purple,
              borderRadius: 20,
              position: 'relative',
              marginHorizontal: 'auto',
              top: hp(10),
              justifyContent: 'center',
              alignItems: 'center',
              gap: 10,
            }}
          >
            <Text style={{ color: 'white', fontWeight: '400', fontSize: 20 }}>
              {selectedScreenSharingParticipant.client.name ||
                selectedScreenSharingParticipant.client.username}{' '}
              Started screen sharing.
            </Text>
          </View>
        </View>
      );
    } else {
      return (
        <CallMemberTile
          onClick={() => {
            console.log('>>>>>>>.. clicked ');
            resetTimer();
          }}
          callDetails={callDetails}
          participent={selectedScreenSharingParticipant}
          idx={selectedScreenSharer.idx}
          isScreenSharing={selectedScreenSharingParticipant.isScreenSharing}
          selectUser={selectUser}
          fullScreenMode={fullScreenMode}
        />
      );
    }
  }

  return producers.videoStream ? (
    <RTCView
      mirror={true}
      streamURL={producers.videoStream.toURL()}
      style={{ width: '100%', height: '100%' }}
      objectFit="cover"
    />
  ) : null;
};

export default FullScreenView;
