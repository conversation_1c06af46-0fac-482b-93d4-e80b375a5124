import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { colors } from '../../theme/colors';
import { commonFontStyle, hp } from '../../theme/fonts';
import ModalWrapper from '../ModalWrapper';
import SelectionSVG from '../../assets/svgIcons/SelectionSVG';

type Props = {
  isVisible: boolean;
  onCloseModal: () => void;
  onConfirmPin: (value: any, removingTime: any) => void;
};

const PinModal = ({ isVisible, onCloseModal, onConfirmPin }: Props) => {
  const { t } = useTranslation();
  let data = [
    { value: '1_HOUR', title: t('1 hour') },
    { value: '24_HOURS', title: t('24 hours') },
    { value: '7_DAYS', title: t('7 days') },
    { value: '30_DAYS', title: t('30 days') },
  ];
  const [selectedIndex, setselectedIndex] = useState(1); // Default to 24 hours

  const onPressTime = (index: any) => {
    const now = new Date();
    const hour1Later = new Date(now.getTime() + 1 * 60 * 60 * 1000).getTime();
    const hours24Later = new Date(now.getTime() + 24 * 60 * 60 * 1000).getTime();
    const days7Later = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).getTime();
    const days30Later = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).getTime();

    const unPinTime =
      index == 0
        ? hour1Later
        : index == 1
        ? hours24Later
        : index == 2
        ? days7Later
        : index == 3
        ? days30Later
        : undefined;
    setselectedIndex(index);
    onConfirmPin(data[index].value, unPinTime);
    setselectedIndex(1);
  };

  const handleCloseModal = () => {
    onCloseModal();
  };

  return (
    <ModalWrapper isVisible={isVisible} onCloseModal={handleCloseModal}>
      <View style={{ paddingHorizontal: hp(1) }}>
        <Text style={styles.title}>{t('Choose how long this pin lasts')}</Text>
        {data.map((item, index) => {
          return (
            <TouchableOpacity onPress={() => onPressTime(index)} style={styles.rowView} key={index}>
              <SelectionSVG size={22} isSelected={selectedIndex == index} />

              <Text style={styles.titleItem}>{item.title}</Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </ModalWrapper>
  );
};

export default PinModal;

const styles = StyleSheet.create({
  title: {
    // ...commonFontStyle(600, 16, colors.black_23),
    color: colors.black_23,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: hp(1.5),
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: hp(1),
  },
  titleItem: {
    // ...commonFontStyle(400, 16, colors.black_23),
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  checkImage: {
    height: 22,
    width: 22,
  },
  roundView: {
    height: 22,
    width: 22,
    borderRadius: 11,
    borderColor: colors.gray_80,
    borderWidth: 1.5,
  },
});
