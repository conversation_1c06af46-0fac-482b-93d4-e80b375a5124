import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface EyedropperSVGProps {
  size?: number;
  color?: string;
}

const EyedropperSVG: React.FC<EyedropperSVGProps> = ({
  size = 24,
  color = '#232323',
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20.385 3.616a3.97 3.97 0 0 0-5.618 0L13.06 5.323l-.708-.708a1 1 0 0 0-1.414 1.414l.708.708-6.642 6.642a2.98 2.98 0 0 0-.878 2.121v1.091l-1.414 1.414a1 1 0 1 0 1.414 1.414l1.414-1.414h1.091a2.98 2.98 0 0 0 2.121-.878l6.642-6.642.708.708a1 1 0 0 0 1.414-1.414l-.708-.708 1.707-1.707a3.97 3.97 0 0 0 0-5.618zM9.11 15.39a.995.995 0 0 1-.707.293H7.111v-1.293c0-.265.105-.52.293-.707l6.642-6.642 1.707 1.707L9.11 15.39zm9.56-9.56l-1.707 1.707-1.707-1.707 1.707-1.707a1.97 1.97 0 0 1 2.79 0 1.97 1.97 0 0 1 0 2.79l-.083-.083z"
        fill={color}
      />
    </Svg>
  );
};

export default EyedropperSVG; 