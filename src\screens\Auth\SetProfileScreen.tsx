import {
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  View,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { Route, RouteProp, useNavigation, useRoute } from '@react-navigation/native';

import { IMAGES } from '../../assets/Images';
import {
  commonFontStyle,
  hexToRgba,
  hp,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
  statusBarHeight,
} from '../../theme/fonts';
import { colors } from '../../theme/colors';
import Input from '../../component/Input';
import ButtonPurple from '../../component/ButtonPurple';
import { SCREENS } from '../../navigation/screenNames';
import RenderRadioButton from '../../component/RenderRadioButton';
import ImageCropPicker from 'react-native-image-crop-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import {
  errorToast,
  navigateTo,
  resetNavigation,
  showToast,
  successToast,
  VALIDATION_RULES,
} from '../../utils/commonFunction';

import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { setUpProfile } from '../../utils/ApiService';
import { setAsyncToken, setStreamToken } from '../../utils/asyncStorage';
import CommonView from '../../component/CommonView';
import EditImageSVG from '../../assets/svgIcons/EditImageSVG';
import { navigationRef } from '../../navigation/RootContainer';
import { Client } from '../../lib/Client';
import { getMe } from '../../utils/userApiService';
import { useMe } from '../../hooks/util/useMe';
import BackArrowSVG from '../../assets/svgIcons/BackArrowSVG';

type Props = {};

const SetProfileScreen = (props: Props) => {
  const [profileImage, setProfileImage] = useState<any>(null);
  const [DOB, setDOB] = useState('');
  const [date, setDate] = useState<Date>();
  const [bio, setBio] = useState('');
  const [gender, setGender] = useState('male');
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const params =
    useRoute<RouteProp<{ params: { data: { interimToken: string; username: string } } }>>().params;

  const navigation = useNavigation();
  const [userName, setUserName] = useState('');
  const { t } = useTranslation();
  const { updateUser } = useMe();

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    setDate(date);
    setDOB(moment(date).format('DD/MM/YYYY'));
    hideDatePicker();
  };

  const handleChange = (text: string) => {
    // Remove all slashes to handle raw input
    let rawText = text.replace(/\//g, '');

    // Handle adding slashes
    if (rawText.length > 2) {
      rawText = rawText.slice(0, 2) + '/' + rawText.slice(2);
    }
    if (rawText.length > 5) {
      rawText = rawText.slice(0, 5) + '/' + rawText.slice(5);
    }

    // Ensure proper removal behavior
    if (text.length < DOB.length) {
      // If user is deleting, handle slashes properly
      if (DOB[DOB.length - 1] === '/' && rawText.length === DOB.length - 1) {
        rawText = rawText.slice(0, -1); // Remove the extra slash
      }
    }

    // Update state
    setDOB(rawText);
  };

  const onChangeImage = () => {
    ImageCropPicker.openPicker({
      cropping: true,
      height: SCREEN_WIDTH,
      width: SCREEN_WIDTH,
    }).then((image) => {
      const imageData = {
        name: image.filename ?? image.path?.split('/')?.pop(),
        type: image.mime,
        uri: image.path ?? image.sourceURL,
      };
      setProfileImage(imageData);
    });
  };

  const onDone = async () => {
    if (userName.trim() === '') {
      errorToast(t('Please enter name'));
      return;
    }
    if (bio.trim() === '') {
      errorToast(t('Please enter bio'));
      return;
    }
    if (userName.trim().length > VALIDATION_RULES.NAME_LENGTH) {
      errorToast(t(`Name cannot exceed ${VALIDATION_RULES.NAME_LENGTH} characters`));
      return;
    }

    // Bio validation
    if (bio.trim().length > VALIDATION_RULES.BIO_LENGTH) {
      errorToast(t(`Bio cannot exceed ${VALIDATION_RULES.BIO_LENGTH} characters`));
      return;
    }

    // DOB validation
    if (DOB === '') {
      errorToast(t('Please select Date of Birth'));
      return;
    }
    if (!/^\d{2}\/\d{2}\/\d{4}$/.test(DOB)) {
      errorToast(t('Invalid date format. Please use DD/MM/YYYY'));
      return;
    }
    const parsedDate = moment(DOB, 'DD/MM/YYYY', true);
    if (!parsedDate.isValid()) {
      errorToast(t('Invalid Date of Birth'));
      return;
    }
    if (parsedDate.isAfter(moment())) {
      errorToast(t('Date of Birth cannot be in the future'));
      return;
    }
    const age = moment().diff(parsedDate, 'years');
    if (age < VALIDATION_RULES.MIN_AGE) {
      errorToast(t(`You must be at least ${VALIDATION_RULES.MIN_AGE} years old`));
      return;
    }
    if (age > VALIDATION_RULES.MAX_AGE) {
      errorToast(t(`Age cannot be more than ${VALIDATION_RULES.MAX_AGE} years`));
      return;
    }

    // Gender validation
    if (!['male', 'female', 'other'].includes(gender)) {
      errorToast(t('Please select a valid gender'));
      return;
    }

    // Profile image validation (optional, uncomment if required)
    // if (!profileImage) {
    //   errorToast(t('Please select a profile picture'));
    //   return;
    // }

    let image = profileImage?.uri || '';

    let data = {
      username: params.data.username,
      token: params.data.interimToken,
      name: userName.trim(),
      bio: bio.trim(),
      dateOfBirth: parsedDate.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
      email: '',
      gender: gender.toUpperCase(),
      profile_pic: image || '',
    };

    const formData = new FormData();

    formData.append('username', params.data.username);
    formData.append('token', params.data.interimToken);
    formData.append('name', userName.trim());
    formData.append('bio', bio.trim());
    formData.append('dateOfBirth', parsedDate.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'));
    formData.append('email', '');
    formData.append('gender', gender?.toUpperCase());
    formData.append('profile_pic', {
      uri: image,
      name: Date.now(),
      type: 'image',
    });
    console.log('formData', formData);

    try {
      setLoading(true);

      const res = await setUpProfile(data);
      if (res?.status) {
        await Client.AuthToken.set(res?.data?.authToken);
        const user = await getMe();
        if (!user) {
          errorToast(t('Failed to fetch user data after profile setup.'));
          setLoading(false);
          return;
        }
        updateUser(user);
        setStreamToken(res?.data?.streamToken);
        setLoading(false);
        navigationRef.reset({
          index: 0,
          routes: [{ name: SCREENS.HomeScreen }],
        });
      } else if (res?.statusCode === -6) {
        setLoading(false);
        console.warn('Verification pending or another issue occurred.');
      } else {
        setLoading(false);
        errorToast(t(res?.message || 'Profile setup failed. Please try again.'));
      }
    } catch (error) {
      setLoading(false);
      console.error('Error during profile setup:', error);
    }
  };

  const renderHeader = () => {
    return (
      <View
        style={{
          height: hp(8),
          marginTop: 25,
          flexDirection: 'row',
          alignItems: 'center',
          paddingLeft: hp(2),
          gap: 15,
        }}
      >
        <TouchableOpacity
          onPress={() => {
            if (!loading) navigation.goBack();
          }}
          style={{}}
        >
          <BackArrowSVG size={20} color="#fff" />
        </TouchableOpacity>
        <View style={{}}>
          <View>
            <Text style={{ ...commonFontStyle(600, 18, colors.white) }}>{'Setup profile'}</Text>
          </View>
        </View>
      </View>
    );
  };

  useEffect(() => {
    const backAction = () => {
      if (loading) {
        return true;
      }
      return false;
    };
    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => {
      backHandler.remove();
    };
  }, [loading]);

  return (
    <CommonView
      customHeader={renderHeader}
      containerStyle={{ backgroundColor: colors.white, paddingTop: hp(0) }}
      // headerContainerStyle={{ marginTop: Number(androidVersion) > 14 ? hp(statusBarHeight()) : 0 }}
      headerColor={colors.mainPurple}
      // headerTitle="Setup profile"
    >
      <KeyboardAwareScrollView
        enableOnAndroid
        // extraHeight={hp(20)}
        // keyboardShouldPersistTaps="handled"
        // extraScrollHeight={Platform.OS === 'ios' ? 100 : 0} // adjust for your layout
        contentContainerStyle={{ paddingTop: hp(3) }}
        showsVerticalScrollIndicator={false}
      >
        {profileImage?.uri ? (
          <TouchableOpacity
            onPress={() => onChangeImage()}
            style={[styles.imageView, { marginBottom: hp(3.5) }]}
          >
            <Image source={{ uri: profileImage?.uri }} style={styles.imageProfile} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => onChangeImage()}
            style={[styles.imageView, { marginBottom: hp(3.5) }]}
          >
            <Image
              source={IMAGES.gallery_gray}
              style={[
                styles.imageProfile,
                {
                  resizeMode: 'contain',
                  alignSelf: 'center',
                  borderRadius: 0,
                },
              ]}
            />
            {/* <EditImageSVG size={22} /> */}
          </TouchableOpacity>
        )}

        <Input
          value={userName}
          extraStyle={styles.input}
          onChangeText={(text: string) => {
            if (text.length <= VALIDATION_RULES.NAME_LENGTH) {
              setUserName(text);
            } else {
              showToast(t(`Name cannot exceed ${VALIDATION_RULES.NAME_LENGTH} characters`));
            }
          }}
          titleColor={colors.gray_80}
          placeHolder={t('Eg: William james')}
          title={t('Enter name')}
        />

        <Input
          value={bio}
          extraStyle={styles.input}
          textInputStyle={styles.multiLineStyle}
          onChangeText={(text: string) => {
            if (text.length <= VALIDATION_RULES.BIO_LENGTH) {
              setBio(text);
            } else {
              showToast(t(`Bio cannot exceed ${VALIDATION_RULES.BIO_LENGTH} characters`));
            }
          }}
          titleColor={colors.gray_80}
          placeHolder={t('Write something about yourself..')}
          title={t('Bio')}
          multiline={true}
          textAlignVertical="top"
        />

        <TouchableOpacity onPress={() => setDatePickerVisibility(true)} activeOpacity={1}>
          <Input
            editable={false}
            RenderRightIcon={<Image source={IMAGES.calender} style={styles.rightIconTextInput} />}
            value={DOB}
            extraStyle={styles.input}
            onChangeText={handleChange}
            placeHolder={t('DD/MM/YYYY')}
            maxLength={10}
            title={t('Date of birth')}
            titleColor={colors.gray_80}
          />
        </TouchableOpacity>

        <View>
          <Text
            style={{
              ...commonFontStyle(400, 16, colors.gray_80),
            }}
          >
            {t('Gender')}
          </Text>
          <View style={styles.radioMainView}>
            <TouchableOpacity onPress={() => setGender('male')} style={styles.radioView}>
              <RenderRadioButton value={gender == 'male'} />
              <Text style={styles.radioTitle}>{t('Male')}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setGender('female')} style={styles.radioView}>
              <RenderRadioButton value={gender == 'female'} />
              <Text style={styles.radioTitle}>{t('Female')}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setGender('other')} style={styles.radioView}>
              <RenderRadioButton value={gender == 'other'} />
              <Text style={styles.radioTitle}>{t('Prefer not to say')}</Text>
            </TouchableOpacity>
          </View>
        </View>

        <ButtonPurple onPress={() => onDone()} title={t('Done')} />
        <DateTimePickerModal
          isVisible={isDatePickerVisible}
          mode="date"
          date={date}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
        />
      </KeyboardAwareScrollView>

      {loading ? (
        <View
          style={{
            height: SCREEN_HEIGHT,
            width: SCREEN_WIDTH,
            zIndex: 100,
            paddingTop: SCREEN_HEIGHT / 2.5,
            alignItems: 'center',
            position: 'absolute',
            backgroundColor: hexToRgba(colors.black, 0.5),
          }}
        >
          <ActivityIndicator size={'large'} color={colors.white} />
        </View>
      ) : null}
    </CommonView>
  );
};

export default SetProfileScreen;

const styles = StyleSheet.create({
  titleText: {
    ...commonFontStyle(700, 25, colors.white),
    textAlign: 'center',
  },
  des: {
    ...commonFontStyle(400, 16, colors.white),
    textAlign: 'center',
    marginBottom: hp(4),
  },
  input: {
    marginBottom: hp(2.5),
  },
  imageView: {
    backgroundColor: colors.gray_f3,
    height: 90,
    width: 90,
    borderRadius: 90 / 2,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  imageProfile: {
    height: 90,
    width: 90,
    borderRadius: 90 / 2,
    resizeMode: 'contain',
  },
  imagePlaceholder: {
    height: 33,
    width: 33,
    resizeMode: 'contain',
    alignSelf: 'center',
  },

  multiLineStyle: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  rightIconTextInput: {
    height: 25,
    width: 25,
    resizeMode: 'contain',
  },
  radioTitle: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  radioMainView: {
    flexDirection: 'row',
    gap: hp(2),
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 8,
    marginBottom: hp(4),
  },
  radioView: {
    flexDirection: 'row',
    gap: 5,
    alignItems: 'center',
  },
});
