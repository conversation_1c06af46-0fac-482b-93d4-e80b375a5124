import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
  size?: number;
  color?: string;
}

const SettingsSVG: React.FC<SvgComponentProps> = ({
  size = 20,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 18) / 17}
      viewBox="0 0 17 18"
      fill="none"
      {...props}
    >
      <Path
        d="M8.183 17.832c-.562 0-1.132-.126-1.576-.386l-4.612-2.658C.117 13.522 0 13.33 0 11.342V6.496c0-1.987.109-2.18 1.954-3.43L6.599.385c.88-.512 2.263-.512 3.144 0L14.37 3.05c1.878 1.266 1.995 1.459 1.995 3.446v4.838c0 1.987-.108 2.18-1.953 3.429l-4.645 2.683c-.453.26-1.023.386-1.585.386zm0-16.568c-.352 0-.696.067-.939.21L2.633 4.14c-1.367.922-1.367.922-1.367 2.356v4.838c0 1.433 0 1.433 1.4 2.38l4.578 2.642c.495.285 1.392.285 1.887 0l4.611-2.666c1.358-.923 1.358-.923 1.358-2.356V6.496c0-1.434 0-1.434-1.4-2.381L9.122 1.474c-.243-.143-.587-.21-.939-.21z"
        fill={color}
      />
      <Path
        d="M8.183 12.062a3.145 3.145 0 010-6.289 3.145 3.145 0 010 6.289zm0-5.03a1.89 1.89 0 00-1.886 1.886 1.89 1.89 0 001.886 1.886 1.89 1.89 0 001.887-1.886A1.89 1.89 0 008.183 7.03z"
        fill={color}
      />
    </Svg>
  );
};

export default SettingsSVG;

