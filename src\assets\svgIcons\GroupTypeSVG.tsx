import * as React from "react";
import Svg, { Path } from "react-native-svg";

function GroupTypeSVG({ size = 24, color = "#232323", ...props }) {
  return (
    <Svg
      width={size}
      height={(size * 19) / 14} 
      viewBox="0 0 14 19"
      fill="none"
      {...props}
    >
      <Path
        d="M3.906 19h5.25a3.91 3.91 0 003.906-3.906v-3.688A3.91 3.91 0 009.156 7.5h-5.25A3.91 3.91 0 000 11.406v3.688A3.91 3.91 0 003.906 19zm-2.344-7.594a2.346 2.346 0 012.344-2.344h5.25a2.346 2.346 0 012.344 2.344v3.688a2.346 2.346 0 01-2.344 2.344h-5.25a2.346 2.346 0 01-2.344-2.344v-3.688zM3.781 6.281c.432 0 .781-.35.781-.781V3.906a2.346 2.346 0 012.344-2.344A2.346 2.346 0 019.25 3.906V5.5a.781.781 0 101.563 0V3.906A3.91 3.91 0 006.905 0 3.91 3.91 0 003 3.906V5.5c0 .431.35.781.781.781z"
        fill={color}
      />
    </Svg>
  );
}

export default GroupTypeSVG;
