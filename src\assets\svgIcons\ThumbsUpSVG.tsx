import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const ThumbsUpSVG: React.FC<IconProps> = ({
  size = 18,
  color = '#000000',
  ...restProps
}) => {
  return (
    <Svg
      width={size}
      height={(size * 18) / 17} // maintains original aspect ratio
      viewBox="0 0 17 18"
      fill="none"
      {...restProps}>
      <Path
        d="M12.0718 17.6196H7.00284C5.86455 17.6196 4.77289 17.1674 3.96801 16.3625C3.16312 15.5576 2.71094 14.466 2.71094 13.3277V7.19252C2.71132 6.34631 2.96289 5.51925 3.43378 4.81616L6.44263 0.298373C6.50458 0.206723 6.58802 0.131633 6.68567 0.0796568C6.78333 0.0276812 6.89222 0.000401168 7.00284 0.000199054C7.65154 -0.00670403 8.2895 0.166062 8.84607 0.499366C9.40264 0.83267 9.8561 1.31351 10.1563 1.88864C10.455 2.43253 10.6047 3.04566 10.5905 3.66602C10.5762 4.28638 10.3983 4.89197 10.0749 5.42155H12.6772C13.3031 5.417 13.9224 5.5494 14.4917 5.80946C15.061 6.06952 15.5665 6.45095 15.9728 6.92701C16.3792 7.40308 16.6765 7.96225 16.8439 8.56534C17.0113 9.16842 17.0448 9.80083 16.942 10.4182L16.3456 14.0325C16.1777 15.0416 15.6548 15.9576 14.8712 16.6153C14.0876 17.273 13.0948 17.6292 12.0718 17.6196ZM7.34619 1.35554L4.56323 5.56612C4.23879 6.04642 4.0657 6.61291 4.06627 7.19252V13.3277C4.06866 14.1058 4.37881 14.8513 4.92901 15.4015C5.4792 15.9517 6.22475 16.2619 7.00284 16.2642H12.0718C12.7686 16.2668 13.4436 16.0207 13.9752 15.5702C14.5069 15.1198 14.8604 14.4944 14.9722 13.8066L15.5686 10.1923C15.6396 9.77215 15.618 9.34152 15.5054 8.93054C15.3927 8.51955 15.1917 8.13811 14.9164 7.81286C14.641 7.48762 14.298 7.2264 13.9112 7.04746C13.5245 6.86853 13.1033 6.77618 12.6772 6.77688H9.90326C9.67561 6.77539 9.45259 6.71248 9.25771 6.59481C9.06283 6.47713 8.90331 6.30905 8.79599 6.10828C8.68867 5.90751 8.63751 5.68151 8.64792 5.45409C8.65833 5.22668 8.72992 5.00629 8.85513 4.81616C9.06316 4.50545 9.18945 4.14728 9.2223 3.7748C9.25514 3.40232 9.19349 3.02757 9.04305 2.68525C8.89261 2.34292 8.65823 2.04408 8.36163 1.81639C8.06502 1.5887 7.71576 1.43951 7.34619 1.38264V1.35554Z"
        fill={color}
      />
      <Path
        d="M0.677668 14.908C0.498667 14.9057 0.327656 14.8335 0.201072 14.7069C0.0744885 14.5804 0.00234018 14.4094 0 14.2304V7.00189C0 6.82216 0.0713969 6.64979 0.198484 6.5227C0.325572 6.39562 0.49794 6.32422 0.677668 6.32422C0.857397 6.32422 1.02976 6.39562 1.15685 6.5227C1.28394 6.64979 1.35534 6.82216 1.35534 7.00189V14.2304C1.353 14.4094 1.28085 14.5804 1.15426 14.7069C1.02768 14.8335 0.856669 14.9057 0.677668 14.908Z"
        fill={color}
      />
    </Svg>
  );
};

export default ThumbsUpSVG; 