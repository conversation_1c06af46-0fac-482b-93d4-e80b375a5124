import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import CommonView from '../../../../component/CommonView';
import { getFollowersinChannels } from '../../../../utils/ApiService';
import { colors } from '../../../../theme/colors';
import { hp, wp } from '../../../../theme/fonts';
import ButtonPurple from '../../../../component/ButtonPurple';

const FollowersScreen = ({ route }: any) => {
  const { chatSpaceId } = route.params;
  const [followers, setFollowers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    fetchFollowers();
  }, []);

  const fetchFollowers = async () => {
    try {
      const res = await getFollowersinChannels(chatSpaceId);
      setFollowers(res?.members || []);
    } catch (err) {
      console.error('Failed to fetch followers:', err);
    } finally {
      setLoading(false);
    }
  };


  const renderItem = ({ item }: any) => (
  <View style={styles.itemContainer}>
    <Image source={{ uri: item?.user?.image }} style={styles.avatar} />
    <View style={styles.textContainer}>
      <View style={styles.row}>
        <View>
        <Text style={styles.name}>{item?.user?.name}</Text>
        <Text style={styles.username}>{item?.user?.username}</Text>
        </View>
        {item?.role === 'owner' && (
          <View style={styles.roleStyle}>
            <Text style={styles.role}>You</Text>
          </View>
        )}
      </View>

      {/* <ButtonPurple
        title={'Remove'}
        type="gray"
        extraStyle={styles.btnStyle}
        titleColor={colors.mainPurple}
        // onPress={() => handleRemoveFollower(item?.user?.id)}
      /> */}
    </View>
  </View>
);


  return (
    <CommonView headerTitle="Followers">
      <View style={styles.container}>
        {loading ? (
          <ActivityIndicator size="large" color={colors.mainPurple} />
        ) : (
          <FlatList
            data={followers}
            keyExtractor={(item, index) => item.id || index.toString()}
            renderItem={renderItem}
            contentContainerStyle={{ paddingVertical: hp(2) }}
          />
        )}
      </View>
    </CommonView>
  );
};

export default FollowersScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: hp(3),
  },
  avatar: {
    width: hp(6),
    height: hp(6),
    borderRadius: hp(3),
    marginRight: wp(3),
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  roleStyle: {
    marginLeft: wp(2),
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.8),
    borderRadius: 24,
    backgroundColor: colors.gray_80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  role: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.white,
  },
  username: {
    fontSize: 14,
    color: colors.gray_80,
  },


});
