import Realm from 'realm';
import { realmSchemaNames } from './schemaNames';

export class ConversationSettingsSchema
  extends Realm.Object<ConversationSettingsSchema>
  implements IConversationSettings
{
  id!: string;
  disappearDuration?: number;
  muteUntil?: string;
  isFavourite?: boolean;
  isArchived!: boolean;
  isPinned!: boolean;
  translationLanguage?: string;
  translateText!: boolean;
  translateVoiceMessages!: boolean;
  translateCalls!: boolean;

  static schema: Realm.ObjectSchema = {
    name: realmSchemaNames.conversation_settings,
    primaryKey: 'id',
    properties: {
      id: 'string',
      disappearDuration: { type: 'int', optional: true }, // in seconds or minutes
      isFavourite: { type: 'bool', default: false },
      muteUntil: { type: 'string', optional: true }, // ISO datetime string
      isArchived: { type: 'bool', default: false },
      isPinned: { type: 'bool', default: false },

      // Translation
      translationLanguage: { type: 'string', optional: true },
      translateText: { type: 'bool', default: false },
      translateVoiceMessages: { type: 'bool', default: false },
      translateCalls: { type: 'bool', default: false },
    },
  };
}

export interface IConversationSettings {
  id: string;
  disappearDuration?: number; // how long before messages disappear
  muteUntil?: string; // ISO datetime string until muted
  isFavourite?: boolean; // favorited or not
  isArchived: boolean; // archived or not
  isPinned: boolean; // pinned or not
  translationLanguage?: string; // e.g. "en", "hi"
  translateText: boolean; // auto-translate text messages
  translateVoiceMessages: boolean; // auto-translate voice messages
  translateCalls: boolean; // auto-translate calls
}
