import * as React from "react"
import Svg, { Circle, Path } from "react-native-svg"

type Props = {
  size?: number
  color?: string
  isRecording?: boolean
}

const RecordingPlayPauseSVG: React.FC<Props> = ({ size = 134, color = "#6A4DBB", isRecording= false, ...props }) => {
  return (
    <>
    {isRecording ?
        <Svg
        width={size}
        height={size}
        viewBox="0 0 134 134"
        fill="none"
        {...props}
        >
        <Circle cx={67.0006} cy={67.0001} r={38.8341} fill={color} />
        <Circle
            opacity={0.3}
            cx={66.9998}
            cy={67.0003}
            r={51.3451}
            stroke={color}
            strokeWidth={1.54868}
            />
        <Circle
            opacity={0.1}
            cx={67}
            cy={67}
            r={66.2257}
            stroke={color}
            strokeWidth={1.54868}
            />
        <Path
            d="M60.868 77.22V56.78M72.11 77.22V56.78"
            stroke="#fff"
            strokeWidth={2.08203}
            strokeLinecap="round"
            strokeLinejoin="round"
            />
        </Svg> : 
        <Svg
        width={size}
        height={size}
        viewBox="0 0 134 134"
        fill="none"
        {...props}
        >
        <Circle cx={67.0006} cy={67.0001} r={38.8341} fill={color} />
        <Circle
            opacity={0.3}
            cx={66.9998}
            cy={67.0003}
            r={51.3451}
            stroke={color}
            strokeWidth={1.54868}
        />
        <Circle
            opacity={0.1}
            cx={67}
            cy={67}
            r={66.2257}
            stroke={color}
            strokeWidth={1.54868}
        />
        <Path
            d="M76.64 65.385v1.4c0 5.407-4.56 9.79-10.183 9.79m-10.183-11.19v1.4c0 5.407 4.56 9.79 10.183 9.79m0 0v4.196m0 0h4.364m-4.364 0h-4.364m4.364-8.392c-3.213 0-5.819-2.505-5.819-5.595v-8.392c0-3.09 2.605-5.595 5.82-5.595 3.213 0 5.818 2.505 5.818 5.595v8.392c0 3.09-2.605 5.595-5.819 5.595z"
            stroke="#fff"
            strokeWidth={2.09808}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        </Svg>
    }
    </>
  )
}

export default RecordingPlayPauseSVG

