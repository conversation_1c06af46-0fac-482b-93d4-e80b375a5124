import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { CInput, CButton } from '../../../../common';
import { useState } from 'react';
import LottieView from 'lottie-react-native';
import SuccessModal from '../../../../component/SuccessModal';
import { emailCheck, nameCheck, errorToast } from '../../../../utils/commonFunction';
import { useNavigation } from '@react-navigation/native';
import { submitUserFeedback } from '../../../../api/Userservice/userService.api';

const ContactUsScreen = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const navigation = useNavigation();

  const handleSubmit = async () => {
    if (!nameCheck(name)) {
      errorToast('Please enter a valid name.');
      return;
    }
    if (!emailCheck(email)) {
      errorToast('Please enter a valid email address.');
      return;
    }
    if (!message || message.length < 5) {
      errorToast('Please enter a message (at least 5 characters).');
      return;
    }
    const payload = {
      name,
      email,
      message,
    };
    try {
      const res = await submitUserFeedback(payload);
      if (res) {
        setShowSuccess(true);
        setName('');
        setEmail('');
        setMessage('');
      } else {
        errorToast('Something went wrong.');
      }
    } catch (error) {
      errorToast('Something went wrong. Please try again.');
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <HeaderBackWithTitle title="Contact us" />
      <View style={styles.whiteContainer}>
        <KeyboardAwareScrollView
          contentContainerStyle={{ paddingBottom: 40, flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          enableOnAndroid={true}
          extraScrollHeight={40}
        >
          <LottieView
            source={require('../../../../assets/animatedLottiImages/contact_us.json')}
            autoPlay
            loop
            style={styles.lottieContact}
          />
          <Text style={styles.infoText}>
            We'd love to hear from you! Please fill out the form below and our team will get back to
            you as soon as possible.
          </Text>
          <View style={styles.formContainer}>
            <CInput placeholder="Name" value={name} onChangeText={setName} cStyle={styles.input} />
            <CInput
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              cStyle={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <CInput
              placeholder="Complain / Suggestion"
              value={message}
              onChangeText={setMessage}
              cStyle={[styles.input, styles.textArea]}
              multiline
              numberOfLines={4}
            />
            <CButton onPress={handleSubmit} cStyle={styles.button}>
              <Text style={styles.buttonText}>Submit</Text>
            </CButton>
          </View>
        </KeyboardAwareScrollView>
        <SuccessModal
          visible={showSuccess}
          message="Thank you for contacting us! We have received your message."
          onClose={() => {
            setShowSuccess(false);
            navigation.goBack();
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  infoText: {
    fontSize: 15,
    color: colors.gray_80,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 10,
  },
  formContainer: {
    justifyContent: 'flex-start',
  },
  input: {
    marginBottom: 14,
    backgroundColor: '#F7F7F7',
    borderRadius: 12,
    paddingHorizontal: 12,
    fontSize: 15,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  button: {
    marginTop: 10,
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    width: '107%',
    alignSelf: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  lottieContact: {
    width: 200,
    height: 200,
    alignSelf: 'center',
    marginTop: 20,
    marginBottom: 0,
  },
});

export default ContactUsScreen;
