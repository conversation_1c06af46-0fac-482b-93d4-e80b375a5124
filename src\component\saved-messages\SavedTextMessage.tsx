import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useMe } from '../../hooks/util/useMe';
import { IMessage } from '../../device-storage/realm/schemas/MessageSchema';

type SavedTextMessageProps = {
  message: IMessage;
};

const TICK_SEEN = '✓✓';
const TICK_DELIVERED = '✓';

const SavedTextMessage = ({ message }: SavedTextMessageProps) => {
  const { user: me } = useMe();
  const isMyMsg = message.senderId === me?._id;

  // Format the timestamp as hh:mm am/pm
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12 || 12;
    return `${hours}:${minutes} ${ampm}`;
  };

  return (
    <View style={[styles.messageRow, isMyMsg ? styles.rightAlign : styles.leftAlign]}>
      <View style={[styles.messageBubble, isMyMsg ? styles.myBubble : styles.otherBubble]}>
        <Text style={styles.messageText}>{message.text}</Text>
        <View style={styles.statusRow}>
          <Text style={styles.timestamp}>{formatTime(message.createdAt)}</Text>
          {isMyMsg && (
            <Text style={styles.tick}>
              {message.seenAt ? TICK_SEEN : message.deliveredAt ? TICK_DELIVERED : ''}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageRow: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 10,
  },
  leftAlign: {
    justifyContent: 'flex-start',
  },
  rightAlign: {
    justifyContent: 'flex-end',
  },
  messageBubble: {
    maxWidth: '75%',
    borderRadius: 14,
    padding: 10,
    marginBottom: 2,
    elevation: 1,
  },
  myBubble: {
    backgroundColor: '#e7e9ef',
  },
  otherBubble: {
    backgroundColor: '#fff',
  },
  messageText: {
    fontSize: 15,
    color: '#222',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: 4,
    gap: 8,
    justifyContent: 'flex-end',
  },
  timestamp: {
    fontSize: 11,
    color: '#868995',
    marginRight: 2,
  },
  tick: {
    fontSize: 14,
    color: '#7c7ca2',
    marginLeft: 3,
  },
});

export default SavedTextMessage;
