import {
  Alert,
  Image,
  Linking,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  Vibration,
  View,
  PermissionsAndroid,
  Pressable,
} from 'react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hexToRgba, hp, SCREEN_WIDTH } from '../../../../theme/fonts';
import { dayPipe, navigateTo, showToast } from '../../../../utils/commonFunction';
import { IMAGES } from '../../../../assets/Images';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import VideoSVG from '../../../../assets/svgIcons/VideoSVG';

import MediaPlaySVG from '../../../../assets/svgIcons/MediaPlaySVG';
import FileSVG from '../../../../assets/svgIcons/FileSVG';
import XlsSVG from '../../../../assets/svgIcons/XlsSVG';
import DocumentSVG from '../../../../assets/svgIcons/DocumentSVG';
import {
  ChannelType,
  ConversationType,
  IMessage,
  MessageEventType,
  MessageStatus,
  MessageType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import TimerSVG from '../../../../assets/svgIcons/TimerSVG';
import MessageStatusIcon from '../../../../component/PersonalChat/MessageStatusIcon';
import ModalWrapper from '../../../../component/ModalWrapper';
import DeleteSVG from '../../../../assets/svgIcons/DeleteSVG';
import EditSVG from '../../../../assets/svgIcons/EditSVG';
import SendArrowSVG from '../../../../assets/svgIcons/SendArrowSVG';
import { useMe } from '../../../../hooks/util/useMe';
import { getLighterShade } from '../../../../theme/fonts';
// import FastImage from '@d11/react-native-fast-image'
import AudioWavesComponent from './AudioWavesComponent';
import { ChatService } from '../../../../service/ChatService';
import GalleryPlaneSVG from '../../../../assets/svgIcons/GalleryPlaneSVG';
import Sound from 'react-native-sound';
import MicSVG2 from '../../../../assets/svgIcons/MicSVG2';
import LocationCard from '../../components/LocationCard';
import UnpinSVG from '../../../../assets/svgIcons/UnpinSVG';
import { useNavigation } from '@react-navigation/native';
import RNFS from 'react-native-fs';
import { formatBytes } from '../../../../common/CFilename';
import FileViewer from 'react-native-file-viewer';
import VideoDocSVG from '../../../../assets/svgIcons/VideoDocSVG';
import LiveLocationCard from '../../components/LiveLocationCard';
import ContactAvatarSVG from '../../../../assets/svgIcons/ContactAvatarSVG';
import Contacts from 'react-native-contacts';
import { ChatSpecificScreenParamsT, IChatScreenProps } from '../ChatSpecificScreen';
import { createThumbnail } from 'react-native-create-thumbnail';
import VideoCallSVG from '../../../../assets/svgIcons/VideoCallSVG';
import ReplyAvatarSVG from '../../../../assets/svgIcons/ReplyAvatarSVG';
import ReplyDocSVG from '../../../../assets/svgIcons/DocReplySVG';
import MusicFileSVG from '../../../../assets/svgIcons/MusicFileSVG';
import MusicSVG from '../../../../assets/svgIcons/MusicSVG';
import LottieView from 'lottie-react-native';
import EventMessage from './EventMessages';
import AnimatedReactionPopup from '../AnimatedReactionPopover';
import useSocket from '../../../../socket-client/useSocket';
import { ChatSocket } from '../../../../socket-client/ChatSocket';
import Clipboard from '@react-native-clipboard/clipboard';
import LocationSVG from '../../../../assets/svgIcons/LocationSVG';
import LiveLocationSVG from '../../../../assets/svgIcons/LiveLocationSVG';
import { useContacts } from '../../../../hooks/contacts/useContacts';
import { useRealm } from '../../../../device-storage/realm/realm';
import useUser from '../../../../device-storage/realm/hooks/useUser';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import { SCREENS } from '../../../../navigation/screenNames';
import UserAvatar from '../../../../component/utils/UserAvatar';

import { useWhatChanged } from '@simbathesailor/use-what-changed';
import ImageVideoCard from './ImageVideoCard';
import { IMediaUserProps } from '../../components/MediaPreviewScreen';

type ReactionData = {
  count: number;
  users: string[];
};

export interface ChatListProps {
  data: IMessage;
  reactions?: Record<string, { count: number; users: string[] }>;
  onCardPress?: () => void;
  selectedMsgs?: any[];
  setSelectedMsgs?: (value: any) => void;
  scrollToItem?: (value?: any) => void;
  isMsgHightlighted?: any;
  unHightlightFunc?: () => void;
  userData: IChatScreenProps;
  type: ConversationType;
  onEditMessage?: (text: string, msgData: any) => void;
}

const ChatListItem = ({
  data,
  reactions = {},
  onCardPress = () => {},
  selectedMsgs = [],
  setSelectedMsgs = () => {},
  scrollToItem = () => {},
  isMsgHightlighted = false,
  unHightlightFunc = () => {},
  userData,
  type,
  onEditMessage = () => {},
}: ChatListProps) => {
  const _deps = [
    data,
    reactions,
    onCardPress,
    selectedMsgs,
    setSelectedMsgs,
    scrollToItem,
    isMsgHightlighted,
    unHightlightFunc,
    userData,
    type,
    onEditMessage,
  ];

  // // Debug all dependencies
  // useWhatChanged(
  //   _deps,
  //   'data, reactions, onCardPress, selectedMsgs, setSelectedMsgs, scrollToItem, isMsgHightlighted, unHightlightFunc, userData, type, onEditMessage',
  //   'ChatListItem-all-props',
  // );
  const { socket } = useSocket();
  const { pureSaveOrUpdate, useContactByPhoneNumber } = useContacts();
  const realm = useRealm();
  const messageRef = useRef<View>(null);
  const { user: me, userPreferencesState } = useMe();
  const navigation = useNavigation();
  const [isPlaying, setIsPlaying] = useState(false);
  const [sentMsgLength, setSentMsgLength] = useState<number>(0);
  const [isSelected, setIsSelected] = useState<boolean>(false);
  const [scheduleItemModal, setScheduleItemModal] = useState(false);
  const [audioDuration, setAudioDuration] = useState<number>(0);
  const [isPinned, setIsPinned] = useState<boolean>(false); // new
  const [showReactionPopup, setShowReactionPopup] = useState(false);
  const [selectedMessageForReaction, setSelectedMessageForReaction] = useState<IMessage | null>(
    null,
  );
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const [isSheetOpen, setSheetOpen] = useState(false);
  const [reactionUsers, setReactionUsers] = useState<Record<string, any>>({});
  const [showFullText, setShowFullText] = useState(false);
  const MAX_LENGTH = 300;

  const isGroup = type === ConversationType.GROUP;

  const senderData = data.sender;

  const msgData = { ...data };
  const isLongText = msgData?.text && msgData?.text.length > MAX_LENGTH;
  const displayedText = showFullText
    ? msgData.text || msgData.translatedText
    : msgData.text?.substring(0, MAX_LENGTH) + '' ||
      msgData.translatedText?.substring(0, MAX_LENGTH) + '...';

  const isTextMessage = msgData?.messageType === MessageType.TEXT;

  // const isPinned = Number(msgData?.pinnedUntil) > Date.now();
  const contactPhone = msgData?.contact?.phoneNumber;
  const normalize = (phone: string) => phone?.replace(/\D/g, '').slice(-10);
  const contact = useContactByPhoneNumber(msgData?.contact?.phoneNumber);
  const { user } = useMe();
  const isRegistered = Boolean(msgData?.contact?.userId && msgData.contact.userId.trim() !== '');
  // const isMyNumber = contact?.userId === user?._id;

  useEffect(() => {
    if (!isSheetOpen) return;

    const loadUsers = async () => {
      const usersMap: Record<string, any> = {};

      for (const [, data] of Object.entries(reactions)) {
        for (const userId of data.users) {
          if (!usersMap[userId]) {
            const user = await ChatService.getOrCreateUser(userId);
            if (user) {
              usersMap[userId] = user;
            }
          }
        }
      }

      setReactionUsers(usersMap);
    };

    loadUsers();
  }, [isSheetOpen]);

  const isMyData = msgData?.senderId === me?._id;
  const isVideo = msgData?.messageType === MessageType.VIDEO;
  const isImage = msgData?.messageType === MessageType.IMAGE;
  const showAddToContact = !isMyData && !contact?.isInDevice;
  const showMessage = isRegistered;
  const isMessageOnly = showMessage && !showAddToContact;
  const disablePinTouch =
    msgData.conversationType === 'channel' &&
    userData?.conversation?.role === ChannelType.MEMBER &&
    !isMyData;

  const handleCopyMessage = () => {
    if (msgData?.text) {
      Clipboard.setString(msgData.text);
      showToast('Message copied to clipboard');
    }
  };

  // useEffect(() => {
  //   const loadUserData = async () => {
  //     let targetId: string | undefined;

  //     if (isGroup) {
  //       targetId = data?.senderId;
  //     } else {
  //       // P2P: Pick the one that is not my ID
  //       if (data?.senderId !== me?._id) {
  //         targetId = data?.senderId;
  //       } else if (data?.receiverId !== me?._id) {
  //         targetId = data?.receiverId;
  //       }
  //     }

  //     if (targetId) {
  //       try {
  //         const user = await ChatService.getOrCreateUser(targetId);
  //       } catch (error) {
  //         console.log(error);
  //       }
  //     }
  //   };

  //   loadUserData();
  // }, [data?.senderId, data?.receiverId, isGroup, me?._id]);

  const isVideoUrl = (url: string) => {
    return /\.(mp4|mov|webm|mkv|avi|flv|3gp)$/i.test(url);
  };

  const replaceVideoUrlsWithThumbnails = async (mediaUrls: string[]): Promise<string[]> => {
    const updatedUrls = await Promise.all(
      mediaUrls.map(async (url) => {
        if (isVideoUrl(url)) {
          try {
            const thumbnail = await createThumbnail({
              url,
              timeStamp: 1000, // 1 second
            });
            return thumbnail.path; // replace video URL with thumbnail image path
          } catch (error) {
            console.warn(`Failed to generate thumbnail for ${url}`, error);
            return url; // fallback to original video URL
          }
        } else {
          return url; // keep image as is
        }
      }),
    );
    console.log('updatedUrls', updatedUrls);
    return updatedUrls;
  };

  const replyedMsgData = ChatService.getMessageByGlobalId(msgData?.replyToMessageId as string);
  // console.log("🚀 ~ ReplyMessageCard ~ replyedMsgData:", JSON.stringify(replyedMsgData, null, 2))

  const originalSenderData = ChatService.getUserData(replyedMsgData?.senderId || '');

  const isYourMsgReply = replyedMsgData?.senderId == me?._id;
  // console.log("🚀 ~ ReplyMessageCard ~ isYourMsgReply:", isYourMsgReply)

  // console.log("🚀 ~ ChatListItem ~ msgData:", msgData)

  const getDownloadDest = (fileName: any) => {
    const version = Number(Platform.Version);
    if (Platform.OS === 'android' && version >= 30) {
      return `${RNFS.ExternalDirectoryPath}/${fileName}`;
    }
    return `${RNFS.DownloadDirectoryPath}/${fileName}`;
  };
  const checkFileExists = async (fileName: string) => {
    const localPath = getDownloadDest(fileName);
    try {
      const exists = await RNFS.exists(localPath);
      return exists; // true if file exists, false otherwise
    } catch (err) {
      console.error('Error checking file existence:', err);
      return false;
    }
  };

  const memoizedIsSelected = useMemo(() => {
    return selectedMsgs.some((m) => m.localId === msgData.localId);
  }, [selectedMsgs, msgData.localId]);

  const file = msgData?.fileName?.split('.');
  const fileName = msgData?.fileName || 'Unknown Document';
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';

  function formatToMinutesSeconds(seconds: any) {
    if (typeof seconds !== 'number' || seconds < 0) return '00:00';

    const totalSeconds = Math.floor(seconds); // Round down to nearest second
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;

    const paddedMins = String(mins).padStart(2, '0');
    const paddedSecs = String(secs).padStart(2, '0');

    return `${paddedMins}:${paddedSecs}`;
  }

  const getAudioDuration = (audioUrl: any) => {
    Sound.setCategory('Playback'); // Required for iOS playback

    const sound = new Sound(audioUrl, undefined, (error) => {
      if (error) {
        console.log('failed to load the sound', error);
        return;
      }
      // console.log('duration in seconds: ' + sound.getDuration() +
      //   'number of channels: ' + sound.getNumberOfChannels());
      setTimeout(() => {
        setAudioDuration(sound.getDuration());
      }, 100);
      sound.release();
    });
  };

  useEffect(() => {
    if (msgData.pinnedUntil !== undefined) {
      const pinned = Number(msgData?.pinnedUntil) > Date.now();
      setIsPinned(pinned);
    }
  }, [msgData?.pinnedUntil]);

  useEffect(() => {
    if (
      (replyedMsgData?.messageType == MessageType.AUDIO ||
        replyedMsgData?.messageType == MessageType.VOICE) &&
      replyedMsgData?.mediaUrl
    ) {
      getAudioDuration(replyedMsgData?.mediaUrl);
    }
    setIsSelected(isMsgHightlighted ? isMsgHightlighted : memoizedIsSelected);
    if (isMsgHightlighted) {
      setTimeout(() => {
        setIsSelected(false);
        unHightlightFunc();
      }, 500);
    }
    return () => {
      setIsSelected(false); // reset on unmount
    };
  }, [memoizedIsSelected, isMsgHightlighted]);
  const messageColor =
    userPreferencesState?.userPreferences?.account?.messageColor?.primary || colors.mainPurple;
  const messageColorLight = getLighterShade(messageColor);

  const handleMessageClick = async (userId: string) => {
    console.log('message navigation clicked', userId);

    const user = await ChatService.buildUserData(userId);
    console.log(user);

    if (!user) return null;
    console.log('user', user);

    const chatScreenParams: ChatSpecificScreenParamsT = {
      userData: {
        displayName: '',
        type: ConversationType.P2P,
        id: '',
        conversation: {} as unknown as IConversation,
      },
      isFollowing: false,
      data: {
        convId: user.id,
        initialConversationInfo: undefined,
      },
    };

    navigateTo(SCREENS.ChatSpecificScreen, chatScreenParams);
  };

  const handleDocumentPress = async (msgData: IMessage) => {
    if (!msgData?.mediaUrl) return;

    try {
      const fileName = msgData.fileName || 'document';
      let localPath = getDownloadDest(fileName);
      const fileExists = await checkFileExists(fileName);
      let canOpen = fileExists;

      if (!fileExists) {
        const downloadResult = await RNFS.downloadFile({
          fromUrl: msgData.mediaUrl,
          toFile: localPath,
        }).promise;
        canOpen = downloadResult.statusCode === 200;
      }

      if (canOpen) {
        await FileViewer.open(localPath, {
          showOpenWithDialog: true,
          displayName: fileName,
        });
      } else {
        Alert.alert('Download failed', 'Could not download the file.');
      }
    } catch (error) {
      if (/no app associated|no activity found/i.test(error?.message)) {
        Alert.alert(
          'Cannot open file',
          'No app was found to open this file type. Please install an appropriate viewer and try again.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Search App',
              onPress: () => {
                const fileExt = fileName.split('.').pop();
                Linking.openURL(`https://play.google.com/store/search?q=${fileExt}+viewer&c=apps`);
              },
            },
          ],
        );
      } else {
        Alert.alert(
          'Error opening file',
          error?.message || 'An unexpected error occurred while opening the file.',
        );
      }
    }
  };
  const handleContactPress = (msgData: IMessage) => {
    navigateTo('ViewContactScreen', {
      contact: msgData?.contact,
      onMessagePress: async () => {
        const contact = msgData?.contact;
        const conversationId = contact?.userId ?? '';
        if (!conversationId) {
          // Handle missing conversation ID
          return;
        }
        await handleMessageClick(conversationId);
      },
    });
  };

  const openNativeContactForm = async (name: string, phoneNumber: string) => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_CONTACTS,
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Permission Denied', 'Cannot add contact without permission.');
          return;
        }
      }

      const contact = {
        givenName: name,
        phoneNumbers: [{ label: 'mobile', number: phoneNumber }],
      };

      Contacts.openContactForm(contact)
        .then((result) => {
          if (result) {
            realm.write(() => {
              pureSaveOrUpdate({
                phoneNumber,
                name,
                isInDevice: true,
              });
            });
            showToast('Contact saved successfully!');
          }
        })
        .catch((err) => {
          console.log('Error opening native contact form:', err);
        });
    } catch (error) {
      console.error('Error launching contact form:', error);
    }
  };

  const handleAddReactionToMessage = (msgData: IMessage, emoji: string) => {
    // console.log('----msgdata-----', msgData);
    // console.log('----emoji-----', emoji);

    const messageId = msgData?.globalId;
    const userId = me?._id;

    ChatSocket.emitAddReaction(socket, messageId as string, emoji, userId as string, (response) => {
      // console.log(
      //   ' emit event in chatlist item response----------------',
      //   JSON.stringify(response, null, 2),
      // );
      if (response?.error) {
        console.error('Socket error while adding reaction:', response.error);
      }
      // setSelectedMsgs([]);
    });
  };

  const handleRemoveReaction = (msgData: IMessage, emoji: string) => {
    const messageId = msgData.globalId;
    const userId = me?._id;

    ChatSocket.emitRemoveReaction(
      socket,
      messageId as string,
      emoji,
      userId as string,
      (response) => {
        if (response?.error) {
          console.error('Failed to remove reaction', response.error);
        }
      },
    );
  };
  const emojis = Object.keys(reactions);

  const renderReactions = () => {
    if (!emojis.length) return null;
    const sorted = [...emojis].sort((a, b) => reactions[b].count - reactions[a].count);
    const top3 = sorted.slice(0, 3);
    const total = sorted.reduce((sum, e) => sum + reactions[e].count, 0);
    return (
      <View style={{ marginTop: -10 }}>
        <Pressable
          style={{
            [isMyData ? 'right' : 'left']: 0,
            flexDirection: 'row',
            backgroundColor: colors.white,
            borderRadius: 12,
            paddingHorizontal: 6,
            paddingVertical: 2,
            elevation: 1,
            alignSelf: isMyData ? 'flex-end' : 'flex-start',
          }}
          onPress={() => setSheetOpen(true)}
        >
          {top3.map((e) => (
            <Text key={e} style={{ fontSize: 14, marginHorizontal: 2 }}>
              {e}
            </Text>
          ))}
          <Text style={{ fontSize: 12, marginLeft: 4, fontWeight: '600', color: colors.black }}>
            {total}
          </Text>
        </Pressable>
      </View>
    );
  };

  const renderReactionDetails = (msgData: IMessage) => {
    const isChannel = msgData.conversationType === 'channel';
    const emojis = Object.keys(reactions);

    if (!emojis.length) {
      return (
        <View style={{ paddingVertical: 20, alignItems: 'center' }}>
          <Text style={{ color: colors.gray_80 }}>No reactions yet</Text>
        </View>
      );
    }
    const totalReactions = emojis.reduce((sum, emoji) => sum + (reactions[emoji]?.count || 0), 0);

    return (
      <View>
        <View style={{ alignItems: 'flex-start', marginBottom: 12 }}>
          <Text style={{ fontWeight: '700', fontSize: 16, color: colors.black }}>
            {totalReactions} Reaction{totalReactions > 1 ? 's' : ''}
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'center',
            marginBottom: 20,
          }}
        >
          {Object.entries(reactions).map(([emoji, data]) => (
            <View
              key={emoji}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: colors._EFEBFC_purple,
                borderRadius: 14,
                paddingHorizontal: 8,
                paddingVertical: 4,
                margin: 4,
              }}
            >
              <Text style={{ fontSize: 18 }}>{emoji}</Text>
              <Text style={{ marginLeft: 4, color: colors.black, fontWeight: '600' }}>
                {data.count}
              </Text>
            </View>
          ))}
        </View>

        {Object.entries(reactions).map(([emoji, data]) => {
          // If it's a channel, filter only current user's ID
          const filteredUsers = isChannel
            ? data.users.filter((userId) => userId === me?._id)
            : data.users;

          if (!filteredUsers.length) return null;

          return (
            <View key={emoji} style={{ marginBottom: 16 }}>
              {filteredUsers.map((userId) => {
                const user = reactionUsers[userId];
                const isMe = userId === me?._id;
                if (!user) return null;

                return (
                  <Pressable
                    key={userId}
                    onPress={() => {
                      if (isMe) {
                        handleRemoveReaction(msgData, emoji);
                        setSheetOpen(false);
                      }
                    }}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      paddingVertical: 6,
                      borderRadius: 6,
                      padding: 6,
                    }}
                  >
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      <Image
                        source={{ uri: user.displayPic || '' }}
                        style={{
                          width: 45,
                          height: 45,
                          borderRadius: 26,
                          marginRight: 10,
                        }}
                      />
                      <View>
                        <Text
                          style={{
                            fontWeight: isMe ? '700' : '400',
                            color: colors.black,
                          }}
                        >
                          {isMe ? 'You' : user.displayName || user.name}
                        </Text>
                        {isMe && (
                          <Text style={{ color: colors.gray_86, fontSize: 10 }}>Tap to remove</Text>
                        )}
                      </View>
                    </View>
                    <Text style={{ marginLeft: 8, fontSize: 18 }}>{emoji}</Text>
                  </Pressable>
                );
              })}
            </View>
          );
        })}
      </View>
    );
  };

  const goToImagePreview = () => {
    const otherUser: IMediaUserProps = {
      id: userData?.id,
      displayName: userData?.displayName,
      type: userData?.type,
      displayPic: userData?.displayPic as string,
      ...(userData?.type == ConversationType.CHANNEL && {
        memberShipStatus: userData?.conversation?.chatSpace?.membershipStatus,
      }),
      sender: msgData?.sender,
    };
    navigateTo('MediaPreviewScreen', {
      msgData,
      otherUserData: otherUser,
    });
  };

  return (
    <>
      {msgData?.messageType === MessageType.EVENT &&
        msgData.eventType !== MessageEventType.GROUP_CALL && (
          <EventMessage msgData={msgData} currentUserId={me?._id} />
        )}

      <TouchableOpacity
        key={msgData?.localId}
        // activeOpacity={msgData?.replyToMessageId ? 0.7 : 1}
        onPress={() => {
          if (msgData.messageType === MessageType.EVENT) {
            return;
          }

          if (msgData?.replyToMessageId && selectedMsgs.length == 0) {
            scrollToItem(replyedMsgData?.localId);
            // setIsSelected(replyedMsgData?.localId === data?.localId ? true : false);
            // setTimeout(() => {
            //   setIsSelected(false)
            // }, 3000);
            return;
          }
          if (!isSelected && selectedMsgs?.length == 0) {
            if (isImage || isVideo) {
              goToImagePreview();
            } else if (msgData?.messageType === MessageType.CONTACT) {
              handleContactPress(msgData);
            } else if (msgData?.messageType === MessageType.DOCUMENT) {
              handleDocumentPress(msgData);
            }
            onCardPress();
          }
          if (selectedMsgs.length > 0) {
            setSelectedMsgs(data);
          }
        }}
        disabled={msgData?.status == MessageStatus.UPLOADING}
        ref={messageRef}
        onLongPress={(event) => {
          Vibration.vibrate(150);

          const { pageX, pageY } = event.nativeEvent;
          setSelectedMessageForReaction(msgData);
          setPopupPosition({ x: pageX, y: pageY - 50 });
          setShowReactionPopup(true);

          if (selectedMsgs.length === 0) {
            setSelectedMsgs(data);
          }
        }}
        style={[
          styles.selectedStyle,
          {
            flexDirection: isMyData ? 'column' : emojis.length ? 'column' : 'row',
            backgroundColor: isSelected ? hexToRgba(colors.mainPurple, 0.3) : 'transparent',
            paddingHorizontal: 20,
          },
        ]}
      >
        {isGroup && !isMyData && msgData.messageType !== MessageType.EVENT && (
          <Image
            source={senderData ? { uri: senderData.profilePic } : IMAGES.profile_image}
            style={styles.profilePic}
          />
        )}

        {/* Render text */}
        {msgData?.messageType == MessageType.TEXT && msgData?.status !== MessageStatus.SCHEDULED ? (
          <>
            <View
              style={[
                msgData?.replyToMessageId ? styles.replyContainer : styles.container,
                {
                  // flex: 1,
                  alignSelf: isMyData ? 'flex-end' : 'flex-start',
                  backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                  borderTopLeftRadius: isMyData ? 15 : 1,
                  borderBottomRightRadius: isMyData ? 1 : 15,
                },
              ]}
            >
              {isGroup && !isMyData && (
                <Text style={{ color: senderData?.textColor, fontSize: 14, fontWeight: 500 }}>
                  {senderData?.name}
                </Text>
              )}

              {/* Pin message */}
              {isPinned && (
                <View
                  style={{
                    position: 'absolute',
                    top: '50%',
                    [isMyData ? 'left' : 'right']: -35,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      ChatService.unpinMessage(msgData.localId, msgData.receiverId);
                    }}
                    disabled={disablePinTouch}
                    style={styles.pinIconButton}
                  >
                    <UnpinSVG size={12} color={colors.mainPurple} />
                  </TouchableOpacity>
                </View>
              )}

              {/* TEST REPLY MESSAGE IN CHATITEMLIST */}
              {msgData?.replyToMessageId ? (
                <View
                  style={{
                    paddingLeft: 11,
                    backgroundColor: isMyData ? messageColorLight : colors.gray_f3,
                    borderRadius: 8,
                    borderLeftColor: isMyData ? messageColor : colors.black_23,
                    borderLeftWidth: 4,
                    maxWidth: hp(35),
                    minWidth: hp(28),
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <View
                    style={{
                      paddingVertical: replyedMsgData?.messageType === MessageType.IMAGE ? 0 : 8,
                    }}
                  >
                    <Text
                      style={[
                        styles.fileText,
                        {
                          color: colors.mainPurple,
                          fontSize: 14,
                          fontWeight: '800',
                          marginBottom: 3,
                        },
                      ]}
                    >
                      {isYourMsgReply ? 'You' : originalSenderData?.name}
                    </Text>
                    {replyedMsgData?.messageType == MessageType.IMAGE ||
                    replyedMsgData?.messageType === MessageType.VIDEO ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                        }}
                      >
                        {replyedMsgData?.messageType === MessageType.VIDEO ? (
                          <VideoCallSVG color={colors.black_23} size={14} />
                        ) : (
                          <GalleryPlaneSVG size={10} color={colors.black_23} />
                        )}
                        <Text numberOfLines={1} style={styles.lastMessage}>
                          {replyedMsgData?.messageType === MessageType.VIDEO ? 'Video' : 'Photo'}
                        </Text>
                      </View>
                    ) : replyedMsgData?.messageType == MessageType.AUDIO ||
                      replyedMsgData?.messageType == MessageType.VOICE ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                        }}
                      >
                        {replyedMsgData?.messageType == MessageType.AUDIO ? (
                          <MusicSVG size={14} pathColor={colors.black_23} withOutBg={true} />
                        ) : (
                          <MicSVG2 size={14} color={colors.black_23} />
                        )}

                        <Text
                          style={[styles.fileText, { color: colors.black_23, fontSize: 12 }]}
                          numberOfLines={2}
                        >{`${
                          replyedMsgData?.messageType == MessageType.AUDIO
                            ? replyedMsgData?.fileName
                            : 'Voice message'
                        } (${formatToMinutesSeconds(audioDuration)})`}</Text>
                      </View>
                    ) : replyedMsgData?.messageType === MessageType.DOCUMENT ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                          flexShrink: 1,
                          maxWidth: hp(25),
                        }}
                      >
                        <ReplyDocSVG size={16} color={colors.black_23} />
                        <Text
                          numberOfLines={1}
                          style={[styles.fileText, { color: colors.black, fontSize: 12 }]}
                        >
                          {replyedMsgData?.fileName ?? 'Document'}{' '}
                          {replyedMsgData?.fileSize
                            ? `(${formatBytes(replyedMsgData?.fileSize)})`
                            : ''}
                        </Text>
                      </View>
                    ) : replyedMsgData?.messageType === MessageType.CONTACT ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                          flexShrink: 1,
                          maxWidth: hp(20),
                        }}
                      >
                        <ReplyAvatarSVG size={12} color={'#232323'} />
                        <Text
                          numberOfLines={1}
                          style={[styles.fileText, { color: colors.black, fontSize: 12 }]}
                        >
                          Contact: {replyedMsgData?.contact?.name ?? 'Contact'}
                        </Text>
                      </View>
                    ) : replyedMsgData?.messageType === MessageType.LOCATION ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                          flexShrink: 1,
                          maxWidth: hp(20),
                        }}
                      >
                        {replyedMsgData?.location?.type === 'live' ? (
                          <LiveLocationSVG
                            size={18}
                            color={colors.black_23}
                            style={{ marginRight: 5 }}
                          />
                        ) : (
                          <LocationSVG size={16} color={'#232323'} />
                        )}
                        <Text
                          numberOfLines={1}
                          style={[styles.fileText, { color: colors.black, fontSize: 12 }]}
                        >
                          {'Location'}
                        </Text>
                      </View>
                    ) : (
                      <Text
                        style={[styles.fileText, { color: colors.black, fontSize: 14 }]}
                        numberOfLines={2}
                      >
                        {replyedMsgData?.text}
                      </Text>
                    )}
                  </View>
                  {replyedMsgData?.messageType == MessageType.IMAGE ||
                  replyedMsgData?.messageType == MessageType.VIDEO ? (
                    <Image
                      source={{
                        uri:
                          replyedMsgData?.messageType == MessageType.VIDEO
                            ? replyedMsgData?.videoThumbnail
                            : replyedMsgData?.mediaUrl,
                      }}
                      style={{
                        width: 49,
                        height: 49,
                        borderTopRightRadius: 8,
                        borderBottomRightRadius: 8,
                      }}
                      resizeMode="cover"
                    />
                  ) : null}
                </View>
              ) : null}

              <View style={{}}>
                <View style={{}}>
                  <Text
                    // onTextLayout={handleTextLayout}
                    style={[styles.text, { marginBottom: 6, marginTop: 4 }]}
                  >
                    {displayedText}
                    {isLongText && (
                      <Text
                        style={{ color: colors.mainPurple, fontWeight: '600' }}
                        onPress={() => setShowFullText(!showFullText)}
                      >
                        {showFullText ? ' See Less' : ' See More'}
                      </Text>
                    )}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    alignSelf: isMyData ? 'flex-end' : 'flex-start',
                  }}
                >
                  <Text
                    style={{
                      color: colors._7A6A90_purple,
                      fontSize: 12,
                      marginRight: 4,
                    }}
                  >
                    {msgData?.isTextEdited ? 'Edited  ' : null}
                    {dayPipe(msgData?.createdAt, 'time')}
                  </Text>
                  <View>{isMyData && <MessageStatusIcon status={msgData.status} />}</View>
                </View>
              </View>
            </View>
          </>
        ) : null}

        {/* Render scheduled text */}
        {msgData?.messageType === MessageType.TEXT &&
        msgData?.scheduledAt &&
        msgData?.status === MessageStatus.SCHEDULED ? (
          <View>
            {/* Pin message */}
            {isPinned && (
              <View
                style={{
                  position: 'absolute',
                  top: '50%',
                  [isMyData ? 'left' : 'right']: -35,
                }}
              >
                <TouchableOpacity
                  onPress={() => {
                    ChatService.unpinMessage(msgData.localId, msgData.receiverId);
                  }}
                  style={styles.pinIconButton}
                >
                  <UnpinSVG size={12} color={colors.mainPurple} />
                </TouchableOpacity>
              </View>
            )}
            <View
              style={[
                styles.container,
                {
                  alignSelf: isMyData ? 'flex-end' : 'flex-start',
                  backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                  borderTopLeftRadius: isMyData ? 15 : 1,
                  borderBottomRightRadius: isMyData ? 1 : 15,
                },
              ]}
            >
              <View style={{}}>
                <View
                  style={{
                    flexDirection: sentMsgLength > 1 ? 'column' : 'row',
                    alignItems: 'center',
                    justifyContent: sentMsgLength > 1 ? 'flex-start' : 'space-between',
                  }}
                >
                  <Text
                    // onTextLayout={handleTextLayout}
                    style={[
                      styles.text,
                      { marginBottom: sentMsgLength > 1 ? 8 : 0, marginRight: 8 },
                    ]}
                    onTextLayout={(e) => {
                      console.log('----=======----', e.nativeEvent.lines);
                      setSentMsgLength(e.nativeEvent.lines.length);
                    }}
                  >
                    {msgData?.text}
                  </Text>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'flex-end',
                      marginBottom: sentMsgLength > 1 ? 8 : 0,
                    }}
                  >
                    <Text
                      style={{
                        color: colors._7A6A90_purple,
                        fontSize: 12,
                        marginRight: 4,
                      }}
                    >
                      {dayPipe(msgData?.createdAt, 'time')}
                    </Text>
                    {isMyData && msgData.status !== MessageStatus.SCHEDULED && (
                      <MessageStatusIcon status={msgData.status} />
                    )}
                  </View>
                </View>

                {/* Hide this after the message has been sent */}
                {msgData.status === MessageStatus.SCHEDULED && (
                  <View
                    style={{
                      flexDirection: 'row',
                      marginTop: sentMsgLength < 2 ? 8 : 0,
                      alignItems: 'center',
                      backgroundColor: colors._DFD9F3_purple,
                      borderRadius: 10,
                      paddingHorizontal: 9,
                      paddingVertical: 7,
                      gap: 10,
                    }}
                  >
                    <MessageStatusIcon status={msgData.status} />
                    <Text
                      style={{
                        color: colors.black_23,
                        fontSize: 12,
                        fontWeight: '400',
                      }}
                    >
                      {`Scheduled for ${dayPipe(msgData?.scheduledAt, 'time')} on ${dayPipe(
                        msgData?.scheduledAt,
                        'monthDay',
                      )}`}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            {renderReactions()}
          </View>
        ) : null}

        {/* Replyed messages */}
        {/* {msgData?.replyToMessageId ?
          <ReplyMessageCard scrollToItem={scrollToItem} isMyData={isMyData} msgData={msgData} otheUserName={otheUserName} myUserId={me?._id} />
          : null} */}

        {/* Render images */}
        {msgData?.messageType === MessageType.IMAGE || isVideo ? (
          <ImageVideoCard
            msgData={msgData}
            isMyData={isMyData}
            isPinned={isPinned}
            onClickImage={goToImagePreview}
          />
        ) : null}

        {msgData?.messageType === MessageType.LOCATION && msgData?.location?.type !== 'live' ? (
          <>
            <LocationCard
              isMyData={isMyData}
              msgData={msgData}
              onPress={() => {
                console.log('location card pressed');
              }}
            />
            {/* <View>{renderReactions()}</View> */}
          </>
        ) : null}

        {msgData?.messageType === MessageType.LOCATION && msgData?.location?.type == 'live' ? (
          <>
            <LiveLocationCard
              isMyData={isMyData}
              msgData={msgData}
              userData={userData}
              onPress={() => {
                console.log('location card pressed');
              }}
            />
            {/* <View>{renderReactions()}</View> */}
          </>
        ) : null}

        {/* image with content */}
        {/* {msgData?.messageType === MessageType.IMAGE && msgData?.text !== '' && !isMyData ? (
          <>
            <View
              style={[
                {
                  maxWidth: hp(36),
                  paddingVertical: 12,
                  paddingLeft: 16,
                  paddingRight: 13,
                  borderRadius: 15,
                  marginBottom: 22,
                  alignSelf: isMyData ? 'flex-end' : 'flex-start',
                  backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                  borderTopLeftRadius: isMyData ? 15 : 1,
                  borderBottomRightRadius: isMyData ? 1 : 15,
                  flex: 1,
                },
              ]}
            >
              <View style={{}}>
                <View style={{}}>
                  <Text style={[styles.text, { marginBottom: 8 }]}>{msgData?.content}</Text>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {images?.slice(0, 3)?.map((item: any, i: number) => {
                      const isLastVisible = i === 2 && images?.length > 3;

                      return (
                        <View
                          key={i}
                          style={{
                            position: 'relative',
                            marginRight: i !== 2 ? 8 : 0,
                          }}
                        >
                          <Image
                            source={item}
                            style={{
                              width: 75,
                              height: 75,
                              resizeMode: 'cover',
                              borderRadius: 8,
                            }}
                          />
                          {isLastVisible && (
                            <View
                              style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                borderRadius: 8,
                                backgroundColor: 'rgba(0, 0, 0, 0.4)',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}
                            >
                              <Text
                                style={{
                                  color: 'white',
                                  fontWeight: 'bold',
                                  fontSize: 16,
                                }}
                              >
                                +{images?.length - 3}
                              </Text>
                            </View>
                          )}
                        </View>
                      );
                    })}
                  </View>
                </View>

                <Text style={[styles.smallText, { marginTop: 8 }]}>
                  {dayPipe(msgData?.createdAt, 'time')}
                </Text>
              </View>
            </View>
          </>
        ) : null} */}

        {/* Render music and voice message */}
        {msgData?.messageType === MessageType.AUDIO ||
        msgData?.messageType === MessageType.VOICE ? (
          <View>
            {/* Pin message */}
            {isPinned && (
              <View
                style={{
                  position: 'absolute',
                  top: '35%',
                  [isMyData ? 'left' : 'right']: 35,
                }}
              >
                <TouchableOpacity
                  onPress={() => {
                    ChatService.unpinMessage(msgData.localId, msgData.receiverId);
                  }}
                  style={styles.pinIconButton}
                >
                  <UnpinSVG size={12} color={colors.mainPurple} />
                </TouchableOpacity>
              </View>
            )}
            <AudioWavesComponent
              isMyData={isMyData}
              msgData={msgData}
              audioDuration={audioDuration}
            />
            {/* {renderReactions()} */}
          </View>
        ) : null}

        {/* Render files */}
        {msgData?.messageType === MessageType.DOCUMENT ? (
          <>
            <View>
              {/* Pin message */}
              {isPinned && (
                <View
                  style={{
                    position: 'absolute',
                    top: '50%',
                    [isMyData ? 'left' : 'right']: 55,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      ChatService.unpinMessage(msgData.localId, msgData.receiverId);
                    }}
                    style={styles.pinIconButton}
                  >
                    <UnpinSVG size={12} color={colors.mainPurple} />
                  </TouchableOpacity>
                </View>
              )}
              <View
                style={[
                  styles.fileContainer,
                  {
                    alignSelf: isMyData ? 'flex-end' : 'flex-start',
                    backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                    borderTopLeftRadius: isMyData ? 16 : 1,
                    borderBottomRightRadius: isMyData ? 1 : 16,
                    minWidth: hp(28),
                    flex: 1,
                  },
                ]}
              >
                <View
                  style={{
                    padding: 10,
                    backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 8,
                  }}
                >
                  <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
                    {msgData?.status === MessageStatus.UPLOADING ? (
                      <LottieView
                        source={require('../../../../assets/animatedLottiImages/loader.json')}
                        autoPlay
                        loop
                        style={{ width: 32, height: 32 }}
                      />
                    ) : fileExtension == 'jpg' || fileExtension == 'pdf' ? (
                      <FileSVG size={20} color={isMyData ? colors.mainPurple : colors.black} />
                    ) : fileExtension == 'mp3' ? (
                      <MusicFileSVG
                        size={22}
                        color={isMyData ? colors.mainPurple : colors.black_23}
                      />
                    ) : fileExtension == 'xls' ? (
                      <XlsSVG
                        width={22}
                        height={23}
                        color={isMyData ? colors.mainPurple : colors.black}
                      />
                    ) : fileExtension == 'wav' ||
                      fileExtension == 'm4a' ||
                      fileExtension == 'aac' ||
                      fileExtension == 'mp4' ||
                      fileExtension == 'mov' ||
                      fileExtension == 'avi' ||
                      fileExtension == 'mkv' ||
                      fileExtension == 'webm' ? (
                      <VideoDocSVG
                        width={22}
                        height={23}
                        color={isMyData ? colors.mainPurple : colors.black}
                      />
                    ) : (
                      <DocumentSVG
                        width={19}
                        height={25}
                        color={isMyData ? colors.mainPurple : colors.black}
                      />
                    )}

                    <Text
                      style={[
                        styles.fileText,
                        {
                          color: isMyData ? colors.mainPurple : colors.black,
                          fontSize: 14,
                          marginLeft: 10,
                          flexShrink: 1,
                          flexWrap: 'wrap',
                        },
                      ]}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {fileName}
                    </Text>
                  </View>

                  {/* <DownloadSVG
                    size={28}
                    color={isMyData ? colors.white : colors.black}
                    backgroundColor={isMyData ? colors.mainPurple : colors.white}
                  /> */}
                </View>

                {msgData?.text ? (
                  <View style={{ paddingHorizontal: 10, paddingTop: 6 }}>
                    <Text
                      style={{
                        fontSize: 13,
                        color: isMyData ? colors.mainPurple : colors.black,
                        flexWrap: 'wrap',
                        flexShrink: 1,
                      }}
                    >
                      {msgData.text}
                    </Text>
                  </View>
                ) : null}

                {/* Right side: time + tick */}
                <View
                  style={{
                    flexDirection: isMyData ? 'row' : 'row-reverse',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginTop: 6,
                  }}
                >
                  {msgData.fileSize && (
                    <Text
                      style={{
                        color: isMyData ? colors._7A6A90_purple : colors.black,
                        fontSize: 12,
                        marginRight: 4,
                      }}
                    >
                      {formatBytes(msgData?.fileSize)}
                    </Text>
                  )}
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                      style={{
                        color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                        fontSize: 12,
                        marginRight: 4,
                      }}
                    >
                      {dayPipe(msgData?.createdAt, 'time')}
                    </Text>
                    {isMyData && <MessageStatusIcon status={msgData.status} />}
                  </View>
                </View>
              </View>
            </View>
            {/* <View>{renderReactions()}</View> */}
          </>
        ) : null}

        {/* Render contact */}
        {msgData?.messageType === MessageType.CONTACT ? (
          <>
            <View>
              {/* Pin message */}
              {isPinned && (
                <View
                  style={{
                    position: 'absolute',
                    top: '50%',
                    [isMyData ? 'left' : 'right']: 45,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      ChatService.unpinMessage(msgData.localId, msgData.receiverId);
                    }}
                    style={styles.pinIconButton}
                  >
                    <UnpinSVG size={12} color={colors.mainPurple} />
                  </TouchableOpacity>
                </View>
              )}
              <View
                style={{
                  flex: 1,
                  alignSelf: isMyData ? 'flex-end' : 'flex-start',
                  padding: 10,
                  borderRadius: 16,
                  backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
                  borderTopLeftRadius: isMyData ? 16 : 1,
                  borderBottomRightRadius: isMyData ? 1 : 16,
                  width: 230,
                }}
              >
                <View
                  style={{
                    padding: 4,
                    backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
                    borderRadius: 10,
                    marginBottom: 12,
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      flexWrap: 'wrap',
                      marginTop: 6,
                      justifyContent: 'center',
                      marginLeft: 4,
                    }}
                  >
                    <ContactAvatarSVG
                      size={35}
                      color={isMyData ? colors.mainPurple : colors.black}
                    />
                    <Text
                      style={{
                        color: isMyData ? colors.mainPurple : colors.black,
                        marginLeft: 4,
                        fontWeight: '600',
                        fontSize: 14,
                        flexShrink: 1,
                        flex: 1,
                        lineHeight: 20,
                      }}
                    >
                      {msgData?.contact?.name}
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      gap: 4,
                    }}
                  >
                    <Text
                      style={{
                        color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                        fontSize: 11,
                        marginRight: isMyData ? 0 : 4,
                      }}
                    >
                      {dayPipe(msgData?.createdAt, 'time')}
                    </Text>
                    {isMyData && <MessageStatusIcon status={msgData.status} />}
                  </View>
                </View>
                <View
                  style={[
                    styles.contactView,
                    isMessageOnly && {
                      justifyContent: 'center',
                      alignItems: 'center',
                      flexDirection: 'row',
                    },
                  ]}
                >
                  {showAddToContact && (
                    <TouchableOpacity
                      onPress={(e: any) => {
                        e.stopPropagation();
                        if (msgData?.contact?.name && contactPhone) {
                          openNativeContactForm(msgData.contact.name, contactPhone);
                        }
                      }}
                    >
                      <Text
                        style={{
                          fontWeight: '500',
                          color: isMyData ? colors.mainPurple : colors.black,
                          fontSize: 12,
                          padding: 4,
                        }}
                      >
                        Add to Contact
                      </Text>
                    </TouchableOpacity>
                  )}

                  {showMessage && (
                    <TouchableOpacity
                      onPress={async () => {
                        const contact = msgData?.contact;
                        const conversationId = contact?.userId ?? '';
                        if (!conversationId) return;
                        await handleMessageClick(conversationId);
                      }}
                      style={{ marginLeft: showAddToContact ? 12 : 0 }}
                    >
                      <Text
                        style={{
                          fontWeight: '500',
                          color: isMyData ? colors.mainPurple : colors.black,
                          fontSize: 13,
                        }}
                      >
                        Message
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
            {/* <View>{renderReactions()}</View> */}
          </>
        ) : null}

        {selectedMessageForReaction && (
          <AnimatedReactionPopup
            visible={showReactionPopup}
            anchorRef={messageRef}
            onEmojiSelect={(emoji) => {
              // console.log('------======--------', emoji);
              setShowReactionPopup(false);
              handleAddReactionToMessage(selectedMessageForReaction, emoji);
            }}
            onClose={() => setShowReactionPopup(false)}
            onCopy={isTextMessage ? handleCopyMessage : undefined}
            onEdit={
              isMyData && isTextMessage
                ? () => onEditMessage(msgData.text as string, msgData)
                : undefined
            }
          />
        )}

        {msgData?.messageType === MessageType.EVENT &&
          msgData.eventType === MessageEventType.GROUP_CALL &&
          null}
        {/* Reaction under every message  */}
        <View>{renderReactions()}</View>
        {/* schedule item sheet */}
        <ModalWrapper
          isVisible={scheduleItemModal}
          onCloseModal={() => setScheduleItemModal(false)}
        >
          <View style={{ paddingHorizontal: hp(2) }}>
            <View>
              <Text
                style={{
                  fontSize: 16,
                  color: colors.black_23,
                  fontWeight: '600',
                  marginBottom: 23,
                }}
              >
                Scheduled messages options
              </Text>

              <TouchableOpacity
                onPress={() => {}}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                  marginBottom: 20,
                }}
              >
                <DeleteSVG color={colors._EC0B0B_red} size={18} />
                <Text style={[styles.bottomText, { color: colors._EC0B0B_red }]}>Delete</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {}}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                  marginBottom: 20,
                }}
              >
                <EditSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setScheduleItemModal(false);
                  // setTimeout(() => {
                  // setScheduleTimerSheet(true)
                  // }, 250);
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                  marginBottom: 20,
                }}
              >
                <TimerSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Reschedule</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {}}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                  marginBottom: 20,
                }}
              >
                <SendArrowSVG color={colors.black_23} size={19} />
                <Text style={styles.bottomText}>Send now</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ModalWrapper>

        <ModalWrapper isVisible={isSheetOpen} onCloseModal={() => setSheetOpen(false)}>
          {renderReactionDetails(msgData)}
        </ModalWrapper>
      </TouchableOpacity>
    </>
  );
};

export default ChatListItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    paddingVertical: 10,
    paddingLeft: 16,
    paddingRight: 13,
    borderRadius: 15,
    // marginBottom: 22,
  },
  replyContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 15,
    // marginBottom: 22,
  },
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(38),
    padding: 8,
    borderRadius: 16,
  },
  imageContainer: {
    alignSelf: 'flex-start',
    maxWidth: hp(40),
  },
  text: {
    marginBottom: 8,
    ...commonFontStyle(700, 14, colors.black),
    alignSelf: 'flex-start',
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  smallText: {
    ...commonFontStyle(700, 12, colors._757575_gray),
    // fontWeight: 'SF-Pro-Text-Medium',
  },
  image: {
    width: '100%',
    height: SCREEN_WIDTH * 0.4,
    justifyContent: 'flex-end',
  },
  contactView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  selectedStyle: {
    paddingVertical: 6,
    backgroundColor: 'transperant',
  },
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  lastMessage: {
    color: colors.black_23,
    fontSize: 12,
    fontWeight: '400',
    // flexShrink: 1,
  },
  pinIconButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePic: {
    width: 25,
    height: 25,
    borderRadius: 15,
    marginRight: 8,
  },
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    maxWidth: 250,
    marginTop: 4,
    marginBottom: 8,
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    marginBottom: 4,
    minWidth: 35,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  emojiText: {
    fontSize: 16,
    marginRight: 4,
  },
  countText: {
    fontSize: 12,
    fontWeight: '500',
    minWidth: 12,
    textAlign: 'center',
  },
});
