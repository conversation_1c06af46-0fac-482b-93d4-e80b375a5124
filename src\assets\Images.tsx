export const IMAGES = {
  introImage: require("./Icons/introImage.png"), // IMAGES.introImage. (no need to change)
  earphone: require("./Icons/earphone.png"), // IMAGES.earphone  -- EarPhoneSVG.tsx (In CallButtons.tsx)
  wrong: require("./Icons/wrong.png"), // IMAGES.wrong.  --(xmark icon in SetUsernameScreen.tsx)
  right: require("./Icons/right.png"), // IMAGES.right  --- (check icon in SetUsernameScreen.tsx & ProfileTab.tsx)
  updateImage: require("./Icons/updateImage.png"), // IMAGES.updateImage.( EditImageSVG.tsx)
  calender: require("./Icons/calender.png"), // IMAGES.calenders (used in SetProfileScreen.tsx)
  tabBG: require("./Icons/tabBG.png"), // IMAGES.tabBG. (no need to change)
  profile_image: require("./Icons/profile_image.png"), // IMAGES.profile_image ---no need to change

  view: require("./Icons/view.png"), // IMAGES.view. ion icon (used in Input.tsx)
  hide: require("./Icons/hide.png"), // IMAGES.hide ion icon (used in Input.tsx)
  editSend: require("./Icons/editSend.png"), // IMAGES.editSend ---(only in storyeditmodal.tsx & ProfileScreen.tsx)
  time: require("./Icons/time.png"), // IMAGES.time. ---(in storypostmodal.tsx & storyeditmodal.tsx)
  lock_profile: require("./Icons/lock_profile.png"), // IMAGES.lock_profile ---(only in storypostmodal.tsx)---done (remove)
  f_image: require("./Icons/f_image.png"), // IMAGES.f_image. (used in scan contact as bgimage)
  gallery_gray: require("./Icons/GalleryGray.png"), // IMAGES.gallery_gray ----(setprofilescreen.tsx)
  live_stream: require("./Icons/live_stream.png"), // IMAGES.live_stream (In EaringView.tsx &liveStreamModal.tsx)
  dollar_icon: require("./Icons/dollar_icon.png"), // IMAGES.dollar_icon --(in LiveScheduleScreen.tsx)
  ic_unSave: require("./Icons/ic_unSave.png"), // IMAGES.ic_unSave. (only used in savepostscreen)
  report: require("./Icons/report.png"), // IMAGES.report ---(OptionModal.tsx)
  videonew: require("./Icons/videonew.png"), // IMAGES.videonew ---(ongoingcall.tsx)
  mute: require("./Icons/mute.png"), // IMAGES.mute ---(In CallMembertile.tsx)
  mute_fill: require("./Icons/mute_fill.png"), // IMAGES.mute_fill  ---( In CallButtons)
  chatList: require("./Icons/chatList.png"), // IMAGES.chatList ( In CallButtons)
  changeCamera: require("./Icons/changeCamera.png"), // IMAGES.changeCamera( In CallButtons)
  audio_selected: require("./Icons/audio_selected.png"), // IMAGES.audio_selected( In CallButtons)
  addUser: require("./Icons/addUser.png"), // IMAGES.addUser (ongoingcall.tsx)
  addCall: require("./Icons/addCall.png"), // IMAGES.addCall ( In CallButtons)
  addVideo: require("./Icons/addVideo.png"), // IMAGES.addVideo ( In CallButtons)
  callBg: require("./Icons/callBg.png"), // IMAGES.callBg ---done (pending in GroupCallScreen.tsx)
  warning: require("./Icons/warning.png"), // IMAGES.warning (in SetUsernameScreen.tsx)
  callVerify: require("./Icons/call_verify.png"), // IMAGES.callVerify  ----MissCallScreen.tsx
  whatsApp: require("./Icons/whatsapp.png"), // IMAGES.whatsApp ---In misscall and scancontact.tsx(no need to change)
  message: require("./Icons/message.png"), // IMAGES.message ----MissCallScreen.tsx
  posterImg: require("./Icons/posterimg.png"), // IMAGES.posterImg. ----MissCallScreen.tsx
  blocked: require("./Icons/blocked.png"), // IMAGES.blocked ----MissCallScreen.tsx
  unSelect: require("./Icons/unselect.png"), // IMAGES.unSelect ----NewCallScreen.tsx
  selected: require("./Icons/selected.png"), // IMAGES.selected ----NewCallScreen.tsx
  videoImageWhite: require("./Icons/videoImageWhite.png"), // IMAGES.videoImageWhite.  ----NewCallScreen.tsx
  callImageWhite: require("./Icons/callImageWhite.png"), // IMAGES.callImageWhite  ----NewCallScreen.tsx
  upload: require("./Icons/upload.png"), // IMAGES.upload ---ScheduleCallScreen.tsx
  translation: require("./Icons/translation.png"), // IMAGES.translation. ----OptionsModal.tsx
  shareScreen: require("./Icons/shareScreen.png"), // IMAGES.shareScreen ----OptionsModal.tsx
  queenSymbol: require("./Icons/queensymbol.png"), // IMAGES.queenSymbol ---no need to change
  unselected_white: require("./Icons/unselected_white.png"), // IMAGES.unselected_white(AddPeopleModal.tsx,LanguageModal.tsx)
  selected_white: require("./Icons/selected_white.png"), // IMAGES.selected_white (AddPeopleModal.tsx,LanguageModal.tsx)
  mute_white: require("./Icons/mute_white.png"), // IMAGES.mute_white (unmute)(CallButton.tsx,CallMembersModal.tsx,CallMembertile.tsx)


    image1: require('./Image/image1.png'), // IMAGES.image1 --not used
  image2: require('./Image/image2.png'), // IMAGES.image2 ---not used
  rainingTeddy: require('./Image/RainingTedday.png'), // IMAGES.rainingTeddy --- used in chatscreen(no need to change) 
  cloudLock: require('./Image/CloudLock.png'), // IMAGES.CloudLock. ----no need to change
 
};

export const chatWallpaper = {
  mainWallpaper: require('./Icons/mainWallpaper.png'),
};

export const emoji = {
  e1: require('./emoji_gif/e1.gif'),
  e2: require('./emoji_gif/e2.gif'),
};
