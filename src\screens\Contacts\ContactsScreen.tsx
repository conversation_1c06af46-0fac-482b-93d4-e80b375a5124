import { <PERSON>S<PERSON>t, <PERSON><PERSON>ist, SafeAreaView, Text, View } from 'react-native';
import React, { useMemo, useState } from 'react';
import GradientView from '../../component/GradientView';
import { AppStyles } from '../../theme/appStyles';
import HomeHeader from '../../component/HomeHeader';

import SearchInput from '../../component/SearchInput';
import { commonFontStyle, hp } from '../../theme/fonts';

import { colors } from '../../theme/colors';

import ContactItem from '../../component/ContactItem';
import { SCREENS } from '../../navigation/screenNames';
import { useTranslation } from 'react-i18next';
import { navigateTo } from '../../utils/commonFunction';
import { useContacts } from '../../hooks/contacts/useContacts';
import { useFilteredContacts } from '../../hooks/common/useFilterContacts';
import { useMe } from '../../hooks/util/useMe';
import { IChatScreenProps } from '../Home/Chats/ChatSpecificScreen';
import { ConversationType } from '../../device-storage/realm/schemas/MessageSchema';
import useProfileViewability from '../../lib/hooks/useProfileViewability';
import UserProfile from './components/UserProfile';
import UnregisteredContact from './components/UnregisteredContact';
import useUsers from '../../device-storage/realm/hooks/useUsers';

const ContactsScreen = () => {
  const [search, setSearch] = useState('');
  const { createViewableItemsHandler } = useProfileViewability();
  const onViewableContactsChanged = createViewableItemsHandler('userId');
  const { registeredContacts: regContacts, unregisteredContacts: unregContacts } = useContacts();
  const { t } = useTranslation();
  const { user } = useMe();
  const myId = user?._id;

  const users = useUsers();

  const { filteredRegistered, filteredUnregistered } = useFilteredContacts(
    regContacts.map((item) => item),
    unregContacts.map((item) => item),
    search,
    { excludeUserId: myId },
  );

  const onOpenChat = (item: any) => {
    const userData: IChatScreenProps = {
      displayName: item.name || '',
      displayPic: item.image || '',
      type: ConversationType.P2P,
      id: item.userId || '',
      isActive: item.isActive ?? false,
      conversation: item,
      chatSpaceId: item.chatSpaceId,
    };

    navigateTo(SCREENS.ChatSpecificScreen, {
      userData,
    });
  };

  return (
    <GradientView>
      <SafeAreaView style={AppStyles.flex}>
        <HomeHeader
          type={'contact'}
          contactsLength={regContacts?.length + unregContacts?.length || 0}
        />
        <View
          style={[AppStyles.bottomWhiteViewWithoutPadding, { flex: 1, paddingVertical: hp(2) }]}
        >
          <View style={styles.searchView}>
            <SearchInput
              value={search}
              onChangeText={(text) => {
                setSearch(text);
              }}
            />
          </View>

          {/* Contacts are rendered here */}
          <FlatList
            data={filteredRegistered || []}
            style={{ paddingBottom: 200 }}
            renderItem={({ item }) => {
              const contactData = users.blockedUsers.find((list) => list.id == item.id);
              const isBlocked = contactData?.id == item?.id;
              return (
                // <ContactItem
                //   type="register"
                //   onPress={() => onOpenChat(item)}
                //   data={item}
                //   isSelectionMode={false}
                // />
                <UserProfile user={item} isBlocked={isBlocked} />
              );
            }}
            onViewableItemsChanged={onViewableContactsChanged}
            ListHeaderComponent={
              unregContacts?.length > 0 ? (
                <Text style={styles.titleText}>{t('Contacts on ChatBucket')}</Text>
              ) : null
            }
            ListFooterComponent={
              <View style={styles.footerStyle}>
                <Text style={styles.titleText}>{t('Invite to ChatBucket')}</Text>
                <FlatList
                  data={filteredUnregistered}
                  keyExtractor={(item, index) => `${index}`}
                  scrollEnabled={false}
                  renderItem={({ item }) => <UnregisteredContact contact={item} />}
                />
              </View>
            }
          />
        </View>
      </SafeAreaView>
    </GradientView>
  );
};

export default ContactsScreen;

const styles = StyleSheet.create({
  searchView: {
    paddingHorizontal: hp(2),
    paddingBottom: hp(2),
  },
  titleText: {
    ...commonFontStyle(500, 16, colors.gray_80),
    paddingHorizontal: hp(2),
    marginTop: hp(2),
  },
  footerStyle: {
    flex: 1,
    paddingBottom: 100,
  },
});
