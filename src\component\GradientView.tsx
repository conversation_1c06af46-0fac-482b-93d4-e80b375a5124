import { StyleSheet } from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '../theme/colors';
import { AppStyles } from '../theme/appStyles';

import CallOverlay from './CallOverlay';

import { hp } from '../theme/fonts';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const GradientView = ({ children, color }: any) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      // locations={[0, 0.2]}
      style={[
        AppStyles.flex,
        { backgroundColor: color ? color : colors.mainPurple },
        {
          paddingTop: insets.top / 2,
        },
      ]}
      colors={color ? [color, color] : colors.gradientPurple}
    >
      {children}
    </LinearGradient>
  );
};

export default GradientView;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    marginTop: 30,
  },
  selectedView: {
    backgroundColor: colors._7155C3_purple,
    paddingVertical: 10,
    paddingHorizontal: 25,
    borderRadius: 50,
  },
  tabIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },

  headerMainView: {
    paddingLeft: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1.5),
    height: hp(13),
  },
  moreMenu: {
    padding: hp(2),
    marginTop: 30,
  },
  moreMenuStyle: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  headerBackView: {
    height: 18 + hp(2),
    width: 18 + hp(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerBackIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
    transform: [{ rotate: '180deg' }],
  },
});
