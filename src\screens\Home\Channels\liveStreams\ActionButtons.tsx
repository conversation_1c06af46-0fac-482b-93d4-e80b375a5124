import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  StyleSheet,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import HeartOutlineSvg from '../../../../assets/svgIcons/HeartOutlineSvg';
import ShareOutlineSvg from '../../../../assets/svgIcons/ShareOutlineSvg';
import StickerOutlineSvg from '../../../../assets/svgIcons/StickerOutlineSvg';
import { useKeyboard } from '../../../../utils/useKeyboard';
import EyeOutlineSvg from '../../../../assets/svgIcons/EyeOutlineSvg';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { colors } from '../../../../theme/colors';

import RNVideo from 'react-native-video';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';

interface ActionButtonsProps {
  likes: number;
  shares: number;
  onLike: () => void;
  onShare: () => void;
  isLiked: boolean;
}

// Action Buttons Component
const ActionButtons: React.FC<ActionButtonsProps> = ({
  likes,
  shares,
  onLike,
  onShare,
  isLiked,
}) => {
  return (
    <View style={ActionButtonsStyles.actionButtons}>
      <TouchableOpacity onPress={onLike} style={ActionButtonsStyles.actionButton}>
        <View style={ActionButtonsStyles.actionButtonCircle}>
          <StickerOutlineSvg />
        </View>
      </TouchableOpacity>
      <TouchableOpacity onPress={onLike} style={ActionButtonsStyles.actionButton}>
        <View style={ActionButtonsStyles.actionButtonCircle}>
          {isLiked ? (
            <FontAwesome5 name="heart" size={24} color="red" solid />
          ) : (
            <HeartOutlineSvg fill={colors.white} />
          )}
        </View>
        <Text style={ActionButtonsStyles.actionButtonText}>{likes}</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onShare} style={ActionButtonsStyles.actionButton}>
        <View style={ActionButtonsStyles.actionButtonCircle}>
          <ShareOutlineSvg />
        </View>
        <Text style={ActionButtonsStyles.actionButtonText}>{shares}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ActionButtons;

export const ActionButtonsStyles = StyleSheet.create({
  actionButtons: {
    bottom: 0,
    gap: 30,
  },
  actionButton: {
    alignItems: 'center',
    width: 70,
  },
  actionButtonCircle: {
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  actionButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
});
