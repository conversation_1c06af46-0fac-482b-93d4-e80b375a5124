import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const AppInfoSVG = (props: any) => (
  <Svg width={20} height={20} viewBox="0 0 19 19" fill="none" {...props}>
    <Path d="M9.32422 7.94922C9.72677 7.92254 10.1253 8.04341 10.4453 8.28906L10.4512 8.29395C10.7215 8.52279 10.873 8.86195 10.8633 9.21582L10.8516 9.37207C10.846 9.43017 10.8398 9.49894 10.8398 9.5791V9.59277C10.8243 9.76398 10.7912 9.93318 10.7412 10.0977L10.7402 10.0967L10.2852 11.6631L10.2861 11.6641C10.2466 11.8014 10.2135 11.9413 10.1885 12.082L10.1875 12.0859C10.1625 12.2044 10.1483 12.3253 10.1445 12.4463V12.46L9.99512 12.4414L10.1436 12.46C10.1306 12.5645 10.1643 12.6684 10.2334 12.7471C10.352 12.8124 10.4869 12.8417 10.6221 12.8291L10.6328 12.8281L10.6436 12.8291C10.7647 12.8353 10.8867 12.8248 11.0049 12.7979C11.1005 12.774 11.1941 12.7428 11.2852 12.7051L11.5625 12.5898L11.4883 12.8809L11.3652 13.3672L11.3457 13.4424L11.2725 13.4707C10.9062 13.61 10.6119 13.7175 10.3906 13.793L10.3877 13.7939C10.126 13.8773 9.85263 13.9165 9.57812 13.9121C9.17036 13.9368 8.76745 13.8141 8.44336 13.5654L8.44141 13.5645C8.15938 13.3434 7.99587 13.0039 7.99805 12.6455C7.99806 12.5096 8.00738 12.3739 8.02637 12.2393V12.2383C8.04924 12.0839 8.07991 11.9306 8.11816 11.7793L8.12012 11.7734L8.5791 10.1973C8.6205 10.0504 8.65454 9.91181 8.68066 9.78125L8.68164 9.77832C8.70715 9.6619 8.7203 9.54303 8.7207 9.42383V9.41602L8.72168 9.4082C8.73316 9.30374 8.70272 9.19957 8.63965 9.11621C8.52854 9.0485 8.39838 9.01821 8.26855 9.03223L8.26074 9.0332H8.25293C8.14458 9.03406 8.03661 9.0494 7.93262 9.08008L7.93359 9.08105C7.81044 9.11956 7.70363 9.15352 7.61719 9.18359L7.35352 9.27539L7.42285 9.00391L7.55469 8.49414L7.57324 8.41992L7.64453 8.39258C7.94884 8.27291 8.23969 8.16922 8.5166 8.08301V8.08398C8.77783 7.99985 9.04985 7.95507 9.32422 7.9502V7.94922Z" fill="#232323" stroke="#232323" strokeWidth={0.3}/>
    <Path d="M10.3486 5.10156C10.6692 5.09506 10.9798 5.21072 11.2168 5.42676C11.2542 5.4593 11.2897 5.49477 11.3223 5.53223C11.7125 5.98049 11.6652 6.65894 11.2178 7.0498L11.2188 7.05078C10.7226 7.49447 9.97251 7.4951 9.47559 7.05273L9.36328 6.94043C9.00038 6.51949 9.02147 5.89702 9.39551 5.50195L9.47559 5.42676C9.71251 5.21187 10.0221 5.09628 10.3418 5.10156V5.10059C10.3429 5.10056 10.3446 5.10159 10.3457 5.10156C10.3469 5.10159 10.3484 5.10056 10.3496 5.10059L10.3486 5.10156Z" fill="#232323" stroke="#232323" strokeWidth={0.3}/>
    <Path d="M9.5 0.849609C14.2773 0.849609 18.1504 4.72273 18.1504 9.5C18.1504 14.2773 14.2773 18.1504 9.5 18.1504C4.72273 18.1504 0.849609 14.2773 0.849609 9.5C0.849609 4.72273 4.72273 0.849609 9.5 0.849609ZM9.5 1.92285C5.31518 1.92285 1.92285 5.31518 1.92285 9.5C1.92285 13.6848 5.31518 17.0771 9.5 17.0771C13.6848 17.0771 17.0771 13.6848 17.0771 9.5C17.0771 5.31518 13.6848 1.92285 9.5 1.92285Z" fill="#232323" stroke="#232323" strokeWidth={0.3}/>
  </Svg>
);

export default AppInfoSVG; 