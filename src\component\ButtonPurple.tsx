import {
  Image,
  StyleSheet,
  Text,
  View,
  ViewStyle,
  TouchableOpacity,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import React from 'react';
import { colors } from '../theme/colors';
import { commonFontStyle, hp } from '../theme/fonts';
import { IMAGES } from '../assets/Images';
import { FontAwesome6Icons } from '../utils/vectorIcons';

type Props = {
  withRightIcon?: Boolean;
  title: string;
  extraStyle?: ViewStyle | ViewStyle[];
  onPress?: () => void;
  titleColor?: any;
  type?: 'purple' | 'gray' | 'border';
  disabled?: boolean;
  leftIcon?: any;
  textStyle?: TextStyle;
  leftSVG?: React.ReactNode;
  isLoading?: boolean;
  loaderColor?: string;
};

const ButtonPurple = ({
  withRightIcon = false,
  titleColor,
  title,
  extraStyle,
  onPress,
  type = 'purple',
  disabled = false,
  textStyle,
  leftIcon,
  leftSVG,
  isLoading = false,
  loaderColor = colors.black,
}: Props) => {
  return (
    <TouchableOpacity
      onPress={() => (onPress ? onPress() : {})}
      disabled={disabled || isLoading}
      style={[
        type == 'border'
          ? styles.borderBtnContainer
          : type == 'purple'
          ? styles.btnContainer
          : styles.grayBtnContainer,
        {
          justifyContent: withRightIcon ? 'space-between' : 'center',
          opacity: disabled ? 0.5 : 1,
        },
        extraStyle,
      ]}
    >
      {isLoading ? (
        <ActivityIndicator size="small" color={loaderColor} />
      ) : (
        <>
          {leftIcon && <Image source={leftIcon} style={styles.rightArrow} />}
          {leftSVG ? leftSVG : null}
          <Text
            style={[
              styles.titleText,
              {
                color: titleColor ? titleColor : type === 'purple' ? colors.white : colors.black,
              },
              textStyle,
            ]}
          >
            {title}
          </Text>
          {withRightIcon && <FontAwesome6Icons name="arrow-right-long" size={20} color="#FFFFFF" />}
        </>
      )}
    </TouchableOpacity>
  );
};

export default ButtonPurple;

const styles = StyleSheet.create({
  btnContainer: {
    height: 55,
    backgroundColor: colors.mainPurple,
    borderRadius: 15,
    paddingHorizontal: hp(3),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },
  borderBtnContainer: {
    height: 55,
    borderWidth: 1,
    borderColor: colors.mainPurple,
    backgroundColor: colors.white,
    borderRadius: 15,
    paddingHorizontal: hp(3),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 10,
  },
  grayBtnContainer: {
    height: 55,
    backgroundColor: colors.gray_f3,
    borderRadius: 15,
    paddingHorizontal: hp(3),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rightArrow: {
    height: 19,
    width: 19,
    resizeMode: 'contain',
  },
  titleText: {
    ...commonFontStyle(600, 16, colors.white),
    textAlign: 'center',
  },
});
