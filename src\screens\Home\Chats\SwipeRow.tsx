// ChatScreen.tsx

import React, { useState } from 'react';
import { View, Text, FlatList, StyleSheet, TextInput, Button, Dimensions } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SWIPE_THRESHOLD = 60;

type Message = {
  id: string;
  text: string;
  sender: 'me' | 'other';
};

type SwipeRowProps = {
  item: Message;
  onReply: (message: Message) => void;
};

const SwipeableMessage: React.FC<SwipeRowProps> = ({ item, onReply }) => {
  const translateX = useSharedValue(0);

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (event.translationX > 0) {
        translateX.value = Math.min(event.translationX, 100);
      }
    })
    .onEnd(() => {
      if (translateX.value > SWIPE_THRESHOLD) {
        runOnJS(onReply)(item);
      }
      translateX.value = withTiming(0);
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const isMyMessage = item.sender === 'me';

  return (
    <GestureDetector gesture={panGesture}>
      <Animated.View
        style={[
          styles.messageContainer,
          isMyMessage ? styles.myMessage : styles.theirMessage,
          animatedStyle,
        ]}
      >
        <Text style={styles.messageText}>{item.text}</Text>
      </Animated.View>
    </GestureDetector>
  );
};

const ChatScreen: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    { id: '1', text: 'Hey there!', sender: 'other' },
    { id: '2', text: 'What’s up?', sender: 'me' },
    { id: '3', text: 'Swipe to reply!', sender: 'other' },
  ]);

  const [input, setInput] = useState('');
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);

  const handleSend = () => {
    if (input.trim()) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          text: input,
          sender: 'me',
        },
      ]);
      setInput('');
      setReplyingTo(null);
    }
  };

  const handleReply = (message: Message) => {
    setReplyingTo(message);
  };

  const renderItem = ({ item }: { item: Message }) => (
    <SwipeableMessage item={item} onReply={handleReply} />
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={styles.chatList}
      />

      {replyingTo && (
        <View style={styles.replyPreview}>
          <Text style={styles.replyLabel}>Replying to:</Text>
          <Text numberOfLines={1} style={styles.replyText}>
            {replyingTo.text}
          </Text>
        </View>
      )}

      <View style={styles.inputContainer}>
        <TextInput
          value={input}
          onChangeText={setInput}
          placeholder="Type a message"
          style={styles.input}
        />
        <Button title="Send" onPress={handleSend} />
      </View>
    </View>
  );
};

export default ChatScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  chatList: {
    padding: 10,
  },
  messageContainer: {
    padding: 10,
    borderRadius: 10,
    marginVertical: 4,
    maxWidth: '80%',
  },
  myMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#DCF8C6',
  },
  theirMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#ff0000',
  },
  messageText: {
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 10,
    borderTopWidth: 0.5,
    borderColor: '#ccc',
    backgroundColor: '#fff',
  },
  input: {
    flex: 1,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 20,
    marginRight: 10,
  },
  replyPreview: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    backgroundColor: '#e0e0e0',
    borderTopWidth: 1,
    borderColor: '#ccc',
  },
  replyLabel: {
    fontSize: 12,
    color: '#555',
  },
  replyText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});
