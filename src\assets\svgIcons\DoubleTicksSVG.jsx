import * as React from "react";
import Svg, { Path } from "react-native-svg";

function DoubleTicksSVG({ size = 18, color = "#fff" }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.62 5.201c.234.205.258.56.054.794l-5.893 6.75a.562.562 0 01-.848 0l-2.357-2.7a.562.562 0 11.848-.74l1.933 2.215 5.47-6.265a.562.562 0 01.793-.054zM15.388 5.265c.**************.02.795l-6.43 6.75a.563.563 0 01-.854-.047l-.321-.422a.563.563 0 01.806-.774l5.984-6.283a.563.563 0 01.795-.02z"
                fill={color}
            />
        </Svg>
    );
}

export default DoubleTicksSVG;
