package com.chatbucket.utils

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.util.Base64
import com.facebook.react.bridge.*
import java.io.ByteArrayOutputStream

class ThumbnailModule(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "ThumbnailModule"
    }

@ReactMethod
fun generateVideoThumbnail(filePath: String, promise: Promise) {
    try {
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(filePath)

        val bitmap: Bitmap? = retriever.getFrameAtTime(1_000_000, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
        retriever.release()

        if (bitmap != null) {
            // Save thumbnail to cache directory
            val cacheDir = reactApplicationContext.cacheDir
            val file = java.io.File(cacheDir, "thumb_${System.currentTimeMillis()}.png")
            val outputStream = java.io.FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()

            promise.resolve(file.absolutePath)
        } else {
            promise.reject("THUMBNAIL_ERROR", "Failed to retrieve frame from video.")
        }
    } catch (e: Exception) {
        promise.reject("THUMBNAIL_EXCEPTION", "Error generating thumbnail: ${e.localizedMessage}", e)
    }
}

}
