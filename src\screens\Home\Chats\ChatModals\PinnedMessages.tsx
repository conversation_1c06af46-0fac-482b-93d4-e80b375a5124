import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  Animated,
  TouchableOpacity,
  Image,
} from 'react-native';
import React, { useRef } from 'react';
import PinSVG from '../../../../assets/svgIcons/PinSVG';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import { MessageType } from '../../../../device-storage/realm/schemas/MessageSchema';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import MicSVG2 from '../../../../assets/svgIcons/MicSVG2';
import GalleryPlaneSVG from '../../../../assets/svgIcons/GalleryPlaneSVG';
import VideoCallSVG from '../../../../assets/svgIcons/VideoCallSVG';

import ReplyAvatarSVG from '../../../../assets/svgIcons/CallSVG';
import ReplyDocSVG from '../../../../assets/svgIcons/DocReplySVG';

import MusicSVG from '../../../../assets/svgIcons/MusicSVG';
import LocationSVG from '../../../../assets/svgIcons/LocationSVG';
import LiveLocationSVG from '../../../../assets/svgIcons/LiveLocationSVG';

type IProps = {
  isVisible: boolean;
  onClose: () => void;
  messages: any[];
  onClickMessage?: (localId: any) => void;
};

const PAGE_HEIGHT = hp(2.2);
const MAX_DOTS = 3;
const DOT_SIZE_NORMAL = 4;
const DOT_SIZE_SMALL = 3;
const GAP_NORMAL = 4;
const GAP_SMALL = 2;
const DOTS_CONTAINER_HEIGHT = hp(2.2);

const PinnedMessages = ({
  isVisible,
  onClose,
  messages = [],
  onClickMessage = () => { },
}: IProps) => {
  const displayMessages = messages.length > 0 ? messages : data;
  const scrollY = useRef(new Animated.Value(0)).current;
  const hasManyMessages = displayMessages.length > MAX_DOTS;
  const shouldShowDots = displayMessages.length > 1; // Hide if only 1 message

  // Calculate current page index
  const currentPageIndex = shouldShowDots
    ? scrollY.interpolate({
      inputRange: displayMessages.map((_, i) => i * PAGE_HEIGHT),
      outputRange: displayMessages.map((_, i) => i),
      extrapolate: 'clamp',
    })
    : new Animated.Value(0); // Fallback for single message

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        {/* Conditionally render dots */}
        {shouldShowDots && (
          <View style={[styles.dotsContainer, { height: DOTS_CONTAINER_HEIGHT }]}>
            {displayMessages.map((_, index) => {
              const dotScale = shouldShowDots
                ? currentPageIndex.interpolate({
                  inputRange: [index - 1, index, index + 1],
                  outputRange: [0.8, 1.2, 0.8],
                  extrapolate: 'clamp',
                })
                : 1; // No anim
              const dotOpacity = shouldShowDots
                ? currentPageIndex.interpolate({
                  inputRange: [index - 1, index, index + 1],
                  outputRange: [0.5, 1, 0.5],
                  extrapolate: 'clamp',
                })
                : 1;

              const size = hasManyMessages ? DOT_SIZE_SMALL : DOT_SIZE_NORMAL;
              const gap = hasManyMessages ? GAP_SMALL : GAP_NORMAL;

              return (
                <Animated.View
                  key={index}
                  style={[
                    styles.dot,
                    {
                      height: size,
                      width: size,
                      marginVertical: gap / 2,
                      transform: [{ scale: dotScale }],
                      opacity: dotOpacity,
                    },
                  ]}
                />
              );
            })}
          </View>
        )}

        <PinSVG size={17} color={colors.black_23} style={styles.pinIcon} />

        <Animated.ScrollView
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: true,
          })}
          pagingEnabled
          showsVerticalScrollIndicator={false}
          style={[styles.scrollView, { height: PAGE_HEIGHT }]}
          snapToInterval={PAGE_HEIGHT}
          decelerationRate="fast"
        >
          {messages.map((msg) => (
            <TouchableOpacity
              key={msg.localId}
              activeOpacity={1}
              onPress={() => {
                onClickMessage(msg?.localId);
              }}
              style={[styles.messageContainer, { width: '100%', height: PAGE_HEIGHT }]}
            >
              {msg?.messageType === MessageType.IMAGE || msg?.messageType === MessageType.VIDEO ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginLeft: 4,
                  }}
                >
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    {msg?.messageType === MessageType.VIDEO ? (
                      <VideoCallSVG color={colors.black_23} size={14} />
                    ) : (
                      <GalleryPlaneSVG size={13} color={colors.black_23} />
                    )}
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      {msg?.messageType === MessageType.VIDEO ? 'Video' : 'Photo'}
                    </Text>
                  </View>
                </View>
              ) : msg?.messageType == MessageType.AUDIO || msg?.messageType == MessageType.VOICE ? (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginLeft: 4 }}>
                  {msg?.messageType == MessageType.AUDIO ? (
                    <MusicSVG size={15} pathColor={colors._464646_gray} withOutBg={true} />
                  ) : (
                    <MicSVG2 size={14} color={colors.black_23} />
                  )}
                  <Text style={{ color: colors.black_23, fontSize: 14 }} numberOfLines={2}>
                    {msg?.messageType == MessageType.AUDIO ? msg?.fileName : `Voice mesage`}
                  </Text>
                </View>
              ) : msg?.messageType == MessageType.DOCUMENT ? (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginLeft: 4 }}>
                  <ReplyDocSVG size={15} color={colors.black_23} />
                  <Text
                    style={{ color: colors.black_23, fontSize: 14 }}
                    numberOfLines={2}
                  >{`Document`}</Text>
                </View>
              ) : msg?.messageType == MessageType.CONTACT ? (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginLeft: 4 }}>
                  <ReplyAvatarSVG size={13} color={colors.black_23} />
                  <Text
                    style={{ color: colors.black_23, fontSize: 14 }}
                    numberOfLines={2}
                  >{`Contact`}</Text>
                </View>
              ) : msg?.messageType == MessageType.LOCATION ?
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginLeft: 4 }}>
                  {msg?.location?.type == 'live' ?
                    <LiveLocationSVG size={18} color={colors.black_23} style={{ marginRight: 3 }} />
                    :
                    <LocationSVG size={17} color={colors.black_23} />}
                  <Text
                    style={{ color: colors.black_23, fontSize: 14 }}
                    numberOfLines={2}
                  >{`Location`}</Text>
                </View>
                :
                <Text numberOfLines={1} style={styles.messageText}>
                  {msg.text}
                </Text>
              }
            </TouchableOpacity>
          ))}
        </Animated.ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    elevation: 0.5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(3),
    paddingVertical: hp(2.5),
  },
  dotsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp(1.6),
  },
  dot: {
    borderRadius: 5,
    backgroundColor: colors.mainPurple,
  },
  pinIcon: {
    transform: [{ rotate: '-45deg' }],
    marginRight: 3,
  },
  scrollView: {
    flex: 1,
  },
  messageContainer: {
    justifyContent: 'center',
    width: '100%',
  },
  messageText: {
    fontSize: 14,
    fontWeight: '400',
    color: colors.black,
    marginLeft: 4,
  },
  lastMessage: {
    color: colors.black_23,
    fontSize: 14,
    fontWeight: '400',
  },
});

// Test with different array lengths
const data = [
  { id: 1, message: 'Only one pinned message' },
  // Uncomment to test with multiple messages:
  { id: 2, message: 'Second message' },
  { id: 3, message: 'Third message' },
  { id: 4, message: 'Fourth message' },
  { id: 5, message: 'Fifth message' },
];

export default PinnedMessages;
