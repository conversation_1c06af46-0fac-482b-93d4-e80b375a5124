import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

const ProfileSVG = ({ size = 20, color = '#222', ...props }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
    <Circle cx={12} cy={12} r={11} stroke={color} strokeWidth={2} fill="none" />
    <Circle cx={12} cy={9} r={4} stroke={color} strokeWidth={2} fill="none" />
    <Path d="M5.5 18c1.5-3 11.5-3 13 0" stroke={color} strokeWidth={2} fill="none" strokeLinecap="round" />
  </Svg>
);

export default ProfileSVG; 