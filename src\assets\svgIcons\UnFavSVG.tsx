import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const UnFavSVG: React.FC<IconProps> = ({
  size = 20,
  color = '#C2C2C2',
  ...restProps
}) => {
  return (
    <Svg
      width={size}
      height={(size * 19) / 20} // maintains original aspect ratio
      viewBox="0 0 20 19"
      fill="none"
      {...restProps}>
      <Path
        d="M10 0.950195C10.4661 0.950195 10.8806 1.26248 11.1416 1.78906L13.2129 5.9668L17.8457 6.63867C18.429 6.72314 18.856 7.01841 19 7.45996C19.1439 7.90149 18.9729 8.39017 18.5508 8.7998L15.1982 12.0518L15.9902 16.6455C16.0946 17.2508 15.9171 17.6201 15.7441 17.8242C15.5423 18.0625 15.2483 18.1923 14.9189 18.1924C14.6715 18.1922 14.41 18.1209 14.1436 17.9814L10 15.8125L5.85645 17.9814C5.5902 18.1208 5.32952 18.1923 5.08203 18.1924C4.75255 18.1924 4.45788 18.0626 4.25586 17.8242C4.08295 17.6201 3.90553 17.2508 4.00977 16.6455L4.80078 12.0518L1.44922 8.7998C1.02706 8.39029 0.856208 7.90149 1 7.45996C1.14403 7.0184 1.57097 6.72312 2.1543 6.63867L6.78613 5.9668L8.8584 1.78906C9.1195 1.2625 9.53392 0.950209 10 0.950195ZM10 2.13477C9.97302 2.16555 9.93538 2.21564 9.89453 2.29785L7.68848 6.74805C7.60441 6.91767 7.44183 7.03522 7.25391 7.0625L2.32031 7.77637C2.23005 7.78945 2.1707 7.80927 2.13281 7.8252C2.15395 7.86022 2.1892 7.91179 2.25488 7.97559L5.8252 11.4395C5.96131 11.5714 6.02325 11.7626 5.99121 11.9492L5.14844 16.8398C5.13302 16.9291 5.1342 16.9915 5.1377 17.0322C5.1776 17.023 5.23807 17.0054 5.31934 16.9629L9.73145 14.6543C9.81548 14.6103 9.90775 14.5879 10 14.5879C10.0923 14.5879 10.1844 14.6103 10.2686 14.6543L14.6807 16.9639C14.7618 17.0063 14.8224 17.023 14.8623 17.0322C14.8658 16.9915 14.868 16.9292 14.8525 16.8398H14.8516L14.0098 11.9492C13.9776 11.7627 14.0397 11.5715 14.1758 11.4395L17.7451 7.97559C17.8103 7.91226 17.846 7.86121 17.8672 7.82617C17.8293 7.81022 17.7703 7.78948 17.6797 7.77637L12.7471 7.0625C12.5591 7.03527 12.3958 6.9177 12.3115 6.74805L10.1055 2.29785C10.0647 2.21562 10.027 2.16556 10 2.13477Z"
        fill={color}
        stroke={color}
        strokeWidth="0.1"
      />
    </Svg>
  );
};

export default UnFavSVG; 