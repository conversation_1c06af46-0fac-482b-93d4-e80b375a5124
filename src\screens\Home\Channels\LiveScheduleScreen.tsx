import { Image, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import CommonView from '../../../component/CommonView';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { commonFontStyle, hp } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import CustomDatePicker from '../../../component/PersonalChat/CustomDatePicker';
import moment from 'moment';
import Input from '../../../component/Input';
import { IMAGES } from '../../../assets/Images';
import { AppStyles } from '../../../theme/appStyles';
import ButtonPurple from '../../../component/ButtonPurple';
import RenderToggle from '../../../component/RenderToggle';

const LiveScheduleScreen = () => {
  const { goBack } = useNavigation();
  const { t } = useTranslation();
  const [scheduleDate, setScheduleDate] = useState(new Date());
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');

  const [allowComments, setAllowComments] = useState(false);
  const [allowGifts, setAllowGifts] = useState(false);

  return (
    <CommonView
      headerTitle={t('Schedule live stream')}
      containerStyle={{ paddingTop: hp(3) }}
      isScrollable
    >
      <Text style={styles.textStyle}>{t('Set date and time')}</Text>
      <CustomDatePicker setScheduleDate={setScheduleDate} scheduleDate={scheduleDate} />
      <View style={{ gap: 20 }}>
        <Input
          placeHolder={t('Enter target amount')}
          value={amount}
          onChangeText={setAmount}
          RenderRightIcon={() => {
            return <Image source={IMAGES.dollar_icon} style={AppStyles.iconStyle20} />;
          }}
        />
        <Input
          placeHolder={t('Enter the cause for')}
          value={description}
          onChangeText={setDescription}
        />

        <Input
          placeHolder={t('Upload thumbnail')}
          value={amount}
          onChangeText={setAmount}
          RenderRightIcon={() => {
            return <Image source={IMAGES.imageWallpaperPick} style={AppStyles.iconStyle20} />;
          }}
        />
        <View style={{ paddingHorizontal: 20 }}>
          <RenderToggle
            title={t('Allow comments')}
            isVisible={allowComments}
            onToggleSwitch={() => setAllowComments(!allowComments)}
          />
          <RenderToggle
            title={t('Allow gifts')}
            isVisible={allowGifts}
            onToggleSwitch={() => setAllowGifts(!allowGifts)}
          />
        </View>
        <ButtonPurple title={t('Schedule')} />
      </View>
    </CommonView>
  );
};

export default LiveScheduleScreen;

const styles = StyleSheet.create({
  textStyle: {
    ...commonFontStyle(500, 15, colors.gray_80),
    paddingBottom: 10,
  },
});
