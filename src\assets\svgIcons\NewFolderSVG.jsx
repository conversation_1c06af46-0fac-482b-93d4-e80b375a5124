import * as React from "react"
import Svg, { Path } from "react-native-svg"

function NewFolderSVG({ size = 24, color = "#232323", strokeColor = "#232323", ...props }) {
    return (
        <Svg
            width={size}
            height={(size * 20) / 22} // maintains original aspect ratio (22:20)
            viewBox="0 0 22 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M6.84.837c.816 0 1.569.41 2.014 1.093l1.39 2.132.052.062c.06.054.137.085.22.085h8.855a2.407 2.407 0 012.403 2.405V16.73a2.407 2.407 0 01-2.403 2.404H3.325a2.407 2.407 0 01-2.404-2.404V3.243A2.408 2.408 0 013.325.837H6.84zm-3.515 1.41a.997.997 0 00-.996.996V16.73c0 .55.447.996.996.996h16.046c.55 0 .996-.447.996-.996v-.08h.001V6.615a.997.997 0 00-.996-.996h-8.854c-.515 0-.996-.226-1.323-.613l-.13-.175-1.39-2.132a.995.995 0 00-.834-.451H3.325z"
                fill={color}
                stroke={strokeColor}
                strokeWidth={0.157799}
            />
        </Svg>
    )
}

export default NewFolderSVG
