import { StyleSheet, StyleSheetProperties, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { colors } from '../theme/colors';
import { commonFontStyle, hp } from '../theme/fonts';
import { AppStyles } from '../theme/appStyles';
import RenderUserIcon from './RenderUserIcon';

import ButtonPurple from './ButtonPurple';
import { useTranslation } from 'react-i18next';
import { IMAGES } from '../assets/Images';
import { IContact } from '../device-storage/realm/schemas/ContactSchema';
import { Image } from 'react-native-svg';
import CustomImage from './CustomImage';
import SelectionSVG from '../assets/svgIcons/SelectionSVG';
import { UserSchema } from '../device-storage/realm/schemas/UserSchema';

type Props = {
  data: Partial<UserSchema>;
  onPress?: (contact: Partial<UserSchema>) => void;
  type?: 'register' | 'unregister';
  onPressInvite?: () => void;
  isSelected?: boolean;
  isSelectionMode: boolean;
  showInviteButton?: boolean;
  disabled?: boolean;
  containerStyles?: any;
  subText?: string;
  isBlocked?: boolean;
};

const ContactItem = ({
  data,
  onPress,
  type = 'register',
  onPressInvite,
  isSelected,
  isSelectionMode,
  showInviteButton,
  disabled,
  containerStyles,
  subText,
  isBlocked = false,
}: Props) => {
  const { t } = useTranslation();

  return (
    <TouchableOpacity
      style={[AppStyles.flex, containerStyles, isBlocked && styles.blockedContainer]}
      onPress={() => onPress?.(data)}
      disabled={disabled}
    >
      <View style={styles.rowView}>
        <View>
          <RenderUserIcon url={data?.profilePic} size={50} />
        </View>
        <View style={AppStyles.flex}>
          <Text numberOfLines={1} style={[styles.title, isBlocked && styles.blockedText]}>
            {/* {data} */}
            {data?.name || data?.name}
          </Text>
          {type === 'register' && (
            <Text numberOfLines={1} style={[styles.lastMessage, isBlocked && styles.blockedText]}>
              {subText ? subText : data?.bio}
            </Text>
          )}
        </View>
        {isBlocked && (
          <View style={styles.blockedIndicator}>
            <Text style={styles.blockedIndicatorText}>Blocked</Text>
          </View>
        )}
        {/* {type === 'unregister' && showInviteButton !== false && (
          <ButtonPurple
            title={t('Invite')}
            type="gray"
            extraStyle={styles.btnStyle}
            titleColor={colors.mainPurple}
            onPress={onPressInvite}
          />
        )} */}
      </View>
      {isSelectionMode && !isBlocked && (
        <View style={styles.checkWrapper}>
          {isSelected && <SelectionSVG size={24} isSelected={isSelected} />}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default ContactItem;

const styles = StyleSheet.create({
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(2),
    paddingVertical: hp(2),
    gap: 15,
  },
  userImage: {
    height: 50,
    width: 50,
    borderRadius: 50 / 2,
    resizeMode: 'cover',
  },
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  lastMessage: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: 3,
  },
  timeText: {
    ...commonFontStyle(400, 13, colors._B1B1B1_gray),
  },
  rowInner: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  onlineView: {
    backgroundColor: colors._33C200_green,
    borderWidth: 2.5,
    height: 13,
    width: 13,
    borderColor: colors.white,
    borderRadius: 13 / 2,
    position: 'absolute',
    right: 0,
    bottom: 0,
  },
  btnStyle: {
    backgroundColor: colors.opacity_main_purple_15,
    height: 35,
    borderRadius: 10,
  },
  checkWrapper: {
    position: 'absolute',
    right: hp(1),
    top: '50%',
    transform: [{ translateY: -18 }],
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },

  unselectedCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: colors.gray_86,
    backgroundColor: 'transparent',
  },

  blockedContainer: {
    opacity: 0.5,
  },

  blockedText: {
    color: colors.gray_80,
  },

  blockedIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: colors._F61B1B_red,
    borderRadius: 12,
  },

  blockedIndicatorText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },

  // checkmark: {
  //   position: 'absolute',
  //   right: hp(2),
  //   top: hp(2),
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   width: 20,
  //   height: 20,
  //   backgroundColor: colors.mainPurple,
  //   borderRadius: 10,
  // }
});
