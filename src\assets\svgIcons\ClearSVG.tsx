import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const ClearSVG: React.FC<IconProps> = ({
    size = 17,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={(size * 13) / 17} // Maintain aspect ratio
            viewBox="0 0 17 13"
            fill="none"
            {...restProps}
        >
            <Path
                d="M16.451 12.064H6.034a.548.548 0 01-.388-.16L.16 6.42a.548.548 0 010-.776L5.645.16A.548.548 0 016.033 0H16.45A.548.548 0 0117 .548v10.968a.548.548 0 01-.549.548zM6.26 10.967h9.643v-9.87H6.26L1.325 6.032l4.935 4.935z"
                fill={color}
            />
            <Path
                d="M12.614 8.774a.548.548 0 01-.389-.16L7.84 4.227a.55.55 0 11.776-.777l4.387 4.387a.548.548 0 01-.388.937z"
                fill={color}
            />
            <Path
                d="M8.227 8.774a.548.548 0 01-.388-.937l4.387-4.387a.55.55 0 01.776.777L8.615 8.614a.548.548 0 01-.388.16z"
                fill={color}
            />
        </Svg>
    );
};

export default ClearSVG;


