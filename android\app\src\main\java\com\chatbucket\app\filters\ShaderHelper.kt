package com.chatbucket.filters

import android.content.Context
import android.opengl.GLES11Ext
import android.opengl.GLES20
import java.nio.FloatBuffer

class FilterShader(private val context: Context) {
    private var program: Int = 0
    private var uFilterMatrixLocation: IntArray = IntArray(4) { -1 } // For 4 rows of the matrix
    private var currentFilter: Int = 0

    fun init() {
        val vertexShaderCode = """
            attribute vec4 aPosition;
            attribute vec2 aTexCoord;
            varying vec2 vTexCoord;
            void main() {
                gl_Position = aPosition;
                vTexCoord = aTexCoord;
            }
        """

        val fragmentShaderCode = """
            #extension GL_OES_EGL_image_external : require
            precision mediump float;
            uniform samplerExternalOES uTexture;
            uniform vec4 uFilterMatrix[4]; // 4x5 matrix split into 4 vec4s (ignoring last column for now)
            varying vec2 vTexCoord;

            void main() {
                vec4 color = texture2D(uTexture, vTexCoord);

                // Apply 4x4 color matrix (ignoring translation for simplicity)
                vec4 result;
                result.r = dot(vec4(color.r, color.g, color.b, 1.0), uFilterMatrix[0]);
                result.g = dot(vec4(color.r, color.g, color.b, 1.0), uFilterMatrix[1]);
                result.b = dot(vec4(color.r, color.g, color.b, 1.0), uFilterMatrix[2]);
                result.a = dot(vec4(color.r, color.g, color.b, 1.0), uFilterMatrix[3]);

                // Clamp values to [0, 1]
                result = clamp(result, 0.0, 1.0);

                // Special case for blur (simplified)
                if (uFilterMatrix[0][0] == 99.0) { // Arbitrary flag for blur
                    vec2 offset = vec2(0.005, 0.005);
                    vec4 sum = vec4(0.0);
                    sum += texture2D(uTexture, vTexCoord + offset);
                    sum += texture2D(uTexture, vTexCoord - offset);
                    sum += texture2D(uTexture, vTexCoord + vec2(-offset.x, offset.y));
                    sum += texture2D(uTexture, vTexCoord + vec2(offset.x, -offset.y));
                    sum += color;
                    result = sum / 5.0;
                }

                gl_FragColor = result;
            }
        """

        val vertexShader = loadShader(GLES20.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES20.GL_FRAGMENT_SHADER, fragmentShaderCode)

        program = GLES20.glCreateProgram().also {
            GLES20.glAttachShader(it, vertexShader)
            GLES20.glAttachShader(it, fragmentShader)
            GLES20.glLinkProgram(it)
        }

        // Get uniform locations for the matrix rows
        uFilterMatrixLocation[0] = GLES20.glGetUniformLocation(program, "uFilterMatrix[0]")
        uFilterMatrixLocation[1] = GLES20.glGetUniformLocation(program, "uFilterMatrix[1]")
        uFilterMatrixLocation[2] = GLES20.glGetUniformLocation(program, "uFilterMatrix[2]")
        uFilterMatrixLocation[3] = GLES20.glGetUniformLocation(program, "uFilterMatrix[3]")
    }

    fun draw(textureId: Int, vertexBuffer: FloatBuffer, texCoordBuffer: FloatBuffer) {
        GLES20.glUseProgram(program)

        val positionHandle = GLES20.glGetAttribLocation(program, "aPosition")
        GLES20.glEnableVertexAttribArray(positionHandle)
        GLES20.glVertexAttribPointer(positionHandle, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer)

        val texCoordHandle = GLES20.glGetAttribLocation(program, "aTexCoord")
        GLES20.glEnableVertexAttribArray(texCoordHandle)
        GLES20.glVertexAttribPointer(texCoordHandle, 2, GLES20.GL_FLOAT, false, 0, texCoordBuffer)

        // Set the filter matrix uniforms
        val matrix = getFilterMatrix(currentFilter)
        GLES20.glUniform4fv(uFilterMatrixLocation[0], 1, matrix, 0)
        GLES20.glUniform4fv(uFilterMatrixLocation[1], 1, matrix, 4)
        GLES20.glUniform4fv(uFilterMatrixLocation[2], 1, matrix, 8)
        GLES20.glUniform4fv(uFilterMatrixLocation[3], 1, matrix, 12)

        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        GLES20.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textureId)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4)

        GLES20.glDisableVertexAttribArray(positionHandle)
        GLES20.glDisableVertexAttribArray(texCoordHandle)
    }

    fun setFilter(filter: String) {
        currentFilter = when (filter.lowercase()) {
            "sepia", "vintage" -> 1  // Vintage uses the same matrix as Sepia
            "grayscale" -> 2
            "brightness" -> 3
            "contrast" -> 4
            "blur" -> 5
            "sharpen" -> 6
            "emboss" -> 7
            "saturation-decrease" -> 8
            "thermal" -> 9
            "highlight" -> 10
            else -> 0  // None/Default
        }
    }

    private fun getFilterMatrix(filterId: Int): FloatArray {
        return when (filterId) {
            1 -> floatArrayOf( // Sepia / Vintage
                0.393f, 0.769f, 0.189f, 0f,
                0.349f, 0.686f, 0.168f, 0f,
                0.272f, 0.534f, 0.131f, 0f,
                0f, 0f, 0f, 1f
            )
            2 -> floatArrayOf( // Grayscale
                0.2126f, 0.7152f, 0.0722f, 0f,
                0.2126f, 0.7152f, 0.0722f, 0f,
                0.2126f, 0.7152f, 0.0722f, 0f,
                0f, 0f, 0f, 1f
            )
            3 -> floatArrayOf( // Brightness
                1.2f, 0f, 0f, 0f,
                0f, 1.2f, 0f, 0f,
                0f, 0f, 1.2f, 0f,
                0f, 0f, 0f, 1f
            )
            4 -> floatArrayOf( // Contrast
                1.5f, 0f, 0f, 0f,
                0f, 1.5f, 0f, 0f,
                0f, 0f, 1.5f, 0f,
                0f, 0f, 0f, 1f
            )
            5 -> floatArrayOf( // Blur (special case flag)
                99f, 0f, 0f, 0f,  // 99 as a flag for blur in shader
                0f, 1f, 0f, 0f,
                0f, 0f, 1f, 0f,
                0f, 0f, 0f, 1f
            )
            6 -> floatArrayOf( // Sharpen
                1f, -1f, 1f, 0f,
                -1f, 5f, -1f, 0f,
                1f, -1f, 1f, 0f,
                0f, 0f, 0f, 1f
            )
            7 -> floatArrayOf( // Emboss
                -2f, -1f, 0f, 0f,
                -1f, 1f, 1f, 0f,
                0f, 1f, 2f, 0f,
                0f, 0f, 0f, 1f
            )
            8 -> floatArrayOf( // Saturation Decrease
                0.5f, 0f, 0f, 0f,
                0f, 0.5f, 0f, 0f,
                0f, 0f, 0.5f, 0f,
                0f, 0f, 0f, 1f
            )
            9 -> floatArrayOf( // Thermal
                1f, 0f, 0f, 0f,
                0f, 0.5f, 0f, 0f,
                0f, 0f, 0.5f, 0f,
                0f, 0f, 0f, 1f
            )
            10 -> floatArrayOf( // Highlight
                1f, 0f, 0f, 0f,
                0f, 1.5f, 0f, 0f,
                0f, 0f, 1.5f, 0f,
                0f, 0f, 0f, 1f
            )
            else -> floatArrayOf( // None/Default
                1f, 0f, 0f, 0f,
                0f, 1f, 0f, 0f,
                0f, 0f, 1f, 0f,
                0f, 0f, 0f, 1f
            )
        }
    }

    private fun loadShader(type: Int, code: String): Int {
        return GLES20.glCreateShader(type).also { shader ->
            GLES20.glShaderSource(shader, code)
            GLES20.glCompileShader(shader)
        }
    }

    fun release() {
        if (program != 0) {
            GLES20.glDeleteProgram(program)
            program = 0
        }
    }
}