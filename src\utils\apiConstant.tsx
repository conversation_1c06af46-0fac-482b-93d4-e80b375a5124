export const API = {
  BASE_URL: 'https://server.chatbucket.chat/api/web',

  login: '/users/auth/login',
  register: '/users/auth/signin',
  getProfile: '/users/auth/get-profile',
  verifyOtp: '/users/auth/verify',
  verifyUsername: '/users/auth/verify-username',
  updateContactEmail: '/users/auth/update-email-or-phone',
  verifyUpdateContactEmail: '/users/auth/verify-updated-email-or-phone-otp',
  setupProfile: '/users/auth/setup-profile',
  setNotificationToken: '/users/auth/set-notificationToken',
  getAppLanguageList: '/users/auth/get-all-languages',
  getUserAppLanguage: '/users/auth/get-user-languages',
  setAppLanguge: '/users/auth/update-languages',
  updateProfile: '/users/auth/update-profile',
  countryList: '/users/static/get-all-phone-codes',

  generate2FA: '/users/auth/regenerate-2FA',
  updatePin: '/users/auth/verify-pin',
  verify2FA: '/users/auth/verify-2fa',
  set2faModeUpdate: '/users/auth/set-2fa-mode',

  forgetPassword: '/users/auth/forgot-password',
  resetPassword: '/users/auth/reset-password',

  setContacts: '/users/auth/add-contacts',
  getContacts: '/users/auth/get-contacts',

  //chat setting
  getChatBackgrounds: '/users/static/chat-backgrounds',
  setChatSettings: '/users/auth/set-chat-setting',
  getChatSettings: '/users/auth/chat-setting',
  setUserWiseChatSettings: '/users/auth/set-userwise-chat-setting',
  getUserWiseChatSettings: '/users/auth/get-userwise-chat-setting',

  // status
  addStatus: '/users/status/add-status',
  getOtherUserStatus: '/users/status/get-status-other-user',
  getOwnStatus: '/users/status/get-status-own',
  like_dislike_status: '/users/status/like-dislike-status',

  // Channel
  deleteChannel: '/channel/delete/',
  createChannel: '/channel/add',
  updateChannel: '/channel/update',
  channelDetails: '/channel/details/',
  getExploreChannels: '/channel/list/',
  getOwnChannels: '/channel/list-followed',
  follow_unFollow: '/channel/follow-unfollow',
  remove_follower: '/channel/remove-follower',
  followers_list: '/channel/followers-list/',
  followers_details: '/channel/follower-details/',
  earning_list: '/channel/earnings/',
  message_list: 'channel/message-list/',
  update_user_location: '/users/auth/update-users-location',
  get_user_location: '/users/auth/get-users-location',
  update_user_location_setting: '/users/auth/update-users-location-setting',
  get_user_location_setting: '/users/auth/get-users-location-setting/',
  channel_post_save_list: '/channel/channel-saved-post-get-all/',
  channel_post_save: '/channel/channel-post-save',
  channel_post_remove: '/channel/channel-post-remove',
  get_channel_wise_chat_setting: '/channel/get-channelwise-chat-setting/',
  set_channel_wise_chat_setting: '/channel/set-channelwise-chat-setting/',
  get_users_location_profile: '/users/auth/get-users-location-profile/',

  // Date Format
  FORMAT_DATE: 'DD/MM/YYYY',
  FORMAT_TIME: 'hh:mm a',
  FORMAT_DATE_TIME: 'DD/MM/YYYY',
  FORMAT_DAY_TIME: 'ddd hh:mm a',
};

export const POST = 'POST';
export const GET = 'GET';
export const PATCH = 'PATCH';
export const DELETE = 'DELETE';
export const PUT = 'PUT';

export const GOOGLE_API_KEY = 'AIzaSyBBixSdj8L9FYlqMmiBFzj89WaZnzK4etY';
