import notifee, {
  AndroidCategory,
  AndroidForegroundServiceType,
  AndroidImportance,
  Event,
  EventType,
} from '@notifee/react-native';
import { DevMenu, Linking, Vibration } from 'react-native';
import Sound from 'react-native-sound';
import InCallManager from 'react-native-incall-manager';

import { incomingCallData } from '../../types/calls.types';
import { notificationIds, notificationChannels } from './createNotificationChannels';
import CallService from '../../service/CallService';
import { writeIncomingHistory } from '../../calls/calls.lib';
import { connectToSocketServer } from '../../socket-client/socket';
import { Socket } from 'socket.io-client';
import { callSocketEvents } from '../../socket-client/socketEvents';
const RINGTONE = require('./ringtone.mp3');

enum IncomingCallAction {
  ANSWER = 'answer',
  DECLINE = 'decline',
  DEFAULT = 'default',
}

enum NotificationType {
  SCREEN_CAPTURE = 'screen_capture',
  CALLS = 'calls',
}

let ringtoneSound: Sound | null = null;

// ------------------------
// Vibration Helpers
// ------------------------
export const startVibration = () => Vibration.vibrate([1000, 1000], true);
export const stopVibration = () => Vibration.cancel();

let currTimeout: NodeJS.Timeout | null = null;
// ------------------------
// Ringtone Helpers
// ------------------------
export const startIncomingCallRingtone = () => {
  startVibration();

  ringtoneSound = new Sound(RINGTONE, (error) => {
    if (error) {
      console.log('[Notif-Log] Failed to load sound', error);
      return;
    }
    ringtoneSound?.setNumberOfLoops(-1);
    ringtoneSound?.play((success) => {
      if (!success) {
        console.log('[Notif-Log] Playback failed due to decoding errors');
      }
    });
    currTimeout = setTimeout(() => {
      stopIncomingCallRingtone();
    }, 30 * 1000);
    console.log('[Notif-Log] Ringtone started');
  });
};

export const stopIncomingCallRingtone = () => {
  stopVibration();
  if (!ringtoneSound) return;

  ringtoneSound.stop(() => {
    ringtoneSound?.release();
    ringtoneSound = null;
  });
  if (currTimeout) clearTimeout(currTimeout);
  currTimeout = null;
  console.log('[Notif-Log] Ringtone stopped');
};

// ------------------------
// Foreground Service Helpers
// ------------------------
export async function showDisplayProjectionNotification(): Promise<void> {
  try {
    await notifee.displayNotification({
      id: notificationIds.screenCapture,
      title: 'Screen Capture',
      body: 'This notification will be here until you stop capturing.',
      android: {
        channelId: notificationChannels.screenCapture.id,
        asForegroundService: true,
        ongoing: true,
        smallIcon: 'react_native',
        autoCancel: false,
        foregroundServiceTypes: [
          AndroidForegroundServiceType.FOREGROUND_SERVICE_TYPE_CAMERA,
          AndroidForegroundServiceType.FOREGROUND_SERVICE_TYPE_MICROPHONE,
        ],
      },
    });
    console.log('[Notif-Log] Screen capture notification displayed');
  } catch (err) {
    console.log('[Notif-Log] Error showing screen capture notification:', err);
  }
}

export async function stopForegroundService(): Promise<void> {
  try {
    await notifee.stopForegroundService();
    console.log('[Notif-Log] Foreground service stopped');
  } catch (err) {
    console.log('[Notif-Log] Error stopping foreground service:', err);
  }
}

// ------------------------
// Incoming Call Notification
// ------------------------
export async function showIncomingCallNotification(payload: incomingCallData): Promise<void> {
  try {
    startIncomingCallRingtone();

    await notifee.displayNotification({
      title: `Incoming ${payload.callType} call`,
      body: `${payload.initiatorDetails.name} is calling...`,
      android: {
        channelId: notificationChannels.calls.id,
        smallIcon: 'react_native',
        autoCancel: true,
        ongoing: true,
        category: AndroidCategory.CALL,
        importance: AndroidImportance.HIGH,
        sound: 'default',
        vibrationPattern: [100, 200],
        fullScreenAction: { id: IncomingCallAction.ANSWER, launchActivity: 'default' },
        pressAction: { id: IncomingCallAction.DEFAULT, launchActivity: 'default' },
        actions: [
          {
            title: 'Decline',
            pressAction: { id: IncomingCallAction.DECLINE },
          },
        ],
      },
      data: {
        deep_link: `chatbucket://call/?roomId=${payload.roomId}&callerName=${payload.initiatorDetails.name}&callType=${payload.callType}&isGroupCall=${payload.isGroupCall}&callerId=${payload.callerId}`,
        // chatbucket://call/?roomId=839403093&callerName=Vamshi&callType=audio&isGroupCall=false&callerId=273838993
        channel: notificationChannels.calls.id,
        callId: payload.callId,
        callType: payload.callType,
        roomId: payload.roomId,
        initiatorDetails: payload.initiatorDetails,
        initiatorId: payload.callerId,
      },
    });

    writeIncomingHistory(payload);
    console.log('[Notif-Log] Incoming call notification displayed', payload);
    console.log('[Notif-Log] deep link', {
      deep_link: `chatbucket://call/?roomId=${payload.roomId}&callerName=${payload.initiatorDetails.name}&callType=${payload.callType}&isGroupCall=${payload.isGroupCall}&callerId=${payload.callerId}`,
    });
  } catch (err) {
    console.log('[Notif-Log] Error showing incoming call notification:', err);
  }
}

// ------------------------
// Handle Interaction
// ------------------------
export function handleIncomingCallInteraction(event: Event): void {
  if (event.type === EventType.DELIVERED) {
    return;
  }

  stopIncomingCallRingtone();

  switch (event.type) {
    case EventType.ACTION_PRESS:
      switch (event.detail.pressAction?.id) {
        case IncomingCallAction.ANSWER:
          console.log('[Notif-Log] Answering call...');
          break;
        case IncomingCallAction.DECLINE:
          declineCall(event);
          break;
        default:
          console.log('[Notif-Log] Unknown action pressed:', event.detail.pressAction?.id);
      }
      break;
    case EventType.DISMISSED:
      console.log('[Notif-Log] Notification dismissed');
      break;
    case EventType.PRESS:
      console.log('[Notif-Log] Notification pressed');
      console.log('[Notif-Log] Notification pressed', event.detail.notification?.data);
      console.log('[Notif-Log] Notification pressed', event.detail.notification?.data?.deep_link);
      const deepLink = event.detail.notification?.data?.deep_link;
      if (deepLink && typeof deepLink === 'string') {
        Linking.openURL(deepLink).catch((err) =>
          console.log('[Notif-Log] Failed to open deep link:', err),
        );
      }
      break;
    default:
      console.log('[Notif-Log] Unhandled notification event:', event.type);
  }
}

// decline call with retry.create a socket instance just to decline call.
const RETRY_COUNT = 3;
const RETRY_DELAY_MS = 1000; // 1 second between retries
let socket: Socket | null = null;

async function declineCall(event: Event) {
  console.log('[Notif-Log] Declining call...', event.detail.notification?.data);

  const data = event.detail.notification?.data as { roomId?: string } | undefined;
  const roomId = data?.roomId;
  if (!roomId) return console.log('[Notif-Log] No roomId found, cannot decline call');

  let attempts = 0;

  const tryDecline = async () => {
    attempts++;
    try {
      if (!socket) {
        socket = await connectToSocketServer();
      }
      socket.emit(callSocketEvents.REJECT_CALL, { roomId }, () => {
        console.log('[Notif-Log] Call declined successfully');
        socket = null;
      });
    } catch (err) {
      console.log(`[Notif-Log] Attempt ${attempts} failed to decline call:`, err);
      if (attempts < RETRY_COUNT) {
        setTimeout(tryDecline, RETRY_DELAY_MS);
      } else {
        console.log('[Notif-Log] Max retry attempts reached, giving up');
        socket = null;
      }
    }
  };

  tryDecline();
}
