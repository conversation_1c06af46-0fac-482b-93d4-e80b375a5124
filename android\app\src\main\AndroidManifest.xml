<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.chatbucket.app">


    <!-- Optional features (hardware) -->
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature android:name="android.hardware.microphone" />

    <!-- Required permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- For Android 12 and below -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"/>

    <!-- For Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage"/>

    <!-- Required permissions for location -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!-- Optional, for background location -->

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />

    <!-- Screen capture -->
    <uses-permission android:name="android.permission.MANAGE_MEDIA_PROJECTION" />

    <!-- Notification permissions (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:mimeType="*/*" />
        </intent>
    </queries>


<!-- AIzaSyAEvaTaqsXyI5ozkcPTB9KY0Y-DNEwoHsc chatbucket old project map api keyn(android)-->
<!-- AIzaSyCOd90DxC2bNdEO9RkYSrpQSa8kcnee66I chatbucket old project map api keyn(android)-->
    <application 
        android:name=".MainApplication" 
        android:label="@string/app_name" 
        android:icon="@mipmap/ic_launcher" 
        android:roundIcon="@mipmap/ic_launcher_round" 
        android:allowBackup="false" 
        android:theme="@style/AppTheme" 
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        
    >
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCTvXrLfe6xv5nkJnNuXOdw3CYvYF60rMc"
        />
        <!-- <uses-library android:name="org.apache.http.legacy" android:required="false"/> -->
  <activity
    android:name=".MainActivity"
    android:label="@string/app_name"
    android:launchMode="singleTask"
    android:exported="true"
    android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
    android:windowSoftInputMode="adjustNothing"
    android:supportsPictureInPicture="true"
    android:resizeableActivity="true">

    

    <!-- Launcher -->
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>

    <!-- Deep link for incoming call -->
    <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="chatbucket" android:host="call"  />
    </intent-filter>

    <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" android:value="default" />
</activity>

<activity
          android:name="com.yalantis.ucrop.UCropActivity"
          android:screenOrientation="portrait"
          android:theme="@style/Theme.App.UCrop"
          tools:replace="android:theme" />
       

        <!-- Notifee foreground service -->
        <service android:name="app.notifee.core.ForegroundService" android:exported="false" android:foregroundServiceType="mediaProjection|camera|microphone" />

    </application>

</manifest>
