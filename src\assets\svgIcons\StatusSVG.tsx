import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";
import { colors } from "../../theme/colors";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const StatusSVG: React.FC<IconProps> = ({
    size = 17,
    color = colors.black_23,
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 17 17"
            fill="none"
            {...restProps}
        >
            <Path
                d="M8.237 13.327A4.747 4.747 0 013.46 8.551a4.747 4.747 0 014.777-4.777 4.747 4.747 0 014.776 4.777 4.747 4.747 0 01-4.776 4.776zm0-8.188A3.379 3.379 0 004.825 8.55a3.379 3.379 0 003.412 3.412 3.379 3.379 0 003.412-3.412 3.379 3.379 0 00-3.412-3.412zm.136 11.6c-2.934 0-5.937-1.57-7.233-4.094-.205-.341-.069-.75.273-.955.34-.205.75-.069.955.273.887 1.706 2.593 2.866 4.64 3.275 2.047.41 4.026-.068 5.528-1.365.273-.273.75-.204.955.069.273.272.205.75-.068.955-1.433 1.228-3.207 1.842-5.05 1.842zm6.346-3.753c-.546 0-.887-.614-.614-1.024 1.024-1.637 1.16-3.684.478-5.663-.683-1.98-2.116-3.412-3.89-4.095-.341-.136-.546-.546-.41-.887.137-.341.547-.546.888-.41 2.184.751 3.89 2.594 4.708 4.914.82 2.32.615 4.845-.614 6.824-.068.205-.273.341-.546.341zM.8 10.393c-.342 0-.615-.205-.683-.546-.41-2.32.273-4.708 1.91-6.62C3.666 1.386 5.917.363 8.237.363c.41 0 .683.341.683.682 0 .41-.41.75-.819.683-1.91 0-3.753.887-5.05 2.388-1.296 1.57-1.91 3.548-1.57 5.46.07.409-.204.818-.682.818z"
                fill={color}
            />
        </Svg>
    );
};

export default StatusSVG;

