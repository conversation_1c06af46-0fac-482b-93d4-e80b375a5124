import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";


interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}



function DeleteSVG({ size = 20, color = "#fff", ...restProps }: IconProps) {


    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 20 21"
            fill="none"
            {...restProps}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.973 0h3.566c.228 0 .427 0 .615.03a2.373 2.373 0 011.73 1.247c.088.169.151.357.223.574l.118.353.03.09a1.318 1.318 0 001.302.87h3.164a.791.791 0 110 1.582H.79a.791.791 0 010-1.582H4.05a1.318 1.318 0 001.237-.96l.118-.353c.072-.217.135-.405.222-.574A2.373 2.373 0 017.358.03C7.546 0 7.745 0 7.973 0zM6.6 3.164a2.908 2.908 0 00.185-.451l.105-.316c.096-.288.119-.347.14-.39a.791.791 0 01.577-.415c.047-.008.11-.01.414-.01h3.47c.304 0 .367.002.414.************.194.576.416.*************.14.39l.106.315.04.12c.**************.145.331H6.6z"
                fill={color}
            />
            <Path
                d="M3.338 6.54a.791.791 0 10-1.578.104l.488 7.332c.09 1.353.163 2.446.334 3.304.178.891.48 1.636 1.104 2.22.624.583 1.387.835 2.288.953.867.113 1.962.113 3.318.113h.927c1.356 0 2.451 0 3.319-.113.9-.118 1.664-.37 2.288-.953.624-.584.926-1.329 1.104-2.22.17-.858.243-1.95.333-3.304l.49-7.332a.791.791 0 00-1.58-.105l-.484 7.277c-.095 1.421-.163 2.41-.311 3.155-.144.721-.345 1.104-.633 1.373-.288.27-.683.445-1.413.54-.752.099-1.744.1-3.168.1h-.816c-1.425 0-2.416-.001-3.168-.1-.73-.095-1.125-.27-1.413-.54-.289-.27-.49-.652-.633-1.373-.148-.745-.216-1.734-.31-3.155l-.486-7.277z"
                fill={color}
            />
            <Path
                d="M7.04 8.441a.791.791 0 01.866.709l.528 5.273a.791.791 0 11-1.575.158l-.527-5.274a.791.791 0 01.708-.866zM12.471 8.441a.791.791 0 01.709.866l-.528 5.274a.79.79 0 11-1.574-.158l.527-5.273a.791.791 0 01.866-.709z"
                fill={color}
            />
        </Svg>
    );
}

export default DeleteSVG;
