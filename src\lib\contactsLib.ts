import { PermissionsAndroid, Platform } from 'react-native';
import Contacts, { Contact } from 'react-native-contacts';
import { formatToE164 } from './lib';
const MIN_PHONE_NUMBER_LENGTH = 5;

const loadAndroidContacts = async () => {
  try {
    // Request permission to access contacts
    const permission = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
      {
        title: 'Contacts Permission',
        message: 'A<PERSON> needs access to your contacts.',
        buttonPositive: 'OK',
      },
    );

    // If not granted, return an empty array
    if (permission !== PermissionsAndroid.RESULTS.GRANTED) {
      return [];
    }

    const data = await Contacts.getAll();
    return data;
    // Load contacts using the Contacts API
    return await Contacts.getAll();
  } catch (error) {
    console.error('Error loading contacts:', error);
    return [];
  }
};

export const loadIosContacts = async () => {
  try {
    // Request permission to access contacts
    const permission = await Contacts.requestPermission();
    if (permission !== 'authorized') {
      return [];
    }
    // Load contacts using the Contacts API
    return await Contacts.getAll();
  } catch (error) {
    console.error('Error loading contacts:', error);
    return [];
  }
};

export const loadContacts = async () => {
  try {
    const isAndroid = Platform.OS === 'android';
    if (isAndroid) {
      return await loadAndroidContacts();
    } else {
      return await loadIosContacts();
    }
  } catch (error) {
    console.error('Error loading contacts:', error);
    return [];
  }
};

function getMobileNumberWithCountry(contact: Contact, countryCode = '91') {
  if (contact && Array.isArray(contact.phoneNumbers) && contact.phoneNumbers.length > 0) {
    let cleanedNumber = contact.phoneNumbers.find((p) => !!p.number)?.number?.replace(/\D/g, '');
    if (cleanedNumber && cleanedNumber.length >= MIN_PHONE_NUMBER_LENGTH) {
      // Add country code if it's not already present
      if (!cleanedNumber.startsWith(countryCode)) {
        cleanedNumber = countryCode + cleanedNumber;
      }

      return cleanedNumber;
    }
  }

  return null;
}

export const getFormattedContacts = async () => {
  try {
    const contacts = await loadContacts();
    const skippedContacts: { name: string | null; rawNumber: string }[] = [];

    const formattedContacts = contacts
      .map((contact) => {
        const phoneNumber = contact.phoneNumbers[0]?.number;
        const formatted = getMobileNumberWithCountry(contact);

        if (!formatted) {
          skippedContacts.push({
            name: contact.displayName,
            rawNumber: phoneNumber,
          });
          return null;
        }

        return {
          name: contact.displayName,
          phoneNumber: formatted,
        };
      })
      .filter((c): c is { name: string; phoneNumber: string } => c !== null);

    return formattedContacts;
  } catch (err) {
    console.error('Error in getFormattedContacts:', err);
    return [];
  }
};
