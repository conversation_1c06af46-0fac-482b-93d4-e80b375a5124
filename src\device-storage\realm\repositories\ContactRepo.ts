import { Realm } from 'realm';
import { realmSchemaNames } from '../schemas/schemaNames';
import { ContactSchema, IContact } from '../schemas/ContactSchema';
import { RemoteUser } from '../../../types/index.types';
import { safeRealmWrite } from '../lib';

export class ContactRepo {
  private static schemaName = realmSchemaNames.contact;
  constructor() {}
  static findByUserId(realm: Realm, userId: string): IContact | undefined {
    return realm
      .objects(this.schemaName)
      .filtered('userId == $0', userId)[0] as unknown as IContact;
  }

  static findByPhoneNumber(realm: Realm, phoneNumber: string) {
    return realm.objects(this.schemaName).filtered('phoneNumber == $0', phoneNumber)[0];
  }
  static findAll(realm: Realm) {
    return realm.objects(this.schemaName);
  }

  static findRealmObjectByUserId(realm: Realm, userId: string) {
    return realm.objects(ContactSchema).filtered('userId == $0', userId)[0];
  }

  static deleteByPhoneNumber(realm: Realm, phoneNumber: string) {
    const contact = realm.objectForPrimaryKey(ContactSchema, phoneNumber);
    if (contact) {
      safeRealmWrite(realm, () => realm.delete(contact));
    }
  }
}
