# Chats

## Tech Stack

- **RealmDB**: Local database for offline-first storage of contacts, conversations, and messages.
- **WebSocket**: Persistent real-time communication channel with the server.

## Database Schema

### Contacts

Maps device contacts to app users for invites, mentions, and profile displays.

| Field        | Type    | Description                           |
| ------------ | ------- | ------------------------------------- |
| phoneNumber  | string  | Primary identifier; raw phone number. |
| name         | string  | Contact display name.                 |
| username     | string? | App username if registered.           |
| image        | string? | Profile image URL.                    |
| bio          | string? | App profile bio.                      |
| isRegistered | boolean | Indicates if contact is an app user.  |
| userId       | string? | App user ID if registered.            |

**Purpose**: Maps phone numbers to app users, enables invites and profile displays.

### Conversations

Represents chat threads (P2P, group, or channel) for home screen and chat list rendering.

| Field                | Type             | Description                                          |
| -------------------- | ---------------- | ---------------------------------------------------- |
| id                   | string           | Unique conversation ID (receiver’s user ID for P2P). |
| type                 | ConversationType | p2p, group, or channel.                              |
| lastMessage          | MessageSchema?   | Embedded latest message.                             |
| lastMessageTimestamp | number?          | Timestamp for sorting chats.                         |
| unreadCount          | number           | Count of unread messages.                            |
| isMuted              | boolean?         | Whether notifications are muted.                     |
| isPinned             | boolean?         | Whether chat is pinned.                              |
| isArchived           | boolean?         | Whether chat is archived.                            |
| isActive             | boolean?         | Whether receiver is online.                          |
| displayName          | string           | Name of user, group, or channel.                     |
| displayPic           | string?          | Display picture.                                     |
| phoneNumber          | string?          | For P2P: other user’s number.                        |
| createdAt            | number           | Creation timestamp.                                  |
| updatedAt            | number           | Last updated timestamp.                              |
| createdBy            | string?          | Creator’s user ID (for group/channel).               |

**Purpose**: Drives chat list UI, manages chat metadata (mute, pin, archive).

### Messages

Holds message data for each conversation.

| Field            | Type              | Description                                                |
| ---------------- | ----------------- | ---------------------------------------------------------- |
| globalId         | string?           | Server-assigned ID for sync.                               |
| receiverId       | string            | Receiver user or group ID.                                 |
| senderId         | string            | Sender user ID.                                            |
| localId          | string            | Local message ID (primary key).                            |
| conversationType | ConversationType  | P2P, group, or channel type.                               |
| conversationId   | string            | Conversation ID.                                           |
| status           | MessageStatus     | Message state (pending, sent, delivered, seen, scheduled). |
| messageType      | MessageType       | Message format (text, image, etc).                         |
| text             | string?           | Text content.                                              |
| mediaUrl         | string?           | Media resource URL.                                        |
| location         | ILocationMessage? | Location payload if present.                               |
| contact          | IContactMessage?  | Contact payload if present.                                |
| replyToMessageId | string?           | ID of message being replied to.                            |
| deliveredAt      | number?           | Delivery timestamp.                                        |
| seenAt           | number?           | Seen timestamp.                                            |
| isPinned         | boolean?          | Whether message is pinned.                                 |
| isSilent         | boolean?          | Whether sent without notification.                         |
| isSeenByMe       | boolean?          | Whether receiver has seen (sender perspective).            |
| createdAt        | number            | Creation timestamp.                                        |
| updatedAt        | number            | Last update timestamp.                                     |
| scheduledAt      | number?           | Scheduled send timestamp.                                  |

**Purpose**: Stores message data for display and sync, supports offline composition and status tracking.

## Table Rationale

- **Contacts**: Maps device contacts to app users for invites and profiles.
- **Conversations**: Enables fast retrieval of chat metadata for UI.
- **Messages**: Handles message data, status, and sync logic efficiently.

## Data Flows

### Pending Message Flow

1. Listener watches Messages table for `status = pending`.
2. On insert, emits message via WebSocket.
3. On server ACK, updates:
   - `globalId`
   - `status = sent`
   - `updatedAt = now`

### Message Send Flow

1. **Prepare conversation data**:
   - If new: Fetch contact info, create conversation, set `lastMessage`.
   - If existing: Update `lastMessage`.
2. **Prepare message payload**:
   - Build message with `status = pending`.
   - Include `replyToMessageId`, `scheduledAt`, or `globalId` if applicable.
3. **Write to DB**:
   - Open Realm write transaction.
   - Insert/update conversation + message.
   - Commit.
4. Trigger pending message listener to send.

### Message Receive Flow

1. **Socket listener**:
   - Listens for incoming message event, parses payload.
2. **Deletion check**:
   - If `isDeleted`, delete message locally; update `lastMessage` if needed.
3. **Prepare conversation data**:
   - If exists: Update `lastMessage`.
   - If new: Fetch sender info or use contact, create conversation.
4. **Prepare message data**:
   - If exists: Update content, status, `deliveredAt`, `seenAt`.
   - If new: Create message entry.
5. **DB write**:
   - Open Realm transaction.
   - Insert/update conversation + message.
   - Commit.
6. **Emit ACK**:
   - Send delivered ACK for new messages.
7. **Seen event**:
   - Emit seen event when user views message.
