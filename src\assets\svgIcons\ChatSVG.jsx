import * as React from "react"
import Svg, { Path } from "react-native-svg"
import { colors } from "../../theme/colors"

function ChatSVG({ size = 23, color = colors.black_23, ...props }) {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 23 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M1 21.916l-.53-.53a.75.75 0 00.53 1.28v-.75zm3.063-3.063l.53.53a.75.75 0 000-1.06l-.53.53zm2.747-5.82a.75.75 0 000 1.5v-1.5zm4.648 1.5a.75.75 0 000-1.5v1.5zM6.81 8.383a.75.75 0 000 1.5v-1.5zm9.296 1.5a.75.75 0 000-1.5v1.5zm5.06 1.574c0 5.362-4.346 9.708-9.708 9.708v1.5c6.19 0 11.208-5.018 11.208-11.208h-1.5zm-19.416 0c0-5.361 4.347-9.708 9.708-9.708V.25C5.268.25.25 5.268.25 11.458h1.5zm9.708-9.708c5.362 0 9.708 4.347 9.708 9.708h1.5C22.666 5.268 17.648.25 11.458.25v1.5zm0 19.416H1v1.5h10.458v-1.5zm-6.865-2.843a9.676 9.676 0 01-2.843-6.865H.25c0 3.095 1.255 5.898 3.283 7.926l1.06-1.061zM1.53 22.447l3.063-3.063-1.06-1.061L.47 21.386l1.06 1.06zm5.28-7.915h4.648v-1.5H6.81v1.5zm0-4.648h9.296v-1.5H6.81v1.5z"
                fill={color}
            />
        </Svg>
    )
}

export default ChatSVG

