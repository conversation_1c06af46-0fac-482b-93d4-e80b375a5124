import React, { useCallback, useEffect, useState } from 'react';
import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import { languages } from '../../../../lib/Languages';
import { useMe } from '../../../../hooks/util/useMe';
import StreamToggleOption from '../../../Home/Channels/components/StreamToggle';
import MessageSVG from '../../../../assets/svgIcons/ChatSVG';
import VoiceSVG from '../../../../assets/svgIcons/MicSVG2';
import MediaSVG from '../../../../assets/svgIcons/GallerySVG';

interface Language {
  id: string;
  symbol: string;
  code: string;
  s2stSupported: boolean;
}

const TranslateLanguageScreen = () => {
  const navigation = useNavigation();
  const { userPreferencesState } = useMe();

  const [s2stSupportedLanguages, setS2stSupportedLanguages] = useState<Language[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Get current translate preferences
  const currentTranslatePrefs = userPreferencesState.userPreferences?.chats?.translateLanguage;

  const [translateToggles, setTranslateToggles] = useState({
    textMessageEnabled: currentTranslatePrefs?.textMessageEnabled ?? false,
    voiceMessageEnabled: currentTranslatePrefs?.voiceMessageEnabled ?? false,
    mediaEnabled: currentTranslatePrefs?.mediaEnabled ?? false,
  });

  const filterS2stSupportedLanguages = useCallback(() => {
    const filtered = languages.filter((lang) => lang.s2stSupported);
    setS2stSupportedLanguages(filtered);
  }, []);

  useEffect(() => {
    filterS2stSupportedLanguages();
    const fetchTranslateLanguage = async () => {
      try {
        const currentLangId = currentTranslatePrefs?.languageId;
        if (currentLangId) {
          setSelectedLanguage(currentLangId);
        } else {
          setSelectedLanguage('eng');
          await updateTranslateLanguage('eng');
        }
      } catch (error) {
        console.error('Error fetching translate language:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchTranslateLanguage();
  }, [filterS2stSupportedLanguages, currentTranslatePrefs]);

  const updateTranslateLanguage = async (langCode: string) => {
    try {
      setSelectedLanguage(langCode);
      userPreferencesState.updatePreferences('chats', {
        translateLanguage: {
          textMessageEnabled: currentTranslatePrefs?.textMessageEnabled ?? false,
          voiceMessageEnabled: currentTranslatePrefs?.voiceMessageEnabled ?? false,
          mediaEnabled: currentTranslatePrefs?.mediaEnabled ?? false,
          languageId: langCode,
        },
      });
    } catch (error) {
      console.error('Error setting translate language:', error);
    }
  };

  const handleToggleChange = (toggleKey: keyof typeof translateToggles) => {
    const newToggles = {
      ...translateToggles,
      [toggleKey]: !translateToggles[toggleKey],
    };

    setTranslateToggles(newToggles);

    userPreferencesState.updatePreferences('chats', {
      translateLanguage: {
        ...currentTranslatePrefs,
        ...newToggles,
        languageId: selectedLanguage || 'eng',
      },
    });
  };

  const renderLanguageItem = useCallback(
    ({ item }: { item: Language }) => {
      const isSelected = selectedLanguage === item.code;
      return (
        <TouchableOpacity
          style={styles.languageOptionRow}
          onPress={() => updateTranslateLanguage(item.code)}
          activeOpacity={0.7}
        >
          <View style={styles.languageLeft}>
            <Text style={styles.symbol}>{item.symbol}</Text>
            <Text style={[styles.language, isSelected && { color: colors.mainPurple }]}>
              {item.id}
            </Text>
          </View>
          {isSelected && (
            <View style={styles.radioOuter}>
              <View style={styles.radioInner} />
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [selectedLanguage, updateTranslateLanguage],
  );

  const renderToggleSection = () => (
    <View style={styles.sectionContainer}>
      <StreamToggleOption
        label="Text Messages"
        value={translateToggles.textMessageEnabled}
        onToggle={() => handleToggleChange('textMessageEnabled')}
        icon={<MessageSVG size={18} color={colors.black_23} />}
      />

      <StreamToggleOption
        label="Voice Messages"
        value={translateToggles.voiceMessageEnabled}
        onToggle={() => handleToggleChange('voiceMessageEnabled')}
        icon={<VoiceSVG size={18} color={colors.black_23} />}
      />

      <StreamToggleOption
        label="Media Messages"
        value={translateToggles.mediaEnabled}
        onToggle={() => handleToggleChange('mediaEnabled')}
        icon={<MediaSVG size={18} color={colors.black_23} />}
      />
    </View>
  );

  const renderLanguageSection = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Select Language</Text>
      <FlatList
        data={s2stSupportedLanguages}
        keyExtractor={(item) => item.code}
        renderItem={renderLanguageItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.languageListContainer}
      />
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <HeaderBackWithTitle title="Translate Language" onBack={() => navigation.goBack()} />
        <View style={styles.whiteContainer}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.mainPurple} />
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Translate Language" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        {renderToggleSection()}
        {renderLanguageSection()}
      </View>
    </SafeAreaView>
  );
};

export default TranslateLanguageScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
    paddingHorizontal: hp(2),
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sectionContainer: {
    marginBottom: hp(4),
  },
  sectionTitle: {
    ...commonFontStyle(200, 18, colors.black_23),
    marginBottom: hp(2),
    paddingHorizontal: hp(1),
  },
  languageListContainer: {
    paddingBottom: hp(30),
  },
  languageOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    backgroundColor: colors.white,
  },
  languageLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  symbol: {
    fontSize: 22,
    marginRight: 12,
  },
  language: {
    fontSize: 16,
    color: colors.black_23,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.black_23,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.mainPurple,
  },
});
