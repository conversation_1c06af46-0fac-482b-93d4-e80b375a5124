import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
    size?: number;
    color?: string;
}

const PollsSVG: React.FC<SvgComponentProps & React.ComponentProps<typeof Svg>> = ({
    size = 20,
    color = "#B9B9B9",
    ...props
}) => {


    return (
        <Svg
            width={(size / 20) * 17}
            height={size}
            viewBox="0 0 17 20"
            fill="none"
            {...props}
        >
            <Path
                d="M2.285 6.502A2.287 2.287 0 014.57 8.787v8.426a2.287 2.287 0 01-2.285 2.285A2.287 2.287 0 010 17.213V8.787a2.282 2.282 0 012.285-2.285zM8.5 0a2.287 2.287 0 012.285 2.285v14.927A2.287 2.287 0 018.5 19.495a2.287 2.287 0 01-2.285-2.284V2.285A2.287 2.287 0 018.5 0zM14.715 12.996A2.287 2.287 0 0117 15.281v1.93a2.287 2.287 0 01-2.285 2.285 2.287 2.287 0 01-2.285-2.285v-1.93a2.287 2.287 0 012.285-2.285z"
                fill={color}
            />
        </Svg>
    );
};

export default PollsSVG;


