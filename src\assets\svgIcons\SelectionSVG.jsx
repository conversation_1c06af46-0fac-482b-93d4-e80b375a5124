import * as React from "react";
import Svg, { Circle, Path } from "react-native-svg";
import { colors } from "../../theme/colors";

function SelectionSVG({ size = 24, isSelected = false, selectColor = colors.mainPurple, unselectColor = colors.gray_80 }) {
    return (
        <>{
            isSelected ?
                <Svg
                    width={size}
                    height={size}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <Circle cx={12} cy={12} r={12} fill={selectColor} />
                    <Path
                        d="M16.22 7.85a.671.671 0 01.844.087l.085.105a.67.67 0 01-.083.838h.001L10.8 15.418l-.004.005-.001-.001a.67.67 0 01-.843.086l-.105-.086-2.91-2.91a.67.67 0 010-.947l.106-.087a.67.67 0 01.843.086l2.43 2.431 5.796-6.054.004-.003.105-.086z"
                        fill="#fff"
                        stroke="#fff"
                        strokeWidth={0.52}
                    />
                </Svg> :
                <Svg
                    width={size}
                    height={size}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <Circle cx={12} cy={12} r={11.25} stroke={unselectColor} strokeWidth={1.5} />
                </Svg>
        }
        </>
    );
}

export default SelectionSVG;
