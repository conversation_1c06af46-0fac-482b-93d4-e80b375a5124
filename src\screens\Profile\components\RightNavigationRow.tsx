import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import RightArrowSVG from '../../../assets/svgIcons/RightArrowSVG';
import { navigateTo } from '../../../utils/commonFunction';

interface RightNavigationRowProps {
  text: string;
  onPress?: () => void;
  screenName?: string;
  icon?: React.ReactNode;
  showRightArrow?: boolean;
}

const RightNavigationRow: React.FC<RightNavigationRowProps> = ({
  text,
  onPress,
  screenName,
  icon,
  showRightArrow = true,
}) => {
  return (
    <TouchableOpacity
      style={styles.settingRow}
      onPress={onPress || (screenName ? () => navigateTo(screenName) : undefined)}
    >
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={styles.rowText}>{text}</Text>
      {showRightArrow && <RightArrowSVG width={12} height={12} color={colors.black_23} />}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 4,
  },
  iconContainer: {
    width: 20,
    height: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rowText: {
    flex: 1,
    ...commonFontStyle(400, 16, colors.black_23),
  },
});

export default RightNavigationRow;
