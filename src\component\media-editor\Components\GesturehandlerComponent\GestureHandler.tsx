import React, { useEffect, useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming,
} from 'react-native-reanimated';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import { useData } from '../../utils/Context';
import { hp } from '../../utils/Constants/dimensionUtils';

interface GestureHandlerProps {
  id: number | string;
  type: 'image' | 'sticker' | 'text';
  children?: React.ReactNode;
  index: number;
  onSelectImage?: (index: number) => void;
  onSelectSticker?: (index: number) => void;
  onSelectText?: (index: number) => void;
  animatedStyleProps?: object;
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number | string, type: string, value: boolean) => boolean;
  deleteItem: (id: number | string, type: string) => void;
  zIndex?: number;
  yPos: number;
  xPos: number;
  rotate: number;
  scale: number;
  isOkayPressed?: boolean;
  topItemzIdx?: { zIndex: number };
  textProps?: {
    text: string;
    color: string;
    fontSize: number;
    fontFamily: string;
    fontWeight: 'normal' | 'bold' | '500';
  };
}

const GestureHandler: React.FC<GestureHandlerProps> = ({
  id,
  type,
  children,
  index,
  onSelectImage = () => {},
  onSelectSticker = () => {},
  onSelectText = () => {},
  animatedStyleProps = {},
  itemDragging,
  isDeletable,
  deleteItem,
  zIndex = 0,
  yPos,
  xPos,
  rotate,
  scale: scl,
  topItemzIdx,
  textProps,
  isOkayPressed,
}) => {
  const { setMemorizedPosition } = useData();

  // Shared values for transforms
  const rotation = useSharedValue(rotate || 0);
  const savedRotation = useSharedValue(rotate || 0);
  const rotationContext = useSharedValue(rotate || 0);
  const translateX = useSharedValue(xPos || 0);
  const translateY = useSharedValue(yPos || 0);
  const panStartX = useSharedValue(0);
  const panStartY = useSharedValue(0);
  const context = useSharedValue({ x: 0, y: 0 });
  const scale = useSharedValue(scl || 1);
  const savedScale = useSharedValue(scl || 1);
  const scaleContext = useSharedValue(scl || 1);
  const scaleStart = useSharedValue(scl || 1);
  const activeCorner = useSharedValue<string | null>(null);

  // Pre-calculate text dimensions to avoid layout recalculations
  const textDimensions = useMemo(() => {
    if (type !== 'text' || !textProps) return { width: 0, height: 0 };

    const text = textProps.text || '';
    const fontSize = textProps.fontSize || 16;

    // More accurate text dimension calculation
    const avgCharWidth = fontSize * 0.6;
    const lines = text.split('\n');
    const maxLineLength = Math.max(...lines.map((line) => line.length));

    return {
      width: maxLineLength * avgCharWidth,
      height: fontSize * 1.2 * lines.length,
    };
  }, [textProps, type]);

  useEffect(() => {
    // Use withTiming for smoother updates
    translateX.value = withTiming(xPos, { duration: 100 });
    translateY.value = withTiming(yPos, { duration: 100 });
    rotation.value = withTiming(rotate, { duration: 100 });
    scale.value = withTiming(scl, { duration: 100 });

    // Update saved values immediately to prevent conflicts
    savedRotation.value = rotate;
    savedScale.value = scl;
  }, [xPos, yPos, rotate, scl]);

  const tapGesture = Gesture.Tap()
    .maxDuration(250)
    .onEnd(() => {
      if (type === 'image') runOnJS(onSelectImage)(index);
      else if (type === 'sticker') runOnJS(onSelectSticker)(index);
      else if (type === 'text') runOnJS(onSelectText)(index);
    });

  let lastDeleteAreaCall = 0;
  const THROTTLE_MS = 16; // Reduced throttle for smoother feedback

  const onDeleteArea = (x: number, y: number) => {
    const now = Date.now();
    if (now - lastDeleteAreaCall < THROTTLE_MS) return false;
    lastDeleteAreaCall = now;

    if ((y > 200 && x > -114 && x < 114) || y > 352) {
      return runOnJS(isDeletable)(id, type, true);
    } else {
      return runOnJS(isDeletable)(id, type, false);
    }
  };

  const returnStatus = (value: boolean) => itemDragging(value);

  const panGesture = Gesture.Pan()
    .minDistance(1)
    .onStart((event) => {
      runOnJS(returnStatus)(true);
      runOnJS(setMemorizedPosition)({ type, id, zIndex: (topItemzIdx?.zIndex || 0) + 1 });
      context.value = { x: translateX.value, y: translateY.value };
      panStartX.value = event.translationX;
      panStartY.value = event.translationY;
    })
    .onUpdate((event) => {
      translateX.value = context.value.x + (event.translationX - panStartX.value);
      translateY.value = context.value.y + (event.translationY - panStartY.value);
      runOnJS(onDeleteArea)(translateX.value, translateY.value);
    })
    .onEnd(() => {
      runOnJS(returnStatus)(false);
      const finalX = translateX.value;
      const finalY = translateY.value;
      const isInDeleteArea = (finalY > 200 && finalX > -114 && finalX < 114) || finalY > 352;
      if (isInDeleteArea) {
        runOnJS(deleteItem)(id, type);
      } else {
        runOnJS(setMemorizedPosition)({ type, id, x: finalX, y: finalY });
      }
    });

  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      scaleContext.value = savedScale.value;
    })
    .onUpdate((event) => {
      const newScale = Math.max(0.5, Math.min(3, scaleContext.value * event.scale));
      scale.value = newScale;
    })
    .onEnd(() => {
      savedScale.value = scale.value;
      runOnJS(setMemorizedPosition)({ type, id, scale: savedScale.value });
    });

  const rotationGesture = Gesture.Rotation()
    .onStart(() => {
      rotationContext.value = savedRotation.value;
    })
    .onUpdate((event) => {
      rotation.value = rotationContext.value + event.rotation;
    })
    .onEnd(() => {
      savedRotation.value = rotation.value;
      runOnJS(setMemorizedPosition)({ type, id, rotation: savedRotation.value });
    });

  const createResizeGesture = (corner: string) =>
    Gesture.Pan()
      .onStart(() => {
        activeCorner.value = corner;
        scaleStart.value = scale.value;
      })
      .onUpdate((event) => {
        if (activeCorner.value !== corner) return;
        let dx = 0;
        let dy = 0;

        switch (corner) {
          case 'bottomLeft':
            dx = -event.translationX;
            dy = event.translationY;
            break;
          case 'bottomRight':
            dx = event.translationX;
            dy = event.translationY;
            break;
          case 'topLeft':
            dx = -event.translationX;
            dy = -event.translationY;
            break;
          case 'topRight':
            dx = event.translationX;
            dy = -event.translationY;
            break;
        }

        const scaleFactor = (dx + dy) * 0.005; // Reduced sensitivity for smoother scaling
        const newScale = Math.max(0.5, Math.min(3, scaleStart.value + scaleFactor));
        scale.value = newScale;
      })
      .onEnd(() => {
        savedScale.value = scale.value;
        runOnJS(setMemorizedPosition)({ type, id, scale: savedScale.value });
      });

  // Pre-calculated corner size
  const cornerSize = hp(1);
  const cornerOffset = hp(0.5);

  const Corner = ({ style, corner }: { style: any; corner: string }) => {
    const resizeGesture = createResizeGesture(corner);
    return (
      <GestureDetector gesture={resizeGesture}>
        <Animated.View
          style={[
            styles.corner,
            {
              width: cornerSize,
              height: cornerSize,
            },
            style,
          ]}
        />
      </GestureDetector>
    );
  };

  // Single animated style for the main container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
        { rotateZ: `${(rotation.value / Math.PI) * 180}deg` },
      ],
    };
  }, []);

  // Separate style for corners to prevent transform conflicts
  const cornerContainerStyle = useAnimatedStyle(() => {
    if (!isOkayPressed || type !== 'text') return { opacity: 0 };

    return {
      opacity: 1,
      position: 'absolute',
      top: -cornerOffset,
      left: -cornerOffset,
      right: -cornerOffset,
      bottom: -cornerOffset,
    };
  }, [isOkayPressed, type]);

  const composedGesture = Gesture.Simultaneous(
    tapGesture,
    panGesture,
    pinchGesture,
    rotationGesture,
  );

  return (
    <GestureDetector key={index} gesture={composedGesture}>
      <Animated.View
        style={[
          styles.viewContainer,
          animatedStyle,
          animatedStyleProps,
          { zIndex },
          isOkayPressed && styles.selectedBorder,
        ]}
      >
        {type === 'text' && textProps ? (
          <View style={styles.textContainer}>
            <Text
              style={[
                styles.text,
                {
                  fontWeight: textProps.fontWeight,
                  color: textProps.color,
                  fontFamily: textProps.fontFamily,
                  fontSize: textProps.fontSize,
                },
              ]}
            >
              {textProps.text}
            </Text>

            {/* Corner handles - separate from main content */}
            <Animated.View style={cornerContainerStyle} pointerEvents="box-none">
              <Corner style={styles.cornerBottomLeft} corner="bottomLeft" />
              <Corner style={styles.cornerBottomRight} corner="bottomRight" />
              <Corner style={styles.cornerTopLeft} corner="topLeft" />
              <Corner style={styles.cornerTopRight} corner="topRight" />
            </Animated.View>
          </View>
        ) : (
          children
        )}
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  viewContainer: {
    position: 'absolute',
  },
  selectedBorder: {
    borderWidth: 1,
    borderColor: '#fff',
  },
  textContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 50,
    minHeight: 30,
  },
  text: {
    textAlign: 'center',
  },
  corner: {
    backgroundColor: '#fff',
    position: 'absolute',
    borderRadius: 2,
  },
  cornerBottomLeft: {
    bottom: 0,
    left: 0,
  },
  cornerBottomRight: {
    bottom: 0,
    right: 0,
  },
  cornerTopLeft: {
    top: 0,
    left: 0,
  },
  cornerTopRight: {
    top: 0,
    right: 0,
  },
});

export default GestureHandler;
