import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface ArrowDownLeftSVGProps {
  size?: number;
  color?: string;
}

const IncomingSVG: React.FC<ArrowDownLeftSVGProps> = ({
  size = 14,
  color = "#F3F3F3",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      {...props} // this spreads allowed props like style or accessibility
    >
      <Path
        d="M17 7L7 17"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M17 17H7V7"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default IncomingSVG;
