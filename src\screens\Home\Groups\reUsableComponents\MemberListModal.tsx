// MembersListModal.tsx
import React from 'react';
import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { IGroupMember } from '../../../../service/ChatSpacesService';
import { FontAwesome6Icons } from '../../../../utils/vectorIcons';
import { IMAGES } from '../../../../assets/Images';
import { colors } from '../../../../theme/colors';

interface IMembersListModalProps {
  showMembersList: boolean;
  filteredMembers: IGroupMember[];
  handleMemberSelect: (member: IGroupMember) => void;
  selectedMemberId: string | null;
}

const MembersListModal = ({
  showMembersList,
  filteredMembers,
  handleMemberSelect,
  selectedMemberId,
}: IMembersListModalProps) => {
  if (!showMembersList) {
    return null;
  }

  return (
    <View style={styles.membersListContainer}>
      <FlatList
        data={filteredMembers}
        keyExtractor={(item) => item._id}
        renderItem={({ item }) => {
          const isSelected = selectedMemberId === item._id;
          return (
            <TouchableOpacity style={styles.memberItem} onPress={() => handleMemberSelect(item)}>
              <Image
                source={item?.user?.image ? { uri: item.user.image } : IMAGES.profile_image}
                style={styles.memberAvatar}
              />
              <View style={{ flex: 1 }}>
                <Text style={styles.memberName}>{item?.user?.name}</Text>
                <Text style={styles.memberUsername}>@{item?.user?.username}</Text>
              </View>
              <View>
                {isSelected ? (
                  <View style={styles.selectedCircle}>
                    <FontAwesome6Icons name="check" size={12} color={colors.white} />
                  </View>
                ) : (
                  <View style={styles.unselectedCircle} />
                )}
              </View>
            </TouchableOpacity>
          );
        }}
        keyboardShouldPersistTaps={'handled'}
        style={{
          maxHeight: 6 * 60,
        }}
      />
    </View>
  );
};

export default MembersListModal;

const styles = StyleSheet.create({
  membersListContainer: {
    backgroundColor: colors.white,
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 15,
  },
  memberName: {
    fontWeight: '600',
    color: colors.black,
    fontSize: 15,
  },
  memberUsername: {
    color: colors.gray_80,
    fontSize: 13,
  },
  unselectedCircle: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 1.5,
    borderColor: colors.gray_80,
    backgroundColor: 'transparent',
  },
  selectedCircle: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: colors.mainPurple,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
