import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
  size?: number;
  color?: string;
}

const ShareSVG: React.FC<SvgComponentProps> = ({
  size = 20,
  color = "#232323",
  ...props
}) => {
    
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Path
        d="M18.185.983a.658.658 0 01.832.832L13.422 18.6a.66.66 0 01-.564.447v-.001a.66.66 0 01-.636-.334l-3.905-7.03-7.029-3.904a.658.658 0 01.112-1.2L18.185.983zM9.67 11.27l-.004.004c-.016.012-.034.022-.05.033l3.032 5.463 4.239-12.725-7.217 7.225zM3.237 7.355l5.461 3.034c.011-.018.023-.035.035-.051l.003-.004 7.221-7.22-12.72 4.241z"
        fill={color}
      />
    </Svg>
  );
};

export default ShareSVG;

