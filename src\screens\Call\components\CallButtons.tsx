import React, { <PERSON>spatch, SetStateAction } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  ToastAndroid,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import { useCallContext } from '../../../Context/CallProvider';
import { useMediasoup } from '../../../Context/RtcProvider';
import { IMAGES } from '../../../assets/Images';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { zIndex } from '../../../utils/Filters';
import EarphoneSvg from '../../../assets/svgIcons/EarPhoneSVG';
import AudioSvg from '../../../assets/svgIcons/calls/AudioSvg';
import AudioSelectedSvg from '../../../assets/svgIcons/calls/AudioSelectedSvg';
import { Ionicons, MaterialIcons, FeatherIcons } from '../../../utils/vectorIcons';
import { colors } from '../../../theme/colors';
import { SCREENS } from '../../../navigation/screenNames';

import { navigationRef } from '../../../navigation/RootContainer';
import { CommonActions } from '@react-navigation/native';
import { getCallPermissions } from '../../../calls/calls.lib';

type CallButtonsProps = {
  setOptionsModal: Dispatch<SetStateAction<boolean>>;
  setCallMembersModal: Dispatch<SetStateAction<boolean>>;
  containerStyles?: StyleProp<ViewStyle>;
};

export function CallButtons({
  setOptionsModal,
  setCallMembersModal,
  containerStyles,
}: CallButtonsProps) {
  const {
    callDetails,
    rejectCall,
    answerCall,
    endCall,
    toggleAudioTrack,
    toggleVideoTrack,
    producers,
    inCallController,
  } = useCallContext();
  const { mediaProducers } = useMediasoup();
  const insets = useSafeAreaInsets();

  async function handleAnswerCall() {
    const hasPermissions = await getCallPermissions({ callType: callDetails?.type });
    if (!hasPermissions) {
      ToastAndroid.show('Permissions denied to answer call', ToastAndroid.SHORT);
      return;
    }

    answerCall({ roomId: callDetails?.roomId });

    // handling answering call from the notification in
    // incoming call screen when there is no initial screen(Homescreen)
    // const currentScreen = navigationRef.current?.getCurrentRoute()?.name;
    // if (currentScreen !== SCREENS.MainCallScreen) {
    //   navigationRef.current?.dispatch(
    //     CommonActions.reset({
    //       index: 1,
    //       routes: [{ name: SCREENS.HomeScreen }, { name: SCREENS.MainCallScreen }],
    //     }),
    //   );
    // }
  }

  function handleRejectCall() {
    rejectCall();
    if (navigationRef.current?.getCurrentRoute()?.name !== SCREENS.MainCallScreen) {
      navigationRef.current?.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: SCREENS.HomeScreen }],
        }),
      );
    }
  }
  switch (callDetails.state) {
    case 'incoming':
      return (
        <View
          style={[
            styles.containerStyle,
            {
              paddingBottom: insets.bottom,
              zIndex: zIndex.level_4,
            },
            containerStyles,
          ]}
        >
          <View style={styles.overlay} />
          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={handleRejectCall}>
              <MaterialIcons name="call-end" size={40} color="white" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleAnswerCall}>
              <Image source={IMAGES.addCall} style={styles.endcallStyle} />
            </TouchableOpacity>
            <TouchableOpacity>
              <Image source={IMAGES.chatList} style={styles.iconStyle} />
            </TouchableOpacity>
          </View>
        </View>
      );

    case 'outgoing':
      return (
        <View
          style={[
            styles.containerStyle,
            {
              paddingBottom: insets.bottom,
            },
            containerStyles,
          ]}
        >
          <View style={styles.overlay} />
          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={toggleAudioTrack}>
              <Image
                source={IMAGES.mute_fill}
                style={[
                  styles.muteIconLarge,
                  { display: producers.isAudioPaused ? 'flex' : 'none' },
                ]}
                resizeMode="contain"
              />
              <Image
                source={IMAGES.mute_white}
                style={[
                  styles.muteIconSmall,
                  { display: producers.isAudioPaused ? 'none' : 'flex' },
                ]}
                resizeMode="contain"
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                endCall('outgoing');
              }}
              style={styles.endCallButton}
            >
              <MaterialIcons name="call-end" size={40} color="white" />
            </TouchableOpacity>

            <TouchableOpacity onPress={inCallController.toggleSpeaker}>
              {inCallController.hasHeadsetPluggedIn && <EarphoneSvg width={40} height={40} />}
              {!inCallController.hasHeadsetPluggedIn && inCallController.isSpeakerOn && (
                <AudioSelectedSvg width={40} height={40} />
              )}
              {!inCallController.hasHeadsetPluggedIn && !inCallController.isSpeakerOn && (
                <AudioSvg width={40} height={40} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      );

    case 'ongoing':
      return (
        <View
          style={[
            styles.containerStyle,
            {
              paddingBottom: insets.bottom,
              zIndex: zIndex.level_2,
            },
            containerStyles,
          ]}
        >
          <BlurView
            style={StyleSheet.absoluteFill}
            blurType="dark"
            blurAmount={6}
            reducedTransparencyFallbackColor="rgba(0, 0, 0, 0.25)"
          />
          <TouchableOpacity
            onPress={() => {
              setOptionsModal(false);
              setCallMembersModal(true);
            }}
          >
            <View style={styles.dragIndicator} />
          </TouchableOpacity>

          <View style={styles.buttonContainer}>
            <TouchableOpacity onPress={toggleAudioTrack}>
              <Image
                source={IMAGES.mute_fill}
                style={[
                  styles.muteIconLarge,
                  { display: producers.isAudioPaused ? 'flex' : 'none' },
                ]}
                resizeMode="contain"
              />
              <Image
                source={IMAGES.mute_white}
                style={[
                  styles.muteIconSmall,
                  { display: producers.isAudioPaused ? 'none' : 'flex' },
                ]}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {callDetails?.type == 'video' && (
              <TouchableOpacity
                style={{
                  backgroundColor: producers.isVideoPaused ? 'white' : colors.overlayWhite_20,
                  padding: 10,
                  borderRadius: 50,
                }}
                onPress={async () => {
                  try {
                    await toggleVideoTrack();
                  } catch (err) {
                    console.log(err);
                  }
                }}
              >
                <FeatherIcons
                  name="video"
                  size={25}
                  color={producers.isVideoPaused ? 'black' : 'white'}
                />
              </TouchableOpacity>
            )}

            <TouchableOpacity onPress={() => endCall()} style={styles.endCallButton}>
              <MaterialIcons name="call-end" size={40} color="white" />
            </TouchableOpacity>

            <TouchableOpacity onPress={inCallController.toggleSpeaker}>
              {inCallController.hasHeadsetPluggedIn && <EarphoneSvg width={40} height={40} />}
              {!inCallController.hasHeadsetPluggedIn && inCallController.isSpeakerOn && (
                <AudioSelectedSvg width={40} height={40} />
              )}
              {!inCallController.hasHeadsetPluggedIn && !inCallController.isSpeakerOn && (
                <AudioSvg width={40} height={40} />
              )}
            </TouchableOpacity>

            {callDetails?.type == 'video' && (
              <TouchableOpacity onPress={mediaProducers.flipCamera}>
                <Image source={IMAGES.changeCamera} style={styles.iconStyle} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      );

    default:
      return null;
  }
}

const styles = StyleSheet.create({
  iconStyle: {
    width: 40,
    height: 40,
  },
  endcallStyle: {
    width: 50,
    height: 50,
  },
  containerStyle: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    overflow: 'hidden',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingVertical: 15,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
  },
  shadeButtonStyles: {
    backgroundColor: colors.overlayWhite_20,
    padding: 10,
    borderRadius: 50,
  },
  dragIndicator: {
    width: 60,
    height: 5,
    alignSelf: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginTop: 10,
    borderRadius: 50,
  },
  muteIconLarge: {
    width: 40,
    height: 40,
  },
  muteIconSmall: {
    width: 30,
    height: 30,
  },
  endCallButton: {
    backgroundColor: 'red',
    padding: 10,
    borderRadius: 50,
  },
});
