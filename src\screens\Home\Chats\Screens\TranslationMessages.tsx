import React, { useMemo, useState } from 'react';
import { FlatList, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';
import CommonView from '../../../../component/CommonView';
import { colors } from '../../../../theme/colors';
import { RouteProp, useRoute } from '@react-navigation/native';
import { ChatService } from '../../../../service/ChatService';
import { languages } from '../../../../lib/Languages';
import { IChatScreenProps } from '../ChatSpecificScreen';
import SearchInput from '../../../../component/SearchInput';
import { hp } from '../../../../theme/fonts';
import useConversationInfo from '../../../../device-storage/realm/hooks/useConversationInfo';

const options = ['Text messages', 'Voice messages', 'Audio & video calls'];

type TranslationMessagesScreenParams = {
  TranslationMessages: {
    userData: string;
  };
};

const TranslationMessages = () => {
  const route = useRoute<RouteProp<TranslationMessagesScreenParams, 'TranslationMessages'>>();

  const conversationId = route.params.userData;
  const { conversationInfo } = useConversationInfo(conversationId);
 
  const [isEnabled, setIsEnabled] = useState(() => {
    const text = !!conversationInfo?.conversationSettings?.translateText;
    const voice = !!conversationInfo?.conversationSettings?.translateVoiceMessages;
    const av = !!conversationInfo?.conversationSettings?.translateCalls;

    return [text, voice, av];
  });

  const [search, setSearch] = useState('');

  const initialLanguage = languages.find(
    (lang) => lang.code === conversationInfo?.conversationSettings?.translationLanguage,
  );
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(
    initialLanguage?.id || null,
  );

  const fieldMap: Record<number, 'translateText' | 'translateVoiceMessages' | 'translateCalls'> = {
    0: 'translateText',
    1: 'translateVoiceMessages',
    2: 'translateCalls',
  };

  const filteredLanguages = languages.filter((lang) =>
    lang.id.toLowerCase().includes(search.toLowerCase()),
  );

  const toggleSwitch = (index: number) => {
    setIsEnabled((prev) => {
      const newValues = [...prev];
      newValues[index] = !newValues[index];

      const fieldName = fieldMap[index];

      ChatService.updateTranslationOptions(conversationId, fieldName, newValues[index]);

      const isNowAnyEnabled = newValues.some((val) => val);

      if (isNowAnyEnabled) {
        if (!selectedLanguage) {
          const defaultLang = languages.find((lang) => lang.id === 'Hindi');
          if (defaultLang) {
            setSelectedLanguage(defaultLang.id);
            ChatService.updateTranslationTextLanguage(conversationId, defaultLang.code);
          }
        }
      } else {
        setSelectedLanguage(null);
        ChatService.updateTranslationTextLanguage(conversationId, '');
      }

      return newValues;
    });
  };

  return (
    <CommonView headerTitle="Translate Language">
      <View style={{ paddingBottom: hp(2) }}>
        <SearchInput value={search} onChangeText={setSearch} placeholder="Search language" />
      </View>
      {/* Select Type Section */}
      <View>
        <Text style={styles.title}>Select Type</Text>
        {options.map((option, index) => (
          <View key={option} style={styles.optionRow}>
            <Text style={styles.optionText}>{option}</Text>
            <Switch
              trackColor={{ true: colors.mainPurple }}
              thumbColor={colors.white}
              onValueChange={() => toggleSwitch(index)}
              value={isEnabled[index]}
            />
          </View>
        ))}
      </View>

      {/* Select Language Section */}
      <View style={{ marginTop: 20, flex: 1 }}>
        <Text style={styles.title}>Select Language</Text>
        <FlatList
          data={filteredLanguages}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => {
            const isSelected = selectedLanguage === item.id;
            return (
              <TouchableOpacity
                style={styles.languageOptionRow}
                activeOpacity={1}
                onPress={() => {
                  setSelectedLanguage(item.id);
                  ChatService.updateTranslationTextLanguage(conversationId, item.code);
                }}
              >
                <View style={styles.languageLeft}>
                  <Text style={styles.symbol}>{item.symbol}</Text>
                  <Text style={[styles.language, isSelected && { color: colors.mainPurple }]}>
                    {item.id}
                  </Text>
                </View>
                {isSelected && (
                  <View style={styles.radioOuter}>
                    <View style={styles.radioInner} />
                  </View>
                )}
              </TouchableOpacity>
            );
          }}
          contentContainerStyle={{ paddingBottom: 100 }}
        />
      </View>
    </CommonView>
  );
};

export default TranslationMessages;

const styles = StyleSheet.create({
  title: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.black_23,
    marginBottom: 16,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  optionText: {
    fontSize: 16,
    color: colors.black_23,
  },
  languageOptionRow: {
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 15,
    padding: 10,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  languageLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  symbol: {
    fontSize: 20,
    marginRight: 10,
  },
  language: {
    color: colors.black_23,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 20,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.black_23,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.mainPurple,
  },
});
