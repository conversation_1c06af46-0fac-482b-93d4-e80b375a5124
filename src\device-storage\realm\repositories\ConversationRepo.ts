import { Realm } from 'realm';
import { realmSchemaNames } from '../schemas/schemaNames';
import { ConversationSchema, IConversation } from '../schemas/ConversationSchema';
import { ChannelType, IMessage, MessageSchema, MessageType } from '../schemas/MessageSchema';
import { ContactRepo } from './ContactRepo';
import { ContactSchema, IContact } from '../schemas/ContactSchema';
import { getUser } from '../../../utils/ApiService';
import { safeRealmWrite } from '../lib';
import { MessageRepo } from './MessageRepo';
import { MemberPermissions } from '../../../screens/Home/Groups/GroupPermissions';
import { useMe } from '../../../hooks/util/useMe';
import { AdminPermissions } from '../../../screens/Home/Groups/AdminPermissions';
import { UserSchema } from '../schemas/UserSchema';
import { IUser, RemoteUser } from '../../../types/index.types';

export class ConversationRepo {
  private static schemaName = realmSchemaNames.conversation;
  constructor() {}

  static archiveUnarchiveConversation(realm: Realm, id: string, archive: boolean) {
    const conversation = this.findById(realm, id);
    if (!conversation) return;

    conversation.isArchived = archive;

    realm.write(() => {
      realm.create(this.schemaName, conversation, Realm.UpdateMode.Modified);
    });
  }

  static findById(realm: Realm, id: string): IConversation | null {
    const obj = realm.objectForPrimaryKey<ConversationSchema>(this.schemaName, id);
    return obj?.toJSON() as unknown as IConversation;
  }

  static findRealmObjectById(realm: Realm, id: string): ConversationSchema | null {
    return realm.objectForPrimaryKey<ConversationSchema>(ConversationSchema.schema.name, id);
  }

  static findUserRealmObjectById(realm: Realm, id: string): UserSchema | undefined {
    return realm.objects(UserSchema).filtered('id == $0', id)[0];
  }

  static findAll(realm: Realm) {
    return realm.objects(this.schemaName);
  }

  //   !!Make sure to call this method in a write transaction!!
  static saveOrUpdate(realm: Realm, conversation: Partial<IConversation> & { id: string }) {
    realm.create(realmSchemaNames.conversation, conversation, Realm.UpdateMode.Modified);
  }

  static saveOrUpdateByMessage(
    realm: Realm,
    conversationData: IConversation,
    message: MessageSchema,
  ): ConversationSchema {
    const existingConversation = realm.objectForPrimaryKey<ConversationSchema>(
      this.schemaName,
      conversationData.id,
    );
    const isEvent = message?.messageType === MessageType.EVENT;

    if (isEvent) {
      if (!existingConversation) {
        return realm.create(
          this.schemaName,
          conversationData,
          Realm.UpdateMode.Modified,
        ) as ConversationSchema;
      } else {
        return existingConversation;
      }
    } else {
      conversationData.lastMessage = message;
      conversationData.lastMessageTimestamp = message.createdAt || Date.now();
      conversationData.unreadCount = (existingConversation?.unreadCount || 0) + 1;

      // Create or update the conversation with the new details.
      return realm.create(
        this.schemaName,
        conversationData,
        Realm.UpdateMode.Modified,
      ) as ConversationSchema;
    }
  }

  static updateLastMessageByGlobalId(realm: Realm, conversationId: string, messageId: string) {
    const conversation = this.findById(realm, conversationId);
    if (!conversation || conversation?.lastMessage?.globalId !== messageId) return;
    const lastMessage = realm
      .objects(realmSchemaNames.message)
      .filtered('conversationId == $0 AND messageType != $1', conversationId, MessageType.EVENT)
      .sorted('createdAt', true)[0] as unknown as MessageSchema;
    if (lastMessage) {
      conversation.lastMessage = lastMessage;
      conversation.lastMessageTimestamp = lastMessage.createdAt;
      realm.create(this.schemaName, conversation, Realm.UpdateMode.Modified);
    } else {
      conversation.lastMessage = undefined;
      conversation.lastMessageTimestamp = undefined;
    }
  }

  static deleteAll(realm: Realm) {
    realm.write(() => {
      realm.delete(realm.objects(this.schemaName));
    });
  }

  static clearLastMessageData(realm: Realm, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      realm.write(() => {
        conversation.lastMessage = undefined;
      });
    }
  }

  static togglePin(realm: Realm, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      safeRealmWrite(realm, () => {
        conversation.isPinned = conversation.isPinned ? false : true;
      });
    }
  }

  static delete(realm: Realm, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      safeRealmWrite(realm, () => {
        realm.delete(conversation);
      });
    }
  }

  static toggleArchive(realm: Realm, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      safeRealmWrite(realm, () => {
        conversation.isArchived = conversation.isArchived ? false : true;
      });
    }
  }

  static updateLastMessage(realm: Realm, conversationId: string) {
    const message = MessageRepo.findRecentMessageByConversationId(realm, conversationId);
    const conversation = realm.objectForPrimaryKey(ConversationSchema, conversationId);
    if (!conversation) {
      console.warn('updating last message for a non existing conversation');
      return;
    }
    if (message && message.messageType === MessageType.EVENT) {
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.lastMessage = message;
    });
  }

  static updateTextLanguage(realm: Realm, conversationId: string, textLanguage: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      conversation.translatedLanguage = textLanguage;
    }
  }

  static updateTranslationOptions(
    realm: Realm,
    conversationId: string,
    field: 'translatedText' | 'translatedVoiceMessages' | 'TranslatedAudioAndVideoCalls',
    value: boolean,
  ) {
    const conversation = this.findRealmObjectById(realm, conversationId);

    if (!conversation) return;

    safeRealmWrite(realm, () => {
      conversation[field] = value;
    });
  }

  static clearChat(realm: Realm, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (conversation) {
      safeRealmWrite(realm, () => {
        realm.delete(conversation);
      });
    }
  }

  static updateMemberPermissionsOverride(
    realm: Realm,
    conversationId: string,
    memberPermissions?: any,
    adminPermissions?: any,
  ) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (!conversation) {
      console.warn(`Conversation with ID ${conversationId} not found`);
      return;
    }

    safeRealmWrite(realm, () => {
      const overrides: any = {};

      if (memberPermissions) {
        overrides.member =
          typeof memberPermissions === 'string'
            ? memberPermissions
            : JSON.stringify(memberPermissions);
      }

      if (adminPermissions) {
        overrides.admin =
          typeof adminPermissions === 'string'
            ? adminPermissions
            : JSON.stringify(adminPermissions);
      }

      conversation.groupMemberOverrides = JSON.stringify(overrides);
    });
  }

  static updateDisappearDuration(
    realm: Realm,
    conversationId: string,
    disappearDuration: number | null,
  ) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (!conversation) {
      console.warn(`Conversation with ID ${conversationId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.disappearDuration = disappearDuration === null ? undefined : disappearDuration;
    });
  }

  static deleteChatspace(realm: Realm, chatSpaceId: string) {
    const conversation = this.findRealmObjectById(realm, chatSpaceId);
    if (!conversation) {
      console.warn(`Conversation with ID ${chatSpaceId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      realm.delete(conversation);
    });
  }

  static clearChatspacesfromrealm(realm: Realm) {
    safeRealmWrite(realm, () => {
      realm.delete(realm.objects(ConversationSchema));
    });
  }

  static createChatSpaceConversation(realm: Realm, conversation: IConversation) {
    const existing = realm.objectForPrimaryKey(ConversationSchema, conversation.id);
    if (existing) {
      return existing; // Don't create, return existing one
    }

    return realm.create(ConversationSchema, conversation, Realm.UpdateMode.Never);
  }

  static blockedUsers(realm: Realm, userId: string) {
    const conversation = this.findRealmObjectById(realm, userId);
    if (!conversation) {
      console.warn(`Conversation with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.isBlocked = true;
    });
  }

  static unBlockedUsers(realm: Realm, userId: string) {
    const conversation = this.findRealmObjectById(realm, userId);
    if (!conversation) {
      console.warn(`Conversation with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.isBlocked = false;
    });
  }

  static blocked(realm: Realm, userId: string) {
    const user = this.findUserRealmObjectById(realm, userId);
    if (!user) {
      console.warn(`User with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      user.isBlocked = true;
    });
  }

  static unBlocked(realm: Realm, userId: string) {
    const user = this.findUserRealmObjectById(realm, userId);
    if (!user) {
      console.warn(`Conversation with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      user.isBlocked = false;
    });
  }

  static getBlockedUsers(realm: Realm) {
    return realm.objects(UserSchema).filtered('isBlocked == true').toJSON();
  }

  static getBlockedConversations(realm: Realm) {
    return realm.objects(ConversationSchema).filtered('isBlocked == true').toJSON();
  }

  static updateConversation(
    realm: Realm,
    userId: string,
    updates: { status?: string; lastSeenAt?: number },
  ) {
    const conversation = this.findRealmObjectById(realm, userId);
    if (!conversation) {
      console.warn(`Conversation with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.lastSeenAt = updates.lastSeenAt;
      conversation.status = updates.status;
    });
  }

  static editConversation(
    realm: Realm,
    userId: string,
    updates: { displayName: string; displayPic?: string },
  ) {
    const conversation = this.findRealmObjectById(realm, userId);
    if (!conversation) {
      console.warn(`Conversation with ID ${userId} not found`);
      return;
    }
    safeRealmWrite(realm, () => {
      conversation.displayName = updates.displayName;
      conversation.displayPic = updates.displayPic;
    });
  }

  static typingTimeouts = new Map();

  static updateTypingStatus(realm: Realm, userId: string, conversationId: string) {
    const conversation = this.findRealmObjectById(realm, conversationId);
    if (!conversation) {
      console.warn(`Conversation with ID ${conversationId} not found`);
      return;
    }

    const timeoutKey = `${conversationId}_${userId}`;

    // Clear previous timeout if exists
    if (this.typingTimeouts.has(timeoutKey)) {
      clearTimeout(this.typingTimeouts.get(timeoutKey));
    }

    safeRealmWrite(realm, () => {
      console.log('User is typing:', userId, 'in conversation:', conversationId);
      const currentTypingUsers = conversation.typingUsers || [];

      if (!currentTypingUsers.includes(userId)) {
        conversation.typingUsers = [...currentTypingUsers, userId];
      }
    });

    // Set new timeout to remove user after 3 seconds of inactivity
    const timeoutId = setTimeout(() => {
      console.log('User stopped typing:', userId, 'in conversation:', conversationId);
      safeRealmWrite(realm, () => {
        conversation.typingUsers = (conversation.typingUsers || []).filter((id) => id !== userId);
      });
      this.typingTimeouts.delete(timeoutKey);
    }, 3000);

    this.typingTimeouts.set(timeoutKey, timeoutId);
  }

  static handleProfileUpdate(realm: Realm, user: RemoteUser) {
    safeRealmWrite(realm, () => {
      const existingConversation = this.findRealmObjectById(realm, user._id);
      if (existingConversation) {
        existingConversation.profileName = user.name;
        existingConversation.displayPic = user.image || undefined;
      }
    });
  }
}
