import { useEffect, useRef, useState } from 'react';
import {
  getStreamInfoApi,
  LiveStreamPayload,
  ViewerLiveStreamSession,
} from '../../api/Chatspace/chatspace.api';
import { IConversation } from '../../device-storage/realm/schemas/ConversationSchema';
import { ConversationType } from '../../device-storage/realm/schemas/MessageSchema';
import useSocket from '../../socket-client/useSocket';
import { navigateTo } from '../../utils/commonFunction';
import { SCREENS } from '../../navigation/screenNames';
import { getStreamToken } from '../../utils/asyncStorage';
import { mockComments } from '../../screens/Home/Channels/liveStreams/data';

export type UiStreamType = 'scheduled' | 'immediate';

export type LiveStreamState = LiveStreamPayload & {
  uiStreamType: UiStreamType;
  streamingStatus: 'idle' | 'streaming';
};

export enum ModalType {
  None = 'none',
  SelectLiveMode = 'selectLiveMode',
  StreamSettings = 'streamSettings',
}

export const initialLiveStreamState: LiveStreamState = {
  targetAmount: 0,
  streamCause: '',
  thumbnail: '',
  allowGifts: false,
  allowChat: false,
  endTime: undefined,
  isScheduled: false,
  scheduledTime: undefined,
  chatspaceId: '',
  uiStreamType: 'immediate',
  streamingStatus: 'idle',
};

type props = {
  Conversation?: IConversation;
  type: ConversationType;
};

export const useLiveStreamController = ({ Conversation, type: ConversationTypeProp }: props) => {
  const { socket } = useSocket();
  const currentConversationRef = useRef(Conversation);
  const [streamingModalState, setStreamingModalState] = useState<{
    showModal: boolean;
    activeModal: ModalType;
  }>({
    showModal: false,
    activeModal: ModalType.None,
  });

  const [streamDetails, setStreamDetails] = useState<LiveStreamState>({
    ...initialLiveStreamState,
    chatspaceId:
      ConversationTypeProp === ConversationType.CHANNEL
        ? Conversation?.chatSpaceId
          ? Conversation.chatSpaceId
          : ''
        : '',
  });

  const openModal = (type: ModalType) => {
    setStreamingModalState({ showModal: true, activeModal: type });
  };

  const closeModal = () => {
    setStreamingModalState({ showModal: false, activeModal: ModalType.None });
  };

  const resetStreamingState = () => {
    setStreamDetails(initialLiveStreamState);
    closeModal();
  };

  function registerStreamSocketEvents() {}

  function unregisterStreamSocketEvents() {}

  useEffect(() => {
    currentConversationRef.current = Conversation;
    setStreamDetails((prev) => {
      return {
        ...prev,
        chatspaceId: Conversation?.chatSpaceId ? Conversation.chatSpaceId : '',
      };
    });
  }, [Conversation]);

  useEffect(() => {
    return () => {
      unregisterStreamSocketEvents();
    };
  }, []);

  return {
    streamingModalState,
    streamDetails,
    setStreamDetails,
    openModal,
    closeModal,
    resetStreamingState,
    joinStream,
  };
};

export async function joinStream(conversation: any) {
  try {
    // const conversation = currentConversationRef.current;
    const streamInfo = await getStreamInfoApi<ViewerLiveStreamSession>(conversation.chatSpaceId!);

    if (!streamInfo || !streamInfo._id) {
      return;
    }
    const streamToken = await getStreamToken();
    const updatedStreamObj: ViewerLiveStreamSession = {
      ...streamInfo,
      llhlsPlaybackUrl: streamInfo.llhlsPlaybackUrl + `?token=${streamToken}`,
    };

    navigateTo(SCREENS.ViewerScreenView, { streamInfo: updatedStreamObj });
  } catch (err) {
    return Promise.reject(err);
  }
}
