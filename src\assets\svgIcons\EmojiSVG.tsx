import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const EmojiSVG: React.FC<IconProps> = ({
    size = 21,
    color = "#232323",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={size} // 1:1 aspect ratio
            viewBox="0 0 21 21"
            fill="none"
            {...restProps}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.125 1.759a8.567 8.567 0 100 17.135 8.567 8.567 0 000-17.135zM0 10.326C0 4.734 4.533.201 10.125.201S20.25 4.734 20.25 10.326s-4.533 10.125-10.125 10.125S0 15.918 0 10.326z"
                fill={color}
            />
            <Path
                d="M6.62 9.547a1.168 1.168 0 100-2.336 1.168 1.168 0 000 2.336zM13.63 9.547a1.168 1.168 0 100-2.336 1.168 1.168 0 000 2.336z"
                fill={color}
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.684 11.99a.779.779 0 011.064.282 3.904 3.904 0 006.753 0 .779.779 0 111.347.782 5.461 5.461 0 01-9.447 0 .779.779 0 01.283-1.065z"
                fill={color}
            />
        </Svg>
    );
};

export default EmojiSVG;

