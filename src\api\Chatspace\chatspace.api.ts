import { ImageOrVideo } from 'react-native-image-crop-picker';
import { ChannelType, MessageType } from '../../device-storage/realm/schemas/MessageSchema';
import { Comment } from '../../screens/Home/Channels/liveStreams/CommentContainer';
import { Channel } from '../../types/chatSpace.types';
import Api from '../../utils/api';
import { ChatSpace } from '../../types/socketPayload.type';
import { MembershipStatus } from '../../types/chats.types';

const chatSpaceRoutes = {
  initStream: 'v1/livestream/init_stream',
  golive: 'v1/livestream/golive',
  stopStream: 'v1/livestream/stoplivestream',
  getChatSpaceDetails: 'v1/chatspaces/summary/',
  getStreamInfo: 'v1/livestream/channels/',
  streampublishSuccess: 'v1/livestream/publish/success/',
  streamPublishFailed: 'v1/livestream/publish/failed/',
  StreamPublishDisconect: 'v1/livestream/publish/disconnect/',
  getCurrentLiveStream: 'v1/livestream/',
  getLiveStreamFeed: 'v1/livestream/streams',
};

export interface BaseLiveStreamSession {
  _id: string;
  allowChat: boolean;
  allowGifts: boolean;
  chatspaceId: string;
  collectedAmount: number;
  createdAt: string;
  endTime: string;
  isActiveStream: boolean;
  isFailedStream: boolean;
  isReadyToWatch: boolean;
  isScheduled: boolean;
  likedUserIds: string[];
  likesNum: number;
  shareNum: number;
  sharedUserIds: string[];
  streamCause: string;
  streamKey: string;
  streamType: 'public_stream' | 'private_stream';
  targetAmount: number;
  thumbnail: string;
  updatedAt: string;
  userId: string;
  created_At: string;
}
export interface StreamerLiveStreamSession extends BaseLiveStreamSession {
  rtmpIngestUrl: string;
  webrtcIngestUrl: string;
  llhlsPlaybackUrl: string;
  webrtcPlaybackUrl: string;
}
export interface ViewerLiveStreamSession extends BaseLiveStreamSession {
  llhlsPlaybackUrl: string;
  webrtcPlaybackUrl: string;
}

export async function initializeStream(
  payload: LiveStreamPayload,
): Promise<StreamerLiveStreamSession> {
  try {
    const res = await Api.post(chatSpaceRoutes.initStream, {
      ...payload,
      chatspaceId: payload.chatspaceId,
    });

    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
}

export const getStreamInfoApi = async <T = any>(chatSpaceId: string): Promise<T> => {
  try {
    const res = await Api.get(chatSpaceRoutes.getStreamInfo + chatSpaceId);
    if (res.body?.status === false) {
      return Promise.reject(res.body);
    }

    return res.body.data as T;
  } catch (err) {
    return Promise.reject(err);
  }
};

export const getCurrentLiveStreamApi = async <T = any>(liveStreamId: string): Promise<T> => {
  try {
    const res = await Api.get(chatSpaceRoutes.getCurrentLiveStream + liveStreamId);
    return res.body.data as T;
  } catch (err) {
    return Promise.reject(err);
  }
};

export async function startStreamApi(liveStreamInfo: StreamerLiveStreamSession) {
  try {
    // notify backend which sends event to all the channel users.

    const payload = {
      chatSpaceId: liveStreamInfo.chatspaceId,
      liveStreamId: liveStreamInfo._id,
    };
    const res = await Api.post(chatSpaceRoutes.golive, payload);

    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
}

export async function stopStreamApi(liveStreamInfo: StreamerLiveStreamSession) {
  try {
    const payload = {
      chatspaceId: liveStreamInfo.chatspaceId,
      liveStreamId: liveStreamInfo._id,
    };

    const res = await Api.post(chatSpaceRoutes.stopStream, payload);
  } catch (err) {
    return Promise.reject(err);
  }
}

export const getChannelDetailsApi = async (chatSpaceId: string) => {
  try {
    const res = await Api.get(chatSpaceRoutes.getChatSpaceDetails + chatSpaceId);

    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
};

export const publishStreamSuccessApi = async (liveStreamInfo: StreamerLiveStreamSession) => {
  try {
    const payload = {
      chatspaceId: liveStreamInfo.chatspaceId,
      liveStreamId: liveStreamInfo._id,
    };
    const res = await Api.post(chatSpaceRoutes.streampublishSuccess, payload);
    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
};

export const publishStreamFailedApi = async (liveStreamInfo: StreamerLiveStreamSession) => {
  try {
    const payload = {
      chatspaceId: liveStreamInfo.chatspaceId,
      liveStreamId: liveStreamInfo._id,
    };
    const res = await Api.post(chatSpaceRoutes.streamPublishFailed, payload);
    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
};

export const streamPublishDisconnectApi = async (liveStreamInfo: StreamerLiveStreamSession) => {
  try {
    const payload = {
      chatspaceId: liveStreamInfo.chatspaceId,
      liveStreamId: liveStreamInfo._id,
    };
    const res = await Api.post(chatSpaceRoutes.StreamPublishDisconect, payload);
    return res.body.data;
  } catch (err) {
    return Promise.reject(err);
  }
};

export const addChatsSpaceMember = async (
  chatSpaceId: string,
  newMembers: string[],
  removeMembers: string[] = [],
) => {
  try {
    const payload = {
      newMembers,
      removeMembers,
    };
    const res = await Api.patch(`v1/chatspaces/${chatSpaceId}/member`, payload);

    return res?.body?.data || {};
  } catch (error) {
    console.log('Add member err', error);
  }
};

export const assignMemberRole = async (
  chatSpaceId: string,
  userIds: string[],
  role: MembershipStatus,
) => {
  try {
    const res = await Api.patch(`v1/chatspaces/${chatSpaceId}/role`, {
      userIds,
      role,
    });
    return res.body?.data;
  } catch (error) {
    console.log('Assign role error', error);
  }
};

export const fetchChatSpace = async (chatSpaceId: string) => {
  try {
    const res = await Api.get(`v1/chatspaces/getChatSpace/${chatSpaceId}`);
    return res?.body || {};
  } catch (error) {
    console.log('Add member err', error);
  }
};

export const deleteChatSpace = async (chatSpaceId: string) => {
  try {
    const res = await Api.delete(`v1/chatspaces/${chatSpaceId}`);
    return res?.body ?? null;
  } catch (error) {
    console.error('Error deleting chat space:', error);
    throw error;
  }
};

interface UpdateChatSpacePayload {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  removeDisplayPic?: boolean;
  file?: { uri: string; type: string; name: string };
}

export const updateChatspaceInfo = async (chatSpaceId: string, payload: UpdateChatSpacePayload) => {
  try {
    const formData = new FormData();

    if (payload.name) formData.append('name', payload.name);
    if (payload.description) formData.append('description', payload.description);
    if (payload.isPrivate !== undefined) formData.append('isPrivate', String(payload.isPrivate));
    if (payload.removeDisplayPic !== undefined)
      formData.append('removeDisplayPic', String(payload.removeDisplayPic));

    if (payload.file) {
      formData.append('file', {
        uri: payload.file.uri,
        type: payload.file.type,
        name: payload.file.name,
      } as any);
    }

    const response = await Api.patch(`v1/chatspaces/${chatSpaceId}/info`, formData, true);
    return response.body.data;
  } catch (error: any) {
    console.log('error', error);
    throw error.response?.data || error;
  }
};

export const joinChatSpace = async (chatSpaceId: string, inviteCode: string) => {
  try {
    const res = await Api.post(`v1/chatspaces/join-request`, { chatSpaceId, inviteCode });
    return res?.body?.data || {};
  } catch (error) {
    console.error('Error joining chat space:', error);
    throw error;
  }
};

export const updateOwnershipTranfer = async (chatSpaceId: string, newOwnerId: string) => {
  try {
    const res = await Api.patch(`v1/chatspaces/${chatSpaceId}/transfer-ownership`, {
      newOwnerId,
    });
    return res?.body?.data || {};
  } catch (error) {
    console.error('Error transferring ownership:', error);
    throw error;
  }
};

export const getLiveStreamChatsApi = async (liveStreamId: string): Promise<Comment[]> => {
  try {
    const res = await Api.get(`v1/livestream/chats/${liveStreamId}`);
    return res?.body?.data ?? null;
  } catch (error) {
    console.error('Error fetching live stream chats:', error);
    throw error;
  }
};

export async function getStreamChatMessagesApi({
  liveStreamId,
  paginationAnchor,
  lastCreatedAt,
  limit = 20,
}: {
  liveStreamId: string;
  paginationAnchor: string;
  lastCreatedAt?: string;
  limit?: number;
}) {
  try {
    const queryParams = new URLSearchParams({
      liveStreamId,
      paginationAnchor,
      limit: limit.toString(),
    });

    if (lastCreatedAt) {
      queryParams.append('lastCreatedAt', lastCreatedAt);
    }

    const queryString = queryParams.toString();
    const res = await Api.get(`v1/livestream/chats?${queryString}`);
    return res.body.data;
  } catch (err) {
    console.error(err);
    return [];
  }
}

export async function getLiveStreamFeed({
  page,
  limit,
  chatSpaceIds,
}: {
  page?: number;
  limit?: number;
  chatSpaceIds?: string[];
}) {
  try {
    const queryParams = new URLSearchParams();

    if (page) queryParams.append('page', String(page));
    if (limit) queryParams.append('limit', String(limit));
    if (chatSpaceIds && chatSpaceIds.length > 0) {
      queryParams.append('chatspaceIds', chatSpaceIds.join(','));
    }

    const queryString = queryParams.toString();
    const res = await Api.get(`${chatSpaceRoutes.getLiveStreamFeed}?${queryString}`);
    return res.body.data;
  } catch (err) {
    console.error(err);
    return {
      results: [],
      pagination: {
        page: 1,
        limit: 20,
        totalPages: 1,
        totalResults: 0,
      },
    };
  }
}

export const sendJoinRequest = async (chatSpaceId: string, inviteCode: string) => {
  try {
    const res = await Api.post(`v1/chatspaces/join-request`, { chatSpaceId, inviteCode });
    return res?.body || {};
  } catch (error) {
    console.error('Error joining group via link:', error);
    throw error;
  }
};

/**
 * Approves a single join request.
 * @param requestId - The ID of the request to approve.
 * @param chatSpaceId - The ID of the group.
 */
export const approveJoinRequest = async (requestId: string, chatSpaceId: string) => {
  try {
    const payload = { requestId, chatSpaceId };
    const res = await Api.post('/v1/chatspaces/join-request/approve', payload);
    return res.body;
  } catch (error) {
    console.error('Error approving join request:', error);
    return null;
  }
};

/**
 * Rejects a single join request.
 * @param requestId - The ID of the request to reject.
 */
export const rejectJoinRequest = async (requestId: string) => {
  try {
    const payload = { requestId };
    const res = await Api.post('/v1/chatspaces/join-request/reject', payload);
    return res.body;
  } catch (error) {
    console.error('Error rejecting join request:', error);
    return null;
  }
};

/**
 * Approves all pending join requests for a group.
 * @param chatSpaceId - The ID of the group.
 */
export const approveAllJoinRequests = async (chatSpaceId: string) => {
  try {
    const payload = { chatSpaceId };
    const res = await Api.post('/v1/chatspaces/join-request/approve-all', payload);
    return res.body;
  } catch (error) {
    console.error('Error approving all requests:', error);
    return null;
  }
};

/**
 * Rejects all pending join requests for a group.
 * @param chatSpaceId - The ID of the group.
 */
export const rejectAllJoinRequests = async (chatSpaceId: string) => {
  try {
    const payload = { chatSpaceId };
    const res = await Api.post('/v1/chatspaces/join-request/reject-all', payload);
    return res.body;
  } catch (error) {
    console.error('Error rejecting all requests:', error);
    return null;
  }
};

export interface PendingJoinRequest {
  userData: {
    _id: string;
    username: string;
    name: string;
  };
  status: string;
  createdAt: Date;
}

export interface PendingJoinRequestPaginatedResponse {
  data: PendingJoinRequest[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export const getPendingJoinRequests = async (
  chatSpaceId: string,
  page: number = 1,
  limit: number = 10,
): Promise<PendingJoinRequestPaginatedResponse | null> => {
  try {
    const res = await Api.get(
      `v1/chatspaces/join-request/getPendingRequests/${chatSpaceId}?page=${page}&limit=${limit}`,
    );
    return res.body;
  } catch (error) {
    console.error('Error fetching pending join requests:', error);
    return null;
  }
};

export interface ChatSpaceSearchResult {
  myGroups: ChatSpace[];
  otherGroups: ChatSpace[];
}

export const getChatSpacesByText = async (
  text: string,
  type: 'group' | 'channel',
): Promise<ChatSpaceSearchResult | null> => {
  try {
    const res = await Api.get(`v1/chatspaces/search?text=${encodeURIComponent(text)}&type=${type}`);
    return res.body.data;
  } catch (error) {
    console.error('Error fetching groups:', error);
    return null;
  }
};

//payload.types
export type LiveStreamPayload = {
  targetAmount?: number;
  streamCause?: string;
  thumbnail?: string;
  thumbnailImage?: ImageOrVideo;
  allowGifts?: boolean;
  allowChat?: boolean;
  endTime?: Date;
  isScheduled?: boolean;
  scheduledTime?: Date;
  chatspaceId: string;
  streamType?: StreamType;
};
export enum StreamType {
  private = 'private_stream',
  public = 'public_stream',
}
