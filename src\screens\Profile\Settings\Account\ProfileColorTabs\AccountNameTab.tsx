import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { colors } from '../../../../../theme/colors';
import { getLighterShade } from '../../../../../theme/fonts';
import { useMe } from '../../../../../hooks/util/useMe';

const AccountNameTab = () => {
  const { userPreferencesState } = useMe();

  const [selectedColor, setSelectedColor] = useState(
    userPreferencesState.userPreferences?.account?.messageColor?.primary || colors.nameTabColors[0],
  );

  const previewMessage = 'How is it looking?';
  const previewResponse = "It's really super cool.. Enjoy.!";

  const colorRows = [
    colors.nameTabColors.slice(0, 6),
    colors.nameTabColors.slice(6, 12),
    colors.nameTabColors.slice(12, 18),
  ];

  const updateUserPreferences = (color: string) => {
    userPreferencesState.updatePreferences('account', {
      messageColor: { primary: color, secondary: getLighterShade(color), opacity: 1 },
    });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.previewContainer}>
        <Image
          source={require('../../../../../assets/Image/ChatWallpaper.png')}
          style={styles.backgroundImage}
          resizeMode="cover"
        />
        <View style={styles.chatPreview}>
          <View style={styles.messageBubble}>
            <View
              style={[
                styles.innerView,
                {
                  borderLeftColor: selectedColor,
                  backgroundColor: getLighterShade(selectedColor),
                },
              ]}
            >
              <Text style={[styles.senderName, { color: selectedColor }]}>John</Text>
              <Text style={styles.replyText}>{previewMessage}</Text>
            </View>
            <Text style={styles.messageText}>{previewResponse}</Text>
          </View>
        </View>
      </View>

      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Select a color</Text>
        </View>

        <View style={styles.colorGridContainer}>
          {colorRows.map((row, rowIndex) => (
            <View key={`row-${rowIndex}`} style={styles.colorRow}>
              {row.map((color, index) => (
                <TouchableOpacity
                  key={`color-${rowIndex}-${index}`}
                  activeOpacity={0.7}
                  onPress={() => {
                    updateUserPreferences(color);
                    setSelectedColor(color);
                  }}
                >
                  <View
                    style={[
                      styles.colorOuterCircle,
                      selectedColor === color && { borderColor: color, borderWidth: 2 },
                    ]}
                  >
                    <View style={[styles.colorInnerCircle, { backgroundColor: color }]} />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  previewContainer: {
    backgroundColor: colors.previewBackground,
    marginVertical: 10,
    borderRadius: 10,
    margin: 15,
    marginBottom: 5,
    marginTop: 20,
    position: 'relative',
    overflow: 'hidden',
    height: 230,
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  chatPreview: {
    width: '100%',
    zIndex: 1,
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  messageBubble: {
    backgroundColor: colors._EFEBFC_purple,
    padding: 10,
    borderTopRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopLeftRadius: 16,
    maxWidth: '75%',
    alignSelf: 'flex-end',
    minWidth: '20%',
  },
  innerView: {
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderLeftWidth: 4,
    marginBottom: 10,
  },
  senderName: {
    fontWeight: '600',
    marginBottom: 2,
    fontSize: 14,
  },
  replyText: {
    fontSize: 13,
    color: colors.black_23,
  },
  messageText: {
    fontSize: 14,
    color: colors.black_23,
  },
  sectionContainer: {
    padding: 15,
    backgroundColor: colors.sectionBackground,
    margin: 15,
    borderRadius: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.black_23,
  },
  eyedropperButton: {
    width: 45,
    height: 45,
    backgroundColor: colors.gray_f3,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorGridContainer: {
    alignItems: 'center',
  },
  colorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  colorOuterCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorInnerCircle: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 2,
    borderColor: 'white',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  colorPickerContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
  },
  colorPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
    color: colors.black_23,
  },
  colorPreview: {
    marginBottom: 15,
    height: 40,
    borderRadius: 8,
  },
  colorPanel: {
    marginBottom: 15,
    borderRadius: 8,
  },
  hueSlider: {
    marginBottom: 15,
    height: 30,
    borderRadius: 8,
  },
  opacitySlider: {
    height: 30,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.gray_f3,
  },
  applyButton: {
    backgroundColor: colors.mainPurple,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.black_23,
  },
  applyButtonText: {
    color: 'white',
  },
});

export default AccountNameTab;
