import React, { useState, useEffect } from 'react';
import { View, SafeAreaView, StyleSheet, TouchableOpacity, Text, Image } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { commonFontStyle, hp } from '../../../../theme/fonts';
import StreamToggleOption from '../../../../screens/Home/Channels/components/StreamToggle';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SCREENS } from '../../../../navigation/screenNames';
import { IMAGES } from '../../../../assets/Images';
import RightArrowSVG from '../../../../assets/svgIcons/RightArrowSVG';

const SecurityScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    pin: true,
  });

  useEffect(() => {
    if (route.params?.twoFactorEnabled) {
      setSecuritySettings((prev) => ({
        ...prev,
        twoFactorAuth: true,
      }));
    }
  }, [route.params]);

  const handle2FAToggle = (value: boolean) => {
    if (value) {
      navigation.navigate(SCREENS.Enable2FAScreen);
    } else {
      setSecuritySettings((prev) => ({
        ...prev,
        twoFactorAuth: false,
      }));
    }
  };

  const handlePinToggle = (value: boolean) => {
    if (value) {
      navigation.navigate(SCREENS.SetSecurityPinScreen);
    } else {
      setSecuritySettings((prev) => ({
        ...prev,
        pin: false,
      }));
    }
  };

  const navigateToChangePinScreen = () => {
    navigation.navigate(SCREENS.ChangeSecurityPinScreen);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Security" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <View style={styles.sectionContainer}>
          <StreamToggleOption
            label="Two-factor authentication (2FA)"
            value={securitySettings.twoFactorAuth}
            onToggle={handle2FAToggle}
          />
          <StreamToggleOption label="PIN" value={securitySettings.pin} onToggle={handlePinToggle} />

          {securitySettings.pin && (
            <TouchableOpacity style={styles.settingRow} onPress={navigateToChangePinScreen}>
              <Text style={styles.rowText}>Change PIN</Text>
              <RightArrowSVG color={colors.black_23} width={14} height={14} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  sectionContainer: {
    paddingHorizontal: hp(2),
    paddingVertical: hp(1),
  },
  sectionTitle: {
    ...commonFontStyle(600, 18, colors.black_23),
    marginBottom: hp(2),
    marginLeft: hp(0.5),
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1),
  },
  rowText: {
    flex: 1,
    ...commonFontStyle(400, 16, colors.black_23),
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginRight: 10,
  },
});

export default SecurityScreen;
