import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { IMAGES } from '../assets/Images';
import { colors } from '../theme/colors';
import { commonFontStyle } from '../theme/fonts';
import { styles as headerStyle } from '../component/PersonalChat/PersonalChatHeader';
import { useNavigation } from '@react-navigation/native';
import BackArrowSVG from '../assets/svgIcons/BackArrowSVG';

type Props = {
  title?: any;
  renderRight?: () => React.ReactNode;
  containerStyle?: any;
  onBack?: () => void;
};

const HeaderBackWithTitle = ({ title, renderRight, containerStyle, onBack }: Props) => {
  const navigation = useNavigation();
  return (
    <View style={[headerStyle.headerMainView, { paddingLeft: 0, marginTop: 25 }, containerStyle]}>
      <View style={[headerStyle.header, { gap: 0 }]}>
        <TouchableOpacity
          onPress={onBack ? onBack : () => navigation.goBack()}
          style={headerStyle.headerBackView}
        >
          <BackArrowSVG size={20} color="#fff" />
        </TouchableOpacity>
        <View style={headerStyle.header}>
          <View>
            <Text style={{ ...commonFontStyle(600, 18, colors.white) }}>{title}</Text>
          </View>
        </View>
      </View>
      {renderRight?.()}
    </View>
  );
};

export default HeaderBackWithTitle;

const styles = StyleSheet.create({});
