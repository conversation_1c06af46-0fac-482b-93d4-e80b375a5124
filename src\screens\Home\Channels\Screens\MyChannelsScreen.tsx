//import liraries
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, StatusBar, TouchableOpacity, FlatList, Image } from 'react-native';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import BackArrowSVG from '../../../../assets/svgIcons/BackArrowSVG';
import { navigationRef } from '../../../../navigation/RootContainer';
import useConversations from '../../../../hooks/conversations/useConversations';
import GoLiveInfoModal from '../components/GoliveInfoModal';
import NewLiveStreamModal from '../components/NewLiveStreamModal';
import {
  useLiveStreamController,
  ModalType,
} from '../../../../hooks/channels/useLiveStreamController';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import { navigateTo } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';

// create a component
const MyChannelsScreen = () => {
  const { conversations, myUnarchivedChannels, unarchivedFollowingChannels } = useConversations();
  console.log('myUnarchivedChannels', myUnarchivedChannels, unarchivedFollowingChannels);

  const followedChannels = conversations.filter((c) => c.role === 'owner');
  const [selectedConversation, setSelectedConveration] = useState<IConversation | undefined>(
    undefined,
  );
  const {
    streamingModalState,
    streamDetails,
    setStreamDetails,
    openModal,
    resetStreamingState,
    joinStream,
  } = useLiveStreamController({
    type: ConversationType.CHANNEL,
    Conversation: selectedConversation,
  });
  function onPressChannel(channel: any) {
    setSelectedConveration(channel);
  }

  useEffect(() => {
    if (selectedConversation) {
      openModal(ModalType.SelectLiveMode);
    }
  }, [selectedConversation]);

  console.log('streamDetails', streamDetails);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#7c3aed" barStyle="light-content" />

      <View style={styles.headerMainView}>
        <View style={styles.header}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
            <TouchableOpacity onPress={() => navigationRef.goBack()}>
              <BackArrowSVG size={30} />
            </TouchableOpacity>
            <Text style={{ fontSize: 20, fontWeight: '500', color: colors.white }}>
              My Channels
            </Text>
          </View>
        </View>
      </View>
      <View
        style={{
          paddingVertical: 15,
        }}
      >
        <Text
          style={{
            color: colors.black,
            fontSize: 20,
            fontWeight: '700',
          }}
        >
          {' '}
          Select Channel to Start Livestream{' '}
        </Text>
      </View>
      <FlatList
        data={followedChannels}
        renderItem={({ item }) => {
          return (
            <TouchableOpacity
              style={{
                backgroundColor: 'white',
                padding: 10,
                margin: 5,
                borderRadius: 10,
                flexDirection: 'row',
              }}
              onPress={() => onPressChannel(item)}
            >
              <View>
                <Image
                  source={{ uri: item.displayPic || 'https://picsum.photos/200' }}
                  style={{
                    width: 50,
                    height: 50,
                    borderRadius: 25,
                    marginRight: 15,
                  }}
                />
              </View>
              <View>
                <Text style={{ fontSize: 16, fontWeight: '700', color: colors.black_23 }}>
                  {item.displayName}
                </Text>
                <Text style={{ fontSize: 12, fontWeight: '400', color: colors.gray_86 }}>
                  {item.description}
                </Text>
              </View>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item) => item.id}
      />
      <NewLiveStreamModal
        visible={
          streamingModalState.showModal &&
          streamingModalState.activeModal === ModalType.SelectLiveMode
        }
        onSchedule={() => {
          resetStreamingState();
          navigateTo(SCREENS.ScheduleLiveStreamScreen, {
            conversation: selectedConversation,
          });
          setSelectedConveration(undefined);
        }}
        onGoLive={() => {
          openModal(ModalType.StreamSettings);
        }}
        onClose={() => {
          resetStreamingState();
          setSelectedConveration(undefined);
        }}
      />
      <GoLiveInfoModal
        liveStreamData={streamDetails}
        isVisible={
          streamingModalState.showModal &&
          streamingModalState.activeModal === ModalType.StreamSettings
        }
        onCloseModal={() => {
          resetStreamingState();
          setSelectedConveration(undefined);
        }}
        setStreamDetails={setStreamDetails}
        resetStreamingState={resetStreamingState}
      />
    </View>
  );
};

// define your styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerMainView: {
    paddingHorizontal: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(1.5),
    height: hp(13),
    backgroundColor: colors.mainPurple,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 30,
  },
  streamGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  goLiveButton: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  goLiveText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },

  searchBox: {
    borderRadius: 100,
    backgroundColor: colors._DADADA_gray,
    padding: 10,
  },
});

//make this component available to the app
export default MyChannelsScreen;
