import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import CustomSwitch from './CommonSwitch';
import { commonFontStyle, hp } from '../theme/fonts';
import { colors } from '../theme/colors';

interface Props {
  title: string;
  isVisible: boolean;
  onToggleSwitch: () => void;
}

const RenderToggle = ({ title, isVisible, onToggleSwitch }: Props) => {
  return (
    <View style={styles.renderTypeView}>
      <Text style={styles.title}>{title}</Text>
      <CustomSwitch isToggleOn={isVisible} onToggleSwitch={onToggleSwitch} />
    </View>
  );
};

export default RenderToggle;

const styles = StyleSheet.create({
  renderTypeView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: hp(1),
  },
  title: {
    ...commonFontStyle(400, 16, colors.black_23),
    flex: 1,
  },
});
