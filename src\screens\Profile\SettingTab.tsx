import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Image,
} from 'react-native';
import React, { useState } from 'react';
import SearchInput from '../../component/SearchInput';
import { commonFontStyle, hp } from '../../theme/fonts';
import { IMAGES } from '../../assets/Images';
import { colors } from '../../theme/colors';
import { SCREENS } from '../../navigation/screenNames';
import { useTranslation } from 'react-i18next';
import { navigateTo } from '../../utils/commonFunction';
import { FeatherIcons, FontAwesome5Icons } from '../../utils/vectorIcons';
import InfoSVG from '../../assets/svgIcons/InfoSVG';
import ChatSVG from '../../assets/svgIcons/ChatSVG';
import BellSVG from '../../assets/svgIcons/BellSVG';
import StarSVG from '../../assets/svgIcons/StarSVG';
import PrivacySVG from '../../assets/svgIcons/PrivacySVG';
import LanguageGlobeSVG from '../../assets/svgIcons/LanguageGlobeSVG';
import PersonSVG from '../../assets/svgIcons/PersonSVG';
import ReplyAvatarSVG from '../../assets/svgIcons/ReplyAvatarSVG';

const boxData = [
  {
    title: 'Account',
    renderIcon: () => <ReplyAvatarSVG size={21} />,
    screenName: SCREENS.AccountScreen,
  },
  {
    title: 'Chats',
    renderIcon: () => <ChatSVG size={23} />,
    screenName: SCREENS.ChatSettingsScreen,
  },
  {
    title: 'Privacy',
    renderIcon: () => <PrivacySVG size={22} />,
    screenName: SCREENS.PrivacySetting,
  },
  {
    title: 'Notifications',
    renderIcon: () => <BellSVG size={22} />,
    screenName: SCREENS.NotificationScreen,
  },
  {
    title: 'Favourites',
    renderIcon: () => <StarSVG size={24} color={colors.black_23} />,
    screenName: SCREENS.FavouriteScreen,
  },
  {
    title: 'Language',
    renderIcon: () => <LanguageGlobeSVG size={22} />,
    screenName: SCREENS.AppLanguageScreen,
  },
];

const SettingTab = () => {
  const [search, setSearch] = useState('');
  const { t } = useTranslation();

  const onPress = (screenName: string) => {
    if (screenName) navigateTo(screenName);
  };

  const RenderBox = ({ item }: any) => {
    return (
      <TouchableOpacity onPress={() => onPress(item.screenName)} style={styles.boxView}>
        {item.renderIcon()}
        <Text style={styles.boxTitle}>{t(item.title)}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <SearchInput value={search} onChangeText={setSearch} />

      <FlatList
        data={boxData.filter((item) => item.title.toLowerCase().includes(search.toLowerCase()))}
        numColumns={3}
        scrollEnabled={false}
        renderItem={({ item }) => <RenderBox item={item} />}
        columnWrapperStyle={styles.columnWrapper}
        contentContainerStyle={styles.flatListContainer}
      />
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: '100%',
          backgroundColor: colors.gray_f3,
          marginTop: 30,
          padding: 10,
          borderRadius: 30,
          gap: 10,
        }}
        onPress={() => navigateTo(SCREENS.PremiumFeaturesScreen)}
      >
        <View
          style={{
            padding: 10,
            backgroundColor: colors.mainPurple,
            borderRadius: 100,
          }}
        >
          <FontAwesome5Icons name="crown" color={'white'} />
        </View>
        <Text style={[styles.boxTitle, { fontSize: 15, fontWeight: '500' }]}>
          ChatBucket Premium
        </Text>
        <FeatherIcons
          name="arrow-right"
          color={colors.gray_80}
          style={{ marginLeft: 'auto' }}
          size={25}
        />
      </TouchableOpacity>
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          // marginBottom: 50,
          marginTop: 20,
        }}
        onPress={() => navigateTo(SCREENS.HelpScreen)}
      >
        <InfoSVG size={18} color="#888" />
        <Text style={{ color: '#888', fontSize: 15, marginLeft: 8 }}>Help</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default SettingTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  flatListContainer: {
    marginTop: hp(1),
  },
  columnWrapper: {
    gap: hp(2),
  },
  boxView: {
    backgroundColor: colors.gray_f3,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(2),
    borderRadius: 15,
    paddingVertical: hp(3),
  },
  boxTitle: {
    // ...commonFontStyle(400, 14, colors.black_23),
    fontSize: 14,
    fontWeight: '400',
    color: colors.black_23,
    marginTop: 3,
  },
  footerBox: {
    height: 100,
    backgroundColor: 'red',
    marginTop: hp(2),
  },

  boxIcon: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
});
