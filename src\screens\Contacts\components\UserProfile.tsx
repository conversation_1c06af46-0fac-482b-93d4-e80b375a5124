import React from 'react';
import { Text, TouchableHighlight, View } from 'react-native';
import { IContact } from '../../../device-storage/realm/schemas/ContactSchema';
import { AppStyles } from '../../../theme/appStyles';
import { IUser, UserSchema } from '../../../device-storage/realm/schemas/UserSchema';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import { colors } from '../../../theme/colors';
import {
  ChatSpecificScreenParams,
  ChatSpecificScreenParamsT,
} from '../../Home/Chats/ChatSpecificScreen';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';
import { ConversationType } from '../../../device-storage/realm/schemas/MessageSchema';
import PressableHighlight from '../../../component/utils/PressableHighlight';
import UserAvatar from '../../../component/utils/UserAvatar';

type UserProfileProps = {
  user: UserSchema;
  isBlocked?: boolean;
};

const UserProfile = (props: UserProfileProps) => {
  if (!props.user.isValid) return null;

  const handleProfilePress = () => {
    const paramsData: ChatSpecificScreenParamsT = {
      userData: {
        displayName: props.user.contactName || props.user.name,
        conversation: {} as IConversation,
        type: ConversationType.P2P,
        id: props.user.id,
      },
      data: {
        convId: props.user.id,
      },
      isFollowing: false,
    };
    navigateTo(SCREENS.ChatSpecificScreen, paramsData);
  };

  return (
    <PressableHighlight onPress={handleProfilePress}>
      <View style={[AppStyles.profileCard, { opacity: props.isBlocked ? 0.5 : 1 }]}>
        <UserAvatar width={50} imgUrl={props.user.profilePic} />
        <View style={{ gap: 2 }}>
          <Text numberOfLines={1} style={[AppStyles.baseText, { fontWeight: 600 }]}>
            {props.user.contactName}
            {props.isBlocked ? (
              <Text
                style={{
                  fontSize: 14,
                  color: colors.gray_80,
                  fontStyle: 'italic',
                  letterSpacing: 0.5,
                }}
              >
                {' (blocked)'}
              </Text>
            ) : null}
          </Text>
          <Text numberOfLines={1} style={{ color: colors.gray_80, fontSize: 14 }}>
            {props.user.bio}
          </Text>
        </View>
      </View>
    </PressableHighlight>
  );
};

export default UserProfile;
