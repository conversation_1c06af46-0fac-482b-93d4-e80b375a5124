import { useEffect, useMemo, useRef, useState } from 'react';
import { ContactSchema, IContact } from '../../device-storage/realm/schemas/ContactSchema';
import { UserSchema } from '../../device-storage/realm/schemas/UserSchema';

export const useFilteredContacts = (
  registered: UserSchema[],
  unregistered: IContact[],
  searchText: string,
  options?: { excludeUserId?: string },
) => {
  const query = searchText.trim().toLowerCase();

  const filteredRegistered = useMemo(() => {
    let list: any[] = registered;
    if (options?.excludeUserId) {
      list = list.filter((c: any) => (c?.id ?? c?.userId ?? c?._id) !== options.excludeUserId);
    }
    if (!query) return list as UserSchema[];
    return list.filter((item) => JSON.stringify(item).toLowerCase().includes(query));
  }, [registered, query, options?.excludeUserId]);

  const filteredUnregistered = useMemo(() => {
    if (!query) return unregistered;
    return unregistered.filter((item) => JSON.stringify(item).toLowerCase().includes(query));
  }, [unregistered, query]);

  return { filteredRegistered, filteredUnregistered };
};
