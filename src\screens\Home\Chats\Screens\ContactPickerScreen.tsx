import React, { useState } from 'react';
import {
  FlatList,
  SafeAreaView,
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ToastAndroid,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import CommonView from '../../../../component/CommonView';
import SearchInput from '../../../../component/SearchInput';
import ContactItem from '../../../../component/ContactItem';
import { useContacts } from '../../../../hooks/contacts/useContacts';
import { useFilteredContacts } from '../../../../hooks/common/useFilterContacts';

import { commonFontStyle, hp } from '../../../../theme/fonts';
import { colors } from '../../../../theme/colors';
import { showToast } from '../../../../utils/commonFunction';
import { UserSchema } from '../../../../device-storage/realm/schemas/UserSchema';

type RootStackParamList = {
  ContactPickerScreen: {
    onSelectContacts?: (contacts: any[]) => void;
  };
};

type ContactPickerScreenRouteProp = RouteProp<RootStackParamList, 'ContactPickerScreen'>;

const ContactPickerScreen = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const MAX_SELECTION = 3;
  const route = useRoute<ContactPickerScreenRouteProp>();
  const { onSelectContacts } = route.params;
  const [search, setSearch] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<UserSchema[]>([]);
  const isSelectionMode = selectedContacts.length > 0;
  const { registeredContacts, unregisteredContacts } = useContacts();
  const { filteredRegistered, filteredUnregistered } = useFilteredContacts(
    [...registeredContacts],
    [...unregisteredContacts],
    search,
  );

  const toggleSelect = (contact: any) => {
    setSelectedContacts((prev) => {
      const isAlreadySelected = prev.some(
        (c) =>
          (c.id && contact.id && c.id === contact.id) ||
          (!c.id && !contact.id && c.phoneNumber === contact.phoneNumber),
      );

      if (isAlreadySelected) {
        return prev.filter(
          (c) =>
            !(
              (c.id && contact.id && c.id === contact.id) ||
              (!c.id && !contact.id && c.phoneNumber === contact.phoneNumber)
            ),
        );
      } else {
        if (prev.length >= MAX_SELECTION) {
          showToast(t('You can select up to 3 contacts only.'));
          return prev;
        }
        return [...prev, contact];
      }
    });
  };

  const handleDeselectAll = () => setSelectedContacts([]);

  const handleSend = () => {
    onSelectContacts?.(selectedContacts);
    navigation.goBack();
  };

  return (
    <CommonView
      headerTitle={`${selectedContacts.length > 0 ? selectedContacts.length : t('Select')} ${t(
        'Contacts',
      )}`}
      renderRight={() =>
        selectedContacts.length > 0 && (
          <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity onPress={handleDeselectAll} style={styles.deselectBtn}>
              <Text style={styles.deselectText}>{t('Deselect All')}</Text>
            </TouchableOpacity>
          </View>
        )
      }
    >
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.searchView}>
          <SearchInput value={search} onChangeText={setSearch} />
        </View>
        <FlatList
          data={filteredRegistered}
          keyExtractor={(item) => item.id as string}
          renderItem={({ item }) => (
            <ContactItem
              type="register"
              data={item}
              onPress={() => toggleSelect(item)}
              isSelectionMode={isSelectionMode}
              isSelected={
                !!selectedContacts.find(
                  (c) =>
                    (c.id && item.id && c.id === item.id) ||
                    (!c.id && !item.id && c.phoneNumber === item.phoneNumber),
                )
              }
            />
          )}
          ListHeaderComponent={<Text style={styles.titleText}>{t('Contacts on ChatBucket')}</Text>}
          ListFooterComponent={
            <>
              {/* Unregistered Contacts */}
              <Text style={styles.titleText}>{t('Invite to ChatBucket')}</Text>
              <FlatList
                data={filteredUnregistered}
                keyExtractor={(item, index) => `${index}`}
                scrollEnabled={false}
                renderItem={({ item }) => (
                  <ContactItem
                    type="unregister"
                    data={item}
                    onPress={() => toggleSelect(item)}
                    isSelectionMode={isSelectionMode}
                    isSelected={
                      !!selectedContacts.find(
                        (c) =>
                          (c.id && item?.id && c.id === item?.id) ||
                          (!c.id && !item?.id && c.phoneNumber === item.phoneNumber),
                      )
                    }
                    showInviteButton={false}
                  />
                )}
              />
            </>
          }
        />

        {/* Send Button */}
        {selectedContacts.length > 0 && (
          <View style={styles.sendButtonWrapper}>
            <TouchableOpacity style={styles.sendButton} onPress={handleSend}>
              <Text style={styles.sendText}>{t('Send')}</Text>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </CommonView>
  );
};

export default ContactPickerScreen;

const styles = StyleSheet.create({
  searchView: {
    paddingHorizontal: hp(2),
    paddingTop: hp(2),
    backgroundColor: colors.gray_f3,
  },
  titleText: {
    ...commonFontStyle(500, 16, colors.gray_80),
    paddingHorizontal: hp(2),
    marginTop: hp(2),
    marginBottom: hp(1),
  },
  deselectBtn: {
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  deselectText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
  },
  sendButtonWrapper: {
    paddingHorizontal: hp(2),
    paddingTop: hp(1),
  },
  sendButton: {
    backgroundColor: colors.mainPurple,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  sendText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
