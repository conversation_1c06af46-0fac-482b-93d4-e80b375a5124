import { Realm } from 'realm';
import { realmSchemaNames } from '../schemas/schemaNames';
import { RemoteUser } from '../../../types/index.types';
import { safeRealmWrite } from '../lib';
import { IUser, UserSchema } from '../schemas/UserSchema';
import { getRandomColor } from '../../../lib/lib';
import { ConversationSettingsRepo } from './ConversationSettingsRepo';

export class UserRepo {
  private static schemaName = realmSchemaNames.user;
  constructor() {}

  static findRealmObjectByUserId(realm: Realm, id: string) {
    return realm.objectForPrimaryKey(UserSchema, id);
  }

  static findByUserId(realm: Realm, userId: string): IUser | null {
    const user = this.findRealmObjectByUserId(realm, userId);
    return user ? (user.toJSON() as unknown as IUser) : null;
  }
  static handleProfileUpdate(realm: Realm, user: RemoteUser) {
    const existingUser = this.findRealmObjectByUserId(realm, user._id);
    if (!existingUser) return;

    safeRealmWrite(realm, () => {
      existingUser.profilePic = user.image || existingUser.profilePic;
      existingUser.username = user.username || existingUser.username;
    });
  }

  static createOrUpdate(
    realm: Realm,
    user: Pick<RemoteUser, '_id' | 'username' | 'name' | 'image' | 'bio'> & {
      contactName?: string;
      phoneNumber?: string;
    },
    isDeviceContact: boolean,
  ): UserSchema {
    const existingUser = this.findRealmObjectByUserId(realm, user._id);

    if (!existingUser) {
      const conversationSettings = ConversationSettingsRepo.getOrcreate(realm, user._id);
      const newUser: IUser = {
        id: user._id,
        username: user.username,
        name: user.name,
        profilePic: user.image,
        bio: user.bio,
        isDeviceContact: isDeviceContact,
        textColor: getRandomColor(),
        contactName: user.name,
        phoneNumber: user.phoneNumber,
        conversationSettings,
      };

      let createdUser!: UserSchema;
      safeRealmWrite(realm, () => {
        const existingContact = this.findRealmObjectByUserId(realm, user._id);
        if (existingContact) {
          newUser.isDeviceContact = true;
        }
        createdUser = realm.create(UserSchema, newUser);
      });

      return createdUser;
    }

    // Update existing user
    safeRealmWrite(realm, () => {
      existingUser.profilePic = user.image || existingUser.profilePic;
      existingUser.username = user.username || existingUser.username;
      existingUser.bio = user.bio || existingUser.bio;
      existingUser.name = user.name || existingUser.name;
      existingUser.isDeviceContact = isDeviceContact || existingUser.isDeviceContact;
      existingUser.contactName = user.contactName || existingUser.contactName;
      existingUser.phoneNumber = user.phoneNumber || existingUser.phoneNumber;
    });

    return existingUser;
  }

  static getContactUsers = (realm: Realm) => {
    return realm.objects(UserSchema).filtered('isDeviceContact == true');
  };
}
