// import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
// import React, { useEffect, useState } from 'react';
// import ModalWrapper from '../ModalWrapper';
// import SearchInput from '../SearchInput';
// import HorizontalListView from '../HorizontalListView';
// import { IMAGES } from '../../assets/Images';
// import { commonFontStyle, hp } from '../../theme/fonts';
// import { colors } from '../../theme/colors';
// import { FILTERS } from '../../utils/Filters';
// import { musicList } from '../../utils/constants';
// import { uploadImage } from '../../utils/apiGlobal';
// // import { AdenCompat } from "react-native-image-filter-kit"; // Default filter for initial render
// import Stickers from '../../utils/stickers.json';
// import CustomImage from '../CustomImage';
// interface IProps {
//   isVisible: boolean;
//   onCloseModal: (value: boolean) => void;
//   setIsSelectedFilter?: (value: any) => void;
//   selectMusic?: (value: { id?: any; title?: any; music?: any }) => void;
// }
// const FilterModal = ({
//   isVisible,
//   onCloseModal,
//   setIsSelectedFilter = () => {},
//   selectMusic = () => {},
// }: IProps) => {
//   let tabs = ['Filter', 'Stickers', 'Music'];
//   const [selectedFilter, setSelectedFilter] = useState('');

//   const [_isVisible, setIsVisible] = useState(isVisible);
//   const [value, setValue] = useState('');
//   const [selectedItem, setSelectedItem] = useState('Filter');
//   const [selectIndex, setSelectIndex] = useState<number>();

//   useEffect(() => {
//     setIsVisible(isVisible);
//   }, [isVisible]);

//   const onClose = () => {
//     setIsVisible(false);
//     onCloseModal(false);
//   };

//   const renderFilterList = (item: any, index: number) => {
//     // const FilterComponent = item.filterComponent; // Dynamically load the filter component

//     return (
//       <TouchableOpacity
//         style={{ marginRight: 10, alignItems: 'center' }}
//         onPress={() => {
//           // setSelectedFilter(item?.title);
//           setIsSelectedFilter(item);
//         }}
//       >
//         {/* <FilterComponent
//           image={ */}
//         <Image
//           source={IMAGES.maskImage}
//           style={{ width: 60, height: 60, borderRadius: 10 }}
//           resizeMode="contain"
//         />
//         {/* }
//           style={{ width: 60, height: 60, borderRadius: 10 }}
//         /> */}
//         <Text style={styles.filterText}>{item?.title}</Text>
//       </TouchableOpacity>
//     );
//   };

//   const renderStickerList = (item: any, index: number) => {
//     return <CustomImage uri={item} size={60} onPress={() => {}} />;
//   };
//   const FilterList = ({ data, type }: any) => {
//     return (
//       <FlatList
//         data={data}
//         renderItem={({ item, index }) => {
//           return type === 'filter' ? renderFilterList(item, index) : renderStickerList(item, index);
//         }}
//         horizontal
//         contentContainerStyle={{ padding: hp(1) }}
//         showsHorizontalScrollIndicator={false}
//       />
//     );
//   };

//   const sendMusic = async (e: any) => {
//     // const obj = {
//     //   uri: e.music,
//     //   type: "audio/mp",
//     //   name: "Music.mp3",
//     // };

//     // const link = await uploadImage(obj);

//     // console.log("link", link);
//     selectMusic(e);
//   };

//   const renderMusicList = ({ item, index }: { item: any; index: number }) => {
//     return (
//       <TouchableOpacity
//         onPress={() => {
//           sendMusic(item);
//         }}
//         key={index}
//         style={styles.musicListContainer}
//       >
//         <Text style={{}}>{index + 1}.</Text>
//         <Image source={IMAGES.maskImage} style={{ width: 50, height: 50 }} resizeMode="contain" />
//         <View style={{ flex: 1 }}>
//           <Text style={styles.musicText}>{item?.title ?? 'Dreamer'}</Text>
//           <Text style={styles.numberText}>{'250,118,880'}</Text>
//         </View>
//         <TouchableOpacity
//           onPress={() => {
//             if (selectIndex === index) {
//               setSelectIndex(undefined);
//             } else {
//               setSelectIndex(index);
//             }
//           }}
//         >
//           <Image
//             source={selectIndex == index ? IMAGES.pause_icon : IMAGES.play_icon}
//             style={{ width: 20, height: 20 }}
//             resizeMode="contain"
//           />
//         </TouchableOpacity>
//       </TouchableOpacity>
//     );
//   };

//   const MusicList = () => {
//     return (
//       <FlatList
//         data={musicList}
//         renderItem={renderMusicList}
//         contentContainerStyle={{
//           padding: hp(1),
//           gap: hp(1),
//           flexGrow: 1,
//         }}
//         showsVerticalScrollIndicator={false}
//       />
//     );
//   };
//   return (
//     <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
//       <HorizontalListView
//         data={tabs}
//         defaultSelectedItem="Filter"
//         selectedItem={setSelectedItem}
//         contentContainerStyle={{ paddingLeft: '20%' }}
//       />
//       <SearchInput value={value} onChangeText={setValue} />
//       <View style={{ paddingVertical: hp(1) }}>
//         {selectedItem == 'Filter' && <FilterList data={FILTERS} type="filter" />}

//         {selectedItem == 'Stickers' && <FilterList data={Stickers} type="sticker" />}

//         {selectedItem == 'Music' && (
//           <View style={{ height: hp(50) }}>
//             <MusicList />
//           </View>
//         )}
//       </View>
//     </ModalWrapper>
//   );
// };

// export default FilterModal;

// const styles = StyleSheet.create({
//   musicListContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     gap: 10,
//   },
//   musicText: {
//     ...commonFontStyle(500, 16, colors.black_23),
//   },
//   numberText: {
//     ...commonFontStyle(400, 14, colors.black_23),
//   },
//   filterText: { ...commonFontStyle(400, 16, colors.black_23) },
// });
