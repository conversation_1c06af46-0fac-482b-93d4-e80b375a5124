import React, { useState, useRef, useEffect, use } from 'react';
import { colors } from '../../../theme/colors';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  PanResponder,
  Animated,
  Dimensions,
  BackHandler,
  ToastAndroid,
  PermissionsAndroid,
} from 'react-native';

import { RTCView } from 'react-native-webrtc';

import CallMemberTile from './CallMembertile';
import { _openAppSetting } from '../../../utils/locationHandler';
import { useCallContext } from '../../../Context/CallProvider';
import { IMAGES } from '../../../assets/Images';
import { Participant } from '../../../types/calls.types';
import { commonFontStyle, hp, wp } from '../../../theme/fonts';
import { Text } from 'react-native-gesture-handler';
import { navigationRef } from '../../../navigation/RootContainer';
import { CallScreenModalKey, CallScreenModals } from '../MainCallScreen';
import { useMediasoup } from '../../../Context/RtcProvider';
import ScreenSharingDisplay from './ScreenSharingDisplay';
import { zIndex } from '../../../utils/Filters';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useResetTimer from '../../../hooks/calls/useresettimer';
import { CallButtons } from './CallButtons';
import FullScreenView from './FullScreenView';
import SingleParticipantView from './PartcipantViews/SingleParticipantView';
import TwoParticipantView from './PartcipantViews/TwoParticipantView';
import ThreeParticipantView from './PartcipantViews/ThreeParticipantView';
import GridParticipantsView from './PartcipantViews/GridParticipantsView';
import Toast from 'react-native-toast-message';
import { useMe } from '../../../hooks/util/useMe';
import { FontAwesome6Icons } from '../../../utils/vectorIcons';
import ThreeDotsSVG from '../../../assets/svgIcons/ThreeDotsSVG';
import { requestCameraPermission } from '../../../utils/usePermission';

const SCREEN_SHARE_BANNER_TIME = 5 * 1000;
const FULL_SCREEN_TIMEOUT = 5 * 1000;

type SelectedUser = {
  participant: Participant | null;
  idx: number | null;
};

type OngoingCallProps = {
  fullScreenMode: boolean;
  setFullScreenMode: React.Dispatch<React.SetStateAction<boolean>>;
  setShowningFooter: React.Dispatch<React.SetStateAction<boolean>>;
  showingFooter: boolean;
  participants: Participant[];
  callModalState: CallScreenModals;
  openModal: (modal: CallScreenModalKey) => void;
  closeModal: (modal: CallScreenModalKey) => void;
  setOptionsModal: React.Dispatch<React.SetStateAction<boolean>>;
  setCallMembersModal: React.Dispatch<React.SetStateAction<boolean>>;
};

type ScreenSharingBannerState = {
  visible: boolean;
  participant: Participant | null;
  timer: NodeJS.Timeout | null;
};

// create a component
const OngoingCall = ({
  fullScreenMode,
  setFullScreenMode,
  setShowningFooter,
  showingFooter,
  participants,
  callModalState,
  openModal,
  closeModal,
  setOptionsModal,
  setCallMembersModal,
}: OngoingCallProps) => {
  const {
    callDetails,
    producers,
    stopScreenSharing,
    endCall,
    showFullScreenCall,
    setShowFullScreenCall,
  } = useCallContext();
  const { mediaConsumers, mediaProducers } = useMediasoup();
  const insets = useSafeAreaInsets();
  const {
    startTimer,
    resetTimer,
    stopTimer,
    timerCompleted: renderFullScreen,
  } = useResetTimer(FULL_SCREEN_TIMEOUT);

  const [selectedUser, setSelectedUser] = useState<SelectedUser>({
    idx: null,
    participant: null,
  });

  const [selectedScreenSharer, setSelectedScreenSharer] = useState<SelectedUser>({
    idx: null,
    participant: null,
  });

  const [showScreenSharingBanner, setShowScreenSharingBanner] = useState<ScreenSharingBannerState>({
    visible: false,
    participant: null,
    timer: null,
  });

  const renderParticipantsGrid = () => {
    const participants = callDetails.participants;
    const participantCount = participants.length;
    // Case 1: Single participant (full screen)
    if (participants.length === 1) {
      return (
        <SingleParticipantView
          participants={participants}
          callDetails={callDetails}
          selectUser={selectUser}
          fullScreenMode={fullScreenMode}
        />
      );
    }

    // Case 2: Two participants (split screen vertical)
    if (participantCount === 2) {
      return (
        <TwoParticipantView
          participants={participants}
          callDetails={callDetails}
          selectUser={selectUser}
          fullScreenMode={fullScreenMode}
        />
      );
    }

    // Case 3: Three participants (special layout - 2 in first row, 1 full width in second row)
    if (participantCount === 3) {
      return (
        <ThreeParticipantView
          participants={participants}
          callDetails={callDetails}
          selectUser={selectUser}
          fullScreenMode={fullScreenMode}
        />
      );
    }

    // Case 4 and more: Four or more participants (2×2 grid with potential scrolling)
    return (
      <GridParticipantsView
        participants={participants}
        callDetails={callDetails}
        selectUser={selectUser}
        fullScreenMode={fullScreenMode}
      />
    );
  };
  function selectUser(participant: Participant, idx: number) {
    if (selectedScreenSharer.idx != null) {
      Toast.show({
        text1: 'A user is actively sharing screen.Cannot switch to fullScreen',
      });
      return;
    }
    setSelectedUser({ participant, idx });

    setFullScreenMode(true);
  }

  const resetScreenShareBanner = () => {
    showScreenSharingBanner.timer && clearTimeout(showScreenSharingBanner.timer);
    setShowScreenSharingBanner({
      participant: null,
      visible: false,
      timer: null,
    });
  };

  useEffect(() => {
    const sharers = mediaConsumers.screenSharers;

    if (!sharers || sharers.length === 0) {
      setFullScreenMode(false);
      resetScreenShareBanner();
      setSelectedScreenSharer({ participant: null, idx: null });
      stopTimer();
      return;
    }

    if (selectedScreenSharer?.idx != null) return;

    const firstSharer = sharers[0];
    const participant = participants[firstSharer.idx];
    if (!participant) return;

    setFullScreenMode(true);
    setSelectedScreenSharer({ idx: firstSharer.idx, participant: participant });
    setSelectedUser({ idx: null, participant: null });
    if (showScreenSharingBanner.timer) {
      clearTimeout(showScreenSharingBanner.timer);
    }

    const timerId = setTimeout(() => {
      resetScreenShareBanner();
      startTimer();
    }, SCREEN_SHARE_BANNER_TIME);

    setShowScreenSharingBanner({
      participant,
      visible: true,
      timer: timerId,
    });
  }, [
    mediaConsumers.screenSharers.length,
    participants,
    selectedScreenSharer?.idx,
    showScreenSharingBanner.timer,
  ]);

  function handleBackPress() {
    if (fullScreenMode) {
      setFullScreenMode(false);
      setSelectedUser({ idx: null, participant: null });
      setSelectedScreenSharer({ idx: null, participant: null });
      return true;
    }
    return false;
  }

  function handleBackButton() {
    if (showFullScreenCall === true) {
      setShowFullScreenCall(false);
    }
    if (fullScreenMode) {
      setFullScreenMode(false);
      setSelectedUser({ idx: null, participant: null });
      setSelectedScreenSharer({ idx: null, participant: null });
      return;
    }
  }

  useEffect(() => {
    const backAction = () => {
      if (fullScreenMode === true) {
        handleBackPress();
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [fullScreenMode]);

  return (
    <View style={{ flex: 1, width: '100%', backgroundColor: 'black' }}>
      {!renderFullScreen && (
        <CallHeader
          callModalState={callModalState}
          closeModal={closeModal}
          fullScreenMode={fullScreenMode}
          openModal={openModal}
          seconds={1}
          setFullScreenMode={setFullScreenMode}
          setOptionsModal={setOptionsModal}
          setSelectedUser={setSelectedUser}
          onBackPress={() => {
            handleBackButton();
          }}
        />
      )}
      {!producers.isScreenSharing && !fullScreenMode && (
        <>
          <View style={{ flex: 1 }}>{renderParticipantsGrid()}</View>
          <MyComponent
            callDetails={callDetails}
            isScreenSharing={mediaProducers.producers.isScreenSharing}
            setFullScreenMode={setFullScreenMode}
          />
        </>
      )}
      {!producers.isScreenSharing && fullScreenMode && (
        <View style={{ flex: 1 }}>
          <View
            style={{
              flex: 1,
              zIndex: zIndex.level_1,
            }}
          >
            <FullScreenView
              selectedUser={selectedUser}
              resetTimer={resetTimer}
              producers={producers}
              callDetails={callDetails}
              showScreenSharingBanner={showScreenSharingBanner}
              fullScreenMode={fullScreenMode}
              selectUser={selectUser}
              selectedScreenSharer={selectedScreenSharer}
            />
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: insets.bottom + 80,
              zIndex: zIndex.level_2,
              height: 220,
              width: '100%',
              display: renderFullScreen ? 'none' : 'flex',
            }}
          >
            <FlatList
              data={callDetails.participants}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={(item) => item.participantId}
              contentContainerStyle={{ paddingHorizontal: 10 }}
              renderItem={({ item, index }) => {
                if (selectedUser?.participant?.participantId === item.participantId) {
                  return null;
                }
                if (selectedScreenSharer?.participant?.participantId === item.participantId) {
                  return null;
                }
                return (
                  <View
                    style={{
                      width: 130,
                      height: 210,
                      borderRadius: 40,
                      marginRight: callDetails.participants.length === index + 1 ? 0 : 15, // your horizontal gap
                    }}
                  >
                    <CallMemberTile
                      callDetails={callDetails}
                      participent={item}
                      idx={index}
                      isScreenSharing={false}
                      selectUser={selectUser}
                      fullScreenMode={fullScreenMode}
                    />
                  </View>
                );
              }}
            />
          </View>
        </View>
      )}
      {producers.isScreenSharing && (
        <>
          <ScreenSharingDisplay
            fullScreenMode={fullScreenMode}
            participants={participants}
            selectUser={selectUser}
            showingFooter={showingFooter}
          />
          {/* <MyComponent
            callDetails={callDetails}
            isScreenSharing={mediaProducers.producers.isScreenSharing}
            setFullScreenMode={setFullScreenMode}
          /> */}
        </>
      )}
      {!renderFullScreen && (
        <CallButtons setCallMembersModal={setCallMembersModal} setOptionsModal={setOptionsModal} />
      )}
    </View>
  );
};

//make this component available to the app
export default OngoingCall;

interface MyComponentProps {
  callDetails?: CallDetails;
  isScreenSharing: boolean | undefined;
  setFullScreenMode: (value: React.SetStateAction<boolean>) => void;
}

interface CallDetails {
  type: 'video' | 'audio';
}

const MyComponent: React.FC<MyComponentProps> = ({
  callDetails,
  isScreenSharing,
  setFullScreenMode,
}) => {
  const { user } = useMe();
  const { producers } = useCallContext();

  const [videoUrl, setVideoURL] = useState<string | null>(null);

  useEffect(() => {
    const iffe = async () => {
      if (producers.videoStream) {
        const url = producers.videoStream.toURL();
        setVideoURL(url);
      }
    };
    iffe();
  }, [producers?.videoStream]);

  const pan = useRef(new Animated.ValueXY({ x: 20, y: 150 })).current;
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // When touch starts, extract the current offset
        pan.extractOffset();
      },
      onPanResponderMove: Animated.event([null, { dx: pan.x, dy: pan.y }], {
        useNativeDriver: false,
      }),
      onPanResponderRelease: () => {
        // When released, flatten the offset to make it permanent
        pan.flattenOffset();
      },
    }),
  ).current;

  return (
    <Animated.View
      onLayout={(e) => {
        const { width, height } = e.nativeEvent.layout;
        console.log('Width:', width, 'Height:', height);
      }}
      {...panResponder.panHandlers}
      style={{
        position: 'absolute',
        transform: pan.getTranslateTransform(),
        zIndex: 999,
        elevation: 10,
        padding: 4,
        bottom: 250,
        right: 40,
      }}
    >
      <TouchableOpacity
        onPress={() => {
          setFullScreenMode(true);
        }}
        style={{
          height: callDetails?.type === 'audio' ? 0 : hp(25),
          width: callDetails?.type === 'audio' ? 0 : wp(35),
        }}
      >
        <View
          style={{
            borderRadius: 100,
            borderWidth: callDetails?.type == 'audio' ? 8 : 0,
            borderColor: 'rgba(0, 0, 0, 0.12)',
            backgroundColor: 'red',
          }}
        >
          <>
            {callDetails?.type === 'video' && videoUrl ? (
              <RTCView
                mirror={true}
                streamURL={videoUrl}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                objectFit="cover"
                zOrder={1}
              />
            ) : callDetails?.type === 'audio' && isScreenSharing ? (
              (() => {
                return (
                  <Image
                    source={{ uri: user?.image || '' }}
                    style={{ height: 140, width: 140, borderRadius: 100 }}
                    resizeMode="cover"
                  />
                );
              })()
            ) : null}
          </>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  gridContainer: {
    flex: 1,
    height: Dimensions.get('window').height, // or a fixed height if needed
    width: '100%',
  },
  fullScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  twoParticipantsContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  halfScreenParticipant: {
    flex: 1,
    width: '100%',
  },
  threeParticipantsContainer: {
    flex: 1,
    width: '100%',
  },
  firstRow: {
    flex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  secondRow: {
    flex: 1,
    width: '100%',
  },
  halfWidthTile: {
    flex: 1,
    width: '50%',
    // height: '50%',
  },
  fullWidthTile: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  buttonContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    right: 10,
    zIndex: zIndex.level_4,
    paddingHorizontal: 10,
  },
  iconStyle: {
    width: 40,
    height: 40,
  },
  iconStyle1: {
    width: 24,
    height: 24,
  },
});

const formatTime = (totalSeconds: any) => {
  const mins = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(1, '0');
  const secs = (totalSeconds % 60).toString().padStart(2, '0');
  return `${mins}:${secs}`;
};

const headerStyle = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // gap: 15,
  },
  selectedView: {
    backgroundColor: colors._7155C3_purple,
    paddingVertical: 10,
    paddingHorizontal: 25,
    borderRadius: 50,
  },
  tabIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  tabText: {
    ...commonFontStyle(500, 16, colors.white),
  },
  headerMainView: {
    paddingLeft: hp(2),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // paddingVertical: hp(1.5),
    height: hp(8),
  },
  moreMenu: {
    padding: hp(2),
  },
  moreMenuStyle: {
    height: 16,
    width: 16,
    resizeMode: 'contain',
  },
  headerBackView: {
    height: 18 + hp(2),
    width: 18 + hp(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerBackIcon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
    transform: [{ rotate: '180deg' }],
  },
});

type CallHeaderProps = {
  fullScreenMode: boolean;
  setFullScreenMode: React.Dispatch<React.SetStateAction<boolean>>;
  callModalState: CallScreenModals;
  openModal: (modal: CallScreenModalKey) => void;
  closeModal: (modal: CallScreenModalKey) => void;
  setOptionsModal: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedUser: React.Dispatch<React.SetStateAction<SelectedUser>>;
  seconds: number;
  onBackPress: () => void;
};

function CallHeader({
  fullScreenMode,
  openModal,
  closeModal,
  callModalState,
  setOptionsModal,
  setFullScreenMode,
  setSelectedUser,
  seconds,
  onBackPress,
}: CallHeaderProps) {
  const { callDetails } = useCallContext();

  const RightSideView = () => {
    return (
      <View
        style={[
          styles.buttonContainer1,
          { display: callDetails?.state == 'ongoing' ? 'flex' : 'none', gap: 20 },
        ]}
      >
        {callDetails.type === 'audio' && (
          <TouchableOpacity
            onPress={async () => {
              let resp = await requestCameraPermission();
              if (resp === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                ToastAndroid.show(
                  'Please enable camera permission in settings',
                  ToastAndroid.SHORT,
                );
                return;
              }
              if (resp !== 'granted') {
                ToastAndroid.show('Camera permission denied', ToastAndroid.SHORT);
                return false;
              }
              openModal('showSwitchToVideoRequest');
            }}
            style={{ display: callDetails?.type == 'audio' ? 'flex' : 'none' }}
          >
            <Image source={IMAGES.videonew} style={styles.iconStyle} />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={() => {
            callModalState.showAddPeople ? closeModal('showAddPeople') : openModal('showAddPeople');
          }}
        >
          <Image source={IMAGES.addUser} style={styles.iconStyle} />
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            padding: 10,
          }}
          onPress={() => {
            setOptionsModal(true);
            10;
          }}
        >
          <ThreeDotsSVG size={18} />
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <View
      style={[
        headerStyle.header,
        {
          position: 'absolute',
          top: 20,
          width: '100%',
          zIndex: zIndex.level_2,
        },
      ]}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          onPress={() => {
            onBackPress && onBackPress();
          }}
          style={headerStyle.headerBackView}
        >
          <FontAwesome6Icons name="arrow-left-long" size={20} color="#FFFFFF" />
        </TouchableOpacity>
        <TouchableOpacity style={headerStyle.header}>
          <View style={{}}>
            <View
              style={{
                flexDirection: 'row',
                gap: 10,
                display:
                  callDetails?.state == 'ongoing' && callDetails?.participants?.length > 1
                    ? 'flex'
                    : 'none',
                top: 3,
              }}
            >
              <Text style={{ ...commonFontStyle(600, 16, colors.white) }}>
                {callDetails?.participants?.length > 1
                  ? `Group ${callDetails?.type === 'audio' ? 'audio' : 'video'} call`
                  : ''}
                {callDetails?.participants?.length > 1 &&
                  ' (' + Number(callDetails?.participants?.length) + ')'}
              </Text>
            </View>

            {callDetails?.state == 'ongoing' && callDetails?.participants?.length > 1 && (
              <Text
                style={{
                  ...commonFontStyle(600, 13, colors.white),
                  marginTop: 10,
                }}
              >
                {formatTime(seconds)}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </View>
      {RightSideView()}
    </View>
  );
}
