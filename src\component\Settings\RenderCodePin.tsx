import { StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { colors } from '../../theme/colors';
import { hp, commonFontStyle } from '../../theme/fonts';

type Props = {
  cellCount: number;
  code: any;
  setCode: (text: any) => void;
};

const RenderCodePin = ({ cellCount, code, setCode }: Props) => {
  const CELL_COUNT = cellCount;
  const [value, setValue] = useState(code);
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  useEffect(() => {
    setCode(value);
  }, [value]);

  return (
    <CodeField
      ref={ref}
      {...props}
      value={value}
      onChangeText={setValue}
      cellCount={CELL_COUNT}
      rootStyle={[styles.codeFieldRoot, { width: cellCount * 48 + (cellCount - 1) * 10 }]}
      keyboardType="number-pad"
      textContentType="oneTimeCode"
      renderCell={({ index, symbol, isFocused }) => (
        <View
          key={index}
          style={[
            styles.cell,
            {
              borderColor: isFocused ? colors.mainPurple : colors._DADADA_gray,
              backgroundColor: isFocused ? 'rgba(106, 77, 187, 0.07)' : colors.white,
            },
          ]}
        >
          <Text style={styles.textStyle} onLayout={getCellOnLayoutHandler(index)}>
            {' '}
            {symbol || (isFocused ? <Cursor /> : null)}{' '}
          </Text>
        </View>
      )}
    />
  );
};

export default RenderCodePin;

const styles = StyleSheet.create({
  cell: {
    width: 48,
    height: 48,
    borderRadius: 15,
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    // marginHorizontal: hp(2.5),
  },
  codeFieldRoot: {
    // marginVertical: hp(4),
    width: 338,
    alignSelf: 'center',
    // width
  },
  textStyle: {
    ...commonFontStyle(500, 25, colors.mainPurple),
  },
});
