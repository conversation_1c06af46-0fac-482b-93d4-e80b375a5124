import React from 'react';
import Sticker from './Sticker';
import { wp } from '../../utils/Constants/dimensionUtils';
import { View, FlatList, StyleSheet, TouchableOpacity } from 'react-native';

const StickerList = ({ data, onPress }) => {
  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        numColumns={6}
        // horizontal
        keyExtractor={(item, index) => index.toString()}
        showsHorizontalScrollIndicator={false}
        renderItem={({ item }) => (
          <TouchableOpacity style={{ margin: wp(2) }} onPress={() => onPress(item)}>
            <Sticker sticker={item} iconStyle={styles.imageIcon} />
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  imageIcon: {
    height: wp(12),
    width: wp(12),
  },
});

export default React.memo(StickerList);
