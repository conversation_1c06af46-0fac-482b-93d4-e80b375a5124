import { StyleSheet, Text, View, Linking, Platform, TouchableOpacity, Alert } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import MessageStatusIcon from '../../../component/PersonalChat/MessageStatusIcon';
import { dayPipe, getCurrentLocation, navigateTo } from '../../../utils/commonFunction';
import MapView, { Marker } from 'react-native-maps';
import LiveLocationSVG from '../../../assets/svgIcons/LiveLocationSVG';
import ButtonPurple from '../../../component/ButtonPurple';
import Geolocation from '@react-native-community/geolocation';
import useSocket from '../../../socket-client/useSocket';
import { IMessage } from '../../../device-storage/realm/schemas/MessageSchema';
import { SCREENS } from '../../../navigation/screenNames';
import { ChatService } from '../../../service/ChatService';
import { ChatSocket } from '../../../socket-client/ChatSocket';
import UnpinSVG from '../../../assets/svgIcons/UnpinSVG';

type OpenMapArgs = {
  lat: string | number;
  lng: string | number;
  label: string;
};

interface IProps {
  msgData: IMessage;
  onPress: () => void;
  isMyData: boolean;
  userData?: object;
}

const LiveLocationCard = ({ msgData, onPress = () => { }, isMyData = false, userData }: IProps) => {
  const [sharingMessage, setSharingMessage] = useState<string>('');
  const [liveTimer, setLiveTimer] = useState<NodeJS.Timeout | null>(null);
  const [shareLive, setShareLive] = useState<boolean>(msgData?.location?.type == 'live');
  const [sharingTime, setSharingTime] = useState<string>('15 minutes');
  const [otherUserCoords, setOtherUserCoords] = useState<any>({});
  const [region, setRegion] = useState<any>(msgData?.location);
  const { socket } = useSocket();
  const pinned = Number(msgData?.pinnedUntil) > Date.now()


  const openGoogleMaps = (latitude: number, longitude: number) => {
    const scheme = Platform.select({
      ios: 'maps:0,0?q=',
      android: 'geo:0,0?q=',
    });
    const latLng = `${latitude},${longitude}`;
    const label = 'Custom Label'; // Optional label
    const url: string | undefined = Platform.select({
      ios: `${scheme}@${latLng}`,
      android: `${scheme}${latLng}`,
    });

    console.log('hellow world -------------- ');
    Linking.openURL(url as string).catch((err) => {
      // If native maps app fails to open, try web URL
      console.log('catch block of linking url');
      const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      // Linking.openURL(webUrl);
    });
  };

  // const startLiveLocationSharing = () => {
  //   const durationMap: Record<string, number> = {
  //     '15 minutes': 15 * 60 * 1000,
  //     '1 hour': 60 * 60 * 1000,
  //     '8 hours': 8 * 60 * 60 * 1000,
  //   };

  //   const intervalId = setInterval(() => {
  //     Geolocation.getCurrentPosition(
  //       (position) => {
  //         const { latitude, longitude } = position.coords;
  //         const locationData = {
  //           latitude,
  //           longitude,
  //           message: sharingMessage || '',
  //           timestamp: Date.now(),
  //           type: 'live', // optional tag
  //         };
  //         console.log('Live location sent:', locationData);
  //         // onSendLocation(locationData);
  //       },
  //       (error) => {
  //         console.log('Live location error:', error);
  //       },
  //       { enableHighAccuracy: true, timeout: 15000, maximumAge: 0 },
  //     );
  //   }, 5000); // every 5 seconds

  //   setLiveTimer(intervalId);

  //   // Stop live sharing after selected time
  //   setTimeout(() => {
  //     stopLiveLocationSharing();
  //     // onCloseModal(); // auto close modal
  //   }, durationMap[sharingTime]);
  // };

  // useEffect(() => {
  //   if (isMyData) {
  //     console.log('-----useeffect socket event hitted----');
  //     const watchId = Geolocation.watchPosition(
  //       (position) => {
  //         console.log('Location: details-----=====-----', position.coords);
  //         socket.emit('delete', {
  //           receiverId: msgData?.receiverId,
  //           latitude: position.coords.latitude,
  //           longitude: position.coords.longitude,
  //         });
  //         setRegion({
  //           ...region,
  //           latitude: position.coords?.latitude,
  //           longitude: position.coords?.longitude,
  //         });
  //       },
  //       (error) => console.log(error),
  //       {
  //         enableHighAccuracy: true,
  //         distanceFilter: 1, // meters
  //         interval: 5000, // Android only
  //         fastestInterval: 2000, // Android only
  //         useSignificantChanges: false, // iOS only
  //       },
  //     );

  //     // return () => {
  //     //   console.log('cleared');
  //     //   if (watchId) Geolocation.clearWatch(watchId);
  //     // };
  //   } else {
  //     console.log('------hey yooooo--------');
  //     socket.on('receive-delete', (data) => {
  //       console.log('--------received location data event--------', data);
  //       setRegion({ ...region, latitude: data?.latitude, longitude: data?.longitude });
  //     });
  //   }
  // }, []);

  const onStopSharing = async () => {
    const currentLocationData = await getCurrentLocation();
    console.log('currentLocationData-stopsharing------------------------', currentLocationData);

    console.log('lkajsdklfjaksjdfjaskdjfjadlkfj');
    setShareLive(false);
    console.log(socket);
    try {
      ChatSocket.sendLiveLocationStopped(socket!, {
        globalId: msgData?.globalId,
        receiverId: msgData?.receiverId,
        latitude: currentLocationData.latitude,
        longitude: currentLocationData.longitude,
      });
      ChatService.updateLocationStatus(
        msgData?.globalId as string,
        true,
        currentLocationData.latitude,
        currentLocationData.longitude,
      );
      console.log('------------');
    } catch (error) {
      Alert.alert('Error', 'Failed to stop live location sharing.');
    }
  };

  const stopLiveLocationSharing = () => {
    if (liveTimer) {
      clearInterval(liveTimer);
      setLiveTimer(null);
      setShareLive(false);
    }
  };

  useEffect(() => {
    if (
      !msgData?.location?.isStopped &&
      Number(msgData?.location?.expiresAt) <= (Date.now() as number)
    ) {
      onStopSharing();
      console.log('locationstopped------------', msgData?.location);
    }
  }, [msgData?.location?.isStopped]);


  return (
    <View
      style={[
        styles.fileContainer,
        {
          flex: 1,
          alignSelf: isMyData ? 'flex-end' : 'flex-start',
          backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
          borderTopLeftRadius: isMyData ? 16 : 1,
          borderBottomRightRadius: isMyData ? 1 : 16,
          minWidth: hp(35),
        },
      ]}
    >
      {pinned && (
        <View
          style={{
            position: 'absolute',
            top: '50%',
            [isMyData ? 'left' : 'right']: -35,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              ChatService.unpinMessage(msgData.localId, msgData.receiverId);
            }}
            style={styles.pinIconButton}
          >
            <UnpinSVG size={12} color={colors.mainPurple} />
          </TouchableOpacity>
        </View>
      )}

      <View
        style={{
          // paddingHorizontal: 10,
          // paddingVertical: 5,
          borderRadius: 16,
          backgroundColor: isMyData ? colors._CDC5E8_purple : colors.gray_f3,
          flexDirection: 'row',
          overflow: 'hidden',
          alignItems: 'center',
          flexGrow: 1,
          gap: 8,
          height: 135, // <-- fixed height here
        }}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          onPress={() => {
            // openGoogleMaps(msgData?.location?.latitude, msgData?.location?.longitude);
            navigateTo(SCREENS.LiveLocationSharingScreen, {
              mapData: {
                ...msgData?.location,
                senderId: msgData?.senderId,
                globalId: msgData?.globalId,
                receiverId: msgData?.receiverId,
              },
              userData: userData,
            });
          }}
          activeOpacity={1}
          disabled={msgData?.location?.isStopped}
        >
          <MapView
            provider={'google'}
            scrollEnabled={false}
            zoomEnabled={true}
            rotateEnabled={false}
            pitchEnabled={false}
            moveOnMarkerPress={false}
            style={{ height: '100%', width: '100%' }}
            mapType="terrain"
            loadingEnabled={true}
            loadingIndicatorColor={colors.mainPurple}
            loadingBackgroundColor={colors.gray_f3}
            initialRegion={{
              latitude: region?.latitude,
              longitude: region?.longitude,
              latitudeDelta: 0.003,
              longitudeDelta: 0.003,
            }}
          >
            <Marker
              coordinate={{
                latitude: region?.latitude,
                longitude: region?.longitude,
              }}
              anchor={{ x: 1, y: 1 }}
              image={require('../../../assets/locationPin.png')}
            />
          </MapView>
        </TouchableOpacity>
      </View>

      {/* Right side: time + tick */}
      <View
        style={{
          flexDirection: isMyData ? 'row' : 'row-reverse',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: 6,
        }}
      >
        {msgData?.location?.isStopped ? (
          <Text
            style={{
              color: colors.black,
              fontSize: 12,
            }}
          >
            Live location ended
          </Text>
        ) : (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <LiveLocationSVG size={18} color={colors.black_23} style={{ marginRight: 5 }} />
            <Text
              style={{
                color: colors.black,
                fontSize: 12,
              }}
            >
              {msgData?.location?.expiresAt
                ? `Live until ${dayPipe(msgData.location.expiresAt, 'time')}`
                : 'Live location'}
            </Text>
          </View>
        )}

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={{
              color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
              fontSize: 12,
              marginRight: 4,
            }}
          >
            {dayPipe(msgData?.createdAt, 'time')}
          </Text>

          {isMyData && <MessageStatusIcon status={msgData?.status} />}
        </View>
      </View>
      {msgData?.text?.trim().length ? (
        <Text style={{ color: colors.black_23, fontSize: 16, fontWeight: '400', marginTop: 10 }}>
          {msgData?.text}
        </Text>
      ) : null}

      {isMyData && shareLive && !msgData?.location?.isStopped && (
        <ButtonPurple
          title="Stop sharing"
          titleColor={'red'}
          extraStyle={{
            backgroundColor: colors._E3DEF4_gray,
            height: 40,
            borderRadius: 10,
            marginTop: 10,
          }}
          onPress={onStopSharing}
        />
      )}
    </View>
  );
};

export default LiveLocationCard;

const styles = StyleSheet.create({
  fileContainer: {
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    padding: 8,
    borderRadius: 16,
    // marginBottom: 22,
  },
  fileText: {
    ...commonFontStyle(500, 14, colors.black),
  },
  pinIconButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
