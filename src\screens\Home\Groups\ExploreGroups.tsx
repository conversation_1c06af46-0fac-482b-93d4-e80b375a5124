import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import _ from 'lodash';
import { SwipeListView } from 'react-native-swipe-list-view';
import { ChatSpace } from '../../../types/socketPayload.type';
import { ChatSpaceSearchResult, getChatSpacesByText } from '../../../api/Chatspace/chatspace.api';
import { colors } from '../../../theme/colors';
import { commonFontStyle } from '../../../theme/fonts';
import { ChatScreenParams, IChatScreenProps } from '../Chats/ChatSpecificScreen';
import { ConversationType, MessageType } from '../../../device-storage/realm/schemas/MessageSchema';
import { errorToast, navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import ChatItemCard from '../All/ChatItemCard'; // Reusing the existing ChatItemCard
import {
  IConversation,
  joinRequestStatus,
} from '../../../device-storage/realm/schemas/ConversationSchema';
import useConversations from '../../../hooks/conversations/useConversations';
import { ChatService } from '../../../service/ChatService';
import { ConversationInfo } from '../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../types/chats.types';
import { getDefaultChatPermissions } from '../../../lib/chatLib';
import { ConversationSettingsSchema } from '../../../device-storage/realm/schemas/ConversationSettingsSchema';
import ExploreChatSpaceCard from './ExploreChatSpaceCard';

type ExploreGroupsProps = {
  searchText?: string;
};

const mapChatSpaceToConversationInfo = (chatSpace: ChatSpace): ConversationInfo => {
  return {
    id: chatSpace.chatSpaceId,
    displayName: chatSpace.name,
    displayPic: chatSpace.displayPic,
    isPrivate: chatSpace.isPrivate,
    bio: chatSpace.description,
    memberCount: chatSpace.memberCount,
    type: ConversationType.GROUP,
    isLoading: false,
    isFromCache: true,
    conversationSettings: null,
    membershipStatus: MembershipStatus.DISCOVERING,
    permissions: getDefaultChatPermissions(MembershipStatus.DISCOVERING, ConversationType.GROUP),
    inviteCode: chatSpace.inviteCode || '',
  };
};

const mapChatSpaceToConversation = (chatSpace: ChatSpace): IConversation => {
  return {
    id: chatSpace.chatSpaceId,
    displayName: chatSpace.name,
    type: ConversationType.GROUP,
    lastMessageTimestamp: 0,
    unreadCount: 0,
    createdAt: 0,
    updatedAt: 0,
    conversationSettings: {} as ConversationSettingsSchema,
  };
};

const ExploreGroups = (props: ExploreGroupsProps) => {
  const [globalGroups, setGlobalGroups] = useState<ChatSpace[]>([]);
  const [myGroups, setMyGroups] = useState<ChatSpace[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { unArchivedConversations } = useConversations();

  const currentConversation = ChatService.getConversation;

  const localGroupIds = useMemo(
    () => new Set(unArchivedConversations.filter((c) => c.type === 'group').map((c) => c.id)),
    [unArchivedConversations],
  );

  const fetchGroups = useCallback(
    _.debounce(async (query: string) => {
      if (!query || query.length < 1) {
        setMyGroups([]);
        setGlobalGroups([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const res = await getChatSpacesByText(query, 'group');
        if (res) {
          const filteredMyGroups = res.myGroups.filter(
            (group) => !localGroupIds.has(group.chatSpaceId),
          );
          setMyGroups(filteredMyGroups);
          setGlobalGroups(res.otherGroups.filter((g) => !g.isPrivate));
        }
      } catch (err) {
        errorToast('Failed to fetch global groups');
      } finally {
        setLoading(false);
      }
    }, 500),
    [],
  );

  useEffect(() => {
    fetchGroups(props.searchText || '');
  }, [props.searchText, fetchGroups]);

  const handleGroupPress = (item: ChatSpace, isAlreadyMember: boolean) => {
    // const localConv = unArchivedConversations.find((c) => c.id === item.chatSpaceId);

    // if (item.isPrivate && !localConv) {
    //   ChatService.upsertConversation({
    //     id: item.chatSpaceId,
    //     displayName: item.name,
    //     displayPic: item.displayPic,
    //     isPrivate: item.isPrivate,
    //     description: item.description,
    //     memberCount: item.memberCount,
    //     inviteCode: item.inviteCode,
    //     chatSpaceId: item.chatSpaceId,
    //     type: ConversationType.GROUP,
    //     createdAt: Date.now(),
    //     updatedAt: Date.now(),
    //     joinRequestStatus: joinRequestStatus.NONE,
    //     unreadCount: 0,
    //   });
    // }

    const convData: IChatScreenProps = {
      id: item.chatSpaceId,
      displayName: item.name,
      chatSpaceId: item.chatSpaceId,
      displayPic: item.displayPic,
      type: ConversationType.GROUP,
      conversation: item as any,
    };
    const convParams: ChatScreenParams = { convId: convData.id };
    navigateTo(SCREENS.ChatSpecificScreen, {
      userData: convData,
      isArchived: false,
      isAlreadyMember: isAlreadyMember,
      data: convParams,
    });
  };

  // Reusable function to render a list section
  const renderGroupList = (data: ChatSpace[], title: string, isMemberGroup: boolean) => {
    if (data.length === 0) {
      return null;
    }

    return (
      <View>
        <Text style={styles.title}>{title}</Text>
        <FlatList
          data={data}
          keyExtractor={(item) => item.chatSpaceId}
          renderItem={({ item }) => <ExploreChatSpaceCard chatSpace={item} />}
          scrollEnabled={false}
        />
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.centeredContainer}>
        <ActivityIndicator color={colors.mainPurple} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centeredContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  // Hide component if no search text or no results at all
  if (!props.searchText || (myGroups.length === 0 && globalGroups.length === 0)) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Show "My Groups" first, limited to 3 items */}
      {renderGroupList(myGroups.slice(0, 3), 'Group Chats', true)}

      {/* Show all "Global Search" results */}
      {renderGroupList(globalGroups, 'Global Search', false)}
    </View>
  );
};
export default ExploreGroups;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    ...commonFontStyle(500, 14, colors.thick_red),
  },
  title: {
    color: colors.gray_80,
    fontSize: 16,
    fontWeight: '500',
    paddingLeft: 16,
    paddingVertical: 10,
  },
  rowBack: {
    alignItems: 'center',
    backgroundColor: colors.gray_f3,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 15,
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 75,
  },
  backRightBtnRight: {
    backgroundColor: colors.mainPurple, // A "Join" or primary action color
    right: 0,
  },
  backTextWhite: {
    color: '#FFF',
  },
  showMoreButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  showMoreText: {
    ...commonFontStyle(500, 14, colors.mainPurple),
  },
});
