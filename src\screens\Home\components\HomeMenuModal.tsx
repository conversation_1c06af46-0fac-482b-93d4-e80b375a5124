import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';

import { commonFontStyle, hp } from '../../../theme/fonts';
import { colors } from '../../../theme/colors';
import ModalWrapper from '../../../component/ModalWrapper';
import ChatSVG from '../../../assets/svgIcons/ChatSVG';
import NewFolderSVG from '../../../assets/svgIcons/NewFolderSVG';
import ArchivedSVG from '../../../assets/svgIcons/ArchivedSVG';
import { ConversationTab } from '../HomeScreen';
import { navigateTo } from '../../../utils/commonFunction';
import useConversations from '../../../hooks/conversations/useConversations';

type HomeScreenMenuModalProps = {
  isVisible: boolean;
  onCloseModal: () => void;
  onMenuFilterPress: (value: string) => void;

  selectedItem: string;
  setSelectedItem: React.Dispatch<React.SetStateAction<ConversationTab>>;
};

export function HomeMenuModal({
  isVisible,
  onCloseModal,
  onMenuFilterPress,

  selectedItem,
  setSelectedItem,
}: HomeScreenMenuModalProps) {
  const { archivedConversations } = useConversations();

  return (
    <ModalWrapper
      isVisible={isVisible}
      onCloseModal={onCloseModal}
      // onCloseModal={() => {
      //   setMenuVisible(false);
      // }}
    >
      <View style={{ paddingHorizontal: hp(1) }}>
        <View>
          <Text style={styles.headText}>Menu</Text>
          <TouchableOpacity
            onPress={() => {
              onCloseModal();
              navigateTo('ContactsScreen');
            }}
            style={styles.flexRow}
          >
            <ChatSVG size={23} color={colors.black_23} />
            <Text style={styles.subText}>New message</Text>
          </TouchableOpacity>
          {/* todo: enable chat folders   */}
          {/* <TouchableOpacity onPress={() => onMenuFilterPress('chatFolders')} style={styles.flexRow}>
            <NewFolderSVG size={24} color={colors.black_23} />
            <Text style={styles.subText}>Chat folders</Text>
          </TouchableOpacity> */}
          <TouchableOpacity
            onPress={() => onMenuFilterPress('archivedChats')}
            style={[styles.flexRow, { marginBottom: 37, justifyContent: 'space-between' }]}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
              <ArchivedSVG size={24} color={colors.black_23} />
              <Text style={styles.subText}>Archived chats</Text>
            </View>
            {archivedConversations?.length !== 0 ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  height: 22,
                  width: 22,
                  justifyContent: 'center',
                  borderRadius: 12,
                  backgroundColor: colors.mainPurple,
                }}
              >
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: colors.white,
                  }}
                >
                  {archivedConversations?.length}
                </Text>
              </View>
            ) : (
              <></>
            )}
          </TouchableOpacity>
        </View>

        {/* todo: enable filters */}
        {/* <View>
          <Text style={styles.headText}>Filters</Text>
          <TouchableOpacity onPress={() => onMenuFilterPress('unread')} style={styles.flexRow}>
            <UnreadSVG size={24} color={colors.black_23} />
            <Text style={styles.subText}>Unread</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onMenuFilterPress('drafts')} style={styles.flexRow}>
            <DraftsSVG size={24} color={colors.black_23} />
            <Text style={styles.subText}>Drafts</Text>
          </TouchableOpacity>
        </View> */}
      </View>
    </ModalWrapper>
  );
}
const styles = StyleSheet.create({
  subText: {
    ...commonFontStyle(500, 16, colors.black),
    fontWeight: '500',
  },
  headText: {
    ...commonFontStyle(600, 16, colors.black),
    fontWeight: '600',
    marginBottom: 14,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 20,
  },
});
