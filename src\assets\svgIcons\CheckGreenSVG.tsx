import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const CheckGreenSVG = (props: any) => (
  <Svg width={400} height={400} viewBox="0 0 400 400" fill="none" {...props}>
    <Path d="M0 0 C0.67675781 0.60328125 1.35351562 1.2065625 2.05078125 1.828125 C29.47390535 27.74592248 44.6440701 66.88266144 45.90820312 104.06494141 C46.86848005 147.7652855 30.36251897 186.21344269 0.3671875 217.59667969 C-28.4110686 247.13550399 -68.54321933 260.44538685 -109 261 C-148.24074895 261.08564109 -183.36405882 243.76751806 -212 218 C-213.01513672 217.09507812 -213.01513672 217.09507812 -214.05078125 216.171875 C-241.47390535 190.25407752 -256.6440701 151.11733856 -257.90820312 113.93505859 C-258.86848005 70.2347145 -242.36251897 31.78655731 -212.3671875 0.40332031 C-189.94616497 -22.61025782 -156.97401899 -38.65238459 -125 -42 C-123.26556641 -42.18175781 -123.26556641 -42.18175781 -121.49609375 -42.3671875 C-75.26288536 -46.19274057 -34.21796738 -30.79040033 0 0 Z " fill="#30CC00" transform="translate(306,91)"/>
    <Path d="M0 0 C3.48805691 1.98102187 6.19713391 4.39426782 8 8 C8.30859375 10.3203125 8.30859375 10.3203125 8.4375 13.125 C8.49808594 14.03507813 8.55867187 14.94515625 8.62109375 15.8828125 C6.93290923 24.35558763 -1.51108458 30.72649444 -7.4375 36.5 C-12.56351981 41.51729826 -17.54528647 46.54473422 -22.21508789 51.99023438 C-26.34951622 56.64549549 -30.89054271 60.92593628 -35.328125 65.2890625 C-39.4765127 69.39511971 -43.42299977 73.57810169 -47.2265625 78.00390625 C-51.15714065 82.42797108 -55.463371 86.49980444 -59.68359375 90.64453125 C-63.81709957 94.73318891 -67.74689552 98.90000578 -71.48681641 103.35351562 C-74.85828664 107.02198856 -78.551779 108.17901724 -83.375 108.625 C-89.20738606 108.4100712 -92.14895449 105.04244936 -96 101 C-97.33202934 99.66536395 -98.66536649 98.33203189 -100 97 C-100.5775 96.401875 -101.155 95.80375 -101.75 95.1875 C-103.57271701 93.41541402 -105.41812995 91.7818687 -107.3515625 90.13671875 C-113.32133338 84.94880243 -118.86712861 79.31889311 -124.45654297 73.72875977 C-126.38620975 71.80130309 -128.32377307 69.88201892 -130.26171875 67.96289062 C-131.49527984 66.73225997 -132.72836994 65.501157 -133.9609375 64.26953125 C-134.53798431 63.69966995 -135.11503113 63.12980865 -135.70956421 62.54267883 C-140.23863119 57.98754743 -142.28447647 55.05198828 -142.4375 48.625 C-142.37192663 44.8469652 -142.14132223 42.41745539 -139.75 39.4375 C-135.59502092 35.75467764 -132.9378674 34.39919747 -127.19309998 34.55892944 C-119.6089646 36.084231 -113.31947849 43.26438538 -108.12109375 48.5859375 C-107.42355484 49.28772125 -106.72601593 49.989505 -106.00733948 50.71255493 C-103.81016217 52.92634609 -101.62372772 55.15040047 -99.4375 57.375 C-97.93755093 58.89001823 -96.43690589 60.40434774 -94.93554688 61.91796875 C-91.28227973 65.60407535 -87.63859973 69.29941661 -84 73 C-80.85532042 71.51217941 -79.05346536 70.17379383 -76.84375 67.46875 C-71.92022832 61.65290563 -66.4541498 56.40748076 -61.02734375 51.06640625 C-57.39660828 47.47063773 -53.82942643 43.87853044 -50.5 40 C-45.66867614 34.37187243 -40.31002622 29.26563402 -35.02734375 24.06640625 C-30.66122795 19.74233972 -26.46448895 15.35465813 -22.4609375 10.69238281 C-20.79424312 8.7616466 -19.05771851 6.92411033 -17.25 5.125 C-16.69828125 4.57070312 -16.1465625 4.01640625 -15.578125 3.4453125 C-10.83126854 -0.90205604 -6.15931086 -1.01225443 0 0 Z " fill="#FBFEFB" transform="translate(267,146)"/>
  </Svg>
);

export default CheckGreenSVG; 