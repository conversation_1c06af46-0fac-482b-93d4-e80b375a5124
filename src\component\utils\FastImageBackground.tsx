import React from 'react';
import { StyleSheet, View, ViewStyle, StyleProp } from 'react-native';
import FastImage, { FastImageProps } from 'react-native-fast-image';

interface Props extends Omit<FastImageProps, 'children' | 'style'> {
  children?: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  imageStyle?: FastImageProps['style'];
}

const FastImageBackground: React.FC<Props> = ({
  children,
  containerStyle,
  imageStyle,
  ...props
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <FastImage
        {...props}
        style={[StyleSheet.absoluteFill, imageStyle]}
        resizeMode={FastImage.resizeMode.cover}
      />
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
});

export default FastImageBackground;
