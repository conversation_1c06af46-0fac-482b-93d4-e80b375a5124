# ChatBucket - Developer Guide

ChatBucket is a next-generation chatting application designed to enhance user communication through a seamless, secure, and feature-rich platform. Integrated with advanced Al models, the app offers intelligent features like message translation, voice-to-text conversion, and personalized user experiences, setting it apart from traditional messaging apps.

## 📁 Core Directory Structure

### **Entry Points**

- `App.tsx` - Main app wrapper with providers
- `index.js` - React Native entry point
- `src/navigation/RootContainer.tsx` - Navigation root
- `src/navigation/Navigator.tsx` - Screen definitions

### **🏠 Screens (`/src/screens/`)**

```
Auth/                    # Authentication flow
├── SignupScreen.tsx     # Phone registration
├── SetUsernameScreen.tsx
└── SetProfileScreen.tsx

Home/                    # Main app screens
├── HomeScreen.tsx       # Chat list (All/Chats/Groups/Channels)
├── Chats/
│   ├── ChatSpecificScreen.tsx  # Individual chat UI
│   └── PersonalProfileScreen.tsx
├── Groups/CreateGroupScreen.tsx
├── Channels/ChannelChatScreen.tsx
└── ArchiveScreen.tsx

Call/                    # Video/Audio calling
├── CallScreen.tsx       # Call history
├── NewCallScreen.tsx    # Start calls
└── GroupCallScreen.tsx  # Multi-participant calls

Contacts/ContactsScreen.tsx
Profile/ProfileScreen.tsx
```

### **🔧 Core Logic (`/src/`)**

```
Context/                 # React Context providers
├── CallProvider.tsx     # Call state management
├── RtcProvider.tsx      # WebRTC/Mediasoup
├── UserProvider.tsx     # User data
└── SocketProvider.tsx   # Socket.IO wrapper

hooks/                   # Custom React hooks
├── calls/              # Call-related hooks
├── chats/messages/     # Message operations
├── contacts/           # Contact sync
└── conversations/      # Chat management

device-storage/realm/    # Local database
├── realm.ts            # Realm configuration
├── schemas/            # Data models
│   ├── MessageSchema.ts
│   ├── ConversationSchema.ts
│   └── ContactSchema.ts
└── repositories/       # Data access layer
```

### **🌐 Communication (`/src/`)**

```
socket-client/          # Real-time messaging
├── socket.ts          # Socket connection
├── SocketHandler.ts   # Event handlers
└── SocketProvider.tsx # React wrapper

service/               # API services
├── AuthServices.tsx   # Login/logout
├── ChatService.ts     # Message APIs
└── [Other]Services.tsx

utils/                 # Utilities
├── api.js            # Base API config
├── apiGlobal.tsx     # HTTP interceptors
└── ApiService.tsx    # API endpoints
```

### **🎨 UI Components (`/src/component/`)**

```
PersonalChat/          # Chat UI components
├── ChatComponent/     # Message rendering
├── ChatInput.tsx      # Message input
├── AudioRecorderModal.tsx
└── ImageEditor/       # Media editing

Channels/              # Channel UI
Settings/              # Settings components
Story/                 # Status/Story UI
Common/                # Reusable components
```

---

## 🛠️ Implementation Guide

### **Adding New Screens**

1. Create screen in `/src/screens/[Category]/`
2. Add route in `/src/navigation/Navigator.tsx`
3. Add screen name in `/src/navigation/screenNames.tsx`

### **Adding New API Endpoints**

1. Add service function in `/src/service/[Category]Services.tsx`
2. Use in components via hooks or direct import

### **Database Changes**

1. Update schema in `/src/device-storage/realm/schemas/`
2. Increment `schemaVersion` in `/src/device-storage/realm/realm.ts`
3. Add repository methods in `/src/device-storage/realm/repositories/`

### **Real-time Features**

1. Add socket events in `/src/socket-client/SocketHandler.ts`
2. Handle in components via `useSocket()` hook
3. Update Context providers if needed

### **Call Features**

1. Modify `/src/Context/CallProvider.tsx` for call logic
2. Update `/src/Context/RtcProvider.tsx` for WebRTC
3. UI changes in `/src/screens/Call/`

### **UI Components**

1. Reusable components → `/src/component/`
2. Screen-specific → `/src/screens/[Screen]/components/`
3. Use existing theme from `/src/theme/`

---

## 🔑 Key Configuration Files

### **App Configuration**

- `app.json` - App metadata
- `android/app/google-services.json` - Firebase Android
- `ios/` - iOS configuration

### **API Configuration**

- `src/utils/api.js` - Base URL: `https://server.chatbucket.chat/`
- `src/socket-client/socket.ts` - Socket URL configuration

### **Database**

- `src/device-storage/realm/realm.ts` - Realm config
- Schema version management for migrations

---

## 🚨 Common Development Tasks

### **Adding New Message Types**

1. Update `MessageSchema.ts` - add new type enum
2. Modify `ChatComponent.tsx` - add rendering logic
3. Update `ChatInput.tsx` - add input handling

### **Adding New Languages**

1. Add JSON file in `/src/i18n/[language].json`
2. Import in translation setup

### **Modifying Call Features**

1. WebRTC logic → `/src/Context/RtcProvider.tsx`
2. Call UI → `/src/screens/Call/`
3. Call notifications → `/src/hooks/calls/`

### **Push Notifications**

1. Firebase setup → `/src/firebase/`
2. Notification handling → `/src/firebase/notifications/`

---

## 📦 Key Dependencies

**Core**: React Native 0.78.1, TypeScript
**State**: Redux Toolkit, React Context
**Database**: Realm Database
**Communication**: Socket.IO, WebRTC, Mediasoup
**Navigation**: React Navigation
**UI**: React Native Vector Icons, Lottie
**Media**: React Native Image Picker, Audio Recorder

---

## 🔧 Development Commands

```bash
# Development
npm start                 # Start Metro
npm run android          # Run Android
npm run ios             # Run iOS

# Build
cd android && ./gradlew assembleRelease  # Android build
npx react-native run-ios --configuration Release  # iOS build
```

---

**💡 Pro Tips for New Developers:**

- Use existing hooks in `/src/hooks/` before creating new ones
- Follow the established folder structure for consistency
- Check `/src/types/` for TypeScript definitions
- Use Redux DevTools for state debugging
- Test real-time features with multiple devices/emulators
