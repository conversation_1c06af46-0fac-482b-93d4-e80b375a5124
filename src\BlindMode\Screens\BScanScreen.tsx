// App.tsx
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, StatusBar, Alert, BackHandler, AppState } from 'react-native';
import { AntDesignIcons, FontAwesome6Icons, Ionicons } from '../../utils/vectorIcons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '../../theme/colors';
import CameraRenderer, { CameraRendererRef, CaptureMode, ModeOptionsType } from '../Components/Camera';

import { GeneratedSummaryState, useWebRTCStream } from '../hooks/useWebrtchhook';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import Sound from 'react-native-sound';
import { Dropdown } from 'react-native-element-dropdown';
import BLanguageModal from '../Components/BLanguageModal';


export interface ImageContent {
    mode?: string;
    image?: string;
    text?: string;
}



const lang_map: Record<string, string> = {
    arb: "Arabic", ben: "Bengali", cat: "Catalan", ces: "Czech", cmn: "Mandarin Chinese",
    cym: "Welsh", dan: "Danish", deu: "German", eng: "English", est: "Estonian",
    fin: "Finnish", fra: "French", hin: "Hindi", ind: "Indonesian", ita: "Italian",
    jpn: "Japanese", kor: "Korean", mlt: "Maltese", nld: "Dutch", pes: "Persian",
    pol: "Polish", por: "Portuguese", ron: "Romanian", rus: "Russian", slk: "Slovak",
    spa: "Spanish", swe: "Swedish", swh: "Swahili", tel: "Telugu", tgl: "Tagalog",
    tha: "Thai", tur: "Turkish", ukr: "Ukrainian", urd: "Urdu", uzn: "Uzbek",
    vie: "Vietnamese"
};


const languages = [
    { name: "Arabic", code: "arb", flag: "🇸🇦" },
    { name: "Bengali", code: "ben", flag: "🇧🇩" },
    { name: "Catalan", code: "cat", flag: "🇪🇸" },
    { name: "Czech", code: "ces", flag: "🇨🇿" },
    { name: "Danish", code: "dan", flag: "🇩🇰" },
    { name: "Dutch", code: "nld", flag: "🇳🇱" },
    { name: "English", code: "eng", flag: "🇬🇧" },
    { name: "Estonian", code: "est", flag: "🇪🇪" },
    { name: "Finnish", code: "fin", flag: "🇫🇮" },
    { name: "French", code: "fra", flag: "🇫🇷" },
    { name: "German", code: "deu", flag: "🇩🇪" },
    { name: "Hindi", code: "hin", flag: "🇮🇳" },
    { name: "Indonesian", code: "ind", flag: "🇮🇩" },
    { name: "Italian", code: "ita", flag: "🇮🇹" },
    { name: "Japanese", code: "jpn", flag: "🇯🇵" },
    { name: "Korean", code: "kor", flag: "🇰🇷" },
    { name: "Maltese", code: "mlt", flag: "🇲🇹" },
    { name: "Mandarin Chinese", code: "cmn", flag: "🇨🇳" },
    { name: "Persian", code: "pes", flag: "🇮🇷" },
    { name: "Polish", code: "pol", flag: "🇵🇱" },
    { name: "Portuguese", code: "por", flag: "🇵🇹" },
    { name: "Romanian", code: "ron", flag: "🇷🇴" },
    { name: "Russian", code: "rus", flag: "🇷🇺" },
    { name: "Slovak", code: "slk", flag: "🇸🇰" },
    { name: "Spanish", code: "spa", flag: "🇪🇸" },
    { name: "Swahili", code: "swh", flag: "🇰🇪" },
    { name: "Swedish", code: "swe", flag: "🇸🇪" },
    { name: "Tagalog", code: "tgl", flag: "🇵🇭" },
    { name: "Telugu", code: "tel", flag: "🇮🇳" },
    { name: "Thai", code: "tha", flag: "🇹🇭" },
    { name: "Turkish", code: "tur", flag: "🇹🇷" },
    { name: "Ukrainian", code: "ukr", flag: "🇺🇦" },
    { name: "Urdu", code: "urd", flag: "🇵🇰" },
    { name: "Uzbek", code: "uzn", flag: "🇺🇿" },
    { name: "Vietnamese", code: "vie", flag: "🇻🇳" },
    { name: "Welsh", code: "cym", flag: "🏴" }, // Wales flag
];




function BScanScreen() {
    const [mode, setMode] = useState<'Photo' | 'Video'>('Photo');
    const CameraRendererRef = useRef<CameraRendererRef>(null);
    const [selectedModeOption, setSelectedModeOption] = useState<ModeOptionsType>();
    const { localStream, status, startStreaming, stopStreaming, flipCamera, generatedSummaryState } =
        useWebRTCStream();
    const [imageContent, setImageContent] = useState<ImageContent>({
        image: '',
        mode: '',
        text: ''
    });
    const [selectedLang, setSelectedLang] = useState<string>("eng"); // default English
    const [languageModal, setLanguageModal] = useState(false);
    const [search, setSearch] = useState('');
    const filteredLanguages = useMemo(() => {
        return languages.filter((lang) => lang.name.toLowerCase().includes(search.toLowerCase()));
    }, [search]);
    const [sortedLanguages, setSortedLanguages] = useState(filteredLanguages);
    const [captureMode, setCaptureMode] = useState<CaptureMode>('image');

    const displayLang = filteredLanguages.find((list) => list.code == selectedLang);

    const [isMuted, setIsMuted] = useState(false);
    const isFocused = useIsFocused();
    const soundRef = useRef<Sound | null>(null);
    const audioFile = useRef<String | null>('')


    const navigation = useNavigation();

    function handleModeOptions(option: ModeOptionsType, flipMode?: boolean) {
        if (flipMode) {
            setMode((prev) => (prev === 'Photo' ? 'Video' : 'Photo'));
        }
        setSelectedModeOption(option);
    }

    function flipMode() {
        setMode((prev) => (prev === 'Photo' ? 'Video' : 'Photo'));
        setSelectedModeOption(undefined);
        setImageContent({})
    }

    function clearData() {
        setMode('Photo');
        setSelectedModeOption(undefined);
        setImageContent({});
    }

    const handleSoundRef = (data: any) => {
        console.log("-----------------------handlesoundref", data);
        soundRef.current = data
        setIsMuted(false);
    }

    const handleMute = () => {
        if (soundRef.current) {
            if (isMuted) {
                soundRef.current.setVolume(1); // unmute
            } else {
                soundRef.current.setVolume(0); // mute
            }
            setIsMuted((prev) => !prev)
        }
    }


    console.log("---------------------------imageContent", imageContent);

    // useEffect(() => {
    //     if (languageModal) {
    //         // When modal opens → reorder list
    //         const selected = filteredLanguages.find(item => item.code === selectedLang);

    //         const others = filteredLanguages
    //             .filter(item => item.code !== selectedLang)
    //             .sort((a, b) => a.name.localeCompare(b.name));

    //         setSortedLanguages(selected ? [selected, ...others] : others);
    //     }
    // }, [languageModal, filteredLanguages, selectedLang]);


    const handleImageContent = useCallback((data: ImageContent) => {
        console.log("----------------data", data, mode);

        setImageContent({
            text: data.text,
            mode: data.mode,
            image: data.image,
        })
    }, []);


    const handleGetImage = () => {
        if (CameraRendererRef.current) {
            const imgPath = CameraRendererRef.current.getCurrentImage();
            audioFile.current = imgPath;
            console.log("✅ audioFile set to:", audioFile.current);
        }
    };


    const renderDropDown = () => {
        return (
            <TouchableOpacity disabled={captureMode !== 'image'} style={{ height: 30, flexDirection: 'row', paddingHorizontal: 10, paddingVertical: 3, backgroundColor: '#f0f0f0', borderRadius: 8, justifyContent: 'space-between', alignItems: 'center' }} onPress={() => { setLanguageModal(true) }}>
                <Text style={{ color: colors.black_23, fontSize: 14, fontWeight: '600', marginRight: 10 }}>{captureMode == 'image' ? displayLang?.name : 'English'}</Text>
                <AntDesignIcons style={styles.icon} color="black" name={languageModal ? 'up' : "down"} size={14} />

                {/* <Dropdown
                    style={{
                        paddingVertical: 3,
                        marginVertical: 4,
                        width: 120,
                        borderColor: "#ccc",
                        borderWidth: 1,
                        borderRadius: 8,
                        paddingHorizontal: 10,
                    }}
                    itemTextStyle={{ fontSize: 14, color: colors.black_23 }}
                    itemContainerStyle={{ paddingVertical: 0, paddingHorizontal: 8 }} // 👈 control padding here
                    placeholderStyle={{ fontSize: 14, color: "#000" }}
                    selectedTextStyle={{ fontSize: 14, color: "#000", fontWeight: '600' }}
                    iconStyle={styles.iconStyle}
                    data={data}
                    maxHeight={200}
                    labelField="label"
                    valueField="value"
                    placeholder="Select"
                    value={selectedLang}
                    onChange={item => setSelectedLang(item.value)}
                    renderRightIcon={() => (
                        <AntDesignIcons style={styles.icon} color="black" name="down" size={14} />
                    )}
                /> */}
            </TouchableOpacity>
        )
    }


    useEffect(() => {
        handleGetImage()
    }, [CameraRendererRef.current?.getCurrentImage])



    useEffect(() => {
        const subscription = AppState.addEventListener("change", (nextState) => {
            if (nextState === "background" || nextState === "inactive") {
                console.log('-------------------------appstate', nextState);
                CameraRendererRef.current?.clearAllData();
            }
        });
        return () => {
            subscription.remove();
        }
    }, []);


    useEffect(() => {
        const backAction = () => {
            if (imageContent.image || imageContent.text) {
                CameraRendererRef.current?.clearAllData();

                if (soundRef.current) {
                    soundRef.current.stop(() => {
                        soundRef.current?.release();
                        soundRef.current = null;
                    });
                }

                console.log('camera data + sound cleared -----------------------------');
                return true;
            }
            return false;
        };

        const backHandler = BackHandler.addEventListener(
            "hardwareBackPress",
            backAction
        );
        return () => backHandler.remove();
    }, [imageContent]);




    return (
        <SafeAreaView style={styles.container}>
            <StatusBar backgroundColor={'white'} barStyle="dark-content" translucent />
            <View
                style={{
                    paddingHorizontal: 21,
                    flexDirection: 'row',
                    width: '100%',
                    justifyContent: 'space-between',
                    backgroundColor: colors.white,
                    alignItems: 'center',
                }}
            >
                {/* <ModeSwitcher mode={mode} onChange={flipMode} /> */}
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity
                        onPress={() => {
                            if (imageContent.image || imageContent.text) {
                                CameraRendererRef.current?.clearAllData();
                                if (soundRef.current) {
                                    soundRef.current.stop(() => {
                                        soundRef.current?.release();
                                        soundRef.current = null;
                                    });
                                }
                                return;
                            } else {
                                navigation.goBack();
                            }
                        }}
                        style={{ marginRight: 20 }}
                    >
                        <FontAwesome6Icons name="arrow-left-long" size={20} color={colors.black_23} />
                    </TouchableOpacity>
                    {imageContent.image && imageContent.mode ?
                        <Text style={{ fontSize: 16, color: colors.black_23, fontWeight: '600' }}>{imageContent.mode == 'image' ? 'Image' : 'Video'}</Text>
                        : <></>
                    }
                </View>

                {imageContent.image || imageContent.text?.trim()?.length ?
                    <View style={{}}>
                        <TouchableOpacity onPress={handleMute} style={{ marginVertical: 13 }}>
                            <Ionicons name={!isMuted ? 'volume-high-outline' : 'volume-mute-outline'} size={20} color={colors.black_23} />
                        </TouchableOpacity>
                    </View>
                    :
                    <View style={{ flexDirection: 'row', alignItems: 'center', paddingTop: 12, paddingBottom: imageContent.text?.trim()?.length ? 12 : 0 }}>
                        <TouchableOpacity style={{ alignItems: 'center', marginRight: 21 }} onPress={() => { setMode('Photo'); console.log("---------------photo") }}>
                            <Text style={{ fontSize: 16, fontWeight: '600', color: mode == 'Photo' ? colors.mainPurple : colors.black_23 }}>Photo</Text>
                            <View style={{ height: 3, backgroundColor: mode == 'Photo' ? colors.mainPurple : "transparent", width: '80%', borderTopLeftRadius: 10, borderTopRightRadius: 10, marginTop: 10 }} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ alignItems: 'center' }} onPress={() => { setMode('Video'); console.log("---------------Video") }}>
                            <Text style={{ fontSize: 16, fontWeight: '600', color: mode == 'Video' ? colors.mainPurple : colors.black_23 }}>Video</Text>
                            <View style={{ height: 3, backgroundColor: mode == 'Video' ? colors.mainPurple : "transparent", width: '80%', borderTopLeftRadius: 10, borderTopRightRadius: 10, marginTop: 10 }} />
                        </TouchableOpacity>
                    </View>
                }

                {!imageContent.text && !imageContent.image && mode !== 'Video' ? renderDropDown() : null}
                {/* Action Buttons */}
                {/* <ActionButtons flipCamera={flipCamera} generatedSummaryState={generatedSummaryState} /> */}
            </View>

            <CameraRenderer
                mode={mode}
                ref={CameraRendererRef}
                handleSoundRef={handleSoundRef}
                handleModeOptions={handleModeOptions}
                selectedModeOptionProp={selectedModeOption}
                localStream={localStream}
                status={status}
                startStreaming={startStreaming}
                stopStreaming={stopStreaming}
                generatedSummaryState={generatedSummaryState}
                flipCamera={flipCamera}
                handleImageContent={handleImageContent}
                flipMode={flipMode}
                clearData={clearData}
                selectedLanguage={selectedLang}
                onCaptureModeChange={(captureMode: CaptureMode) => {
                    console.log("-----------------captuerMode", captureMode)
                    setCaptureMode(captureMode);
                }}
            />


            <BLanguageModal
                filteredLanguages={filteredLanguages}
                languageModal={languageModal}
                search={search}
                selectedLanguage={selectedLang}
                setLanguageModal={setLanguageModal}
                setSearch={setSearch}
                setSelectedLanguage={setSelectedLang}
            />

            {/* Bottom Bar */}
            {/* <BottomBar /> */}
        </SafeAreaView>
    );
}

type ModeSwitcherProps = {
    mode: 'Photo' | 'Video';
    onChange: (mode: 'Photo' | 'Video') => void;
};

const ModeSwitcher: React.FC<ModeSwitcherProps> = ({ mode, onChange }) => (
    <View style={styles.switcherContainer}>
        {['Photo', 'Video'].map((m) => (
            <TouchableOpacity
                key={m}
                onPress={() => onChange(m as 'Photo' | 'Video')}
                style={{
                    padding: 10,
                    borderRadius: 25,
                    backgroundColor: mode === m ? 'white' : 'transparent',
                    paddingHorizontal: 20,
                }}
            >
                <Text style={[styles.switcherText, mode === m && styles.switcherActive]}>{m}</Text>
            </TouchableOpacity>
        ))}
    </View>
);

type ActionButtonsProps = {
    flipCamera: () => void;
    generatedSummaryState: GeneratedSummaryState;
};

const ActionButtons: React.FC<ActionButtonsProps> = ({ flipCamera, generatedSummaryState }) => (
    <View style={styles.actionContainer}>
        {/* <TouchableOpacity style={styles.actionButton}>
      <Ionicons name="document-text-outline" size={22} color="#333" />
    </TouchableOpacity> */}
        <TouchableOpacity style={styles.actionButton} onPress={flipCamera}>
            <Ionicons name="camera-reverse" size={22} color="#333" />
        </TouchableOpacity>
        {/* {generatedSummaryState.summary != '' && (
      <TouchableOpacity style={styles.actionButton} onPress={flipCamera}>
        <EntypoIcons name="sound" size={22} color="#333" />
      </TouchableOpacity>
    )} */}
    </View>
);
export default BScanScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EEF2FF', // bluish background
        justifyContent: 'space-between',
    },
    // Mode Switcher
    switcherContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 10,

        backgroundColor: colors._F4F4F4_gray,
        padding: 5,
        borderRadius: 50,
    },
    switcherText: {
        fontSize: 16,
        color: '#555',
    },
    switcherActive: {
        color: '#5A4BFF',
        fontWeight: 'bold',
    },
    // Action Buttons
    actionContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        margin: 16,
        gap: 12,
    },
    actionButton: {
        backgroundColor: colors._F4F4F4_gray,
        padding: 12,
        borderRadius: 50,
    },
    // Document Preview
    previewContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'white',
    },
    previewImage: {
        width: '90%',
        height: '90%',
        borderRadius: 8,
    },
    // Bottom Bar
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#fff',
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        elevation: 5,
    },
    captureButton: {
        backgroundColor: '#5A4BFF',
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 25,
    },
    captureText: {
        color: '#fff',
        fontWeight: '600',
    },
    options: {
        flexDirection: 'row',
        gap: 16,
    },
    optionText: {
        color: '#333',
        fontSize: 14,
    },
    container2: { padding: 20 },
    dropdown: {
        height: 30,
        width: 80, // small box
        borderColor: "#ccc",
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 10,
    },
    placeholderStyle: { fontSize: 14, color: "#999" },
    selectedTextStyle: { fontSize: 14, color: "#000" },
    iconStyle: { width: 16, height: 16 },
    icon: { marginRight: 0 },

});
