import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

interface IconProps {
  size?: number;
  color?: string;
}

const ShareGroupSVG: React.FC<IconProps> = ({
  size = 14,
  color = '#F3F3F3',
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={(size * 13) / 14} // maintains original aspect ratio
      viewBox="0 0 14 13"
      fill="none"
      {...props}
    >
      <Path
        d="M.44 13a.435.435 0 01-.437-.41C0 12.504-.352 4.226 7.003 3.088V.434a.44.44 0 01.735-.318l6.123 5.633a.432.432 0 010 .635l-6.123 5.634a.442.442 0 01-.473.08.433.433 0 01-.262-.398V9.085h-.008c-1.22 0-4.796.296-6.147 3.643A.437.437 0 01.44 13zM7.877 1.427v2.04c0 .22-.166.405-.386.43-4.93.575-6.2 4.54-6.517 6.9C3.29 7.93 7.427 8.232 7.475 8.236a.435.435 0 01.401.432v2.04l5.044-4.64-5.044-4.64z"
        fill={color}
      />
    </Svg>
  );
};

export default ShareGroupSVG;
