import React, { Component } from "react";
import { View, Image, TouchableOpacity, FlatList, StyleSheet, Text, Dimensions } from "react-native";
import * as ImagePicker from "react-native-image-picker";
import Gestrurewrapper from "../Common/Gestrurewrapper"; // Ensure correct import

const { width, height } = Dimensions.get("window");

class StoryEditor extends Component {
    constructor(props) {
        super(props);
        this.state = {
            stories: [], // Stores stories [{ background: "...", stickers: ["...", "..."] }]
            selectedStoryIndex: null, // Active story index
        };
    }

    // Pick main images for stories
    pickInitialImages = () => {
        ImagePicker.launchImageLibrary(
            { mediaType: "photo", selectionLimit: 5, quality: 1 },
            (response) => {
                if (response.assets) {
                    const newStories = response.assets.map((img) => ({
                        background: img.uri,
                        stickers: [],
                    }));
                    this.setState((prevState) => ({
                        stories: [...prevState.stories, ...newStories],
                        selectedStoryIndex: prevState.stories.length, // Select first new story
                    }));
                }
            }
        );
    };

    // Pick sticker images and add to selected story
    pickSticker = () => {
        const { selectedStoryIndex, stories } = this.state;
        if (selectedStoryIndex === null) return;

        ImagePicker.launchImageLibrary({ mediaType: "photo", quality: 1 }, (response) => {
            if (response.assets && response.assets.length > 0) {
                const newSticker = response.assets[0].uri;
                const updatedStories = [...stories];
                updatedStories[selectedStoryIndex].stickers.push(newSticker);

                this.setState({ stories: updatedStories });
            }
        });
    };

    // Set active story
    setSelectedStoryIndex = (index) => {
        this.setState({ selectedStoryIndex: index });
    };

    render() {
        const { stories, selectedStoryIndex } = this.state;

        return (
            <View style={styles.container}>
                {/* Main Story View */}
                {selectedStoryIndex !== null && stories[selectedStoryIndex] ? (
                    <View style={styles.storyContainer}>
                        {/* Background Image (Centered) */}
                        <View style={styles.imageWrapper}>
                            <Image source={{ uri: stories[selectedStoryIndex].background }} style={styles.mainImage} />
                        </View>

                        {/* Stickers (Only in Selected Story) */}
                        {stories[selectedStoryIndex].stickers.map((sticker, idx) => (
                            <Gestrurewrapper key={idx}>
                                    <Image source={{ uri: sticker }} style={styles.sticker} />
                            </Gestrurewrapper>
                        ))}

                        {/* Add Sticker Button */}
                        <TouchableOpacity style={styles.addStickerButton} onPress={this.pickSticker}>
                            <Text style={styles.buttonText}>+ Sticker</Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <TouchableOpacity style={styles.addStoryButton} onPress={this.pickInitialImages}>
                        <Text style={styles.buttonText}>+ Add Story</Text>
                    </TouchableOpacity>
                )}

                {/* Story List (Bottom of the Screen) */}
                <View style={styles.storyListContainer}>
                    <FlatList
                        horizontal
                        data={stories}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item, index }) => (
                            <TouchableOpacity onPress={() => this.setSelectedStoryIndex(index)} style={styles.storyThumbnail}>
                                <Image source={{ uri: item.background }} style={styles.thumbnailImage} />
                            </TouchableOpacity>
                        )}
                    />
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: "#000", justifyContent: "center", alignItems: "center" },

    storyContainer: { flex: 1, justifyContent: "center", alignItems: "center", },

    imageWrapper: {
        width: width * 0.9,
        height: height * 0.6,
        justifyContent: "center",
        alignItems: "center",
        alignSelf: 'center',
        position: 'absolute',
    },

    mainImage: {
        width: "100%",
        height: "100%",
        resizeMode: "contain",
        alignSelf: 'center'
    },

    sticker: {
        width: 100,
        height: 100,
        resizeMode: "contain",
    },

    addStickerButton: {
        position: "absolute",
        bottom: height * 0.15,
        backgroundColor: "#ff0",
        padding: 10,
        borderRadius: 5
    },

    addStoryButton: {
        backgroundColor: "#ff0",
        padding: 15,
        borderRadius: 10,
        marginBottom: 20
    },

    buttonText: {
        color: "#000",
        fontWeight: "bold"
    },

    storyListContainer: {
        position: "absolute",
        bottom: 20,
        width: "100%",
        paddingHorizontal: 10
    },

    storyThumbnail: {
        marginHorizontal: 5,
        borderRadius: 20,
        overflow: "hidden",
        borderWidth: 1,
        borderColor: "white",
        height: 70,
        width: 70
    },

    thumbnailImage: {
        width: 70,
        height: 70
    },
});

export default StoryEditor;
