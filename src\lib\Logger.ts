import { config } from '../appConfig';
class Logger {
  static log(message: any, force: boolean = false): void {
    if (force || (!force && config.debug)) {
      console.log(`[LOG] ${new Date().toISOString()}:`, message);
    }
  }

  static info(message: any, force: boolean = false): void {
    if (force || (!force && config.debug)) {
      console.info(`[INFO] ${new Date().toISOString()}:`, message);
    }
  }

  static warn(message: any, force: boolean = false): void {
    if (force || (!force && config.debug)) {
      console.warn(`[WARN] ${new Date().toISOString()}:`, message);
    }
  }

  static error(message: any, force: boolean = false): void {
    if (force || (!force && config.debug)) {
      console.error(`[ERROR] ${new Date().toISOString()}:`, message);
    }
  }

  static logBg(message: any, bgColor: string = '#007acc', textColor: string = 'white'): void {
    // if (config.debug) {
    const style = `background-color: ${bgColor}; color: ${textColor}; padding: 4px 8px; font-weight: bold; border-radius: 3px;`;
    console.log(`%c ${new Date().toISOString()}: ${message} `, style);
    // }
  }
}

export default Logger;
