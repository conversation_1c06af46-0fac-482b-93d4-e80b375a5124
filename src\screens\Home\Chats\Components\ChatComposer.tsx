import {
  Image,
  Keyboard,
  LayoutAnimation,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  ToastAndroid,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import useConversations from '../../../../hooks/conversations/useConversations';
import { IChatScreenProps } from '../ChatSpecificScreen';
import {
  ConversationSchema,
  IConversation,
} from '../../../../device-storage/realm/schemas/ConversationSchema';
import { ChatSpacePermissions } from '../../../../service/ChatSpacePermissions';
import { ChatService } from '../../../../service/ChatService';
import { defaultMemberPermissions } from '../../Groups/GroupPermissions';
import {
  ChannelType,
  ConversationType,
  IMessage,
  MessageStatus,
  MessageType,
  ScheduleUpdateType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { sendJoinRequest } from '../../../../api/Chatspace/chatspace.api';
import {
  bytesToMB,
  errorToast,
  formatBytes,
  handleMediaFileSharing,
  navigateTo,
  showToast,
} from '../../../../utils/commonFunction';
import ButtonPurple from '../../../../component/ButtonPurple';
import { colors } from '../../../../theme/colors';
import { hp, SCREEN_WIDTH } from '../../../../theme/fonts';
import CameraSVG from '../../../../assets/svgIcons/CameraSVG';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import FileSVG from '../../../../assets/svgIcons/FileSVG';
import AudioSVG from '../../../../assets/svgIcons/AudioSVG';
import PersonSVG from '../../../../assets/svgIcons/PersonSVG';
import LocationSVG2 from '../../../../assets/svgIcons/LocationSVG2';
import { keepLocalCopy, pick } from '@react-native-documents/picker';
import { ComposedMessage } from '../../../../types/index.types';
import { IContact } from '../../../../device-storage/realm/schemas/ContactSchema';
import ImageCropPicker from 'react-native-image-crop-picker';
import { useMe } from '../../../../hooks/util/useMe';
import LinearGradient from 'react-native-linear-gradient';
import VideoCallSVG from '../../../../assets/svgIcons/VideoCallSVG';
import CloseFilledSVG from '../../../../assets/svgIcons/CloseFilledSVG';
import MusicSVG from '../../../../assets/svgIcons/MusicSVG';
import MicSVG2 from '../../../../assets/svgIcons/MicSVG2';
import ReplyDocSVG from '../../../../assets/svgIcons/DocReplySVG';
import ReplyAvatarSVG from '../../../../assets/svgIcons/ReplyAvatarSVG';
import CloseSVG from '../../../../assets/svgIcons/CloseSVG';
import TimerSVG from '../../../../assets/svgIcons/TimerSVG';
import PlusSVG from '../../../../assets/svgIcons/PlusSVG';
import ShareIconSVG from '../../../../assets/svgIcons/ShareIconSVG';
import AudioRecorderModal from '../../../../component/PersonalChat/AudioRecorderModal';
import VoiceRecorderSVG from '../../../../assets/svgIcons/VoiceRecorderSVG';
import SendSVG from '../../../../assets/svgIcons/SendSVG';
import ScheduleMessageModal from '../ChatModals/ScheduleMessageModal';
import { generateLocalMessageId } from '../../../../device-storage/realm/lib';
import KeyboardModal from './KeyboardModal';
import CaptureTypeModal from './CaptureTypeModal';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { UserSchema } from '../../../../device-storage/realm/schemas/UserSchema';
import { MembershipStatus } from '../../../../types/chats.types';
import { IGroupMember } from '../../../../service/ChatSpacesService';
import MembersListModal from '../../Groups/reUsableComponents/MemberListModal';
import { IMediaUserProps } from '../../components/MediaPreviewScreen';

interface IChatComposerProps {
  userData: IChatScreenProps;
  replyMsgData?: IMessage | null;
  setReplyMsgData?: (value: IMessage | null) => void;
  editedMessage?: IMessage | null;
  setEditedMessage?: (value: IMessage | null) => void;
  conversationInfo?: ConversationInfo | null;
  groupMembers?: IGroupMember[];
}

const fileUploadUI = [
  {
    id: 1,
    icon: <CameraSVG size={19} color={colors._B9B9B9_gray} />,
    title: 'Camera',
  },
  {
    id: 2,
    icon: <GallerySVG size={20} color={colors._B9B9B9_gray} />,
    title: 'Gallery',
  },
  {
    id: 3,
    icon: <FileSVG size={18} color={colors._B9B9B9_gray} />,
    title: 'Document',
  },
  {
    id: 4,
    icon: <AudioSVG size={20} color={colors._B9B9B9_gray} />,
    title: 'Audio',
  },
  {
    id: 5,
    icon: <PersonSVG size={22} color={colors._B9B9B9_gray} />,
    title: 'Contact',
  },
  {
    id: 6,
    icon: <LocationSVG2 size={22} color={colors._B9B9B9_gray} />,
    title: 'Location',
  },
  // todo: enable polls
  // {
  //   id: 7,
  //   icon: <PollsSVG size={20} color={colors._B9B9B9_gray} />,
  //   title: 'Poll',
  // },
];

const ChatComposer = ({
  userData,
  replyMsgData,
  setReplyMsgData = () => {},
  editedMessage,
  setEditedMessage = () => {},
  conversationInfo,
  groupMembers = [],
}: IChatComposerProps) => {
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [plusIconModal, setPlusIconModal] = useState<boolean>(false);
  const [fileUploadModal, setFileUploadModal] = useState<boolean>(false);
  const [composerType, setComposerType] = useState<string>('keyboard');
  const [voiceMessageModal, setVoiceMessageModal] = useState<boolean>(false);
  const [imageModal, setImageModal] = useState<boolean>(false);
  const [scheduleTimerSheet, setScheduleTimerSheet] = useState<boolean>(false);
  const [selectedScheduledMessage, setSelectedScheduledMessage] = useState<IMessage | null>(null);
  const [scheduleDate, setScheduleDate] = useState(null);

  const { user: me } = useMe();
  const initialComposedMessage: ComposedMessage = {
    senderId: me?._id || '',
    receiverId: conversationInfo?.id as string,
    isSilent: false,
    scheduledAt: undefined,
    // !! update this (make it dynamic)
    conversationType:
      conversationInfo?.type === 'group'
        ? ConversationType.GROUP
        : conversationInfo?.type === 'channel'
        ? ConversationType.CHANNEL
        : ConversationType.P2P,
    messageType: MessageType.TEXT,
    text: '',
  };

  const [composedMessage, setComposedMessage] = useState<ComposedMessage>(initialComposedMessage);

  // refs
  const textInputRef = useRef<TextInput>(null);

  const { getConversattionById } = useConversations();
  const currentConversation: IConversation = getConversattionById(conversationInfo?.id as string);
  const liveConversationRealm = ChatService.getLiveConversation(conversationInfo?.id as string);
  const isMyreply = replyMsgData?.senderId == me?._id;
  const originalSenderData = ChatService.getUserData(replyMsgData?.senderId ?? '');

  const canShowComposer =
    conversationInfo?.type === ConversationType.P2P ||
    conversationInfo?.type === ConversationType.GROUP ||
    (conversationInfo?.type === ConversationType.CHANNEL &&
      conversationInfo?.membershipStatus === 'owner' &&
      currentConversation);

  // const memberPermissions = currentConversation
  //   ? ChatSpacePermissions(liveConversation ?? userData.conversation)
  //   : defaultMemberPermissions;

  // const permissionMap: Record<string, keyof typeof memberPermissions> = {
  //     Camera: 'canSendPhotos',
  //     Gallery: 'canSendPhotos',
  //     Document: 'canSendFiles',
  //     Audio: 'canSendMusic',
  //     Contact: 'canSendContact',
  //     Location: 'canSendLocation',
  // };

  const [showMembersList, setShowMembersList] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [mentions, setMentions] = useState<IGroupMember[]>([]);

  const filteredMembers = useMemo(() => {
    if (!mentionQuery) {
      return groupMembers;
    }
    return groupMembers.filter(
      (member) =>
        member?.user?.name?.toLowerCase().includes(mentionQuery.toLowerCase()) ||
        member?.user?.username?.toLowerCase().includes(mentionQuery.toLowerCase()),
    );
  }, [groupMembers, mentionQuery]);

  const handleMemberSelect = (member: IGroupMember) => {
    const mentionUsername = member?.user?.name || '';
    //@ts-ignore
    const currentText = composedMessage.text;

    if (!mentions.some((m) => m.userId === member.userId)) {
      setMentions((prevMentions) => [...prevMentions, member]);
    }

    const words = currentText.split(' ');
    words[words.length - 1] = `@${mentionUsername}`;
    const newText = words.join(' ') + ' ';

    setComposedMessage((prev) => ({ ...prev, text: newText }));
    setShowMembersList(false);
    setMentionQuery('');
    setSelectedMemberId(null);

    textInputRef.current?.focus();
  };

  const handleJoinGroupviaLink = async (chatSpaceId: string, inviteCode: string) => {
    setIsCreating(true);
    try {
      const joinResponse = await sendJoinRequest(chatSpaceId, inviteCode);
      if (joinResponse?.status === true) {
        if (joinResponse?.status === true) {
          // const localChatSpace = ChatService.saveChatSpace(
          //   joinResponse.data?.chatSpace,
          //   MembershipStatus.OWNER,
          //   joinResponse.data?.systemMessage,
          // );
          console.log('🚀 ~ handleJoinGroupviaLink ~ joinResponse:', joinResponse);

          ChatService.onIncomingMessage(joinResponse.data);
        } else {
          errorToast(joinResponse?.message || 'Failed to join group.');
        }
      } else {
        errorToast(joinResponse?.message || 'Failed to join group. Please try again.');
      }
    } catch (error) {
      console.error('Error joining group via link:', error);
      errorToast('Failed to join group. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleSendMessage = async (value?: any, isEmit: boolean = true) => {
    const audioData: ComposedMessage = {
      ...composedMessage,
      messageType: MessageType.VOICE,
      mediaUrl: value?.audioUrl,
      fileName: generateLocalMessageId(),
      fileSize: 12345,

      isMedia: value?.isMedia,
    };
    const audioToText: ComposedMessage = {
      ...composedMessage,
      messageType: MessageType.TEXT,
      text: value?.text,
    };
    const locationData: ComposedMessage = {
      ...composedMessage,
      // ...composedMessage,
      messageType: value?.messageType,
      ...(value?.text && { text: value?.text }),
      location: {
        latitude: value?.latitude,
        longitude: value?.longitude,
        type: value?.type,
        ...(value?.type == 'live' && {
          expiresAt: value?.expiresAt,
          isStopped: false,
        }),
      },
    };

    const documentData: ComposedMessage = {
      ...composedMessage,
      messageType: MessageType.DOCUMENT,
      mediaUrl: value?.mediaUrl,
      fileName: value?.fileName ?? '',
      fileSize: typeof value?.fileSize === 'number' ? value?.fileSize : 0,
      text: value?.text ?? '',

      isMedia: value?.isMedia,
    };

    const contactData: ComposedMessage = {
      ...composedMessage,
      messageType: MessageType.CONTACT,
      contact: {
        name: value?.contact?.name ?? '',
        phoneNumber: value?.contact?.phoneNumber ?? '',
        ...(value?.contact?.userId && { userId: value.contact.userId }),
      },
    };

    const data =
      value?.messageType == 'voice'
        ? audioData
        : value?.messageType === 'location'
        ? locationData
        : value?.messageType == 'audioText'
        ? audioToText
        : value?.messageType === 'document'
        ? documentData
        : value?.messageType === 'contact'
        ? contactData
        : value || composedMessage;

    // const data = value?.messageType == 'audio' ? audioData : (value || composedMessage)
    const mentionedIds = mentions.map((member) => member.userId);
    const composeReplyMsg = {
      ...data,
      replyToMessageId: replyMsgData !== null ? replyMsgData?.globalId : null,
      ...(mentionedIds.length > 0 && { mentionedIds }),
    };

    if (data.messageType === MessageType.TEXT && !data.text.trim()) return;
    if (data.scheduledAt && data.status == MessageStatus.SCHEDULED) {
      const scheduledData: ComposedMessage = {
        ...data,
        senderId: me?._id || '',
        receiverId: conversationInfo?.id,
        conversationType: ConversationType.P2P,
      };
    }
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    const sentData = await ChatService.sendMessage(editedMessage ? value : { ...composeReplyMsg });

    setComposedMessage(initialComposedMessage);
    setComposerType('keyboard');
    setMentions([]);
    if (replyMsgData !== null) {
      setReplyMsgData(null);
    }
    if (value?.isMedia) {
      return sentData;
    }
  };

  const onCameraPress = async (type: 'photo' | 'video' | 'any') => {
    try {
      setImageModal(false);
      const media = await ImageCropPicker.openCamera({
        mediaType: type, // allows both image and video
        cropping: false, // you can enable cropping for photos if needed
      });
      console.log('🚀 ~ onFileIconPress ~ media:', JSON.stringify(media, null, 2));

      if (!media?.path) {
        console.log('❌ Media path not found');
        return;
      }

      const sizeInMB = bytesToMB(media.size ?? 0);
      if (sizeInMB > 50) {
        ToastAndroid.show(
          `File size too large (${sizeInMB.toFixed(2)} MB). Please upload a file under 50 MB.`,
          ToastAndroid.LONG,
        );
        return;
      }

      const isVideoMsg = media.mime.startsWith('video');
      const otherUser: IMediaUserProps = {
        id: conversationInfo?.id as string,
        displayName: conversationInfo?.displayName as string,
        type: conversationInfo?.type as ConversationType,
        displayPic: conversationInfo?.displayPic as string,
      };
      navigateTo('MediaPreviewScreen', {
        msgData: media,
        isFromCameraScreen: true,
        handleSendMessage: handleSendMessage,
        otherUserData: otherUser,
      });
    } catch (error) {
      console.log('🚀 ~ Camera Picker Error:', error);
    }
  };

  const onFileIconPress = async (type: string) => {
    console.log(' File icon pressed. Type:', type);
    if (type === 'Document') {
      setFileUploadModal(false);
      try {
        const results = await pick({
          type: ['*/*'],
          multiple: true,
          allowMultiSelection: true,
        });

        if (results.length > 3) {
          showToast('You can select up to 3 documents at a time.');
          setFileUploadModal(false);
          return;
        }

        if (results.length === 1) {
          //  Case 1 - Single document - go to preview screen
          try {
            const [res] = results;
            const [copyResult] = await keepLocalCopy({
              files: [
                {
                  uri: res.uri,
                  fileName: res.name ?? 'document',
                },
              ],
              destination: 'documentDirectory',
            });

            if (copyResult.status === 'success') {
              navigateTo('DocumentPreviewScreen', {
                fileUrl: copyResult.localUri,
                fileName: res.name ?? 'document',
                fileSize: res.size ?? 0,
                composedMessageProp: composedMessage,
                onSend: handleSendMessage,
                isFromChat: false,
              });
            } else {
              console.warn('File copy failed:', copyResult.copyError);
            }
          } catch (err) {
            console.error('Document Picker Error (single):', err);
          }
        } else {
          //  Case 2 - Multiple documents - directly send
          for (let i = 0; i < results.length; i++) {
            const res = results[i];
            try {
              const [copyResult] = await keepLocalCopy({
                files: [
                  {
                    uri: res.uri,
                    fileName: res.name ?? 'document',
                  },
                ],
                destination: 'documentDirectory',
              });

              const documentData: ComposedMessage = {
                ...composedMessage,
                messageType: MessageType.DOCUMENT,
                mediaUrl: copyResult.sourceUri,
                fileName: res.name ?? 'document',
                fileSize: res.size ?? 0,
                text: i === 0 ? composedMessage?.text : '', // ✅ only first doc gets text
                isMedia: true,
              };

              const msgData = await handleSendMessage(documentData);

              if (copyResult.status === 'success') {
                const formData = new FormData();
                formData.append('file', {
                  uri: copyResult.sourceUri,
                  name: res.name ?? 'document',
                  type: res.type || 'application/octet-stream',
                });
                await handleMediaFileSharing(formData, msgData);
              } else {
                console.warn('File copy failed:', copyResult.copyError);
              }
            } catch (err) {
              console.error('Document Picker Error:', err);
            }
          }
        }
        setFileUploadModal(false);
      } catch (pickerError) {
        console.error('[Document Picker] Error during pick:', pickerError);
      }
    } else if (type === 'Audio') {
      console.log('[Audio] Starting audio pick process');
      setFileUploadModal(false);
      try {
        const result = await pick({
          type: ['audio/*'],
          multiple: true,
          allowMultiSelection: true,
        });
        console.log('[Audio] Pick result:', result);

        if (result?.length > 3) {
          showToast('You can select up to 3 documents at a time.');
          setFileUploadModal(false);
          return;
        }
        if (!result?.length) {
          console.log('❌ Audio file not selected');
          return;
        }
        for (const res of result) {
          try {
            const [copyResult] = await keepLocalCopy({
              files: [
                {
                  uri: res.uri,
                  fileName: res.name ?? 'audio',
                },
              ],
              destination: 'documentDirectory',
            });

            const audioData: ComposedMessage = {
              ...composedMessage,
              messageType: MessageType.AUDIO,
              mediaUrl: copyResult.sourceUri,
              fileName: res.name ?? 'audio',
              fileSize: res.size ?? 0,
              isMedia: true,
              localPath: copyResult?.localUri ? copyResult?.localUri : '',
            };
            const msgData = await handleSendMessage(audioData);
            console.log('msgData', JSON.stringify(msgData, null, 2));

            if (copyResult.status === 'success') {
              console.log('✅ Audio file copied:', JSON.stringify(copyResult, null, 2));
              const formData = new FormData();
              formData.append('file', {
                uri: copyResult.sourceUri,
                name: res.name ?? `file.${res?.type?.split('/')[1]}`,
                type: res.type,
              });

              console.log('[Audio] FormData prepared for sharing:', formData);
              handleMediaFileSharing(formData, msgData);
              console.log('[Audio] Media file sharing initiated');
            } else {
              console.warn('❌ Audio copy failed:', copyResult.copyError);
            }
          } catch (error) {
            console.log('error sending auido files');
          }
        }
      } catch (err) {
        console.error('Audio Picker Error:', err);
      }
    } else if (type === 'Contact') {
      navigateTo('ContactPickerScreen', {
        onSelectContacts: (selectedContacts: UserSchema[]) => {
          selectedContacts.forEach((contact: UserSchema) => {
            handleSendMessage({
              messageType: 'contact',
              contact: {
                name: contact.name,
                phoneNumber: contact.phoneNumber,
                userId: contact.id ?? '',
              },
            });
          });
        },
      });
      setFileUploadModal(false);
    } else if (type === 'Gallery') {
      setFileUploadModal(false);
      try {
        const images = await ImageCropPicker.openPicker({
          width: 400,
          height: 400,
          cropping: false,
          mediaType: 'any',
          multiple: true,
          maxFiles: 3,
        });

        for (let i = 0; i < images.length; i++) {
          const image = images[i];

          if (!image?.path) {
            console.log('❌ File path not found for one of the images');
            continue;
          }

          const sizeInMB = bytesToMB(image.size ?? 0);
          if (sizeInMB > 50) {
            ToastAndroid.show(
              `File size too large. Please upload a file under 50 MB.`,
              ToastAndroid.LONG,
            );
            continue;
          }

          const isVideoMsg = image.mime.startsWith('video');
          if (isVideoMsg) {
            image.filename = image.path.split('/').pop();
          }

          const data: ComposedMessage = {
            ...composedMessage,
            messageType: isVideoMsg ? MessageType.VIDEO : MessageType.IMAGE,
            mediaUrl: image.path,
            fileName: image.filename,
            fileSize: image.size,
            text: i === 0 ? composedMessage?.text : '', // ✅ only first keeps text
            isMedia: true,
          };

          const realmData = await handleSendMessage(data, false);

          const formData = new FormData();
          formData.append('file', {
            uri: image.path,
            name: image.filename ?? `file.${image.mime.split('/')[1]}`,
            type: image.mime,
          });

          await handleMediaFileSharing(formData, realmData, isVideoMsg);
        }
      } catch (error) {
        console.log('🚀 ~ onFileIconPress ~ error:', error);
      }
    } else if (type === 'Location') {
      setFileUploadModal(false);
      navigateTo('GoogleMapsScreen', {
        handleSendMessage: handleSendMessage,
        userData: conversationInfo,
        composedMessage,
      });
    } else if (type === 'Camera') {
      setFileUploadModal(false);
      setImageModal(true);
      // navigateTo('CameraScreen', { handleSendMessage: handleSendMessage, otherUserData: userData });
    }
  };

  const handleSendAndRecord = () => {
    console.log('-----------------------------');

    const editedData = {
      ...editedMessage,
      text: composedMessage.text,
      isForwarding: false,
      isTextEdited: true,
    };
    if (composerType == 'schedule') {
      setTimeout(() => {
        setScheduleTimerSheet(true);
      }, 200);
      return;
      // todo: solve this error.we shouldn't use @ts-ignore
      //  @ts-ignore
    } else if (composedMessage?.text?.trim() == '') {
      // if (memberPermissions?.canSendVoiceMessages) {
      setVoiceMessageModal(true);
      return;
      // }
    }
    console.log('---------=========================');

    handleSendMessage(editedMessage ? editedData : null);
    if (editedMessage) {
      Keyboard.dismiss();
      setEditedMessage(null);
    }
  };

  const onChangeText = (text: string) => {
    if (text.length > 3000) {
      text = text.substring(0, 3000);
    }

    // !!TODO: Fix typing
    // ChatService.onTyping(conversationInfo?.id as string);
    const words = text.split(' ');
    const currentWord = words[words.length - 1];

    if (currentWord.startsWith('@') && conversationInfo?.type === ConversationType.GROUP) {
      setShowMembersList(true);
      setMentionQuery(currentWord.substring(1));
    } else {
      setShowMembersList(false);
      setMentionQuery('');
    }

    const updatedMentions = mentions.filter((member) => text.includes(`@${member?.user?.name}`));
    if (updatedMentions.length !== mentions.length) {
      setMentions(updatedMentions);
    }
    setComposedMessage((prev) => ({ ...prev, text }));
  };

  const onClosePress = () => {
    setReplyMsgData(null);
  };

  const onAttatchmentPress = (title: string) => {
    // if (isAllowed) {
    onFileIconPress(title);
    // }
  };

  const onTimerAndPlus = () => {
    // if (memberPermissions?.canSendText) {
    Keyboard.dismiss();
    setTimeout(() => {
      setPlusIconModal(!plusIconModal);
    }, 100);
    // }
  };

  const onClickSchedule = () => {
    if (selectedScheduledMessage) {
      console.log(
        selectedScheduledMessage.globalId as string,
        composedMessage.scheduledAt as number,
      );
      ChatService.updateScheduleMessage(
        selectedScheduledMessage.globalId as string,
        ScheduleUpdateType.RESCHEDULE,
        composedMessage.scheduledAt as number,
      );
      setSelectedScheduledMessage(null);
      setComposedMessage((prev) => ({ ...prev, scheduledAt: undefined }));
    } else {
      handleSendMessage();
    }
    setScheduleTimerSheet(false);
  };

  const onSlientMessage = () => {
    // setComposerType('silentMessage');
    setComposedMessage((prev) => ({
      ...prev,
      isSilent: prev.isSilent ? false : true,
    }));
    setPlusIconModal(false);
  };

  const onSchedulePress = () => {
    setComposerType(composerType === 'schedule' ? 'keyboard' : 'schedule');
    setPlusIconModal(false);
  };

  const switchToKeyboard = () => {
    setComposerType('keyboard');
    setPlusIconModal(false);
  };

  useEffect(() => {
    if (editedMessage) {
      setComposedMessage((prev) => ({
        ...prev,
        text: editedMessage?.text as string,
        localId: editedMessage?.localId,
      }));
      setTimeout(() => {
        textInputRef?.current?.focus();
      }, 200);
    } else {
      setComposedMessage((prev) => ({ ...prev, text: '', localId: '' }));
    }
  }, [editedMessage]);

  if (
    conversationInfo?.type === ConversationType.GROUP &&
    conversationInfo?.membershipStatus === MembershipStatus.DISCOVERING
  ) {
    const isPrivate = conversationInfo?.isPrivate;

    if (isPrivate) {
      // const status = liveConversation?.joinRequestStatus;
      // switch (status) {
      //   case joinRequestStatus.PENDING:
      //     return (
      //       <View style={styles.keyboardView}>
      //         <ButtonPurple
      //           title={'Request Sent'}
      //           disabled={true}
      //           extraStyle={styles.joinButton}
      //         />
      //       </View>
      //     );
      //   case joinRequestStatus.REJECTED:
      //     return (
      //       <View style={styles.keyboardView}>
      //         <ButtonPurple title={'Request Rejected'} extraStyle={styles.joinButton} />
      //       </View>
      //     );
      //   case joinRequestStatus.NONE:
      //   default:
      //     return (
      //       <View style={styles.keyboardView}>
      //         <ButtonPurple
      //           title={'Send Request to Join'}
      //           onPress={() =>
      //             handleRequestJoin(conversationInfo?.id, userData.conversation?.inviteCode || '')
      //           }
      //           isLoading={isCreating}
      //           extraStyle={styles.joinButton}
      //         />
      //       </View>
      //     );
      // }
    } else {
      return (
        <View style={[styles.keyboardView, { zIndex: 12, justifyContent: 'center' }]}>
          <ButtonPurple
            title={'Join Group'}
            onPress={() =>
              handleJoinGroupviaLink(conversationInfo?.id, conversationInfo?.inviteCode || '')
            }
            isLoading={isCreating}
            disabled={isCreating}
            extraStyle={styles.joinButton}
          />
        </View>
      );
    }
  }
  // if (liveConversation?.isBlocked) {
  //   return (
  //     <View style={[styles.keyboardView, { zIndex: 12, justifyContent: 'center' }]}>
  //       <TouchableOpacity
  //         style={{
  //           paddingHorizontal: 20,
  //           paddingVertical: 10,
  //         }}
  //         onPress={() => setShowUnblockAlert(true)}
  //       >
  //         <Text style={{ color: colors.mainPurple, fontWeight: '600', fontSize: 16 }}>
  //           {`Unblock`}
  //         </Text>
  //       </TouchableOpacity>

  //       <CustomAlert
  //         visible={showUnblockAlert}
  //         onCancel={() => setShowUnblockAlert(false)}
  //         onConfirm={handleConfirmUnblockUser}
  //         title="Unblock User"
  //         message={`Are you sure you want to unblock ${liveConversation?.displayName}?`}
  //         confirmText="Unblock"
  //         cancelText="Cancel"
  //       />
  //     </View>
  //   );
  // }

  return (
    <>
      {fileUploadModal ? (
        <View
          style={{
            backgroundColor: colors.white,
            borderTopRightRadius: 15,
            borderTopLeftRadius: 15,
          }}
        >
          <View style={[styles.line, { marginBottom: hp(1) }]} />
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: colors.white,
                alignItems: 'center',
                paddingLeft: 35,
                paddingVertical: 10,
              }}
            >
              {fileUploadUI.map(({ id, title, icon }) => {
                // const permissionKey = permissionMap[title];
                // const isAllowed = permissionKey ? memberPermissions[permissionKey] : true;
                return (
                  <TouchableOpacity
                    style={{
                      marginRight: 35,
                      // opacity: isAllowed ? 1 : 0.5
                      opacity: 0.5,
                    }}
                    key={id}
                    onPress={() => onAttatchmentPress(title)}
                  >
                    <View
                      style={{
                        height: 50,
                        width: 50,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: 30,
                        backgroundColor: colors.gray_f3,
                      }}
                    >
                      {icon}
                    </View>
                    <Text
                      style={{
                        color: colors.black,
                        fontSize: 12,
                        fontWeight: '400',
                        alignSelf: 'center',
                        marginTop: 5,
                      }}
                    >
                      {title}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </ScrollView>
        </View>
      ) : (
        <></>
      )}

      {replyMsgData && !fileUploadModal ? (
        <View style={{}}>
          <LinearGradient
            start={{ x: 1, y: 0 }}
            end={{ x: 1, y: 1 }}
            colors={['#ffffff', '#ffffff', '#f7f7f7']}
            style={{}}
          >
            <View
              style={{
                flexDirection: 'row',
                paddingLeft: 21,
                paddingVertical: replyMsgData?.messageType == MessageType.IMAGE ? 10 : 14,
                paddingRight:
                  replyMsgData?.messageType == MessageType.IMAGE ||
                  replyMsgData?.messageType == MessageType.VIDEO
                    ? 7
                    : 40,
                alignItems: 'center',
              }}
            >
              <View
                style={{
                  flex: 1,
                  marginRight:
                    replyMsgData?.messageType == MessageType.IMAGE ||
                    replyMsgData?.messageType === MessageType.VIDEO
                      ? 0
                      : 10,
                }}
              >
                {replyMsgData?.messageType !== MessageType.IMAGE &&
                replyMsgData?.messageType !== MessageType.VIDEO ? (
                  <Text
                    style={{
                      color: colors.mainPurple,
                      fontSize: 15,
                      fontWeight: '500',
                      marginBottom: 2,
                    }}
                  >
                    {isMyreply
                      ? 'You'
                      : conversationInfo?.type === ConversationType.P2P
                      ? conversationInfo?.displayName
                      : originalSenderData?.name}
                  </Text>
                ) : null}

                {replyMsgData?.messageType === MessageType.IMAGE ||
                replyMsgData?.messageType === MessageType.VIDEO ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <View>
                      <Text
                        style={{
                          color: colors.mainPurple,
                          fontSize: 15,
                          fontWeight: '500',
                          marginBottom: 5,
                        }}
                      >
                        {isMyreply
                          ? 'You'
                          : conversationInfo?.type === ConversationType.P2P
                          ? conversationInfo?.displayName
                          : originalSenderData?.name}
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 5,
                        }}
                      >
                        {replyMsgData?.messageType === MessageType.VIDEO ? (
                          <VideoCallSVG color={colors.black_23} size={14} />
                        ) : (
                          <GallerySVG size={14} color={colors.black_23} />
                        )}

                        <Text numberOfLines={1} style={styles.lastMessage}>
                          {replyMsgData?.messageType === MessageType.VIDEO ? 'Video' : 'Photo'}
                          {replyMsgData?.messageType === MessageType.VIDEO &&
                            ` (${formatBytes(Number(replyMsgData?.fileSize))})`}
                        </Text>
                      </View>
                    </View>

                    <View style={{ elevation: 4 }}>
                      <Image
                        source={{
                          uri:
                            replyMsgData?.messageType === MessageType.VIDEO
                              ? replyMsgData?.videoThumbnail
                              : replyMsgData?.mediaUrl,
                        }}
                        style={{ width: 60, height: 60, borderRadius: 10 }}
                        resizeMode="cover"
                      />
                      <TouchableOpacity
                        style={{ position: 'absolute', top: 7, right: 7 }}
                        hitSlop={{ top: 10, right: 10, left: 10, bottom: 10 }}
                        onPress={onClosePress}
                      >
                        <CloseFilledSVG size={16} color={colors.gray_80} />
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : replyMsgData?.messageType == MessageType.AUDIO ||
                  replyMsgData?.messageType == MessageType.VOICE ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 5,
                    }}
                  >
                    {replyMsgData?.messageType == MessageType.AUDIO ? (
                      <MusicSVG size={15} pathColor={colors._464646_gray} withOutBg={true} />
                    ) : (
                      <MicSVG2 size={14} color={colors.black_23} />
                    )}
                    <Text style={{ color: colors.black_23, fontSize: 14 }} numberOfLines={2}>
                      {replyMsgData?.messageType == MessageType.AUDIO
                        ? replyMsgData?.fileName
                        : `Voice mesage`}
                    </Text>
                  </View>
                ) : replyMsgData?.messageType === MessageType.DOCUMENT ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <ReplyDocSVG size={16} color={colors.black_23} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      {replyMsgData?.fileName ?? 'Document'}
                      {replyMsgData?.fileSize ? ` (${formatBytes(replyMsgData.fileSize)})` : ''}
                    </Text>
                  </View>
                ) : replyMsgData?.messageType === MessageType.CONTACT ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <ReplyAvatarSVG size={15} color={colors.black_23} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      {replyMsgData?.contact?.name ?? 'Contact'}
                    </Text>
                  </View>
                ) : replyMsgData?.messageType === MessageType.LOCATION ? (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                    <LocationSVG2 size={16} color={colors.black_23} />
                    <Text numberOfLines={1} style={styles.lastMessage}>
                      {'Location'}
                    </Text>
                  </View>
                ) : (
                  <Text
                    style={{
                      color: colors.black_23,
                      fontSize: 14,
                      fontWeight: '400',
                    }}
                    numberOfLines={1}
                  >
                    {replyMsgData?.text}
                  </Text>
                )}
              </View>

              {replyMsgData?.messageType !== MessageType.IMAGE &&
              replyMsgData?.messageType !== MessageType.VIDEO ? (
                <TouchableOpacity
                  hitSlop={{ top: 10, bottom: 10, right: 10, left: 10 }}
                  style={{}}
                  onPress={onClosePress}
                >
                  <CloseSVG size={13} color={colors.gray_80} />
                </TouchableOpacity>
              ) : null}
            </View>
          </LinearGradient>
        </View>
      ) : null}
      {/* </GestureHandlerRootView> */}

      {/* </BottomSheetAboveComposer> */}

      {userData.type === ConversationType.GROUP && (
        <MembersListModal
          showMembersList={showMembersList}
          filteredMembers={filteredMembers}
          handleMemberSelect={handleMemberSelect}
          selectedMemberId={selectedMemberId}
        />
      )}

      {canShowComposer && (
        <View style={[styles.keyboardView, { zIndex: 12 }]} onLayout={(e) => {}}>
          <View
            style={{
              flex: 1,
              minHeight: 50,
              maxHeight: 90,
              backgroundColor: colors.gray_f3,
              padding: 12,
              borderRadius: 15,
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
              <TouchableOpacity style={{}} onPress={onTimerAndPlus}>
                {composerType == 'schedule' && !plusIconModal ? (
                  <TimerSVG size={26} color={colors.mainPurple} />
                ) : (
                  <PlusSVG
                    style={{
                      transform: [{ rotate: plusIconModal ? '45deg' : '0deg' }],
                    }}
                    size={26}
                    color={plusIconModal ? colors.mainPurple : colors.black_23}
                  />
                )}
              </TouchableOpacity>
              <View style={{ flex: 1, marginLeft: 10 }}>
                <TextInput
                  ref={textInputRef}
                  value={
                    composedMessage.messageType === MessageType.TEXT ? composedMessage.text : ''
                  }
                  placeholder={
                    // memberPermissions?.canSendText
                    composedMessage.isSilent ? 'Write your silent message' : 'Write your message'
                    // : "You can't send messages in this group"
                  }
                  placeholderTextColor={colors._B5B5B5_gray}
                  onChangeText={onChangeText}
                  style={{ fontSize: 14, color: colors.black, padding: 0 }}
                  multiline
                  // editable={memberPermissions?.canSendText}
                  maxLength={3000}
                />
              </View>
            </View>

            <TouchableOpacity style={{}} onPress={() => setFileUploadModal((prev) => !prev)}>
              <ShareIconSVG />
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={{}} onPress={handleSendAndRecord}>
            {composedMessage.messageType === MessageType.TEXT && composedMessage.text?.trim() ? (
              <SendSVG size={47} color={colors.white} backgroundColor={colors.mainPurple} />
            ) : (
              <VoiceRecorderSVG size={47} color={colors.mainPurple} background={colors.white} />
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Voice recording modal */}
      {voiceMessageModal ? (
        <AudioRecorderModal
          isVisible={voiceMessageModal}
          onCloseModal={() => {
            setVoiceMessageModal(false);
          }}
          handleSendMessage={handleSendMessage}
        />
      ) : null}

      {/* capture type sheet */}
      <CaptureTypeModal
        imageModal={imageModal}
        onCameraPress={onCameraPress}
        setImageModal={setImageModal}
      />

      {/* schedule timer sheet */}
      <ScheduleMessageModal
        isVisible={scheduleTimerSheet}
        onClose={() => {
          setScheduleTimerSheet(false);
        }}
        setscheduleDate={(scheduledAt: any) => {
          setComposedMessage((prev) => ({ ...prev, scheduledAt }));
        }}
        scheduleDate={scheduleDate}
        onClickSchedule={onClickSchedule}
      />

      <KeyboardModal
        composedMessage={composedMessage}
        onSchedulePress={onSchedulePress}
        onSlientMessage={onSlientMessage}
        plusIconModal={plusIconModal}
        setPlusIconModal={setPlusIconModal}
        switchToKeyboard={switchToKeyboard}
        composerType={composerType}
      />
    </>
  );
};

export default ChatComposer;

const styles = StyleSheet.create({
  keyboardView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    width: SCREEN_WIDTH,
    paddingHorizontal: 21,
    paddingVertical: 13,
    gap: 13,
  },
  bottomText: {
    fontSize: 16,
    color: colors.black_23,
    fontWeight: '400',
  },
  line: {
    width: 45,
    height: 5,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 13,
    borderRadius: 10,
    alignSelf: 'center',
    marginBottom: hp(2.5),
  },
  linearGradient: {},
  lastMessage: {
    color: colors.black_23,
    fontSize: 14,
    fontWeight: '400',
    flexShrink: 1,
  },
  joinButton: {
    flex: 1,
    backgroundColor: colors.mainPurple,
    paddingVertical: 15,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
