// import {useCallContext} from '@/lib/contexts/CallProvider';
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ImageBackground,
  Pressable,
} from 'react-native';
import { useCallContext } from '../../../Context/CallProvider';
import { CallDetails, Participant } from '../../../types/calls.types';
import FastImageBackground from '../../../component/utils/FastImageBackground';

import React, { useEffect, useRef, useState } from 'react';

import { RTCView, MediaStream, MediaStreamTrack, mediaDevices } from 'react-native-webrtc';
import { BlurView } from '@react-native-community/blur';
import LottieView from 'lottie-react-native';

import SoundWaveJson from '../../../assets/animated_profile/SoundWave.json';
import { useMediasoup } from '../../../Context/RtcProvider';
import { IMAGES } from '../../../assets/Images';
import VoiceRecorderSVG from '../../../assets/svgIcons/VoiceRecorderSVG';
import { FeatherIcons } from '../../../utils/vectorIcons';

const DEFAULT_IMAGE = require('../UserAvatar.png');

type CallMemberTileProps = {
  participent: Participant;
  callDetails: CallDetails;
  seconds?: number;
  idx: number;
  selectUser(participent: Participant, idx: number): void;
  isScreenSharing?: boolean;
  fullScreenMode?: boolean;
  onClick?: () => void;
};

function CallMemberTile({
  participent,
  callDetails,
  selectUser,
  idx,
  isScreenSharing = false,
  fullScreenMode = false,
  onClick,
}: CallMemberTileProps) {
  return (
    <>
      {(callDetails.type === 'video' || (callDetails.type === 'audio' && isScreenSharing)) && (
        <VideoCallTile
          callDetails={callDetails}
          participent={participent}
          selectUser={selectUser}
          idx={idx}
          isScreenSharing={isScreenSharing}
          fullScreenMode={fullScreenMode}
          onClick={onClick}
        />
      )}
      {callDetails.type === 'audio' && !isScreenSharing && (
        <AudioCallTile
          callDetails={callDetails}
          participent={participent}
          selectUser={selectUser}
          isScreenSharing={isScreenSharing}
          idx={idx}
          onClick={onClick}
        />
      )}
    </>
  );
}

export function VideoCallTile({
  participent,
  selectUser,
  idx,
  isScreenSharing = false,
  fullScreenMode = false,
  onClick,
}: CallMemberTileProps) {
  const { callDetails } = useCallContext();

  return (
    <View
      style={[
        {
          height: 370,
          flex: 1,
          overflow: 'hidden',
          borderRightWidth: !fullScreenMode ? 1 : 0,
          borderTopWidth: idx > 1 && !fullScreenMode ? 1 : 0,
          borderColor: '#FFF',
        },
      ]}
    >
      {/* Top Bar */}

      {/* Main Video Area */}
      <View style={vidCallTileStyles.videoArea}>
        <VideoRenderer
          participent={participent}
          isScreenSharing={isScreenSharing}
          callDetails={callDetails}
          idx={idx}
          fullScreenMode={fullScreenMode}
          selectUser={selectUser}
          onClick={onClick}
        />
      </View>

      {/* Bottom Bar */}
    </View>
  );
}

/**
 * Component responsible for rendering video and audio for a participent
 */

export function VideoRenderer({
  participent,
  isScreenSharing = false,
  callDetails,
  idx,
  selectUser,
  fullScreenMode,
  onClick,
}: VideoAudioRendererProps) {
  const [videoURL, setVideoURL] = useState<string | null>(null);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [muted, setMuted] = useState<boolean>(false);
  // Handle video stream
  useEffect(() => {
    const iffe = async () => {
      if (participent.videoTrack) {
        const stream = new MediaStream([participent.videoTrack]);
        const url = stream.toURL();

        setVideoURL(url);
      } else {
        setVideoURL(null);
      }

      return () => {
        setVideoURL(null);
      };
    };
    iffe();
  }, [participent.videoTrack, participent.participantId, isScreenSharing]);

  // Handle audio stream (just keep it alive)
  useEffect(() => {
    if (participent.audioTrack) {
      const stream = new MediaStream([participent.audioTrack]);
      setAudioStream(stream);
    } else {
      setAudioStream(null);
    }

    return () => {
      setAudioStream(null);
    };
  }, [participent.audioTrack, participent.participantId, isScreenSharing]);

  const toggleMute = () => {
    const audioTrack = participent?.audioTrack;
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;
      const isMuted = !audioTrack.enabled;
      setMuted(isMuted);
    } else {
      console.warn('Audio track not available');
    }
  };

  return (
    <Pressable
      style={videoRendererStyles.container}
      onPress={(e) => {
        console.log('inrenderer');
        onClick && onClick();
      }}
      onLongPress={(e) => {
        selectUser(participent, idx);
      }}
    >
      {videoURL && (!participent.isVideoPaused || isScreenSharing) && (
        <RTCView
          mirror={isScreenSharing ? false : true} // should be false for screen sharing, coz screen sharing is not mirror
          streamURL={videoURL}
          style={[videoRendererStyles.video, isScreenSharing && videoRendererStyles.screenSharing]}
          objectFit="cover"
        />
      )}
      {callDetails.type === 'video' && participent.isVideoPaused && (
        <View
          style={[videoRendererStyles.video, { alignItems: 'center', justifyContent: 'center' }]}
        >
          <FeatherIcons name="video-off" size={50} color="white" />
        </View>
      )}

      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: !fullScreenMode ? 'space-between' : 'center',
          position: 'absolute',
          bottom: 20,
          // bottom: 10,
          paddingHorizontal: 15,
          width: '100%',
        }}
      >
        <View style={{}}>
          <Text style={{ color: '#fff' }}>{participent?.client?.name}</Text>
        </View>
        <TouchableOpacity
          onPress={(e) => {
            e.stopPropagation();
            toggleMute();
          }}
          style={{
            display: !fullScreenMode ? 'flex' : 'none',
          }}
        >
          {participent.audioConsumer && (
            <SoundWaveVisualizer producerId={participent.audioConsumer?.producerId} muted={muted} />
          )}
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        onPress={(e) => {
          e.stopPropagation();
          toggleMute();
        }}
        style={{
          top: 12,
          right: 15,
          position: 'absolute',
          display: fullScreenMode ? 'flex' : 'none',
        }}
      >
        <Image
          source={muted ? IMAGES.mute_white : IMAGES.mute}
          style={{ width: 22, height: 22 }}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </Pressable>
  );
}

const vidCallTileStyles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  fullScreenBorder: {
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  topBar: {
    position: 'absolute',
    top: 0,
    width: '100%',
    padding: 8,
    zIndex: 50,
  },
  fullScreenTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  miniTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 999,
  },
  username: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  rightControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  videoArea: {
    flex: 1,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
});
const videoRendererStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#374151', // Tailwind bg-gray-700
  },
  video: {
    width: '100%',
    height: '100%',
  },
  screenSharing: {
    borderWidth: 2,
    borderColor: '#3b82f6', // Tailwind border-blue-500
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  profileWrapper: {
    borderWidth: 3,
    borderRadius: 100,
  },
});

export function AudioCallTile({
  participent,
  callDetails,
  selectUser,
  idx,
  fullScreenMode,
  onClick,
}: CallMemberTileProps) {
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    if (callDetails?.state === 'ongoing' && !timerRef.current) {
      timerRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    }

    // Cleanup if component unmounts or call ends
    if (callDetails?.state !== 'ongoing' && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setSeconds(0); // Reset timer if needed
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [callDetails?.state]);
  const formatTime = (totalSeconds: any) => {
    const mins = Math.floor(totalSeconds / 60)
      .toString()
      .padStart(1, '0');
    const secs = (totalSeconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  return (
    <View style={audioCallTileStyles.container}>
      {/* Top bar */}
      <View style={audioCallTileStyles.topBar}>
        <TouchableOpacity style={audioCallTileStyles.optionsButton}></TouchableOpacity>
      </View>

      {/* Audio Renderer */}
      <View style={audioCallTileStyles.audioWrapper}>
        {callDetails.type === 'audio' && (
          <AudioRenderer
            participent={participent}
            callDetails={callDetails}
            seconds={formatTime(seconds)}
            selectUser={selectUser}
            idx={idx}
            fullScreenMode={fullScreenMode ? fullScreenMode : false}
            onClick={onClick}
          />
        )}
      </View>
    </View>
  );
}

const audioCallTileStyles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  topBar: {
    position: 'absolute',
    top: 0,
    width: '100%',
    padding: 8,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    zIndex: 10,
  },
  optionsButton: {
    padding: 8,
  },
  audioWrapper: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});

interface VideoAudioRendererProps {
  participent: Participant; // Fixed typo in prop name
  callDetails: CallDetails; // <-- Add this line
  seconds?: string;
  isScreenSharing?: boolean;
  idx: number;
  fullScreenMode: boolean;
  selectUser: (participant: Participant, idx: number) => void;
  onClick?: () => void;
}

export function AudioRenderer({
  participent,
  callDetails,
  seconds,
  onClick,
  idx,
  selectUser,
}: VideoAudioRendererProps) {
  const { audioTrack } = participent;
  const audioStreamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    if (audioTrack) {
      const stream = new MediaStream([audioTrack]);
      audioStreamRef.current = stream;
    }

    return () => {
      audioStreamRef.current = null;
    };
  }, [audioTrack]);

  const toggleMute = () => {
    const audioTrack = participent?.audioTrack;
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;
      const isMuted = !audioTrack.enabled;
    } else {
      console.warn('Audio track not available');
    }
  };

  return (
    <ImageBackground
      source={{
        uri: participent.client.image,
      }}
      // resizeMode="contain"
      style={{
        flex: 1,
        justifyContent: 'center',
        backgroundColor: !participent.client.image ? '#001F3F' : 'transparent',
      }}
    >
      <BlurView blurType="dark" blurAmount={90} />
      <Pressable
        // onPress={item.onPress}
        onPress={(e) => {
          console.log('clicked');
          onClick && onClick();
        }}
        onLongPress={() => {
          console.log('pressed long');
          selectUser(participent, idx);
        }}
        style={[
          {
            paddingVertical: 40,
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            borderColor: '#FFFFFF',
          },
        ]}
      >
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: 190,
            width: 300,
          }}
        >
          <Image
            source={participent.client?.image ? { uri: participent.client.image } : DEFAULT_IMAGE}
            style={{ width: 100, height: 100, borderRadius: 50 }}
          />
          <Text style={[{ fontSize: 16, marginTop: 5, color: '#FFF' }]}>
            {participent?.client?.username}
          </Text>
          <Text
            style={{
              color: '#FFF',
              marginTop: callDetails?.participants?.length === 1 ? 10 : 0,
            }}
          >
            {callDetails?.state === 'incoming'
              ? 'Incoming audio call'
              : callDetails?.state === 'outgoing'
              ? 'Ringing'
              : callDetails?.state === 'ongoing'
              ? callDetails?.participants?.length === 1 && seconds
              : ''}
          </Text>
          <TouchableOpacity
            onPress={() => {
              toggleMute();
            }}
          >
            <TouchableOpacity
              onPress={() => {
                toggleMute();
              }}
            >
              {/* {participent.audioConsumer && (
                <SoundWaveVisualizer
                  producerId={participent.audioConsumer.producerId}
                  muted={muted}
                />
              )} */}
            </TouchableOpacity>
          </TouchableOpacity>
        </View>
      </Pressable>
    </ImageBackground>
  );
}

export default CallMemberTile;

type SoundWaveVisualizerProps = {
  muted: boolean;
  producerId: string;
};

function SoundWaveVisualizer({ muted, producerId }: SoundWaveVisualizerProps) {
  const [isSilent, setIsSilent] = useState(false);
  const { mediaConsumers } = useMediasoup();

  const silentValue = !!mediaConsumers.producerSilentMap[producerId];

  useEffect(() => {
    setIsSilent((prev) => (prev !== silentValue ? silentValue : prev));
  }, [silentValue]);

  return (
    <View>
      {muted ? (
        <Image source={IMAGES.mute_white} style={{ width: 25, height: 25 }} resizeMode="contain" />
      ) : (
        <VoiceRecorderSVG size={25} />
      )}
    </View>
  );
}
