import { StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import ModalWrapper from '../ModalWrapper';
interface IProps {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
}

const TimeScheduleModal = ({ isVisible, setIsVisible }: IProps) => {
  const [_isVisible, _setIsVisible] = useState(isVisible);
  const onClose = () => {
    _setIsVisible(false);
    setIsVisible(!_isVisible);
  };
  return (
    <ModalWrapper isVisible={_isVisible} onCloseModal={onClose}>
      <Text>TimeScheduleModal</Text>
    </ModalWrapper>
  );
};

export default TimeScheduleModal;

const styles = StyleSheet.create({});
