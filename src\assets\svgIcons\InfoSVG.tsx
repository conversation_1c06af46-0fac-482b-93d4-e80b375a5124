import * as React from 'react';
import Svg, { Circle, Path, SvgProps } from 'react-native-svg';

interface InfoSVGProps extends SvgProps {
  size?: number;
  color?: string;
}

const InfoSVG: React.FC<InfoSVGProps> = ({ size = 20, color = 'currentColor', ...props }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <Circle cx="12" cy="12" r="11" stroke={color} strokeWidth="2" fill="none" />
    {/* Question mark path */}
    <Path d="M12 17.2a1.2 1.2 0 1 0 0-2.4 1.2 1.2 0 0 0 0 2.4zM12 7c-1.65 0-3 1.12-3 2.5 0 .41.34.75.75.75s.75-.34.75-.75c0-.41.67-1 1.5-1s1.5.59 1.5 1.25c0 .56-.41.93-1.13 1.54-.7.6-1.12 1.13-1.12 2.21 0 .41.34.75.75.75s.75-.34.75-.75c0-.56.19-.77.82-1.31.77-.67 1.43-1.25 1.43-2.19C15 8.12 13.65 7 12 7z" fill={color} />
  </Svg>
);

export default InfoSVG; 