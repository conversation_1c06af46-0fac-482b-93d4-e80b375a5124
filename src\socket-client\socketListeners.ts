// import IdbChats from "@/IDB/IdbChats";
// import SchemaConvertor from "@/lib/SchemaConvertor";
// import { AppDispatch, RootState } from "@/store";
// import { loadMessagesForConversation } from "@/store/slices/chatsSlice";
// import { ILocalConversation, IServerMessage } from "@/types/chats.types";
// import { User } from "@/types/user.types";
import { Realm, UpdateMode } from 'realm';

import { Socket } from 'socket.io-client';
import { notifEvents } from './notificationTypes';
import { ChatService } from '../service/ChatService';
import { getSocket } from './socket';
import { getRealm } from '../device-storage/realm/realm';
import { ServerMessage } from '../types/socketPayload.type';
import { MessageRepo } from '../device-storage/realm/repositories/MessageRepo';
import { ChatSpaceService } from '../service/chatSpace.service';
import { IConversation } from '../device-storage/realm/schemas/ConversationSchema';
import { useMe } from '../hooks/util/useMe';
import { Reaction } from '../device-storage/realm/schemas/MessageSchema';
import { IUser, RemoteUser } from '../types/index.types';

// export const listenNewMessage = async (
//   data: IServerMessage,
//   dispatch: AppDispatch,
//   chatsState: RootState["chats"],
//   user: User
// ) => {
//   const message = SchemaConvertor.serverMessageToLocalMessage(data);
//   await IdbChats.saveMessage(message);
//   const isOpenedConv =
//     chatsState.selectedConversation?.conv_id === data.receiver_id ||
//     chatsState.selectedConversation?.conv_id === data.sender_id;
//   if (isOpenedConv) {
//     const convId =
//       data.receiver_id === user._id ? data.sender_id : data.receiver_id;
//     dispatch(loadMessagesForConversation(convId));
//   }
// };

type AckMessagePayload = {
  sender_local_id: string | number;
  global_message_id: string;
};
export const listenAcknowledgeMessage = (data: AckMessagePayload, callback: any) => {
  callback(true);
};

export const initChatService = () => {
  const socket = getSocket();
  const realm = getRealm();
  ChatService.setRealm(realm);
  ChatService.setSocket(socket);
  registerChatSocketEvents(socket);
  console.log('ChatService initialized');
  ChatService.sendPendingMessages();
};

export type IncomingSocketPayload = {
  type: string;
  body: string;
};
const parseSocketPayload = (data: IncomingSocketPayload) => {
  return {
    ...data,
    body: JSON.parse(data.body),
  };
};

export const registerChatSocketEvents = (socket: Socket) => {
  socket.on(notifEvents.messages.incoming, (data) => {
    // console.log('[Socket] messages.incoming raw:', data);

    const parsed = parseSocketPayload(data).body;
    // console.log('[Socket] parsedMessage:', JSON.stringify(parsed, null, 2));
    if (
      parsed.messageType?.toLowerCase?.() === 'event' &&
      parsed.eventType &&
      ['chat_reaction_added', 'chat_reaction_updated', 'chat_reaction_removed'].includes(
        parsed.eventType,
      )
    ) {
      const realm = getRealm();
      if (
        parsed.eventType === 'chat_reaction_added' ||
        parsed.eventType === 'chat_reaction_updated'
      ) {
        const { messageId, reaction, reactionUserId } = parsed.eventPayload;

        const reactionId = `${messageId}_${reactionUserId}_${reaction}`;
        // console.log('About to write reaction:', reactionId);
        realm.write(() => {
          try {
            realm.create(
              'Reaction',
              {
                _id: reactionId,
                messageId,
                userId: reactionUserId,
                emoji: reaction,
                createdAt: Date.now(),
              },
              UpdateMode.Modified,
            );
            // console.log(
            //   '[Debug] Reaction table right after insert:',
            //   realm.objects('Reaction').map((r) => r.toJSON()),
            // );
          } catch (err) {
            console.error('Realm write failed:', err);
          }
        });
      } else if (parsed.eventType === 'chat_reaction_removed') {
        const { messageId, reactionUserId } = parsed.eventPayload;
        // console.log('[Reaction Removed] payload:', {
        //   messageId,
        //   reactionUserId,
        // });

        realm.write(() => {
          const existing = realm
            .objects<Reaction>('Reaction')
            .filtered('messageId == $0 && userId == $1', messageId, reactionUserId);
          realm.delete(existing);
        });
      }

      return; // prevent normal ChatService message handling
    }
    ChatService.onIncomingMessage(parsed);
  });
  socket.on('chatspace-role-updated', (data) => {
    ChatService.updateRoleofMember(data.chatSpaceId, data.newRole);
  });

  socket.on('chatspace-privileges-updated', (data) => {
    const { chatSpaceId, newPrivileges, newPrivileges1 } = data;
    ChatService.updateMemberPermissionOverride(chatSpaceId, newPrivileges, newPrivileges1);
  });

  socket.on('message:disappearDurationUpdated', (data) => {
    ChatService.disappearDuration(data.userId, data.durationMs);
  });

  socket.on('user:offline', (data) => {
    const { userId, lastActive } = data;
    ChatService.updateConversation(userId, {
      status: null,
      lastSeenAt: lastActive,
    });
  });

  socket.on('user:online', (data) => {
    const { userId, status } = data;
    ChatService.updateConversation(userId, {
      status: status,
      lastSeenAt: undefined,
    });
  });

  socket.on(notifEvents.chatSpaces.deleted, (res) => {
    const { chatSpaceId } = res;
    ChatService.deleteChatspace(chatSpaceId);
  });

  socket.on(notifEvents.chatSpaces.infoUpdated, (data) => {
    ChatService.updateChatSpace(data);
  });

  socket.on(notifEvents.messages.status.sentScheduled, (data) => {
    ChatService.ackSentScheduledMessages(parseSocketPayload(data).body);
  });

  socket.on(notifEvents.messages.status.delivered, (data) => {
    ChatService.ackDeliveredMessages(parseSocketPayload(data));
  });

  socket.on(notifEvents.messages.status.seen, (data) => {
    ChatService.ackSeenMessages(parseSocketPayload(data));
  });

  socket.on(notifEvents.messages.queued, (data) => {
    ChatService.onQueuedMessages(data);
  });

  socket.on(notifEvents.messages.liveLocationStopped, (data) => {
    console.log('--------------stopped', data);
    ChatService.updateLocationStatus(data.globalId, true, data.latitude, data.longitude);
  });

  socket.on(notifEvents.messages.status.deleted, (data) => {
    ChatService.handleMessagesDeletedEvent(data);
  });

  socket.on(notifEvents.messages.status.pinned, (data) => {
    ChatService.handlePinResponse(data);
  });

  socket.on(notifEvents.chatSpaces.joinRequestReceived, (data) => {
    ChatService.saveOrUpdateRequests({
      ...data,
      requestName: notifEvents.chatSpaces.joinRequestReceived,
    });
    ChatService.getOrCreateUser(data.requestedBy);
  });

  socket.on(notifEvents.messages.status.unpinned, (data) => {
    console.log('[SocketListener] Received unpin status:', data);
    ChatService.handleUnpinResponse(data);
  });

  socket.on(notifEvents.users.typing, (data: { userId: string; conversationId: string }) => {
    ChatService.updateTypingStatus(data.userId, data.conversationId);
  });

  socket.on(notifEvents.users.profile_update, (data: RemoteUser) => {
    ChatService.handleProfileUpdate(data);
  });

  registerChannelEvents(socket);
};

export const unregisterChatSocketEvents = (socket: Socket) => {
  socket.removeAllListeners(notifEvents.messages.incoming);
};

const chatSpacesEvents = {
  start_stream: 'channel:start_stream',
  end_stream: 'channel:end_stream',
};

function registerChannelEvents(socket: Socket) {
  socket.on(chatSpacesEvents.start_stream, (data) => {
    ChatSpaceService.updateChatSpace({
      isLiveStreaming: true,
      chatSpaceId: data.chatSpaceId,
    });
  });
  socket.on(chatSpacesEvents.end_stream, (data) => {
    ChatSpaceService.updateChatSpace({
      isLiveStreaming: false,
      chatSpaceId: data.chatSpaceId,
    });
  });
  socket.on(notifEvents.chatSpaces.deleted, async ({ chatSpaceId }) => {
    try {
      const realm = getRealm();
      const conversation = realm
        .objects<IConversation>('Conversation')
        .filtered('chatSpaceId == $0', chatSpaceId)[0];

      if (!conversation) return;

      const isOwner = conversation.role === 'owner';

      if (isOwner) {
        // Owner: delete completely
        ChatService.deleteConversation(chatSpaceId);
      } else {
        // Follower: mark as deleted in UI
        ChatService.markChannelAsDeleted(chatSpaceId);
      }
    } catch (error) {
      console.error('[chatSpace:deleted] Failed to process event:', error);
    }
  });
}
