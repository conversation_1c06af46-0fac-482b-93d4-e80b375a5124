import {
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  SafeAreaView,
  View,
  ScrollView,
  BackHandler,
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { commonFontStyle, hp } from '../../theme/fonts';
import { colors } from '../../theme/colors';
import Input from '../../component/Input';
import ButtonPurple from '../../component/ButtonPurple';
import { SCREENS } from '../../navigation/screenNames';
import { navigateTo, errorToast, VALIDATION_RULES } from '../../utils/commonFunction';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

import CommonView from '../../component/CommonView';
import HeaderBackWithTitle from '../../component/HeaderBackWithTitle';
import { FontAwesome6Icons, Ionicons } from '../../utils/vectorIcons';
import debounce from 'lodash.debounce';
import { doesUsernameExist } from '../../utils/userApiService';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import CustomAlert from '../../component/Common/CustomAlert';

const SetUsernameScreen = () => {
  const params = useRoute<RouteProp<{ params: { interimToken: string } }>>().params;
  const navigation = useNavigation();
  const allowExitRef = useRef<boolean>(false);
  const [userName, setUserName] = useState('');
  const [isValidUsername, setIsValidUsername] = useState<boolean | null>(null);
  const [isButtonActive, setIsButtonActive] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [showExitDialog, setShowExitDialog] = useState(false);
  const { t } = useTranslation();

  useFocusEffect(
    useCallback(() => {
      const backAction = () => {
        if (userName?.trim()?.length > 0) {
          setShowExitDialog(true);
          return true;
        }
        return false;
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        backHandler.remove();
      };
    }, [userName]),
  );

  // Navigates to SetProfileScreen with the input username.
  const onSubmit = () => {
    const routeData = { username: userName, ...params }; // has interim token in params data
    navigateTo(SCREENS.SetProfileScreen, { data: routeData });
  };

  const handleBackPress = () => {
    setShowExitDialog(true);
  };

  const confirmExit = () => {
    setShowExitDialog(false);
    allowExitRef.current = true;
    navigation.goBack();
  };

  const cancelExit = () => {
    setShowExitDialog(false);
  };

  // Real-time validation while typing
  const handleTextChange = (text: string) => {
    if (text.length > VALIDATION_RULES.USERNAME_LENGTH) {
      return;
    }
    setUserName(text);

    if (!text) {
      setErrorMsg(null);
      setIsValidUsername(null);
      return;
    }

    if (text.length > VALIDATION_RULES.USERNAME_LENGTH) {
      setErrorMsg(`Maximum limit for username is ${VALIDATION_RULES.USERNAME_LENGTH} characters.`);
      setIsValidUsername(false);
      return;
    }

    if (!VALIDATION_RULES.USERNAME_REGEX.test(text)) {
      setErrorMsg('Username can only contain letters, numbers, and underscores.');
      setIsValidUsername(false);
      return;
    }

    if (text.trim().length < 3) {
      setErrorMsg('Username must be at least 3 characters long.');
      setIsValidUsername(false);
      return;
    }

    // No error so far → wait for debounce check (availability)
    setErrorMsg(null);
    setIsValidUsername(true);
  };

  // Debounced availability check
  const validateUsernameAvailability = async (name: string) => {
    if (!name || errorMsg) return;

    const doesExist = await doesUsernameExist(name, params.interimToken);
    if (doesExist.exists) {
      setErrorMsg('Username already taken.');
      setIsValidUsername(false);
    } else {
      setErrorMsg(null);
      setIsValidUsername(true);
    }
    setIsButtonActive(true);
  };

  const debouncedValidateUsername = useCallback(
    debounce((name) => validateUsernameAvailability(name), 300),
    [errorMsg],
  );

  useEffect(() => {
    setIsButtonActive(false);
    debouncedValidateUsername(userName);
    return () => {
      debouncedValidateUsername.cancel();
    };
  }, [userName, debouncedValidateUsername]);

  return (
    <CommonView
      containerStyle={{ backgroundColor: colors.white }}
      headerColor={colors.mainPurple}
      headerTitle="Set username"
      customHeader={() => (
        <HeaderBackWithTitle
          title="Set username"
          onBack={handleBackPress}
          containerStyle={{ paddingHorizontal: 20 }}
        />
      )}
    >
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <SafeAreaView style={{ paddingHorizontal: hp(1) }}>
          <Input
            value={userName}
            extraStyle={styles.input}
            onChangeText={handleTextChange}
            placeHolder={t('Eg: willian_james')}
            title={t('Enter username')}
            titleColor={colors.gray_80}
            RenderRightIcon={<IsValidIcon isValidUsername={isValidUsername} />}
          />
          <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
            {errorMsg && (
              <>
                <Ionicons
                  name="information-circle-outline"
                  size={16}
                  color={colors.red_ff4444}
                  style={{ marginTop: 3 }}
                />
                <Text style={styles.errorText}>{errorMsg}</Text>
              </>
            )}
          </View>
          <ButtonPurple
            onPress={onSubmit}
            title={t('Next')}
            extraStyle={{ marginTop: hp(1) }}
            disabled={!isValidUsername || !isButtonActive}
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>

      <CustomAlert
        visible={showExitDialog}
        title="Are you sure you want to exit?"
        message="Your progress will be lost."
        confirmText="Yes, Exit"
        cancelText="Cancel"
        onConfirm={confirmExit}
        onCancel={cancelExit}
      />
    </CommonView>
  );
};

export default SetUsernameScreen;

type IsValidIconProps = {
  isValidUsername: boolean | null;
};
const IsValidIcon = (props: IsValidIconProps) => {
  const { isValidUsername } = props;
  if (isValidUsername === null) return null;
  return isValidUsername ? (
    <FontAwesome6Icons name="check" size={20} color="#4CAF50" />
  ) : (
    <FontAwesome6Icons name="xmark" size={20} color="#e41111ff" />
  );
};

const styles = StyleSheet.create({
  titleText: {
    ...commonFontStyle(700, 31, colors.white),
    textAlign: 'center',
  },
  des: {
    ...commonFontStyle(400, 16, colors.white),
    textAlign: 'center',
    marginBottom: hp(4),
  },
  input: {
    marginTop: hp(2),
    gap: 5,
  },
  notAvailableView: {
    // backgroundColor: colors._FFF4F4_red,
    borderRadius: 15,
    // padding: hp(2),
    marginBottom: hp(2),
  },
  titleRed: {
    ...commonFontStyle(500, 16, colors._DB1515_red),
  },
  titleTextAvailable: {
    ...commonFontStyle(400, 16, colors.black_23),
    marginTop: 5,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },

  nameView: {
    backgroundColor: colors.gray_f3,
    padding: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 10,
    marginTop: 10,
  },
  nameText: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  errorText: {
    ...commonFontStyle(400, 11, colors._DB1515_red),
    marginTop: 3,
  },
});
