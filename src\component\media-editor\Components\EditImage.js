import { StyleSheet, View } from 'react-native';
import React, { memo } from 'react';
import FilterImgCanvas from './FilterImgCanvas';
import GestureHandler from './GesturehandlerComponent/GestureHandler';

interface EditImagesProps {
  imagesFiles: {
    uri: string,
    id: string,
    initialX?: number,
    initialY?: number,
    rotate?: number,
    scale?: number,
  }[];
  itemDragging: (value: boolean) => boolean;
  isDeletable: (id: number, type: string, bool: boolean) => boolean;
  zIndexMap: { id: string, zIndex: number, type: 'image' }[];
  onSelectElement: (id: string) => void;
}

const EditImages = memo(
  ({ imagesFiles, itemDragging, isDeletable, zIndexMap, onSelectElement }: EditImagesProps) => {
    return (
      <View style={styles.container}>
        {imagesFiles?.map((image, index) => {
          return (
            <GestureHandler
              type={'image'}
              id={image.id}
              key={index}
              index={index}
              onSelectImage={handleSelectImage}
              zIndex={zIndexMap[index]}
              itemDragging={itemDragging}
              yPos={image.initialX || 0}
              xPos={image.initialY || 0}
              isDeletable={(id, type, bool) => isDeletable(id, type, bool)}
              rotate={image.rotate || 0}
              scale={image.scale || 1}
            >
              <FilterImgCanvas images={image} index={index} />
            </GestureHandler>
          );
        })}
      </View>
    );
  },
);

export default EditImages;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 201,
  },
  canvas: {
    width: '100%',
    height: '100%',
  },
});
