import React from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import RightSelectionRow from '../../components/RightSelectionRow';
import { navigateTo } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';
import { useMe } from '../../../../hooks/util/useMe';
import { PrivacyEnum } from '../../../../device-storage/realm/schemas/UserPrefSchema';
import { useContacts } from '../../../../hooks/contacts/useContacts';

const LastSeenScreen = () => {
  const { userPreferencesState } = useMe();
  const { userPreferences, updatePreferences } = userPreferencesState;
  const lastSeen = userPreferences?.privacy?.lastSeen;
  const exceptedContacts = lastSeen?.exceptedContacts || [];
  const { registeredContacts } = useContacts();
  const visibleIds = new Set((registeredContacts || []).map((u: any) => u?.id));
  const visibleExceptedCount = exceptedContacts.filter((id: string) => visibleIds.has(id)).length;
  const selected = lastSeen?.privacy || PrivacyEnum.EVERYONE;

  const options = [
    { key: PrivacyEnum.EVERYONE, label: 'Everyone' },
    { key: PrivacyEnum.CONTACTS, label: 'My contacts' },
    {
      key: PrivacyEnum.CONTACTS_EXCEPT,
      label: 'My contacts except',
      subtitle: visibleExceptedCount > 0 ? `${visibleExceptedCount} people` : undefined,
    },
    { key: PrivacyEnum.NOBODY, label: 'Nobody' },
  ];

  const handleSelect = async (key: string) => {
    if (key === PrivacyEnum.CONTACTS_EXCEPT) {
      navigateTo(SCREENS.ContactsExceptLastSeen);
    } else {
      const newLastSeen = {
        ...lastSeen,
        privacy: key as PrivacyEnum,
      };
      await updatePreferences('privacy', {
        lastSeen: newLastSeen,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Last seen" />
      <View style={styles.whiteContainer}>
        <View style={{ marginTop: 24 }}>
          {options.map((option) => (
            <RightSelectionRow
              key={option.key}
              label={option.label}
              subtitle={option.subtitle}
              selected={selected === option.key}
              onPress={() => handleSelect(option.key)}
            />
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
});

export default LastSeenScreen;
