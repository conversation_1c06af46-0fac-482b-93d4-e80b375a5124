import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { colors } from '../theme/colors';
import { commonFontStyle, hp } from '../theme/fonts';
import { AppStyles } from '../theme/appStyles';
import RenderUserIcon from './RenderUserIcon';
import { IMAGES } from '../assets/Images';
import { IContact } from '../device-storage/realm/schemas/ContactSchema';
import FavSVG from '../assets/svgIcons/FavSVG';
import UnFavSVG from '../assets/svgIcons/UnFavSVG';

type Props = {
  data: Partial<IContact>;
  onPress?: (contact: Partial<IContact>) => void;
  onPressFavourite?: (contact: Partial<IContact>) => void;
  isFavourite?: boolean;
};

const FavouriteItem = ({ data, onPress, onPressFavourite, isFavourite = false }: Props) => {
  const handleFavouritePress = () => {
    onPressFavourite?.(data);
  };

  return (
    <TouchableOpacity style={AppStyles.flex} onPress={handleFavouritePress}>
      <View style={styles.rowView}>
        <View>
          <RenderUserIcon url={data?.profilePic} size={50} />
        </View>
        <View style={AppStyles.flex}>
          <Text numberOfLines={1} style={styles.title}>
            {data?.contactName}
          </Text>
          <Text numberOfLines={1} style={styles.lastMessage}>
            {data?.bio}
          </Text>
        </View>
        <TouchableOpacity onPress={handleFavouritePress} style={styles.favouriteButton}>
          {isFavourite ? (
            <FavSVG size={24} color={colors.mainPurple} />
          ) : (
            <UnFavSVG size={24} color={colors.gray_80} />
          )}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default FavouriteItem;

const styles = StyleSheet.create({
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: hp(1),
    paddingVertical: hp(1),
    gap: 15,
  },
  title: {
    ...commonFontStyle(600, 16, colors.black_23),
  },
  lastMessage: {
    ...commonFontStyle(400, 14, colors.gray_80),
    marginTop: 3,
  },
  favouriteButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
