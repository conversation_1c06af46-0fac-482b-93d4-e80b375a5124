import { IMessage } from '../device-storage/realm/schemas/MessageSchema';
import { IChatScreenProps } from '../screens/Home/Chats/ChatSpecificScreen';
import { IMediaUserProps } from '../screens/Home/components/MediaPreviewScreen';

export type NavigationRouteParams = {
  ChatSpecificScreen: {
    userData: IChatScreenProps;
    isExploreScreen?: boolean;
  };
  MediaPreviewScreen: {
    msgData?: IMessage | any;
    otherUserData: IMediaUserProps;
    isFromCameraScreen?: boolean;
    handleSendMessage?: (value?: any, isMedia?: boolean) => Promise<void>;
  };
  MediaEditor: {
    initialMedia?: any;
    handleSendMessage?: (value?: any, isMedia?: boolean) => Promise<void>;
    otherUserData?: any;
    isFromMediaPreview?: boolean;
    originalMsgData?: any;
  };
  CameraScreen: {
    handleSendMessage?: (value?: any, isMedia?: boolean) => Promise<void>;
    otherUserData: IChatScreenProps;
  };
  LiveLocation: {
    userData: any;
    mapData?: any;
    isExploreScreen?: boolean;
  };
};
