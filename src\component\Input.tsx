import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  TextInput,
  View,
  ViewStyle,
  KeyboardType,
} from 'react-native';
import React, { useState } from 'react';
import { commonFontStyle, hp } from '../theme/fonts';
import { colors } from '../theme/colors';
import { IMAGES } from '../assets/Images';
import Ionicons from 'react-native-vector-icons/Ionicons';

type Props = {
  value?: any;
  onChangeText?: any;
  icon?: any;
  placeHolder?: string;
  title?: string;
  extraStyle?: ViewStyle;
  maxLength?: number;
  RenderRightIcon?: React.ReactNode;
  multiline?: boolean;
  textInputStyle?: ViewStyle;
  editable?: boolean;
  secureTextEntry?: boolean;
  iconTintColor?: any;
  inputContainer?: ViewStyle;
  textAlignVertical?: 'top' | 'center' | 'bottom';
  placeholderTextColor?: string;
  titleColor?: string;
  error?: string;
  keyboardType?: KeyboardType;
};

const Input = ({
  value,
  secureTextEntry = false,
  onChangeText,
  editable = true,
  icon,
  placeHolder,
  textInputStyle,
  RenderRightIcon,
  title,
  extraStyle,
  maxLength,
  keyboardType,
  multiline = false,
  iconTintColor = undefined,
  inputContainer,
  textAlignVertical,
  placeholderTextColor = colors._CCCCCC_gray,
  titleColor = colors.black_23,
  error,
}: Props) => {
  const [passwordHide, setpasswordHide] = useState(true);
  return (
    <View style={extraStyle}>
      {title && (
        <Text
          style={{
            ...commonFontStyle(400, 16, titleColor),
          }}
        >
          {title}
        </Text>
      )}
      <View style={{ ...styles.rowView, ...inputContainer }}>
        {icon && <Image source={icon} style={[styles.icon, { tintColor: iconTintColor }]} />}
        <TextInput
          value={value}
          onChangeText={onChangeText}
          style={[styles.inputStyle, textInputStyle]}
          maxLength={maxLength}
          placeholder={placeHolder}
          keyboardType={keyboardType}
          placeholderTextColor={placeholderTextColor}
          multiline={multiline}
          editable={editable}
          secureTextEntry={secureTextEntry ? passwordHide : false}
          textAlignVertical={textAlignVertical}
        />
        {RenderRightIcon && RenderRightIcon}
        {secureTextEntry && (
          <TouchableOpacity onPress={() => setpasswordHide(!passwordHide)}>
            <Ionicons
              name={passwordHide ? 'eye-off-outline' : 'eye-outline'}
              size={24}
              color="#232323"
            />
          </TouchableOpacity>
        )}
        {error && <Text style={{ color: 'red', marginTop: 4 }}>{error}</Text>}
      </View>
    </View>
  );
};

export default Input;

export const styles = StyleSheet.create({
  title: {
    ...commonFontStyle(400, 16, colors.black_23),
  },
  inputStyle: {
    flex: 1,
    height: 55,
    ...commonFontStyle(400, 16, colors.black_23),
  },
  rowView: {
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 15,
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  icon: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
    marginHorizontal: 10,
  },
  imageView: {
    width: 20,
    height: 55,
    resizeMode: 'contain',
    marginLeft: 12,
    tintColor: colors._CCCCCC_gray,
  },
});
