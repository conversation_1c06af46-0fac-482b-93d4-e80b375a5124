import React, { useState, useEffect } from 'react';
import { View, SafeAreaView, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import { useNavigation } from '@react-navigation/native';
import ButtonPurple from '../../../../component/ButtonPurple';
import OpacitySVG from '../../../../assets/svgIcons/OpacitySVG';
import Slider from '@react-native-community/slider';
import ImageCropPicker, { ImageOrVideo, PickerErrorCode } from 'react-native-image-crop-picker';
import ChatColorTab from './ChatColorTabs/ChatColorTab';
import ChatImageTab from './ChatColorTabs/ChatImageTab';
import { presetWallpapers } from '../../../../assets/presetWallpapers';
import { useMe } from '../../../../hooks/util/useMe';
import { uploadFiles } from '../../../../utils/ApiService';
import { errorToast } from '../../../../utils/commonFunction';
import { useTranslation } from 'react-i18next';
import {
  setAsyncCustomChatWallpaper,
  getAsyncCustomChatWallpaper,
} from '../../../../utils/asyncStorage';

type UploadResult = { url?: string; fileUrl?: string } | string;

type SelectedWallpaper =
  | { type: 'color'; value: string }
  | { type: 'wallpaper'; value: number }
  | { type: 'custom'; localImage: ImageOrVideo; remoteUrl?: string };

const ChatWallpaperScreen = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const [selected, setSelected] = useState<SelectedWallpaper>({ type: 'wallpaper', value: 0 });
  const [wallpaperOpacity, setWallpaperOpacity] = useState(1);
  const [activeTab, setActiveTab] = useState<'color' | 'image'>('image');
  const [isApplying, setIsApplying] = useState(false);
  const [customWallpaper, setCustomWallpaper] = useState<ImageOrVideo | undefined>(undefined);
  const { userPreferencesState } = useMe();

  useEffect(() => {
    (async () => {
      const url = await getAsyncCustomChatWallpaper();
      if (url) {
        setCustomWallpaper((prev) => prev || ({ path: url } as ImageOrVideo));
      }
      const chatWallpaper = userPreferencesState?.userPreferences?.chats?.chatWallpaper;
      if (chatWallpaper) {
        if (chatWallpaper.color) {
          setSelected({ type: 'color', value: chatWallpaper.color });
          setActiveTab('color');
          setWallpaperOpacity(chatWallpaper.opacity ?? 1);
        } else if (chatWallpaper.url) {
          let preset = presetWallpapers.find((w) => w.source.uri === chatWallpaper.url);
          if (preset) {
            setSelected({ type: 'wallpaper', value: preset.id });
            setActiveTab('image');
            setWallpaperOpacity(chatWallpaper.opacity ?? 1);
          } else {
            setSelected({
              type: 'custom',
              localImage: { path: chatWallpaper.url } as ImageOrVideo,
              remoteUrl: chatWallpaper.url,
            });
            setActiveTab('image');
            setWallpaperOpacity(chatWallpaper.opacity ?? 1);
            setCustomWallpaper((prev) => prev || ({ path: chatWallpaper.url } as ImageOrVideo));
          }
        }
      }
    })();
    // eslint-disable-next-line
  }, [userPreferencesState.userPreferences]);

  const previewMessage = 'Its really super cool.. Enjoy.!';
  const previewResponse = 'Wow, is that so?';

  const handleApply = async () => {
    setIsApplying(true);
    let chatWallpaper;
    try {
      if (selected.type === 'color') {
        chatWallpaper = { color: selected.value, opacity: wallpaperOpacity };
      } else if (selected.type === 'wallpaper') {
        const preset = presetWallpapers.find((w) => w.id === selected.value);
        if (preset) chatWallpaper = { url: preset.source.uri, opacity: wallpaperOpacity };
      } else if (selected.type === 'custom') {
        let remoteUrl = selected.remoteUrl;
        let localImage = selected.localImage;
        const isRemote =
          typeof localImage.path === 'string' &&
          (localImage.path.startsWith('http://') || localImage.path.startsWith('https://'));
        if (!remoteUrl && !isRemote) {
          const uploadResult: UploadResult = await uploadFiles(localImage);
          remoteUrl =
            typeof uploadResult === 'string'
              ? uploadResult
              : uploadResult?.url || uploadResult?.fileUrl || '';
        } else if (!remoteUrl && isRemote) {
          remoteUrl = localImage.path;
        } else if (remoteUrl) {
        }
        chatWallpaper = { url: remoteUrl, opacity: wallpaperOpacity };
        setCustomWallpaper({ path: remoteUrl } as ImageOrVideo);
        setSelected({ type: 'custom', localImage: { path: remoteUrl } as ImageOrVideo, remoteUrl });
        if (remoteUrl) {
          await setAsyncCustomChatWallpaper(remoteUrl);
        } else {
        }
      }
      if (chatWallpaper) {
        await userPreferencesState.updatePreferences('chats', { chatWallpaper });
      }
      navigation.goBack();
    } catch (error) {
      errorToast(t('Failed to apply wallpaper'));
    } finally {
      setIsApplying(false);
    }
  };

  const handleAddCustomWallpaper = async () => {
    try {
      const image = await ImageCropPicker.openPicker({
        width: 400,
        height: 600,
        cropping: true,
        mediaType: 'photo',
      });
      if (image && image.path) {
        setCustomWallpaper(image);
        setSelected({ type: 'custom', localImage: image });
        setActiveTab('image');
      }
    } catch (error) {
      if (error?.code && error?.code === 'E_PICKER_CANCELLED') {
        return;
      }

      errorToast(t('Failed to add custom wallpaper'));
    }
  };

  const customWallpapers = customWallpaper
    ? [{ id: -1, source: { uri: customWallpaper.path }, localImage: customWallpaper }]
    : [];

  const getSelectedCustomId = () => {
    if (selected.type === 'custom') {
      return -1;
    }
    return undefined;
  };

  const renderPreview = () => {
    if (selected.type === 'color') {
      return (
        <View style={styles.previewContainer}>
          <View
            style={[
              StyleSheet.absoluteFill,
              { backgroundColor: selected.value, opacity: wallpaperOpacity },
            ]}
          />
          <View style={styles.chatPreview}>
            <View style={styles.messageBubble}>
              <View
                style={[
                  styles.innerView,
                  {
                    borderLeftColor: colors.mainPurple,
                    backgroundColor: colors.white,
                  },
                ]}
              >
                <Text style={[styles.senderName, { color: colors.mainPurple }]}>John</Text>
                <Text style={styles.replyText}>{previewMessage}</Text>
              </View>
              <Text style={styles.messageText}>{previewResponse}</Text>
            </View>
          </View>
        </View>
      );
    } else if (selected.type === 'wallpaper') {
      let wallpaperObj = presetWallpapers.find((w) => w.id === selected.value);
      return (
        <View style={styles.previewContainer}>
          <Image
            source={wallpaperObj ? wallpaperObj.source : presetWallpapers[0].source}
            style={[styles.backgroundImage, { opacity: wallpaperOpacity }]}
            resizeMode="cover"
          />
          <View style={styles.chatPreview}>
            <View style={styles.messageBubble}>
              <View
                style={[
                  styles.innerView,
                  {
                    borderLeftColor: colors.mainPurple,
                    backgroundColor: colors.white,
                  },
                ]}
              >
                <Text style={[styles.senderName, { color: colors.mainPurple }]}>John</Text>
                <Text style={styles.replyText}>{previewMessage}</Text>
              </View>
              <Text style={styles.messageText}>{previewResponse}</Text>
            </View>
          </View>
        </View>
      );
    } else if (selected.type === 'custom') {
      const imageUri = selected.remoteUrl || selected.localImage.path;
      return (
        <View style={styles.previewContainer}>
          <Image
            source={{ uri: imageUri }}
            style={[styles.backgroundImage, { opacity: wallpaperOpacity }]}
            resizeMode="cover"
          />
          <View style={styles.chatPreview}>
            <View style={styles.messageBubble}>
              <View
                style={[
                  styles.innerView,
                  {
                    borderLeftColor: colors.mainPurple,
                    backgroundColor: colors.white,
                  },
                ]}
              >
                <Text style={[styles.senderName, { color: colors.mainPurple }]}>John</Text>
                <Text style={styles.replyText}>{previewMessage}</Text>
              </View>
              <Text style={styles.messageText}>{previewResponse}</Text>
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <View style={styles.previewContainer}>
          <View style={styles.chatPreview}>
            <View style={styles.messageBubble}>
              <View
                style={[
                  styles.innerView,
                  {
                    borderLeftColor: colors.mainPurple,
                    backgroundColor: colors.white,
                  },
                ]}
              >
                <Text style={[styles.senderName, { color: colors.mainPurple }]}>John</Text>
                <Text style={styles.replyText}>{previewMessage}</Text>
              </View>
              <Text style={styles.messageText}>{previewResponse}</Text>
            </View>
          </View>
        </View>
      );
    }
  };

  const applyButtonStyle = isApplying
    ? [styles.applyButtonStyle, { backgroundColor: colors._EFEBFC_purple }]
    : styles.applyButtonStyle;

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Chat wallpaper" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        {renderPreview()}
        <View style={styles.controlsContainer}>
          <OpacitySVG size={35} />
          <Slider
            style={styles.slider}
            minimumValue={0.2}
            maximumValue={1}
            step={0.01}
            value={wallpaperOpacity}
            onValueChange={setWallpaperOpacity}
            minimumTrackTintColor={colors.mainPurple}
            maximumTrackTintColor={colors._E7E7E7_gray}
            thumbTintColor={colors.mainPurple}
          />
        </View>
        <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
          <TouchableOpacity
            style={{
              paddingHorizontal: 24,
              paddingVertical: 8,
              borderBottomWidth: activeTab === 'color' ? 2 : 0,
              borderBottomColor: colors.mainPurple,
              marginRight: 20,
            }}
            onPress={() => setActiveTab('color')}
            disabled={isApplying}
          >
            <Text
              style={{
                color: activeTab === 'color' ? colors.mainPurple : colors.gray_80,
                fontWeight: '600',
              }}
            >
              Color
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              paddingHorizontal: 24,
              paddingVertical: 8,
              borderBottomWidth: activeTab === 'image' ? 2 : 0,
              borderBottomColor: colors.mainPurple,
            }}
            onPress={() => setActiveTab('image')}
            disabled={isApplying}
          >
            <Text
              style={{
                color: activeTab === 'image' ? colors.mainPurple : colors.gray_80,
                fontWeight: '600',
              }}
            >
              Image
            </Text>
          </TouchableOpacity>
        </View>
        {activeTab === 'color' ? (
          <ChatColorTab
            selectedColor={selected.type === 'color' ? selected.value : ''}
            onColorSelect={(color) => setSelected({ type: 'color', value: color })}
          />
        ) : (
          <ChatImageTab
            selectedImageId={selected.type === 'wallpaper' ? selected.value : undefined}
            selectedCustomId={getSelectedCustomId()}
            onImageSelect={(id) => setSelected({ type: 'wallpaper', value: id })}
            onCustomImageSelect={(customWallpaper) => {
              const isRemote =
                typeof customWallpaper.localImage?.path === 'string' &&
                (customWallpaper.localImage.path.startsWith('http://') ||
                  customWallpaper.localImage.path.startsWith('https://'));
              setSelected({
                type: 'custom',
                localImage: customWallpaper.localImage!,
                remoteUrl: isRemote ? customWallpaper.localImage.path : undefined,
              });
            }}
            onAddCustomImage={handleAddCustomWallpaper}
            customWallpapers={customWallpapers}
          />
        )}
        <View style={styles.applyButtonContainer}>
          <ButtonPurple
            title="Apply"
            onPress={handleApply}
            extraStyle={applyButtonStyle}
            disabled={isApplying}
            isLoading={isApplying}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: hp(2),
    marginTop: 8,
  },
  previewContainer: {
    borderWidth: 1,
    borderColor: colors.gray_f3,
    height: 300,
    marginHorizontal: 15,
    marginTop: 10,
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  chatPreview: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 20,
    justifyContent: 'center',
  },
  messageText: {
    fontSize: 14,
    color: colors.black_23,
    lineHeight: 18,
  },
  slider: {
    width: 260,
    height: 30,
    marginLeft: -20,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 20,
    backgroundColor: colors.white,
    borderRadius: 25,
    marginHorizontal: 15,
    marginTop: 15,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray_f3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.black_23,
    marginBottom: 15,
  },
  wallpaperItemSpacing: {
    marginRight: 18,
  },
  selectedWallpaperItem: {
    borderColor: colors.mainPurple,
  },
  applyButtonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  applyButtonStyle: {
    marginTop: 15,
  },
  messageBubble: {
    backgroundColor: colors._EFEBFC_purple,
    padding: 10,
    borderTopRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopLeftRadius: 16,
    maxWidth: '75%',
    alignSelf: 'flex-end',
    minWidth: '20%',
  },
  innerView: {
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderLeftWidth: 4,
    marginBottom: 10,
  },
  senderName: {
    fontWeight: '600',
    fontSize: 14,
  },
  replyText: {
    fontSize: 13,
    color: colors.black_23,
  },
  wallpaperItemLarge: {
    width: 90,
    height: 110,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent',
    backgroundColor: colors.gray_f3,
    marginVertical: 4,
  },
  wallpaperThumbnailLarge: {
    width: '100%',
    height: '100%',
    borderRadius: 18,
  },
});

export default ChatWallpaperScreen;
