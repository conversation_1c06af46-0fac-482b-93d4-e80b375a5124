import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';
import { colors } from '../../../../theme/colors';
import { useFetchMessages } from '../../../../hooks/chats/messages/useFetchMessages';
import { useMemo } from 'react';

type ConversationSharedFilesProps = {
  conversationId: string;
};

const ConversationSharedFiles: React.FC<ConversationSharedFilesProps> = ({ conversationId }) => {
  const { messages } = useFetchMessages(conversationId);
  const sharedImages = useMemo(() => {
    return messages.filter((msg) => msg.messageType === 'image' && !!msg.mediaUrl);
  }, [messages]);

   if (sharedImages.length === 0) {
     return null;
   }

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Shared files</Text>
      {sharedImages?.length === 0 ? (
        <Text style={styles.noDataText}>No shared images yet</Text>
      ) : (
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.imagesRow}>
            {sharedImages.map((msg) => (
              <View key={msg.localId} style={styles.sharedImageContainer}>
                <Image source={{ uri: msg.mediaUrl }} style={styles.sharedImage} />
              </View>
            ))}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

export default ConversationSharedFiles;

const styles = StyleSheet.create({
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: colors.black_23,
  },
  noDataText: {
    color: colors.gray_80,
    fontSize: 14,
    paddingTop: 8,
  },
  imagesRow: {
    flexDirection: 'row',
  },
  sharedImageContainer: {
    marginRight: 10,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sharedImage: {
    width: 90,
    height: 90,
    borderRadius: 12,
  },
});
