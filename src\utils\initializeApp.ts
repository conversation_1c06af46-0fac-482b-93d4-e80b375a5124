import AsyncStorage from '@react-native-async-storage/async-storage';
import { ChatService } from '../service/ChatService';

import { getRealm } from '../device-storage/realm/realm';
import { getBlockedUsers } from './userApiService';
import { Client } from '../lib/Client';

import { ChatSpaceRepo } from '../device-storage/realm/repositories/ChatSpaceRepo';
import { getChatspaces } from '../service/ChatSpacesService';
import { ChatSpacesResponse } from '../types/chatSpace.types';

const FIRST_INSTALL_KEY = 'app_first_install';
const LAST_OPEN_TIMESTAMP_KEY = 'last_open_timestamp';

let appInitialized = false;

const initializeApp = async () => {
  const accessToken = await Client.AuthToken.get();
  if (!accessToken) return;
  if (appInitialized) return;
  appInitialized = true;
  const isFirstInstall = await AsyncStorage.getItem(FIRST_INSTALL_KEY);
  if (!isFirstInstall) {
    await AsyncStorage.setItem(FIRST_INSTALL_KEY, 'true');
    handleFirstTimeSetup();
  }

  await handleEveryAppOpen();
};

const handleFirstTimeSetup = async () => {
  initializeChatspaces();
  initiailizeUsers();
};

const handleEveryAppOpen = async () => {
  const now = Date.now().toString();
  await AsyncStorage.setItem(LAST_OPEN_TIMESTAMP_KEY, now);
};

export const initializeChatspaces = async () => {
  try {
    const _realm = getRealm();
    ChatService.setRealm(_realm);
    const isFetchedConversation = await AsyncStorage.getItem('isFetchedConversation');

    if (!isFetchedConversation) {
      const { channels, groups } = await getChatspaces();
      const chatspaces = [...(channels ?? []), ...(groups ?? [])];

      if (chatspaces.length > 0) {
        const createChatspaces = ChatSpaceRepo.syncChatSpacesAndConversations(_realm, {
          channels,
          groups,
        } as ChatSpacesResponse);

        // for (const chatspace of createChatspaces) {
        //   const conversationData = getConversationDataFromLocalChatSpace(chatspace);
          
        //   const result = ChatService.createChatspaceBulk(conversationData);          
        // }
      }

      // const groupIds = chatspaces
      //   .filter((space) => space.type === 'group')
      //   .map((space) => space.chatSpaceId);

      // let privilegesMap = new Map();

      // if (groupIds.length > 0) {
      //   const privilegesResponses = await getMyPrivilegesForAll(groupIds);

      //   if (Array.isArray(privilegesResponses)) {
      //     privilegesMap = new Map(privilegesResponses.map((p) => [p.chatSpaceId, p]));
      //   }
      // }

      // const conversations: ChatSpace[] = [];
      // for (const space of chatspaces) {
      //   let groupMemberOverrides = {};

      //   const privilegeData = privilegesMap.get(space.chatSpaceId);

      //   if (privilegeData) {
      //     const { role, privileges } = privilegeData;

      //     if (role === 'owner') {
      //       groupMemberOverrides = {
      //         member: privileges.memberPrivileges,
      //         admin: privileges.adminPrivileges,
      //       };
      //     } else if (role === 'admin') {
      //       groupMemberOverrides = {
      //         admin: privileges,
      //       };
      //     } else if (role === 'member') {
      //       groupMemberOverrides = {
      //         member: privileges,
      //       };
      //     }
      //   }

      //   const conversationData = {
      //     ...space,
      //     // groupMemberOverrides,
      //     lastMessageTimestamp: Date.now(),
      //   };

      //   conversations.push(conversationData);
      // }

      // ChatService.createChatspaceBulk(conversations);
      await AsyncStorage.setItem('isFetchedConversation', 'true');
    }
  } catch (error) {
    console.error('Error fetching/storing chatspaces:', error);
  }
};

export { initializeApp, initiailizeUsers };

const initiailizeUsers = async () => {
  const blockedUsers: string[] = await getBlockedUsers();
  ChatService.addBulkUsers(blockedUsers).then(() => {
    ChatService.blockUsers(blockedUsers);
  });
};
