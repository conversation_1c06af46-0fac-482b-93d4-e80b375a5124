import React, { useEffect, useRef, useState } from 'react';
import { Animated, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Popover, { PopoverPlacement } from 'react-native-popover-view';
import FilledPlusSVG from '../../../assets/svgIcons/FilledPlusSVG';
import EmojiModal from 'react-native-emoji-modal';
import ModalWrapper from '../../../component/ModalWrapper';
import CopyIconSVG from '../../../assets/svgIcons/CopyIconSVG';
import EditIconSVG from '../../../assets/svgIcons/EditIconSVG';
import { colors } from '../../../theme/colors';

interface AnimatedReactionPopupProps {
  visible: boolean;
  anchorRef: React.RefObject<any>;
  onEmojiSelect: (emoji: string) => void;
  onClose: () => void;
  onEdit?: () => void;
  onCopy?: () => void;
}

const RECENT_EMOJIS = ['❤️', '😂', '🔥', '👍'];

const AnimatedReactionPopup: React.FC<AnimatedReactionPopupProps> = ({
  visible,
  anchorRef,
  onEmojiSelect,
  onClose,
  onEdit,
  onCopy,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const [fullPickerVisible, setFullPickerVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } else {
      scaleAnim.setValue(0);
    }
  }, [visible]);

  const handleEmojiPick = (emoji: string | null) => {
    setFullPickerVisible(true);
    if (emoji) {
      onEmojiSelect(emoji);
      setFullPickerVisible(false);
      onClose();
    }
  };

  return (
    <>
      <Popover
        isVisible={visible}
        from={anchorRef}
        onRequestClose={onClose}
        placement={PopoverPlacement.TOP}
        backgroundStyle={{ backgroundColor: 'transparent' }}
        popoverStyle={{ backgroundColor: 'transparent', elevation: 0 }}
      >
        <View style={{ display: 'flex', flexDirection: 'row' }}>
          <Animated.View
            style={[
              styles.popupContainer,
              {
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            {RECENT_EMOJIS.map((emoji) => (
              <TouchableOpacity
                key={emoji}
                onPress={() => {
                  onEmojiSelect(emoji);
                  onClose();
                }}
                style={styles.emojiButton}
              >
                <Text style={styles.emojiText}>{emoji}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              onPress={() => {
                setFullPickerVisible(true);
              }}
              style={styles.emojiButton}
            >
              <FilledPlusSVG size={24} />
            </TouchableOpacity>
          </Animated.View>
          <View style={styles.actionsRow}>
            {onEdit && (
              <TouchableOpacity
                style={styles.circleIcon}
                onPress={() => {
                  onEdit?.();
                  onClose();
                }}
              >
                <EditIconSVG color={colors.black_23} size={16} />
              </TouchableOpacity>
            )}
            {onCopy && (
              <TouchableOpacity
                style={styles.circleIcon}
                onPress={() => {
                  onCopy?.();
                  onClose();
                }}
              >
                <CopyIconSVG color={colors.black_23} size={16} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Popover>

      <ModalWrapper isVisible={fullPickerVisible} onCloseModal={() => setFullPickerVisible(false)}>
        <View style={{ height: 300 }}>
          <EmojiModal
            onEmojiSelected={handleEmojiPick}
            onPressOutside={() => setFullPickerVisible(false)}
            emojiSize={28}
            backgroundStyle={{ backgroundColor: '#ffffff' }}
            modalStyle={{ backgroundColor: '#ffffff', borderRadius: 20, flex: 1 }}
          />
        </View>
      </ModalWrapper>
    </>
  );
};

const styles = StyleSheet.create({
  popupContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 8,
    borderRadius: 30,
    elevation: 5,

    alignItems: 'center',
  },
  emojiButton: {
    paddingHorizontal: 4,
  },
  emojiText: {
    fontSize: 20,
  },
  emojiRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 4, // creates the gap
  },
  circleIcon: {
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 10,
    marginHorizontal: 4,
    elevation: 2,
  },
});

export default AnimatedReactionPopup;
