import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { IMAGES } from '../assets/Images';
import { commonFontStyle, SCREEN_WIDTH } from '../theme/fonts';
import { SCREENS } from '../navigation/screenNames';
import { colors } from '../theme/colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import RenderUserIcon from './RenderUserIcon';
import { useAnimatedKeyboard } from 'react-native-reanimated';

import { useTranslation } from 'react-i18next';
import { useMe } from '../hooks/util/useMe';
import HomeSvg from '../assets/svgIcons/HomeSvg';
import ContactsSVG from '../assets/svgIcons/ContactTabSVG';
import CallSVG from '../assets/svgIcons/CallTabSVG';

const MyTabbar = ({ state, descriptors, navigation }: any) => {
  const insets = useSafeAreaInsets();
  useAnimatedKeyboard({
    isStatusBarTranslucentAndroid: true,
  });
  const keyboardVisible = false;

  const { user: me } = useMe();

  const { t } = useTranslation();
  if (state.index !== 3 && !keyboardVisible) {
    return (
      <View style={styles.container}>
        <View style={[styles.rowStyle, { paddingBottom: insets.bottom > 0 ? insets.bottom : 2 }]}>
          {state.routes.map((route: any, index: any) => {
            // console.log(route.name, 'route.name');
            const { options } = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;
            const isFocused = state.index === index;
            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name, route.params);
              }
            };
            let IconComponent;
            if (route.name === 'HomeScreenTab') {
              IconComponent = HomeSvg;
            } else if (route.name === SCREENS.ContactsScreen) {
              IconComponent = ContactsSVG;
            } else if (route.name === SCREENS.CallScreen) {
              IconComponent = CallSVG;
            }
            const onLongPress = () => {
              navigation.emit({ type: 'tabLongPress', target: route.key });
            };
            if (route.name === SCREENS.ProfileScreen) {
              return (
                <TouchableOpacity
                  key={index}
                  accessibilityRole="button"
                  accessibilityState={isFocused ? { selected: true } : {}}
                  accessibilityLabel={options.tabBarAccessibilityLabel}
                  testID={options.tabBarTestID}
                  onPress={onPress}
                  onLongPress={onLongPress}
                  style={styles.itemContainer}
                >
                  <RenderUserIcon size={35} url={me?.image ? me?.image : IMAGES.profile_image} />
                </TouchableOpacity>
              );
            }
            return (
              <TouchableOpacity
                key={index}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={styles.itemContainer}
              >
                {IconComponent && (
                  <IconComponent
                    width={20}
                    height={23}
                    color={isFocused ? colors.mainPurple : colors.gray_80}
                  />
                )}
                <Text
                  style={{
                    ...styles.labelTextStyle,
                    color: isFocused ? colors.mainPurple : colors.gray_80,
                  }}
                >
                  {t(label)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  } else {
    return <></>;
  }
};

export default MyTabbar;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    backgroundColor: 'transparent',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 999,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 50 },
    shadowOpacity: 1,
    shadowRadius: 30,
    elevation: 24,
  },
  rowStyle: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingTop: 5,
    width: SCREEN_WIDTH,
    shadowColor: colors.black,
    elevation: 40,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.white,
    shadowOffset: { width: 0, height: -25 },
    shadowOpacity: 1,
    shadowRadius: 25,
    borderTopWidth: 0.1,
    borderLeftWidth: 0.1,
    borderRightWidth: 0.1,
    borderTopColor: colors.gray_80,
    borderLeftColor: colors.gray_80,
    borderRightColor: colors.gray_80,
  },
  itemContainer: {
    flex: 1,
    alignItems: 'center',
    height: 65,
    justifyContent: 'center',
  },
  iconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
  labelTextStyle: {
    ...commonFontStyle(500, 15, colors.gray_80),
    marginTop: 3,
  },
});
