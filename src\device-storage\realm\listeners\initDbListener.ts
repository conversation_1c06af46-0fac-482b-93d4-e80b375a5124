import { Realm } from 'realm';
import { getRealm } from '../realm';
import { IMessage, MessageStatus } from '../schemas/MessageSchema';
import { realmSchemaNames } from '../schemas/schemaNames';
import { getSocket, initSocket } from '../../../socket-client/socket';
import { Socket } from 'socket.io-client';
import { ChatSocket, NewMessagePayload } from '../../../socket-client/ChatSocket';
import { safeRealmWrite } from '../lib';

let isDbListenerInitialized = false;

export const initDbListeners = () => {
  if (isDbListenerInitialized) {
    console.log('Db listeners already initialized');
    return;
  }
  const realm = getRealm();
  const socket = getSocket();
  newMessageListener(realm, socket);
  isDbListenerInitialized = true;
};

export const newMessageListener = (realm: Realm, socket: Socket) => {
  const newMessages = realm
    .objects(realmSchemaNames.message)
    .filtered(`status = $0`, MessageStatus.PENDING);
  newMessages.addListener((collection, changes) => {
    const pendingMessages = collection.toJSON() as unknown as IMessage[];
    const messagesToSend: NewMessagePayload[] = pendingMessages.map((msg) => ({
      ...msg,
      senderLocalId: msg.localId,
    }));
    if (!socket.connected) {
      socket.connect();
      return;
    }

    // !!Make sure you dont use realm.write in a any of the inside functions!!
    safeRealmWrite(realm, () => {
      messagesToSend.forEach((message) => {
        console.log(message, '>>>>>>>>>>>>> help message');
        ChatSocket.emitNewMessage(socket, message);
      });
    });
  });
};
