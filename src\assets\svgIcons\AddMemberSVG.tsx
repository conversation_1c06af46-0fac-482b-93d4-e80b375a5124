import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const AddMemberSVG: React.FC<IconProps> = ({
  size = 17, 
  color = "#232323",
  ...restProps
}) => {
  const width = (size * 16) / 17; 

  return (
    <Svg
      width={width}
      height={size}
      viewBox="0 0 16 17"
      fill="none"
      {...restProps}
    >
      <Path
        d="M7.62 0C4.993 0 2.844 2.15 2.844 4.778c0 1.606.806 3.028 2.03 3.894C2.024 9.815 0 12.604 0 15.858c-.023 1.026 1.528 1.026 1.505 0a6.226 6.226 0 016.238-6.237 6.2 6.2 0 013.277.933c.857.525 1.644-.759.788-1.284a7.733 7.733 0 00-1.373-.651 4.766 4.766 0 001.957-3.841C12.392 2.15 10.247 0 7.62 0zm0 1.506a3.258 3.258 0 013.267 3.272A3.253 3.253 0 017.62 8.043a3.257 3.257 0 01-3.271-3.265 3.262 3.262 0 013.27-3.272zm4.616 12.803v1.505c.021.981 1.483.981 1.505 0v-1.505h1.506c1.004 0 1.004-1.506 0-1.506H13.74v-1.505a.753.753 0 10-1.505 0v1.505h-1.541c-1.04.048-.968 1.554.036 1.506h1.505z"
        fill={color}
      />
    </Svg>
  );
};

export default AddMemberSVG;
