import React from 'react';
import { View, SafeAreaView, StyleSheet } from 'react-native';
import HeaderBackWithTitle from '../../../../component/HeaderBackWithTitle';
import { colors } from '../../../../theme/colors';
import RightSelectionRow from '../../components/RightSelectionRow';
import { navigateTo } from '../../../../utils/commonFunction';
import { SCREENS } from '../../../../navigation/screenNames';
import { useMe } from '../../../../hooks/util/useMe';
import { PrivacyEnum } from '../../../../device-storage/realm/schemas/UserPrefSchema';

const PhoneNumberScreen = () => {
  const { userPreferencesState } = useMe();
  const { userPreferences, updatePreferences } = userPreferencesState;
  const phoneNumber = userPreferences?.privacy?.phoneNumber;
  const selected = phoneNumber?.privacy || PrivacyEnum.EVERYONE;
  const exceptedContacts = phoneNumber?.exceptedContacts || [];

  const options = [
    { key: PrivacyEnum.EVERYONE, label: 'Everyone' },
    { key: PrivacyEnum.CONTACTS, label: 'My contacts' },
    {
      key: PrivacyEnum.CONTACTS_EXCEPT,
      label: 'My contacts except',
      subtitle: exceptedContacts.length > 0 ? `${exceptedContacts.length} people` : undefined,
    },
    { key: PrivacyEnum.NOBODY, label: 'Nobody' },
  ];

  const handleSelect = async (key: string) => {
    if (key === PrivacyEnum.CONTACTS_EXCEPT) {
      navigateTo(SCREENS.ContactsExceptPhoneNumber);
    } else {
      // Only update privacy, do NOT clear exceptedContacts in local state
      const newPhoneNumber = {
        ...phoneNumber,
        privacy: key as PrivacyEnum,
        // exceptedContacts: phoneNumber?.exceptedContacts || [], // keep the old list!
      };
      console.log('[DEBUG] Updating phoneNumber for privacy option:', newPhoneNumber);
      await updatePreferences('privacy', {
        phoneNumber: newPhoneNumber,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="Phone number" />
      <View style={styles.whiteContainer}>
        <View style={{ marginTop: 24 }}>
          {options.map((option) => (
            <RightSelectionRow
              key={option.key}
              label={option.label}
              subtitle={option.subtitle}
              selected={selected === option.key}
              onPress={() => handleSelect(option.key)}
            />
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 8,
  },
});

export default PhoneNumberScreen;
