import { StatusBar, Text, View, TouchableOpacity } from 'react-native';
import { colors } from '../../theme/colors';
import { SafeAreaView } from 'react-native-safe-area-context';
import { navigateTo } from '../../utils/commonFunction';

const BlindHomeScreen = () => {
  return (
    <>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar backgroundColor={colors.white} barStyle="dark-content" />

        <View
          style={{
            backgroundColor: 'white',
            flex: 1,
          }}
        >
          <TouchableOpacity
            onPress={() => navigateTo('BCallScreen')}
            style={{
              padding: 10,
              backgroundColor: colors.mainPurple,
              borderRadius: 10,
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              margin: 20,
            }}
          >
            <Text style={{ color: colors.white, fontSize: 60, textAlign: 'center' }}>
              Call a volunteer
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </>
  );
};

export default BlindHomeScreen;
