import RNFS from 'react-native-fs';
import { requestStoragePermission } from './usePermission';
import { successToast, errorToast } from './commonFunction';

export interface SaveToGalleryOptions {
  showToast?: boolean;
}

export const saveToGallery = async (
  mediaUrl: string,
  fileName: string,
  mediaType: 'image' | 'video',
  options: SaveToGalleryOptions = {},
): Promise<boolean> => {
  const { showToast = true } = options;

  try {
    const hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      if (showToast) {
        errorToast('Storage permission is required to save media');
      }
      return false;
    }

    const getDownloadDest = (fileName: string) => {
      return `${RNFS.DownloadDirectoryPath}/${fileName}`;
    };

    const finalFileName = fileName;

    const localPath = getDownloadDest(finalFileName);

    const downloadResult = await RNFS.downloadFile({
      fromUrl: mediaUrl,
      toFile: localPath,
    }).promise;

    if (downloadResult.statusCode === 200) {
      if (showToast) {
        const mediaTypeText = mediaType === 'image' ? 'Image' : 'Video';
        successToast(`${mediaTypeText} downloaded successfully`);
      }
      return true;
    } else {
      if (showToast) {
        errorToast('Failed to download media');
      }
      return false;
    }
  } catch (error) {
    if (showToast) {
      errorToast('Failed to download media');
    }
    return false;
  }
};
