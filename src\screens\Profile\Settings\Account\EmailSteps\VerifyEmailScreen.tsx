import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { colors } from '../../../../../theme/colors';
import HeaderBackWithTitle from '../../../../../component/HeaderBackWithTitle';
import { useNavigation, useRoute } from '@react-navigation/native';
import { CButton } from '../../../../../common';
import { hp } from '../../../../../theme/fonts';
import { SCREENS } from '../../../../../navigation/screenNames';
import { navigationRef } from '../../../../../navigation/RootContainer';
import PinInputField from '../../../../../component/Common/PinInputField';

function maskEmail(email: any) {
  if (!email) return '';
  const [name, domain] = email.split('@');
  if (!name || !domain) return email;
  const maskedName =
    name.length > 2 ? name.slice(0, 2) + '*'.repeat(name.length - 2) : name[0] + '*';
  return `${maskedName}@${domain}`;
}

const VerifyEmailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const email = route.params?.email || '';
  const [otp, setOtp] = useState(['', '', '', '']);

  const handleOtpChange = (newOtp: string[]) => {
    setOtp(newOtp);
  };

  const handleSubmit = () => {
    navigationRef.current?.navigate(SCREENS.HomeScreen, { screen: SCREENS.ProfileScreen });
  };

  const handleResend = () => {
    // Placeholder for resend logic
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.mainPurple }}>
      <HeaderBackWithTitle title="Add email" />
      <View style={styles.whiteContainer}>
        <Text style={styles.title}>Enter OTP</Text>
        <Text style={styles.subtitle}>OTP has been sent to {maskEmail(email)}</Text>
        <View style={styles.otpRow}>
          <PinInputField
            length={4}
            value={otp}
            onChange={handleOtpChange}
            autoFocus={true}
            containerStyle={styles.pinContainer}
            inputStyle={styles.otpBox}
            focusedStyle={styles.otpBoxFocused}
          />
        </View>
        <CButton onPress={handleSubmit} cStyle={styles.button}>
          <Text style={styles.buttonText}>Submit</Text>
        </CButton>
        <Text style={styles.resendText}>
          Didn't receive OTP?{' '}
          <Text style={styles.resendLink} onPress={handleResend}>
            Resend
          </Text>
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
    color: colors.black_23,
    marginBottom: 8,
    marginTop: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: colors.black_23,
    marginBottom: 25,
    textAlign: 'center',
  },
  otpRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    marginTop: 4,
  },
  pinContainer: {
    justifyContent: 'center',
  },
  otpBox: {
    width: 60,
    height: 54,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray_f3,
    backgroundColor: '#fff',
    fontSize: 22,
    color: colors.mainPurple,
    marginHorizontal: 16,
  },
  otpBoxFocused: {
    borderColor: colors.mainPurple,
  },
  button: {
    backgroundColor: colors.mainPurple,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    width: '109%',
    marginBottom: 16,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  resendText: {
    color: colors.black_23,
    fontSize: 14,
    textAlign: 'center',
  },
  resendLink: {
    color: colors.mainPurple,
    fontWeight: '500',
  },
});

export default VerifyEmailScreen;
