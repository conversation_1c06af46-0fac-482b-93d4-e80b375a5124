import Geolocation from 'react-native-geolocation-service';
import { Alert, Linking, PermissionsAndroid, Platform } from 'react-native';
import {

  promptForEnableLocationIfNeeded,
} from 'react-native-android-location-enabler';

export const requestLocationPermission = async (
  onSuccess: (res: any) => void,
  onFail: (err: any) => void,
) => {
  if (Platform.OS === 'ios') {
    getCurrentPosition(
      (data) => {
        if (onSuccess) onSuccess(data);
      },
      (error) => {
        if (onFail) onFail(error);
        // _openAppSetting();
      },
    );
  } else {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        // @ts-ignore
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        locationEnabler(
          (isEnabled) => {
            if (isEnabled) {
              getCurrentPosition(
                (data) => {
                  if (onSuccess) onSuccess(data);
                },
                (error) => {
                  if (onFail) onFail(error);
                },
              );
            }
          },
          (err) => {
            if (onFail) onFail(err);
            locationOffModal();
          },
        );
      } else {
        if (onFail) onFail(true);
      }
    } catch (err) {
      console.warn(err);
    }
  }
};

const getCurrentPosition = async (onSuccess: (res: any) => void, onFail: (err: any) => void) => {
  Geolocation.getCurrentPosition(
    async (pos) => {
      const crd = pos.coords;
      let position = {
        latitude: crd.latitude,
        longitude: crd.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      if (onSuccess) onSuccess(position);
    },
    (error) => {
      if (onFail) onFail(error);
    },
    {
      enableHighAccuracy: true,
      timeout: 60000,
      maximumAge: 10000,
    },
  );
};

export const locationOffModal = () => {
  Alert.alert('Location Permission', 'Please turn on location services', [
    {
      text: 'Ok',
      onPress: () => {
        locationEnabler();
      },
    },
  ]);
};

export const _openAppSetting = () => {
  Alert.alert('Location Permission', 'Please allow app to access your location', [
    {
      text: 'Setting',
      onPress: () => Linking.openSettings(),
    },
    {
      text: 'cancel',
      onPress: () => console.log('Cancel Pressed'),
      style: 'cancel',
    },
  ]);
};


export const requestLocationPer = async (onSuccess: any, onFail: any) => {
  if (Platform.OS === 'ios') {
    Geolocation.requestAuthorization('whenInUse')
      .then(() => {
        getCurrentPosition(
          (data) => {
            if (onSuccess) onSuccess(data);
          },
          (error) => {
            if (onFail) onFail(error);
            _openAppSetting();
          },
        );
      })
      .catch((error) => {
        if (onFail) onFail(error);
        console.log('error', error);
        _openAppSetting();
      });
  } else {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        //@ts-ignore
        {
          title: 'Location Access Required',
          message: 'This App needs to Access your location',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        await locationEnabler(
          (isEnabled) => {
            if (isEnabled) {
              getCurrentPosition(
                (data) => {
                  if (onSuccess) onSuccess(data);
                },
                (error) => {
                  console.log('location error', error);
                  if (onFail) onFail(error);
                },
              );
            }
          },
          (err) => {
            if (onFail) onFail(err);
            locationOffModal();
          },
        );
      } else {
        if (onFail) onFail();
        _openAppSetting();
      }
    } catch (err) {
      if (onFail) onFail(err);
      _openAppSetting();
    }
  }
};

export const locationEnabler = async (
  onSuccess?: (res: any) => void,
  onFail?: (err: any) => void,
) => {
  if (Platform.OS === 'android') {
    await promptForEnableLocationIfNeeded()
      .then(() => {
        if (onSuccess) {
          onSuccess(true);
        }

      })
      .catch((err) => {
        if (onFail) {
          onFail(err);
        }

      });
  }
};
