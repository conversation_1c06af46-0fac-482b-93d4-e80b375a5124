import React from 'react';
import { Svg, Path } from 'react-native-svg';

interface ClearChatsSVGProps {
  size?: number;
  color?: string;
}

const ClearChatsSVG: React.FC<ClearChatsSVGProps> = ({ size = 17, color = '#232323' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 17 18" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.5 16.2563C12.4902 16.2563 15.725 13.0215 15.725 9.03125C15.725 5.04099 12.4902 1.80625 8.5 1.80625C4.50974 1.80625 1.275 5.04099 1.275 9.03125C1.275 13.0215 4.50974 16.2563 8.5 16.2563ZM8.5 17.5312C13.1944 17.5312 17 13.7256 17 9.03125C17 4.33683 13.1944 0.53125 8.5 0.53125C3.80558 0.53125 0 4.33683 0 9.03125C0 13.7256 3.80558 17.5312 8.5 17.5312Z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.04365 12.4894C4.79469 12.2404 4.79469 11.8367 5.04365 11.5878L11.0541 5.57734C11.303 5.32839 11.7067 5.32839 11.9556 5.57734C12.2046 5.82631 12.2046 6.22995 11.9556 6.4789L5.94521 12.4894C5.69626 12.7382 5.29261 12.7382 5.04365 12.4894Z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.04366 5.57734C5.29261 5.32839 5.69625 5.32839 5.94522 5.57734L11.9557 11.5877C12.2045 11.8367 12.2045 12.2404 11.9557 12.4893C11.7067 12.7383 11.303 12.7383 11.0541 12.4893L5.04366 6.4789C4.79469 6.22995 4.79469 5.8263 5.04366 5.57734Z"
        fill={color}
      />
    </Svg>
  );
};

export default ClearChatsSVG; 