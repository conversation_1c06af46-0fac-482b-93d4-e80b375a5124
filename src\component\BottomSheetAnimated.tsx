import React, { useEffect } from 'react';
import { Dimensions, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

interface BottomSheetProps {
  visible: boolean;
  onClose?: () => void;
  children: React.ReactNode;
  height?: number;
  bottomOffset?: number; // how far above the bottom (i.e., above composer)
}

const BottomSheetAboveComposer: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  children,
  height = 100,
  bottomOffset = 70, // adjust to match composer height
}) => {
  const translateY = useSharedValue(height);

  const gesture = Gesture.Pan()
    .onUpdate((event) => {
      if (event.translationY > 0) {
        translateY.value = event.translationY;
      }
    })
    .onEnd(() => {
      if (translateY.value > 100) {
        translateY.value = withTiming(height, {}, () => {
          if (onClose) {
            runOnJS(onClose)();
          }
        });
      } else {
        translateY.value = withSpring(0);
      }
    });

  useEffect(() => {
    translateY.value = visible ? withTiming(0) : withTiming(height);
  }, [visible, height]);

  const animatedStyle = useAnimatedStyle(() => {
    const style: ViewStyle = {
      transform: [{ translateY: translateY.value }],
      position: 'absolute',
      bottom: bottomOffset,
      left: 0,
      right: 0,
      zIndex: 10,
    };
    return style;
  });

  return (
    <GestureDetector gesture={gesture}>
      <Animated.View
        pointerEvents={visible ? 'auto' : 'none'} // only intercept touches when visible
        style={[
          {
            // height,
            backgroundColor: '#fff',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            shadowColor: '#000',
            shadowOpacity: 0.1,
            shadowOffset: { height: -2, width: 0 },
            shadowRadius: 4,
            elevation: 4,
            overflow: 'hidden',
          },
          animatedStyle,
        ]}
      >
        {children}
      </Animated.View>
    </GestureDetector>
  );
};

export default BottomSheetAboveComposer;
