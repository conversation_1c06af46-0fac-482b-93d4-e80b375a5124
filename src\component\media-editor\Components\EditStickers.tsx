import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import StickerItem from './StickerHandler/StickerItem';

export interface Sticker {
  uri: any;
  id: string;
}

interface StickerRendererProps {
  stickers: Sticker[];
  itemDragging: any;
  isDeletable: (id: string, type: string, bool: boolean) => void;
}

const StickerRenderer: React.FC<StickerRendererProps> = ({
  stickers,
  itemDragging,
  isDeletable,
}) => {
  const [zIndexMap, setZIndexMap] = useState<number[]>([]);

  //   useEffect(() => {
  //     setZIndexMap(stickers.map((_, index) => index + 1));
  //   }, [stickers]);

  const handleSelectImage = (index: number) => {
    setZIndexMap(stickers?.length > 0 ? stickers.map((_, index) => index + 1) : []);
    setZIndexMap((prevZIndexMap) => {
      const maxZIndex = Math.max(...prevZIndexMap);
      return prevZIndexMap.map((z, i) => (i === index ? maxZIndex + 1 : z));
    });
  };
  // console.log('Updated zIndexMap:', zIndexMap);

  return (
    <View style={styles.container}>
      {stickers.map((sticker, idx) => (
        <View key={sticker.id} style={[styles.stickerWrapper, { zIndex: zIndexMap[idx] }]}>
          <StickerItem
            index={idx}
            id={sticker.id}
            sticker={sticker}
            onSelectSticker={() => handleSelectImage(idx)}
            itemDragging={itemDragging}
            isDeletable={isDeletable}
          />
        </View>
      ))}
    </View>
  );
};

export default StickerRenderer;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: '35%',
    left: '35%',
    zIndex: 202,
  },
  stickerWrapper: {
    position: 'absolute',
    // top: '40%',
    // left: '40%',
    backgroundColor: 'red',
  },
});
