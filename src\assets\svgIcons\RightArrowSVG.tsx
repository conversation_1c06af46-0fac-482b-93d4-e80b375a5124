import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface RightArrowSVGProps {
  width?: number;
  height?: number;
  color?: string;
}

const RightArrowSVG: React.FC<RightArrowSVGProps> = ({
  width = 6,
  height = 11,
  color = "#6A4DBB",
  ...props
}) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 6 11"
      fill="none"
      {...props} // spread last so props like style still apply
    >
      <Path
        d="M6 5.263a.735.735 0 01-.216.52L1.258 10.31A.737.737 0 11.216 9.268L4.22 5.263.216 1.258A.737.737 0 011.258.216L5.784 4.74A.735.735 0 016 5.263z"
        fill={color}
      />
    </Svg>
  );
};

export default RightArrowSVG;
