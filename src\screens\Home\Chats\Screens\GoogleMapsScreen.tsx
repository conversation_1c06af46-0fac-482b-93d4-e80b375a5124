import {
  Image,
  Linking,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Animated,
  FlatList,
  ScrollView,
  KeyboardAvoidingView,
  ActivityIndicator,
  Alert,
  Keyboard,
  ToastAndroid,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { hexToRgba, hp, SCREEN_HEIGHT, SCREEN_WIDTH } from '../../../../theme/fonts';
import { colors } from '../../../../theme/colors';
import SelecteUnselectSVG from '../../../../assets/svgIcons/SelecteUnselectSVG';
import SendSVG from '../../../../assets/svgIcons/SendSVG';
import MapView, { Marker, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { RouteProp, useIsFocused, useNavigation, useRoute } from '@react-navigation/native';
import ExpandSVG from '../../../../assets/svgIcons/ExpandSVG';
import CurrentLocationSVG from '../../../../assets/svgIcons/CurrentLocationSVG';
import ButtonPurple from '../../../../component/ButtonPurple';
import LocationSVG2 from '../../../../assets/svgIcons/LocationSVG2';
import CommonView from '../../../../component/CommonView';
import Geolocation from '@react-native-community/geolocation';
import LocationSVG from '../../../../assets/svgIcons/LocationSVG';
import ContractSVG from '../../../../assets/svgIcons/ContractSVG';
import DeviceInfo from 'react-native-device-info';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import SearchSVG from '../../../../assets/svgIcons/SearchSVG';
import RefreshSVG from '../../../../assets/svgIcons/RefreshSVG';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MessageType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { KeyboardStickyView } from 'react-native-keyboard-controller';
import { getCurrentLocation, VALIDATION_RULES } from '../../../../utils/commonFunction';
import { useFetchMessages } from '../../../../hooks/chats/messages/useFetchMessages';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import { ChatSocket } from '../../../../socket-client/ChatSocket';
import useSocket from '../../../../socket-client/useSocket';
import { ChatService } from '../../../../service/ChatService';

interface IProps {
  //   onSendLiveLocation?: () => void;
  //   onSendLocation?: (location: object) => void;
}

export type GoogleMapsScreenParams = {
  ChatSpecificScreen: {
    handleSendMessage: (value: any) => Promise<void>;
    composedMessage: any;
    userData?: IConversation;
  };
};

let userLocation: { latitude: number | null; longitude: number | null } = {
  latitude: null,
  longitude: null,
};

type Address = {
  address?: string;
  distance?: string | number; // in km
};

const GoogleMapsScreen = ({}: IProps) => {
  const route = useRoute<RouteProp<GoogleMapsScreenParams, 'ChatSpecificScreen'>>();
  const { socket } = useSocket();

  const { handleSendMessage: onSendLocation, composedMessage, userData } = route.params;

  const [locationType, setLocationType] = useState<'current' | 'live'>('current');
  const [pointMyLocation, setPointMyLocation] = useState<boolean>(false);
  const [region, setRegion] = useState<any>(null);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [liveTimer, setLiveTimer] = useState<NodeJS.Timeout | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const [shareLive, setShareLive] = useState<boolean>(false);
  const [sharingTime, setSharingTime] = useState<string>('15_minutes');
  const [sharingMessage, setSharingMessage] = useState<string>('');
  const [address, setAddress] = useState<Address>({
    address: '',
    distance: '',
  });
  const { messages } = useFetchMessages(userData?.id as string);
  const getRecentLiveData = messages.filter(
    (list) => list.location?.type == 'live' && !list.location.isStopped,
  );

  // console.log("getrecentlivedata", getRecentLiveData)
  const mapViewRef = React.useRef<MapView>(null);
  const inputRef = React.useRef<TextInput>(null);
  const markerScale = useRef(new Animated.Value(1)).current;

  // const { messages } = useFetchMessages(String(userData?.id));

  // const liveItems = messages.filter(item => item.location?.type === "live" && !item.location.isStopped);

  // // Step 2: Find latest by createdTime
  // const latestLive = liveItems.reduce((latest: any, current: any) => {
  //   return !latest || current.createdTime > latest.createdTime ? current : latest;
  // }, null);
  // console.log("latestLive", JSON.stringify(latestLive, null, 2))

  const navigation = useNavigation();

  const nearAddresses = [
    {
      id: 1,
      address: '8-3-815, Mumbai highway 500018, IN',
      distance: 'Accurate to 5 meters',
    },
    {
      id: 2,
      address: '8-3-815, Mumbai highway 500018, IN',
      distance: 'Accurate to 5 meters',
    },
    {
      id: 3,
      address: '8-3-815, Mumbai highway 500018, IN',
      distance: 'Accurate to 5 meters',
    },
  ];

  const _renderInput = () => {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <View style={{ flex: 1, marginRight: 15 }}>
          <TextInput
            ref={inputRef}
            value={sharingMessage}
            placeholderTextColor={colors._B5B5B5_gray}
            placeholder="Write your message"
            onChangeText={(text: string) => {
              setSharingMessage(text);
            }}
            style={{
              paddingHorizontal: hp(2),
              padding: 0,
              height: 50,
              borderRadius: 15,
              backgroundColor: colors.gray_f3,
              color: colors.black_23,
              fontSize: 16,
              fontWeight: '400',
            }}
            maxLength={VALIDATION_RULES.TEXT_MESSAGE_LENGTH}
          />
        </View>
        <TouchableOpacity
          onPress={() => {
            if (sharingMessage?.trim().length >= VALIDATION_RULES.TEXT_MESSAGE_LENGTH) {
              ToastAndroid.show('Message limit exceeded.', ToastAndroid.SHORT);
              return null;
            }
            Keyboard.dismiss();
            if (locationType == 'current') {
              console.log('send this location cilcked ');
              onSendLocation({
                ...composedMessage,
                ...region,
                text: sharingMessage.trim(),
                messageType: MessageType.LOCATION,
                type: 'current',
              });
              navigation.goBack();
            } else {
              onSendLiveLocation();
            }
          }}
        >
          <SendSVG size={46} color={colors.white} backgroundColor={colors.mainPurple} />
        </TouchableOpacity>
      </View>
    );
  };

  const requestLocationPer = async () => {
    try {
      let granted = false;
      if (Number(Platform.Version) >= 33) {
        granted = true;
      } else {
        // Request multiple permissions for older Android versions
        const permissions = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_BACKGROUND_LOCATION,
        ]);

        // Check the results of each permission
        granted =
          permissions['android.permission.ACCESS_FINE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_COARSE_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED &&
          permissions['android.permission.ACCESS_BACKGROUND_LOCATION'] ===
            PermissionsAndroid.RESULTS.GRANTED;
      }

      if (granted) {
        getCurrentLocationssss(); // Call this function if permission is granted
      } else {
        console.log('Location permission denied');
        // Optionally open settings if the permission is denied
        Linking.openSettings();
      }
    } catch (err) {
      console.log('Error requesting location permission:', err);
    }
  };

  const animateMarkerSize = (toValue: number) => {
    Animated.spring(markerScale, {
      toValue,
      useNativeDriver: true,
      friction: 5,
    }).start();
  };

  const getCurrentLocationssss = async () => {
    try {
      const currentLocationData = await getCurrentLocation();
      userLocation = {
        latitude: currentLocationData.latitude,
        longitude: currentLocationData.longitude,
      };
      const addressData = await fetchAddressWithDistance(
        currentLocationData.latitude,
        currentLocationData.longitude,
        currentLocationData.latitude,
        currentLocationData.longitude,
      );
      setAddress(addressData);
      setRegion(currentLocationData);
      // console.log('locData', locData);
      setTimeout(() => {
        gotoInitialPostion(currentLocationData);
      }, 100);
    } catch (error) {
      console.log('Catch block error:', error);
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const gotoInitialPostion = (values: any) => {
    // console.log('🚀 ~ gotoInitialPostion ~ values:', values);
    const { latitude, longitude } = values;
    mapViewRef.current?.animateToRegion(
      {
        latitude: latitude,
        longitude: longitude,
        latitudeDelta: 0.002,
        longitudeDelta: 0.002,
      },
      1000,
    );
    if (loading) {
      console.log('setLoading(false) gotoInitialPostion');
      setLoading(false);
    }
  };

  const stopLiveLocationForAll = async (
    messages: any[],
    currentLocationData: { latitude: number | null; longitude: number | null },
  ) => {
    try {
      for (const msgData of messages) {
        ChatSocket.sendLiveLocationStopped(socket!, {
          globalId: msgData?.globalId,
          receiverId: msgData?.receiverId,
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
        });

        ChatService.updateLocationStatus(
          msgData?.globalId as string,
          true,
          userLocation.latitude as number,
          userLocation.longitude as number,
        );
      }

      console.log('Stopped live location for all elements');
    } catch (error) {
      Alert.alert('Error', 'Failed to stop live location sharing for some messages.');
      console.error(error);
    }
  };

  const onSendLiveLocation = async () => {
    const now = new Date();
    const min15Later = new Date(now.getTime() + 15 * 60 * 1000).getTime() - 0.5 * 60 * 1000;
    const hourLater = new Date(now.getTime() + 60 * 60 * 1000).getTime();
    const hours8Later = new Date(now.getTime() + 8 * 60 * 60 * 1000).getTime() - 0.2 * 60 * 1000;
    const expiresAt =
      sharingTime == '15_minutes' ? min15Later : sharingTime == '1_hour' ? hourLater : hours8Later;
    if (getRecentLiveData.length > 0) {
      await stopLiveLocationForAll(getRecentLiveData, userLocation);
    }

    await onSendLocation({
      ...userLocation,
      type: locationType,
      isLocationSharing: true,
      expiresAt: expiresAt,
      messageType: MessageType.LOCATION,
      text: sharingMessage?.trim(),
    });
    navigation.goBack();
  };

  const insets = useSafeAreaInsets();

  const fetchNearbyPlaces = async (lat: number, lng: number, apiKey: string) => {
    let places = [];
    let url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=5000&key=${apiKey}`;
    let nextPageToken = null;
    console.log('url', url);
    do {
      const response = await fetch(url);
      const data = await response.json();
      console.log('🚀 ~ fetchNearbyPlaces ~ data:', data);

      if (data.results) {
        places.push(...data.results);
      }

      nextPageToken = data.next_page_token;

      if (nextPageToken) {
        // wait for the token to activate
        await new Promise((resolve) => setTimeout(resolve, 2000));
        url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=${nextPageToken}&key=${apiKey}`;
      }
    } while (nextPageToken);

    console.log('All places:', places);
  };

  // 17.428530276523862, 78.4511698535559
  const fetchAddress = async (lat: number, lon: number) => {
    try {
      const userAgent = await DeviceInfo.getUserAgent();
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=16&addressdetails=1`,
        {
          headers: {
            'Accept-Language': 'en',
            // 'User-Agent': 'MyApp/1.0 (<EMAIL>)', // required by Nominatim
            'User-Agent': userAgent, // required by Nominatim
          },
        },
      );
      const data = await response.json();
      console.log('data nomination', data);
      if (data.error) {
        throw new Error(data.error);
      }
      return data;
    } catch (err: any) {
      console.log('nomination error', err.message);
    } finally {
    }
  };

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Earth's radius in km
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
  };

  // Usage with your fetchAddress function
  const fetchAddressWithDistance = async (
    currentLat: number,
    currentLon: number,
    targetLat: number,
    targetLon: number,
  ) => {
    try {
      // Get address info
      const addressData: any = await fetchAddress(targetLat, targetLon);

      console.log('addressdata', addressData);

      // Calculate distance
      // const distance = calculateDistance(currentLat, currentLon, targetLat, targetLon);
      // console.log('distanceCalculate', distance);
      // const distanceInMeters = distance * 1000; // Convert to meters if needed
      // console.log('distanceInMeters calculated', distanceInMeters);
      // const properAddress =
      //   distanceInMeters < 1
      //     ? 0
      //     : distanceInMeters < 1000
      //     ? `${distanceInMeters?.toFixed(2)} meters`
      //     : `${distance?.toFixed(2)} ${distance > 1 ? 'kms' : 'km'}`;
      return {
        address: addressData?.display_name,
        // distance: properAddress, // in km
      };
    } catch (err) {
      console.error('Error:', err);
      throw err;
    }
  };

  const getDistanceAndAddress = async (newRegion?: Region) => {
    try {
      let regions = newRegion ? newRegion : region;
      const addressData = await fetchAddressWithDistance(
        userLocation?.latitude as number,
        userLocation.longitude as number,
        regions?.latitude,
        regions?.longitude,
      );
      setAddress(addressData);
      if (loading) {
        setLoading(false);
      }
    } catch (error) {
      console.log('error getdistancefunction', error);
    }
  };

  // useEffect(() => {
  //   // fetchAddress(region?.latitude, region?.longitude);
  //   getDistanceAndAddress();
  // }, [region?.latitude, region?.longitude]);

  useEffect(() => {
    console.log('permisstions from location modal');
    requestLocationPer();

    return () => {
      console.log('empty region----------');
      // setRegion({})
      userLocation = { latitude: null, longitude: null };
    };
  }, []);

  console.log('isExpanded', isExpanded);

  return (
    <>
      <CommonView
        headerTitle="Send location"
        isScrollable={true}
        headerContainerStyle={{ marginTop: 10 }}
        renderRight={() => {
          return (
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 18 }}>
              {/* todo: enable search and refresh */}
              {/* <TouchableOpacity style={{}} onPress={() => {}}>
                <SearchSVG size={19} color={colors.white} />
              </TouchableOpacity>
              <TouchableOpacity style={{}} onPress={() => {}}>
                <RefreshSVG size={18} color={colors.white} />
              </TouchableOpacity> */}
            </View>
          );
        }}
        containerStyle={styles.commonView}
      >
        <KeyboardStickyView>
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              alignItems: 'center',
              marginTop: hp(3),
              marginHorizontal: hp(2),
            }}
          >
            <View style={{ flex: 1 }}>
              <View
                style={{
                  minHeight: !isExpanded
                    ? SCREEN_HEIGHT * 0.55
                    : shareLive
                    ? // ? SCREEN_HEIGHT * 0.55
                      480
                    : SCREEN_WIDTH - 38,
                  width: SCREEN_WIDTH - 38,
                  borderRadius: 10,
                  overflow: 'hidden',
                  alignSelf: 'center',
                }}
              >
                <MapView
                  ref={mapViewRef}
                  provider={PROVIDER_GOOGLE}
                  mapType="standard"
                  style={{ flex: 1 }}
                  initialRegion={region}
                  // region={region}
                  onRegionChange={() => {
                    animateMarkerSize(0.7); // Increase marker size while moving
                    if (!loading) {
                      setLoading(true);
                    }
                  }}
                  onRegionChangeComplete={(newRegion) => {
                    animateMarkerSize(0.5); // Shrink marker when stopped
                    console.log('newRegion', newRegion);
                    setRegion(newRegion); // Update center position
                    getDistanceAndAddress(newRegion);
                  }}
                  loadingEnabled
                  showsCompass
                  showsUserLocation
                  showsMyLocationButton={false}
                />

                {/* Center marker */}
                <Animated.View
                  pointerEvents="none"
                  style={{
                    position: 'absolute',
                    top: !isExpanded
                      ? SCREEN_HEIGHT * 0.25
                      : shareLive
                      ? 202
                      : (SCREEN_WIDTH - 38) / 2 - 20,
                    // top:  !isExpanded || shareLive ? SCREEN_HEIGHT * 0.28 : (SCREEN_WIDTH - 38) / 2 - 20,
                    left: (SCREEN_WIDTH - 38) / 2 - 15,
                    transform: [{ scale: markerScale }],
                    zIndex: 10,
                  }}
                >
                  <Image
                    source={require('../../../../assets/locationPin2.png')}
                    style={{ width: 40, height: 40 }}
                    resizeMode="contain"
                  />
                </Animated.View>
              </View>

              <View
                style={{ position: 'absolute', right: 0, marginTop: hp(2), marginRight: hp(2) }}
              >
                {!shareLive && (
                  <TouchableOpacity
                    style={{
                      elevation: 2,
                      height: 40,
                      width: 40,
                      borderRadius: 30,
                      backgroundColor: colors.white,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    activeOpacity={0.7}
                    onPress={() => {
                      setIsExpanded(!isExpanded);
                    }}
                  >
                    {isExpanded ? <ExpandSVG color={colors.black_23} size={14} /> : <ContractSVG />}
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => {
                    getCurrentLocationssss();
                    gotoInitialPostion(userLocation);
                  }}
                  activeOpacity={0.7}
                  style={{
                    marginTop: hp(2),
                    elevation: 2,
                    height: 40,
                    width: 40,
                    borderRadius: 30,
                    backgroundColor: colors.white,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <CurrentLocationSVG color={colors.black_23} size={19} />
                </TouchableOpacity>
              </View>

              {shareLive ? (
                <View style={{ height: 100, marginBottom: 100 }}>
                  <View style={{ marginTop: hp(4) }}>
                    <Text style={{ fontSize: 16, fontWeight: '600', color: colors.black_23 }}>
                      Share live location
                    </Text>
                    <View
                      style={{
                        backgroundColor: hexToRgba(colors.black, 0.1),
                        height: 1.5,
                        marginTop: hp(2),
                      }}
                    />
                    <View
                      style={{
                        marginTop: hp(2),
                        marginBottom: hp(2),
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <TouchableOpacity
                        style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                        onPress={() => {
                          setSharingTime('15_minutes');
                        }}
                      >
                        <SelecteUnselectSVG size={20} isSelected={sharingTime == '15_minutes'} />
                        <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                          15 minutes
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                        onPress={() => {
                          setSharingTime('1_hour');
                        }}
                      >
                        <SelecteUnselectSVG size={20} isSelected={sharingTime == '1_hour'} />
                        <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                          1 hour
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
                        onPress={() => {
                          setSharingTime('8_hours');
                        }}
                      >
                        <SelecteUnselectSVG size={20} isSelected={sharingTime == '8_hours'} />
                        <Text style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}>
                          8 hours
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {_renderInput()}
                  </View>
                </View>
              ) : (
                <View>
                  <View
                    style={{
                      backgroundColor: colors._F8F8F8_gray,
                      // marginHorizontal: hp(2),
                      paddingHorizontal: 15,
                      paddingVertical: 15,
                      borderRadius: 15,
                      marginTop: 20,
                      marginBottom: shareLive || !isExpanded ? hp(12) : 0,
                      alignSelf: 'center',
                      width: SCREEN_WIDTH - 38,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '400',
                        color: colors._7B7B7B_gray,
                        marginBottom: 13,
                      }}
                    >
                      Current Location
                    </Text>
                    {loading ? (
                      <ActivityIndicator
                        color={colors.black}
                        size={'large'}
                        style={{ marginVertical: 10 }}
                      />
                    ) : (
                      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                        <LocationSVG size={24} color={colors.black_23} />
                        <View style={{ flexShrink: 1 }}>
                          <Text style={{ fontSize: 15, fontWeight: '400', color: colors.black_23 }}>
                            {address.address}
                          </Text>
                          <Text
                            style={{ fontSize: 13, fontWeight: '400', color: colors._9B9B9B_gray }}
                          >
                            {/* {`Accurate to ${address.distance}`} */}
                            Accurate to 5 meters
                          </Text>
                        </View>
                      </View>
                    )}
                  </View>

                  {false && (
                    <View>
                      <Text
                        style={{
                          fontSize: 16,
                          fontWeight: '400',
                          color: colors.gray_80,
                          marginTop: 20,
                          marginBottom: 13,
                        }}
                      >
                        Nearby to you
                      </Text>

                      {/* <FlatList
                      data={nearAddresses}
                      keyExtractor={(item) => item?.id.toString()}
                      renderItem={({ item, index }) => {
                        return (
                          <View
                            key={index}
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              gap: 10,
                              marginBottom: 20,
                            }}
                          >
                            <LocationSVG size={24} color={colors.black_23} />
                            <View>
                              <Text
                                style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}
                              >
                                {item.address}
                              </Text>
                              <Text
                                style={{ fontSize: 14, fontWeight: '400', color: colors.gray_80 }}
                              >
                                {item.distance}
                              </Text>
                            </View>
                          </View>
                        );
                      }}
                      contentContainerStyle={{ marginBottom: 100 }}
                    /> */}
                      <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
                        {nearAddresses?.map((item, index) => (
                          <View
                            key={item?.id?.toString() || index.toString()}
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              gap: 10,
                              marginBottom: 20,
                            }}
                          >
                            <LocationSVG size={24} color={colors.black_23} />
                            <View>
                              <Text
                                style={{ fontSize: 16, fontWeight: '400', color: colors.black_23 }}
                              >
                                {item.address}
                              </Text>
                              <Text
                                style={{ fontSize: 14, fontWeight: '400', color: colors.gray_80 }}
                              >
                                {item.distance}
                              </Text>
                            </View>
                          </View>
                        ))}
                      </ScrollView>
                    </View>
                  )}
                </View>
              )}
            </View>
          </View>
        </KeyboardStickyView>
      </CommonView>

      <View
        style={{
          position: 'absolute',
          bottom: insets.bottom,
          width: '100%',
          backgroundColor: colors.white,
          paddingHorizontal: 16,
        }}
      >
        <KeyboardStickyView style={{ backgroundColor: colors.white, paddingVertical: 16 }}>
          {_renderInput()}
        </KeyboardStickyView>
      </View>
      {/* {!shareLive && ( */}
      {false && (
        <View
          style={{
            // flex: 0.4,
            position: 'absolute',
            bottom: insets.bottom,
            width: '100%',
            backgroundColor: colors.white,
            padding: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: 14,
          }}
        >
          {/* todo: enable live location  */}
          <ButtonPurple
            type="border"
            extraStyle={{ flex: 1, height: 50 }}
            withRightIcon={false}
            titleColor={colors.mainPurple}
            onPress={() => {
              console.log('Get Verification Call Pressed');
              console.log('region----------', region);
              setShareLive(true);
              setIsExpanded(true);
              setLocationType('live');
            }}
            title={'Live location'}
            disabled={!region}
          />
          <ButtonPurple
            extraStyle={{ flex: 1, height: 50 }}
            withRightIcon={false}
            titleColor={colors.white}
            onPress={() => {
              console.log('send this location cilcked ');
              onSendLocation({
                ...composedMessage,
                ...region,
                messageType: MessageType.LOCATION,
                type: 'current',
              });
              navigation.goBack();
            }}
            title={'This location'}
            disabled={!region}
          />
        </View>
      )}
    </>
  );
};

export default GoogleMapsScreen;

const styles = StyleSheet.create({
  commonView: {
    flex: 1,
    paddingHorizontal: 0,
    paddingTop: 0,
    paddingBottom: 0,
    backgroundColor: colors.white,
  },
});
