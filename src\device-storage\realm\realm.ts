// src/realm/index.js
import Realm from 'realm';
import { createRealmContext } from '@realm/react';
import {
  ContactMessageSchema,
  LocationMessageSchema,
  MessageSchema,
  Reaction,
} from './schemas/MessageSchema';
import { ContactSchema } from './schemas/ContactSchema';
import { ConversationSchema } from './schemas/ConversationSchema';
import { UserSchema } from './schemas/UserSchema';
import { CallSchema } from './schemas/CallSchema';
import { RequestSchema } from './schemas/RequestSchema';
import { ChatSpaceSchema } from './schemas/ChatSpaceSchema';
import { ConversationSettingsSchema } from './schemas/ConversationSettingsSchema';

let realm: Realm | null = null;

const realmConfig: Realm.Configuration = {
  schemaVersion: 2,
  schema: [
    ContactSchema,
    MessageSchema,
    LocationMessageSchema,
    ContactMessageSchema,
    ConversationSchema,
    UserSchema,
    CallSchema,
    Reaction,
    RequestSchema,
    ChatSpaceSchema,
    ConversationSettingsSchema,
  ],
  deleteRealmIfMigrationNeeded: __DEV__,
};

export const { RealmProvider, useRealm, useQuery, useObject } = createRealmContext(realmConfig);
// Debug schema registration

export const getRealm = () => {
  if (realm && !realm.isClosed) {
    return realm;
  }
  const _realm = new Realm(realmConfig);
  realm = _realm;
  console.log('Realm connection established', realm.isClosed);
  // clearnConversationsSchema();
  return _realm;
};
export const closeRealm = (realm: Realm) => {
  if (realm && !realm.isClosed) {
    realm.close();
  }
};

export const clearRealmData = () => {
  const realm = getRealm();
  if (realm && !realm.isClosed) {
    // realm.removeAllListeners();
    if (realm.isInTransaction) {
      realm.deleteAll();
      console.log('All Realm data cleared inside an existing transaction.');
    } else {
      realm.write(() => {
        realm.deleteAll();
      });

      console.log('All Realm data cleared.', realm.isClosed);
    }
  }
};
