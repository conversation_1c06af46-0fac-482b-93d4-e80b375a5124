import React from 'react';
import { Text, Pressable } from 'react-native';
import Gestrurewrapper from '../Common/Gestrurewrapper';

interface CustomTextInputProps {
  otherProps?: any;
  setInputText: (text: string) => void;
  borderColor?: string;
  isOkayPressed: any;
  onSubmit: (text: string) => void;
  id: any;
  text: string;
  color: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: 'normal' | 'bold' | '500';
  onPress: () => void;
}

const CustomText: React.FC<CustomTextInputProps> = ({
  isOkayPressed,
  id,
  text,
  color,
  fontFamily,
  onPress,
}) => {
  return (
    <Gestrurewrapper>
      <Pressable
        onPress={onPress}
        style={{ backgroundColor: 'red', position: 'absolute', alignSelf: 'center' }}
      >
        <Text
          key={id}
          style={{
            fontSize: 24,
            fontWeight: '600',
            color: color,
            fontFamily: fontFamily,
            borderWidth: isOkayPressed ? 1 : 0,
            borderColor: '#fff',
          }}
        >
          {text || ''}
        </Text>
      </Pressable>
    </Gestrurewrapper>
  );
};

export default CustomText;
