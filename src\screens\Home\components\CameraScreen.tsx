import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, StatusBar, Linking } from 'react-native';
import {
  Camera,
  CameraDevice,
  useCameraDevice,
  CameraPermissionStatus,
  PhotoFile,
  VideoFile,
  useCameraFormat,
  useCameraDevices,
} from 'react-native-vision-camera';
import { RouteProp, useIsFocused, useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { colors } from '../../../theme/colors';
import { androidVersion, hexToRgba } from '../../../theme/fonts';
import Animated from 'react-native-reanimated';
import CloseSVG from '../../../assets/svgIcons/CloseSVG';
import LinearGradient from 'react-native-linear-gradient';
import { NavigationRouteParams } from '../../../navigation/navigationParams';
import { navigateTo } from '../../../utils/commonFunction';
import { createThumbnail } from 'react-native-create-thumbnail';

type IProps = {};

const CameraScreen = ({}: IProps) => {
  const route = useRoute<RouteProp<NavigationRouteParams, 'CameraScreen'>>();
  const handleSendMessage = route.params.handleSendMessage || (() => {});
  const otherUserData = route.params.otherUserData || {};
  console.log('🚀 ~ CameraScreen ~ route:', otherUserData, route.params.handleSendMessage);

  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [cameraPosition, setCameraPosition] = useState<'back' | 'front'>('front');
  const [flash, setFlash] = useState<'on' | 'off'>('on');
  const [isVideo, setIsVideo] = useState<boolean>(false);
  const [videoCountDown, setVideoCountDown] = useState<number>(0);
  const [canStop, setCanStop] = useState<boolean>(false);

  console.log('🚀 ~ CameraScreen ~ flash:', flash, androidVersion);

  const camera = useRef<Camera>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const devices = useCameraDevice(cameraPosition);
  const deviceInfo = useCameraDevices();
  console.log('🚀 ~ CameraScreen ~ deviceInfo:', deviceInfo);

  const format = devices?.formats.find((f) => f.photoWidth === 1920 && f.photoHeight === 1080);
  console.log('🚀 ~ CameraScreen ~ devices:', devices);
  // console.log('🚀 ~ CameraScreen ~ devices:', JSON.stringify(devices, null, 2));
  const isFocused = useIsFocused();
  const navigation = useNavigation();

  const ensurePermissions = async () => {
    const cameraPermission = await Camera.getCameraPermissionStatus();
    const micPermission = await Camera.getMicrophonePermissionStatus();

    if (cameraPermission !== 'granted') {
      const newCameraPermission = await Camera.requestCameraPermission();
      if (newCameraPermission !== 'granted') return false;
    }

    if (micPermission !== 'granted') {
      const newMicPermission = await Camera.requestMicrophonePermission();
      if (newMicPermission !== 'granted') {
        Alert.alert(
          'Microphone Permission Required',
          'Please enable microphone access in your settings to record videos.',
          [{ text: 'Open Settings', onPress: () => Linking.openSettings() }],
        );
        return false;
      }
    }

    return true;
  };

  useEffect(() => {
    ensurePermissions().then(setHasPermission);
    return () => {
      camera.current?.stopRecording();
    };
  }, []);

  // useEffect(() => {
  //   (async () => {
  //     const cameraStatus = await Camera.requestCameraPermission();
  //     console.log('🚀 ~ CameraScreen ~ cameraStatus:', cameraStatus);
  //     const micStatus = await Camera.requestMicrophonePermission();
  //     console.log('🚀 ~ CameraScreen ~ micStatus:', micStatus);
  //     setHasPermission(cameraStatus === 'granted' && micStatus === 'granted');
  //   })();
  // }, []);

  const takePhoto = async (): Promise<void> => {
    try {
      const photo: PhotoFile | undefined = await camera.current?.takePhoto({
        flash: cameraPosition == 'front' ? 'off' : flash,
      });
      if (photo) {
        navigateTo('MediaPreviewScreen', {
          msgData: photo,
          isFromCameraScreen: true,
          handleSendMessage: handleSendMessage,
          otherUserData: otherUserData,
        });
        console.log('Photo Taken!', JSON.stringify(photo, null, 2));
      }
    } catch (error) {
      console.error('Failed to take photo:', error);
    }
  };

  const startRecording = async () => {
    setVideoCountDown(0);
    setIsRecording(true);
    setCanStop(false);

    timerRef.current = setInterval(() => {
      setVideoCountDown((prev) => prev + 1);
    }, 1000);

    // Allow stopping after 1 second
    setTimeout(() => setCanStop(true), 1000);

    await camera.current?.startRecording({
      fileType: 'mp4',
      flash: cameraPosition == 'front' ? 'off' : flash,
      onRecordingFinished: async (video) => {
        clearInterval(timerRef.current);
        timerRef.current = null;
        setIsRecording(false);
        console.log('Video Recorded:', video);

        // let thumbnail = '';
        // try {
        //   const thumb = await createThumbnail({ url: video.path, timeStamp: 1000 });
        //   thumbnail = thumb?.path || '';
        //   console.log('🚀 ~ startRecording ~ thumbnail:', thumbnail);
        // } catch (err) {
        //   console.error('Thumbnail generation failed:', err);
        // }
        if (video) {
          navigateTo('MediaPreviewScreen', {
            msgData: video,
            isFromCameraScreen: true,
            handleSendMessage: handleSendMessage,
            otherUserData: otherUserData,
          });
        }
      },
      onRecordingError: (error) => {
        clearInterval(timerRef.current);
        timerRef.current = null;
        setIsRecording(false);
        console.error('Recording error:', error);
      },
    });
  };

  const stopRecording = async () => {
    if (!canStop) return; // Prevent stopping too early
    clearInterval(timerRef.current);
    timerRef.current = null;
    await camera.current?.stopRecording();
  };

  const recordingTime = () => {
    return '00.00';
  };

  const formatRecordingTime = () => {
    if (videoCountDown < 0) return;

    const hrs = Math.floor(videoCountDown / 3600);
    const mins = Math.floor((videoCountDown % 3600) / 60);
    const secs = videoCountDown % 60;

    const paddedMins = String(mins).padStart(2, '0');
    const paddedSecs = String(secs).padStart(2, '0');

    if (hrs > 0) {
      return `${String(hrs).padStart(2, '0')}:${paddedMins}:${paddedSecs}`;
    }
    return `${paddedMins}:${paddedSecs}`;
  };

  console.log('device, hasPermission', hasPermission);

  if (!devices || !hasPermission) {
    return (
      <View style={styles.centered}>
        <Text style={{ color: 'white' }}>Loading Camera...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, marginTop: -Number(StatusBar.currentHeight) }}>
      <StatusBar barStyle={'dark-content'} backgroundColor={'transparent'} />
      <View style={styles.container}>
        <View
          style={{
            position: 'absolute',
            top: 20,
            left: 25,
            zIndex: 10,
            marginTop: Number(StatusBar.currentHeight),
          }}
        >
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              backgroundColor: hexToRgba(colors.black, 0.2),
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 30,
              marginBottom: 20,
            }}
            onPress={() => {
              navigation.goBack();
            }}
          >
            <Ionicons name={'close'} size={25} color={colors.white} />
          </TouchableOpacity>
        </View>

        <View
          style={{
            position: 'absolute',
            top: 20,
            right: 25,
            zIndex: 10,
            marginTop: Number(StatusBar.currentHeight),
          }}
        >
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              backgroundColor: hexToRgba(colors.black, 0.2),
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 30,
              marginBottom: 20,
            }}
            onPress={() => {
              setCameraPosition(cameraPosition == 'back' ? 'front' : 'back');
            }}
          >
            <Ionicons name={'camera-reverse-sharp'} size={20} color={colors.white} />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              width: 40,
              height: 40,
              backgroundColor: hexToRgba(colors.black, 0.2),
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 30,
            }}
            onPress={() => {
              setFlash(flash == 'on' ? 'off' : 'on');
            }}
          >
            <Ionicons
              name={flash == 'on' ? 'flash-sharp' : 'flash-off-sharp'}
              size={22}
              color={colors.white}
            />
          </TouchableOpacity>
        </View>

        {isFocused && (
          <Animated.View style={{ flex: 1 }}>
            <Camera
              ref={camera}
              style={StyleSheet.absoluteFill}
              device={devices}
              format={format}
              isActive={true}
              photo={true}
              video={true}
              audio={true}
              focusable={true}
            />
          </Animated.View>
        )}

        <View style={[styles.controls, { zIndex: 2 }]}>
          {isVideo && (
            <Text
              style={{
                fontSize: 12,
                fontWeight: '600',
                color: colors.red_ff4444,
                backgroundColor: colors.white,
                paddingHorizontal: 8,
                paddingVertical: 3,
                borderRadius: 30,
                marginBottom: 20,
              }}
            >
              {formatRecordingTime()}
            </Text>
          )}
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20, marginBottom: 20 }}>
            <Text
              style={[
                styles.border,
                {
                  color: colors.white,
                  fontSize: 14,
                  marginLeft: isRecording && isVideo ? 0 : 50,
                  fontWeight: '700',
                },
              ]}
            >
              {isVideo ? 'Video' : 'Photo'}
            </Text>
            {!isRecording && (
              <TouchableOpacity
                onPress={() => {
                  setIsVideo(isVideo ? false : true);
                }}
              >
                <Text
                  style={[
                    {
                      // marginRight: isVideo ? 70 : 0,
                      color: hexToRgba(colors.white, 0.5),
                      fontSize: 14,
                      fontWeight: '400',
                    },
                  ]}
                >
                  {!isVideo ? 'Video' : 'Photo'}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            onPress={() => {
              if (isVideo) {
                if (isRecording) {
                  stopRecording();
                }
                startRecording();
              } else {
                takePhoto();
              }
            }}
          >
            <LinearGradient
              colors={['#ffffff', '#f5f5f5']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{
                height: 70,
                width: 70,
                borderRadius: 50,
                // elevation: 2,
                marginBottom: 40,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {isVideo && !isRecording && (
                <View
                  style={{
                    height: 60,
                    width: 60,
                    backgroundColor: 'red',
                    borderRadius: 35,
                  }}
                />
              )}
              {isVideo && isRecording && (
                <View
                  style={{
                    width: 25,
                    height: 25,
                    borderRadius: 5,
                    backgroundColor: 'red',
                    position: 'absolute',
                  }}
                ></View>
              )}
            </LinearGradient>
          </TouchableOpacity>

          {/* <View
            style={{
              backgroundColor: 'blue',
              flexDirection: 'row',
              marginBottom: 30,
              marginTop: 20,
            }}
          >
            <TouchableOpacity
              onPress={() => setCameraPosition((prev) => (prev === 'back' ? 'front' : 'back'))}
            >
              <Text style={styles.button}>Flip</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={takePhoto}>
              <Text style={styles.button}>📷</Text>
            </TouchableOpacity>

            {!isRecording ? (
              <TouchableOpacity onPress={startRecording}>
                <Text style={styles.button}>🎥</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity onPress={stopRecording}>
                <Text style={[styles.button, { color: 'red' }]}>⏹ Stop</Text>
              </TouchableOpacity>
            )}
          </View> */}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CameraScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    alignItems: 'center',
  },
  button: {
    fontSize: 20,
    color: 'white',
    padding: 10,
    backgroundColor: '#00000080',
    borderRadius: 10,
  },
  border: {
    borderBottomColor: colors.white,
    paddingBottom: 2,
    borderBottomWidth: 1,
  },
});
