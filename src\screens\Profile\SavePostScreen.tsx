import React, { useEffect, useMemo, useState } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import CommonView from '../../component/CommonView';
import { useTranslation } from 'react-i18next';
import { colors } from '../../theme/colors';
import HorizontalListView from '../../component/HorizontalListView';
import { hp, wp } from '../../theme/fonts';
import CustomImage from '../../component/CustomImage';
import { IMAGES } from '../../assets/Images';
import ThreeDotsSVG from '../../assets/svgIcons/ThreeDotsSVG';
import { IMessage, MessageType } from '../../device-storage/realm/schemas/MessageSchema';
import { ChatService } from '../../service/ChatService';
import SavedTextMessage from '../../component/saved-messages/SavedTextMessage';
import SavedImageMessage from '../../component/saved-messages/SavedImageMessage';
import SavedVideoMessage from '../../component/saved-messages/SavedVideoMessage';

const SavePostScreen = () => {
  const { t } = useTranslation();

  const savedTextMessages = useMemo(() => {
    return ChatService.getSavedMessages(MessageType.TEXT);
  }, []);

  const savedImageMessages = useMemo(() => {
    return ChatService.getSavedMessages(MessageType.IMAGE);
  }, []);

  const savedVideoMessages = useMemo(() => {
    return ChatService.getSavedMessages(MessageType.VIDEO);
  }, []);

  const [selectedTab, setSelectedTab] = useState('Text');
  const [selectedItem, setSelectedItem] = useState<IMessage[]>([]);
  const tab = ['Text', 'Photos', 'Videos'];

  useEffect(() => {
    getSaveData();
  }, []);

  const getSaveData = async () => {
    try {
      const obj = {
        onSuccess: () => {
          setSelectedItem([]);
        },
        onFailure: () => {},
      };
      // Add actual data fetching logic here if needed
    } catch (error) {}
  };

  const onSelect = (item: IMessage) => {
    if (selectedItem.includes(item)) {
      setSelectedItem(selectedItem.filter((i) => i !== item));
    } else {
      setSelectedItem([...selectedItem, item]);
    }
  };

  const onUnsavedPost = () => {
    const obj = {
      data: { messageId: selectedItem.map((i) => i._id) },
      onSuccess: () => {
        getSaveData();
      },
      onFailure: () => {},
    };
    // Add actual unsave logic here if needed
  };

  return (
    <CommonView
      headerTitle={t('Saved')}
      containerStyle={styles.container}
      contentContainerStyle={{ paddingBottom: 20 }}
      renderRight={() =>
        selectedItem.length > 0 ? (
          <View style={styles.row}>
            <CustomImage source={IMAGES.ic_unSave} size={25} onPress={onUnsavedPost} />
          </View>
        ) : (
          <ThreeDotsSVG color={colors.white} size={15} />
        )
      }
    >
      <View style={styles.container}>
        <HorizontalListView
          data={tab}
          defaultSelectedItem={'Text'}
          selectedItem={setSelectedTab}
          bottomLine={false}
          styleViewTwo={true}
          contentContainerStyle={{
            gap: 10,
            alignSelf: 'center',
            paddingHorizontal: wp(13),
          }}
          scrollEnabled={false}
        />

        {selectedTab === 'Text' && (
          <View style={styles.viewContainer}>
            <FlatList
              data={savedTextMessages}
              keyExtractor={(item) => item?.localId?.toString()}
              renderItem={({ item }) => <SavedTextMessage message={item} />}
              inverted={true}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
        {selectedTab === 'Photos' && (
          <View style={styles.viewContainer}>
            <FlatList
              data={savedImageMessages}
              keyExtractor={(item) => item?.localId?.toString()}
              renderItem={({ item }) => (
                <View>
                  <SavedImageMessage message={item} onPress={() => onSelect(item)} />
                  <CustomImage
                    source={IMAGES.checkPurple}
                    size={20}
                    containerStyle={styles.checkIcon}
                    tintColor={selectedItem.includes(item) ? 'a' : 'transparent'}
                  />
                </View>
              )}
              contentContainerStyle={{ paddingBottom: 20 }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
        {selectedTab === 'Videos' && (
          <View style={styles.viewContainer}>
            <FlatList
              data={savedVideoMessages}
              keyExtractor={(item) => item?.localId?.toString()}
              renderItem={({ item }) => (
                <View>
                  <SavedVideoMessage message={item} onPress={() => onSelect(item)} />
                  {/* <CustomImage
                    source={IMAGES.checkPurple}
                    size={20}
                    containerStyle={styles.checkIcon}
                    tintColor={selectedItem.includes(item) ? 'a' : 'transparent'}
                  /> */}
                </View>
              )}
              contentContainerStyle={{ paddingBottom: 20 }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}
      </View>
    </CommonView>
  );
};

export default SavePostScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  viewContainer: {
    marginTop: hp(2),
    gap: 10,
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  checkIcon: {
    position: 'absolute',
    top: 10,
    right: 20,
  },
});
