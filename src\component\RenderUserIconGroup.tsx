import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import RenderUserIcon from './RenderUserIcon';

type Props = {
  mainSize: number;
  subSize: number;
  mainUrl?: string;
  subUrl?: string;
  mainImage?: any;
  subImage?: any;
  containerStyle?: ViewStyle;
};

const RenderUserIconGroup = ({
  mainSize,
  subSize,
  mainUrl,
  subUrl,
  mainImage,
  subImage,
  containerStyle,
}: Props) => {
  const overlapOffset = subSize / 3;

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Main icon */}
      <RenderUserIcon size={mainSize} url={mainUrl} image={mainImage} />

      {/* Sub icon positioned over main icon */}
      <View
        style={[
          styles.subIconWrapper,
          {
            height: subSize,
            width: subSize,
            bottom: -overlapOffset,
            right: -overlapOffset,
          },
        ]}
      >
        <RenderUserIcon size={subSize} url={subUrl} image={subImage} />
      </View>
    </View>
  );
};

export default RenderUserIconGroup;

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  subIconWrapper: {
    position: 'absolute',
  },
});
