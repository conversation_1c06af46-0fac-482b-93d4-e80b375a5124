import * as React from "react";
import Svg, { Path } from "react-native-svg";

interface SvgComponentProps {
  size?: number;
  color?: string;
}

const ContractSVG: React.FC<SvgComponentProps> = ({
  size = 16,
  color = "#232323",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <Path
        d="M11.748 4.838h3.665a.586.586 0 000-1.173h-3.078V.587a.587.587 0 00-1.173 0v3.665a.586.586 0 00.586.586zM.585 4.838H4.25a.586.586 0 00.586-.586V.587a.586.586 0 10-1.173 0v3.078H.585a.586.586 0 100 1.173zM.585 12.336h3.078v3.078a.586.586 0 001.173 0v-3.665a.586.586 0 00-.586-.586H.585a.586.586 0 100 1.173zM11.748 16a.587.587 0 00.587-.586v-3.078h3.078a.586.586 0 000-1.173h-3.665a.586.586 0 00-.586.586v3.665a.586.586 0 00.586.586z"
        fill={color}
      />
    </Svg>
  );
};

export default ContractSVG;


