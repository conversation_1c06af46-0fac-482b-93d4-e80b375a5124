import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  Image,
  StyleSheet,
} from 'react-native';
import { IMAGES } from '../../../../assets/Images';
import { IConversation } from '../../../../device-storage/realm/schemas/ConversationSchema';
import { ConversationType } from '../../../../device-storage/realm/schemas/MessageSchema';
import { SCREENS } from '../../../../navigation/screenNames';
import { colors } from '../../../../theme/colors';
import { navigateTo } from '../../../../utils/commonFunction';
import { FeatherIcons } from '../../../../utils/vectorIcons';
import { ChatSpecificScreenParamsT } from '../ChatSpecificScreen';
import { ConversationInfo } from '../../../../device-storage/realm/hooks/useConversationInfo';
import { getCommonGroups, ICommonGroup } from '../../../../service/ChatSpacesService';
import { useCallback, useState } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import useConversations from '../../../../hooks/conversations/useConversations';

type UserCommonGroupsProps = {
  conversationInfo: ConversationInfo | null;
  myId?: string;
};

const UserCommonGroups: React.FC<UserCommonGroupsProps> = ({ conversationInfo, myId }) => {
  const conversationId = conversationInfo?.id || '';

  const isP2P = conversationInfo?.type === ConversationType.P2P;
  if (!isP2P || conversationId === myId) return null;

  const { unArchivedConversations } = useConversations();

  const [commonGroups, setCommonGroups] = useState<ICommonGroup[]>([]);
  const [showAllGroups, setShowAllGroups] = useState(false);

  const [loading, setLoading] = useState(false);
  useFocusEffect(
    useCallback(() => {
      if (conversationInfo?.type === ConversationType.P2P) {
        handleCommonGroups();
      }

      return () => {};
    }, [conversationInfo]),
  );

  const handleCommonGroups = async () => {
    try {
      setLoading(true);
      const res = await getCommonGroups(conversationId);
      if (res) {
        setCommonGroups(res);
      }
    } catch (error) {
      setCommonGroups([]);
    } finally {
      setLoading(false);
    }
  };
  const visibleGroups = showAllGroups ? commonGroups : commonGroups.slice(0, 3);

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Common groups</Text>
      {conversationId !== '' && (
        <TouchableOpacity
          style={styles.memberRow}
          onPress={() => {
            navigateTo(SCREENS.CreateGroupScreen, {
              initialUsers: [conversationId],
            });
          }}
        >
          <View style={styles.imageContainer}>
            <FeatherIcons name="users" size={20} color={colors.white} />
          </View>

          <View style={styles.memberInfo}>
            <Text
              style={styles.memberName}
            >{`Create group with ${conversationInfo?.displayName}`}</Text>
            <Text style={styles.memberUsername}>{commonGroups.length} groups in common</Text>
          </View>
        </TouchableOpacity>
      )}

      {loading ? (
        <ActivityIndicator size="large" color={colors.mainPurple} style={{ marginTop: 20 }} />
      ) : (
        <FlatList
          data={visibleGroups}
          keyExtractor={(item) => {
            return item.chatSpaceId;
          }}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.memberRow}
              onPress={() => {
                const conversation = unArchivedConversations.find(
                  (conv: IConversation) => conv.id === item.chatSpaceId || conv.id === item._id,
                );

                const paramsData: ChatSpecificScreenParamsT = {
                  userData: {
                    displayName: item.name,
                    displayPic: item.displayPic as string,
                    type: ConversationType.GROUP,
                    id: item.chatSpaceId || item._id,
                    chatSpaceId: item.chatSpaceId,
                    isActive: false,
                    isDeleted: false,
                    conversation: conversation
                      ? JSON.parse(JSON.stringify(conversation))
                      : {
                          id: item.chatSpaceId,
                          role: 'member',
                          type: ConversationType.GROUP,
                          memberCount: item.memberCount,
                        },
                  },
                  data: { convId: item.chatSpaceId },
                  isFollowing: false,
                };
                navigateTo(SCREENS.ChatSpecificScreen, paramsData);
              }}
            >
              <Image
                source={
                  item.displayPic ? { uri: item.displayPic } : IMAGES.profile_image
                  // : require('../../../assets/Image/beautiful.png')
                }
                style={styles.memberAvatar}
              />

              <View style={styles.memberInfo}>
                <Text style={styles.memberName}>{item.name}</Text>
                <Text style={styles.memberUsername}>{item.memberCount} members</Text>
              </View>
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
          scrollEnabled={false}
        />
      )}

      {commonGroups.length > 3 && (
        <TouchableOpacity
          onPress={() => setShowAllGroups(!showAllGroups)}
          style={{ marginTop: 8, alignSelf: 'center' }}
        >
          <Text style={{ color: colors.mainPurple, fontWeight: '600' }}>
            {showAllGroups ? 'See less' : 'See more'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default UserCommonGroups;

const styles = StyleSheet.create({
  section: {
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: colors.black_23,
  },
  memberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderColor: '#eee',
    gap: 12,
  },
  memberAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black_23,
  },
  memberUsername: {
    fontSize: 13,
    color: colors.gray_80,
  },
  roleBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 14,
    backgroundColor: 'rgba(128, 128, 128, 0.08)',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray_80,
    lineHeight: 15,
    textAlign: 'center',
  },
  imageContainer: {
    backgroundColor: colors.mainPurple,
    borderRadius: 40,
    height: 50,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
