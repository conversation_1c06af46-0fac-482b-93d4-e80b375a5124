import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import PressableHighlight from '../../../component/utils/PressableHighlight';
import { AppStyles } from '../../../theme/appStyles';
import UserAvatar from '../../../component/utils/UserAvatar';
import { ChatSpace } from '../../../types/socketPayload.type';
import { ConversationInfo } from '../../../device-storage/realm/hooks/useConversationInfo';
import { MembershipStatus } from '../../../types/chats.types';
import { getDefaultChatPermissions } from '../../../lib/chatLib';
import { navigateTo } from '../../../utils/commonFunction';
import { SCREENS } from '../../../navigation/screenNames';
import { ChatSpecificScreenParamsT } from '../Chats/ChatSpecificScreen';
import { IConversation } from '../../../device-storage/realm/schemas/ConversationSchema';

type ExploreChatSpaceCardProps = {
  chatSpace: ChatSpace;
};
const ExploreChatSpaceCard = (props: ExploreChatSpaceCardProps) => {
  const { chatSpace } = props;
  const convInfo = useMemo(() => {
    const convInfo: ConversationInfo = {
      id: chatSpace.chatSpaceId,
      displayName: chatSpace.name,
      displayPic: chatSpace.displayPic,
      isPrivate: chatSpace.isPrivate,
      bio: chatSpace.description,
      memberCount: chatSpace.memberCount,
      type: chatSpace.type,
      isLoading: false,
      isFromCache: true,
      conversationSettings: null,
      membershipStatus: MembershipStatus.DISCOVERING,
      permissions: getDefaultChatPermissions(MembershipStatus.DISCOVERING, chatSpace.type),
      inviteCode: chatSpace.inviteCode || '',
    };
    return convInfo;
  }, [props.chatSpace]);
  const handleChatSpacePress = () => {
    console.log('Pressed chat space:', convInfo);
    const paramsData: ChatSpecificScreenParamsT = {
      userData: {
        displayName: props.chatSpace.name,
        conversation: {} as IConversation,
        type: chatSpace.type,
        id: chatSpace.chatSpaceId,
      },
      data: {
        convId: chatSpace.chatSpaceId,
        initialConversationInfo: convInfo,
      },
      isFollowing: false,
    };
    navigateTo(SCREENS.ChatSpecificScreen, paramsData);
  };
  return (
    <PressableHighlight onPress={handleChatSpacePress}>
      <View style={[AppStyles.profileCard]}>
        <UserAvatar imgUrl={props.chatSpace.displayPic} />
        <View style={{ flexDirection: 'column', gap: 4 }}>
          <Text numberOfLines={1} style={[AppStyles.baseText, { fontWeight: 600 }]}>
            {props.chatSpace.name}
          </Text>
          <Text numberOfLines={1}>{props.chatSpace.description}</Text>
        </View>
      </View>
    </PressableHighlight>
  );
};

export default React.memo(ExploreChatSpaceCard);
