import React from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
  Animated,
  Easing,
  PanResponder,
} from 'react-native';

const CustomModal = ({ visible, onClose, onCloseHandlers, children }) => {
  const slideAnim = React.useRef(new Animated.Value(0)).current;
  const pan = React.useRef(new Animated.ValueXY()).current;
  const [modalHeight, setModalHeight] = React.useState(250);
  const minHeight = 250;
  const maxHeight = 650;

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: Animated.event([null, { dy: pan.y }], { useNativeDriver: false }),
      onPanResponderGrant: (e, gestureState) => {
        setModalHeight(gestureState.moveY); // Track initial drag position
      },
      onPanResponderRelease: (e, gestureState) => {
        pan.extractOffset(); // Reset pan state after drag

        if (gestureState.moveY > 700) {
          console.log('pressed');

          onClose();
        } else {
          Animated.spring(pan, {
            toValue: { x: 0, y: 0 },
            useNativeDriver: false,
          }).start();
        }
      },
    }),
  ).current;

  React.useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 500,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Interpolate the modal height based on the vertical drag position (pan.y)
  const height = pan.y.interpolate({
    inputRange: [-350, 0],
    outputRange: [maxHeight, minHeight],
    extrapolate: 'clamp',
  });

  if (!visible) return null;

  return (
    <Modal transparent visible={visible} animationType="slide" animationOutTiming={1000}>
      <TouchableWithoutFeedback
        onPress={() => {
          console.log('pressed');
          onCloseHandlers();
          onClose();
        }}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            style={[styles.modalContent, { height }]}
            onStartShouldSetResponder={() => true}
            onResponderStart={(e) => e.stopPropagation()}
          >
            <View
              style={{ height: 30, width: '100%', justifyContent: 'center' }}
              {...panResponder.panHandlers}
            >
              <View style={styles.handlebar} />
            </View>
            {children}
          </Animated.View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    zIndex: 10001,
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  handlebar: {
    width: 40,
    height: 5,
    backgroundColor: 'lightgray',
    borderRadius: 2.5,
    alignSelf: 'center',
  },
});

export default CustomModal;
