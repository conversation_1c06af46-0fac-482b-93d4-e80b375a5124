import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { IMAGES } from '../../../assets/Images';
import { colors } from '../../../theme/colors';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface IProps {
  onPress?: () => void;
}

const CreateChannelButton = ({ onPress }: IProps) => {
  return (
    <TouchableOpacity style={styles.plusIcon} onPress={onPress}>
      <Ionicons name={'add-outline'} size={35} color={colors.mainPurple} />
    </TouchableOpacity>
  );
};

export default CreateChannelButton;

const styles = StyleSheet.create({
  plusIcon: {
    height: 50,
    width: 50,
    borderRadius: 25,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: '25%',
    right: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,

    elevation: 10,
  },
  plusIconStyle: {
    height: 20,
    width: 20,
    resizeMode: 'contain',
  },
});
