import * as React from "react";
import Svg, { <PERSON>, <PERSON>, <PERSON>, Defs, SvgProps } from "react-native-svg";

interface Props extends SvgProps {
  size?: number;
  color?: string;
};

const UnpinSVG2: React.FC<Props> = ({
  size = 26,
  color = "#232323",
  ...props
}) => {
  const scale = size / 26; // Original width is 26

  return (
    <Svg
      width={26 * scale}
      height={31 * scale}
      viewBox="0 0 26 31"
      fill="none"
      {...props}
    >
      <G>
        <Mask
          id="a"
          maskUnits="userSpaceOnUse"
          x={0}
          y={1}
          width={26}
          height={26}
        >
          <Path
            d="M0 13.677L12.34 1l12.677 12.34-12.34 12.678L0 13.677z"
            fill="#fff"
          />
        </Mask>
        <G
          mask="url(#a)"
          stroke={color}
          strokeWidth={1.03661}
          strokeMiterlimit={10}
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <Path d="M12.667 25.288l-.114-8.453M17.44 16.769l-9.773.131-.012-.916a2.211 2.211 0 012.182-2.24l5.35-.073a2.212 2.212 0 012.24 2.182l.013.916zM15.748 7.358a1.647 1.647 0 01-1.166.502l-4.3.057a1.658 1.658 0 11-.045-3.316l4.3-.058a1.658 1.658 0 011.21 2.815zM14.661 13.678l-.854-5.807M11.058 7.908l-.698 5.828" />
        </G>
      </G>
      <Path
        stroke={color}
        strokeWidth={2}
        d="M2.87673 6.69073L23.5632 19.6549"
      />
      <Path stroke={color} d="M1.26552 6.66129L21.952 19.6254" />
      <Defs />
    </Svg>
  );
};

export default UnpinSVG2;

