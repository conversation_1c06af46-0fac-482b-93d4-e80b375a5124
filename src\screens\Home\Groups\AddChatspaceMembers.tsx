import React, { useMemo, useState } from 'react';
import { ActivityIndicator, FlatList, StyleSheet, Text, View } from 'react-native';
import CommonView from '../../../component/CommonView';
import { colors } from '../../../theme/colors';
import { commonFontStyle, hp } from '../../../theme/fonts';
import { useTranslation } from 'react-i18next';
import { useContacts } from '../../../hooks/contacts/useContacts';
import ContactItem from '../../../component/ContactItem';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { IContact } from '../../../device-storage/realm/schemas/ContactSchema';
import { addChatsSpaceMember } from '../../../api/Chatspace/chatspace.api';
import { ChatService } from '../../../service/ChatService';
import SingleTickSVG from '../../../assets/svgIcons/SingleTickSVG';
import SearchInput from '../../../component/SearchInput';
import { BlockedUserData } from '../../Profile/Settings/Privacy/BlockedPeopleScreen';
import { opacity } from 'react-native-reanimated/lib/typescript/Colors';
import { IUser, UserSchema } from '../../../device-storage/realm/schemas/UserSchema';

type AddChatspaceMembersParams = {
  AddChatspaceMembers: {
    data: string[];
    chatSpaceId: string;
  };
};

const AddChatspaceMembers = () => {
  const { t } = useTranslation();
  const route = useRoute<RouteProp<AddChatspaceMembersParams, 'AddChatspaceMembers'>>();
  const memberIds: string[] = route.params?.data || [];
  const memberIdsSet = new Set(memberIds);
  const chatSpaceId = route.params?.chatSpaceId || '';
  const [search, setSearch] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  const navigation = useNavigation();
  const blocked = ChatService.getBlockedEntities();
  const blockedUsers = (blocked.data as unknown as BlockedUserData[]) || [];
  const blockedSet = new Set(blockedUsers?.map((user) => user.id));

  const [selectedContacts, setSelectedContacts] = useState<UserSchema[]>([]);  

  const { registeredContacts: registeredContacts } = useContacts();

  const displayContacts = useMemo(() => {
    let contacts = registeredContacts?.filter((contact) => !memberIds.includes(contact.id || ''));

    if (search.trim()) {
      const lowercasedSearch = search.toLowerCase();
      contacts = contacts?.filter((contact) =>
        contact.name?.toLowerCase().includes(lowercasedSearch),
      );
    }

    const unblockedContacts = contacts?.filter((contact) => !blockedSet.has(contact?.id || ''));
    return unblockedContacts || [];
  }, [registeredContacts, memberIds, search]);

  const handleSelectContact = (contact: UserSchema) => {
    const isAlreadyMember = memberIds.includes(contact.id);
    if (isAlreadyMember) return;

    const isSelected = selectedContacts.some((c) => c.id === contact.id);
    if (isSelected) {
      setSelectedContacts((prev) => prev.filter((c) => c.id !== contact.id));
    } else {
      setSelectedContacts((prev) => [...prev, contact]);
    }
  };

  const newMemberIds = selectedContacts
    .map((c) => c.id)
    .filter((id): id is string => typeof id === 'string');

  const handleAddMembers = async (chatSpaceId: string, newMemberIds: string[]) => {
    if (isAdding || newMemberIds.length === 0) {
      return;
    }

    setIsAdding(true);
    try {
      const res = await addChatsSpaceMember(chatSpaceId, newMemberIds, []);
      if (res?.systemMessage) {
        ChatService.onIncomingMessage(res.systemMessage);
      }
      navigation.goBack();
    } catch (error) {
      console.error('Failed to add members:', error);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <CommonView
      headerTitle="Add members"
      containerStyle={{ paddingHorizontal: hp(1), paddingVertical: hp(2) }}
      renderRight={() =>
        isAdding ? (
          <ActivityIndicator color={colors.white} size="small" />
        ) : (
          <SingleTickSVG
            color={colors.white}
            size={17}
            onPress={() => {
              handleAddMembers(chatSpaceId, newMemberIds);
            }}
          />
        )
      }
    >
      <View>
        <SearchInput
          value={search}
          onChangeText={(text) => {
            setSearch(text);
          }}
        />
      </View>

      <View>
        <View style={{ paddingHorizontal: hp(1) }}>
          <Text style={styles.groupText}>{t('Contacts')}</Text>
        </View>

        <FlatList
          data={registeredContacts || []}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
          renderItem={({ item }) => {
            const isSelected = selectedContacts.some((c) => c.id === item.id);
            return (
              <ContactItem
                data={item}
                isSelected={isSelected}
                onPress={() => handleSelectContact(item)}
                isSelectionMode={true}
                disabled={memberIdsSet.has(item.id || '')}
                containerStyles={{
                  opacity: memberIdsSet.has(item.id || '') ? 0.3 : 1,
                }}
                subText={memberIdsSet.has(item.id || '') ? 'Already a Member' : undefined}
              />
            );
          }}
        />
      </View>
    </CommonView>
  );
};

export default AddChatspaceMembers;

const styles = StyleSheet.create({
  groupText: {
    ...commonFontStyle(500, 16, colors.black_23),
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
});
