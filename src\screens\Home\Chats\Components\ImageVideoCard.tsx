import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { ChatService } from '../../../../service/ChatService';
import UnpinSVG from '../../../../assets/svgIcons/UnpinSVG';
import LottieView from 'lottie-react-native';
import GallerySVG from '../../../../assets/svgIcons/GallerySVG';
import VideoSVG from '../../../../assets/svgIcons/VideoSVG';
import { dayPipe, navigateTo } from '../../../../utils/commonFunction';
import MessageStatusIcon from '../../../../component/PersonalChat/MessageStatusIcon';
import { hexToRgba, hp, SCREEN_WIDTH } from '../../../../theme/fonts';
import MediaPlaySVG from '../../../../assets/svgIcons/MediaPlaySVG';
import { colors } from '../../../../theme/colors';
import { formatBytes } from '../../../../common/CFilename';
import {
  IMessage,
  MessageStatus,
  MessageType,
} from '../../../../device-storage/realm/schemas/MessageSchema';
import { createThumbnail } from 'react-native-create-thumbnail';

interface IImageProps {
  msgData: IMessage;
  isMyData: boolean;
  isPinned?: boolean;
  onClickImage?: () => void;
}

const guidelineBaseWidth = 375;

const ImageVideoCard = ({ msgData, isPinned, isMyData, onClickImage = () => {} }: IImageProps) => {
  const [thumbnail, setThumbnail] = useState<string>(
    msgData?.videoThumbnail ? msgData?.videoThumbnail : (msgData?.mediaUrl as string),
  );
  const images = [thumbnail];

  const isVideo = msgData?.messageType === MessageType.VIDEO;
  const isImage = msgData?.messageType === MessageType.IMAGE;
  const isTextImage = Number(msgData?.text?.length) > 0;
  const scaleWidth = (size: number) => (SCREEN_WIDTH / guidelineBaseWidth) * size;

  const getThumbnail = () => {
    createThumbnail({
      url: msgData?.mediaUrl as string,
      timeStamp: 1000,
    })
      .then((response: any) => {
        console.log('createThumbnail', response);
        setThumbnail(response.path);
        ChatService.updateThumbnail(msgData?.globalId as string, response.path);
      })
      .catch((err: any) => console.log({ err }));
  };

  useEffect(() => {
    if (isVideo && !msgData?.videoThumbnail) {
      getThumbnail();
    }
  }, []);

  return (
    <View>
      {/* Pin message */}
      {isPinned ? (
        <View
          style={{
            position: 'absolute',
            top: '45%',
            [isMyData ? 'left' : 'right']: 35,
          }}
        >
          <TouchableOpacity
            onPress={() => {
              ChatService.unpinMessage(msgData.localId, msgData.receiverId);
            }}
            style={styles.pinIconButton}
          >
            <UnpinSVG size={12} color={colors.mainPurple} />
          </TouchableOpacity>
        </View>
      ) : null}
      <View
        style={[
          styles.imageContainer,
          {
            alignSelf: isMyData ? 'flex-end' : 'flex-start',
            borderTopLeftRadius: isMyData ? 15 : 1,
            borderBottomRightRadius: isMyData ? 1 : 15,
            ...(isTextImage && {
              padding: 8,
              backgroundColor: isMyData ? colors._EFEBFC_purple : colors.white,
              borderTopLeftRadius: isMyData ? 15 : 1,
              borderBottomRightRadius: isMyData ? 1 : 15,
            }),
          },
        ]}
      >
        <View style={{ flexDirection: 'column', justifyContent: 'center' }}>
          {images?.map((list: any, i: any) => {
            const isTopImage = i === 0;
            const imageWidth = isTopImage ? (isTextImage ? 240 : 250) : 220;
            const imageHeight = isTopImage ? (isTextImage ? 163 : 169) : 145;

            return (
              <View
                key={i}
                style={{
                  alignSelf: 'center',
                  zIndex: isTopImage ? 10 : 1,
                  marginTop: isTopImage ? 0 : -135,
                }}
              >
                <View
                  style={{
                    width: imageWidth,
                    height: imageHeight,
                    borderRadius: 15,
                    overflow: 'hidden',
                    backgroundColor: '#e0e0e0', // Fallback background
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {msgData?.status === MessageStatus.UPLOADING ? (
                    <View
                      style={{
                        position: 'absolute',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 9,
                        width: '100%',
                        height: '100%',
                        backgroundColor: hexToRgba(colors.black, 0.4),
                        //  hexToRgba(
                        //   isVideo ? colors._33C200_green : colors.red_ff4444,
                        //   0.3,
                        // ),
                      }}
                    >
                      <LottieView
                        source={require('../../../../assets/animatedLottiImages/loader.json')}
                        autoPlay
                        loop
                        style={{ width: 32, height: 32 }}
                      />
                    </View>
                  ) : null}
                  <Image
                    source={{ uri: list }}
                    style={{ width: '100%', height: '100%' }}
                    resizeMode="cover"
                  />

                  {/* Overlay Content */}
                  {isTopImage && msgData?.imageStatus !== 'download' ? (
                    <View
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        backgroundColor:
                          images?.length == 1 ? 'transparent' : hexToRgba('#000000', 0.5),
                        padding: 6,
                        paddingHorizontal: 13,
                        flexDirection: 'row',
                        justifyContent: images?.length == 1 ? 'flex-end' : 'space-between',
                        alignItems: 'center',
                        borderBottomLeftRadius: 15,
                        borderBottomRightRadius: 15,
                      }}
                    >
                      {images?.length > 1 ? (
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 13 }}>
                          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                            <GallerySVG size={11} />
                            <Text style={{ color: 'white', fontSize: 12 }}>8</Text>
                          </View>
                          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                            <VideoSVG size={11} />
                            <Text style={{ color: 'white', fontSize: 12 }}>2</Text>
                          </View>
                        </View>
                      ) : null}

                      {/* Right side: time + tick */}
                      {!isVideo && !isTextImage ? (
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: isMyData ? 'flex-end' : 'flex-start',
                            flex: 1,
                          }}
                        >
                          <Text
                            style={{
                              color: 'white',
                              fontSize: 12,
                              marginLeft: isMyData ? 8 : 0,
                              textShadowColor: hexToRgba('#000000', 0.3),
                              textShadowOffset: { width: 0.3, height: 0.3 },
                              textShadowRadius: 1,
                              marginRight: 4,
                            }}
                          >
                            {dayPipe(msgData?.createdAt, 'time')}
                          </Text>
                          {isMyData && <MessageStatusIcon status={msgData.status} />}
                        </View>
                      ) : null}
                    </View>
                  ) : null}

                  {/* Download Overlay */}
                  {/* {msgData?.imageStatus === 'download' && ( */}
                  {isVideo ? (
                    <View
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: hexToRgba('#000000', 0.3),
                        zIndex: msgData?.status == MessageStatus.UPLOADING ? 4 : 6,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: 15,
                      }}
                    >
                      <TouchableOpacity
                        onPress={() => {
                          // if (selectedMsgs?.length !== 0) {
                          onClickImage();
                          // }
                        }}
                      >
                        {/* {isMyData  ? ( */}
                        {msgData?.status !== MessageStatus.UPLOADING && (
                          <MediaPlaySVG
                            size={35}
                            color={isMyData ? hexToRgba(colors.black_23, 0.8) : colors.white}
                            backgroundColor={
                              isMyData
                                ? hexToRgba(colors.white, 0.8)
                                : hexToRgba(colors.black_23, 0.4)
                            }
                          />
                        )}
                        {/* ) : (
                                <DownloadSVG
                                  size={35}
                                  color={isMyData ? colors.white : colors.black}
                                  backgroundColor={isMyData ? colors.mainPurple : colors.white}
                                /> */}
                      </TouchableOpacity>
                      <View
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          backgroundColor:
                            images?.length == 1 ? 'transparent' : hexToRgba('#000000', 0.5),
                          padding: 6,
                          paddingHorizontal: 13,
                          flexDirection: isMyData ? 'row' : 'row-reverse',
                          justifyContent: !isVideo ? 'flex-end' : 'space-between',
                          alignItems: 'center',
                          borderBottomLeftRadius: 15,
                          borderBottomRightRadius: 15,
                        }}
                      >
                        {msgData?.fileSize && isVideo ? (
                          <Text
                            style={{
                              color: 'white',
                              fontSize: 12,
                              marginRight: 4,
                            }}
                          >
                            {formatBytes(msgData?.fileSize)}
                          </Text>
                        ) : null}
                        <View
                          style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: isMyData ? 'flex-end' : 'flex-start',
                          }}
                        >
                          <Text
                            style={{
                              color: 'white',
                              fontSize: 12,
                              marginRight: 4,
                            }}
                          >
                            {dayPipe(msgData?.createdAt, 'time')}
                          </Text>
                          {isMyData ? <MessageStatusIcon status={msgData.status} /> : null}
                        </View>
                      </View>
                    </View>
                  ) : null}
                </View>
              </View>
            );
          })}
        </View>

        {Number(msgData?.text?.length) > 0 ? (
          <View>
            <Text
              style={{
                width: 240,
                color: colors.black_23,
                fontSize: 14,
                paddingVertical: 5,
              }}
            >
              {msgData?.text}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: isMyData ? 'flex-end' : 'flex-start',
                // flex: 1,
              }}
            >
              <Text
                style={{
                  color: isMyData ? colors._7A6A90_purple : colors._757575_gray,
                  fontSize: 12,
                  marginLeft: isMyData ? 8 : 0,
                  marginRight: 4,
                }}
              >
                {dayPipe(msgData?.createdAt, 'time')}
              </Text>
              {isMyData && <MessageStatusIcon status={msgData.status} />}
            </View>
          </View>
        ) : null}
      </View>
    </View>
  );
};

export default ImageVideoCard;

const styles = StyleSheet.create({
  pinIconButton: {
    backgroundColor: colors.gray_f3,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    alignSelf: 'flex-start',
    maxWidth: hp(35),
    borderRadius: 15,
  },
});
