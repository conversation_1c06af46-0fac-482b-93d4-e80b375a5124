import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const ReplySVG: React.FC<IconProps> = ({
    size = 23,
    color = "#fff",
    ...restProps
}) => {

    return (
        <Svg
            width={size}
            height={(size * 21) / 23} // maintains original aspect ratio (23:21)
            viewBox="0 0 23 21"
            fill="none"
            {...restProps}
        >
            <Path
                d="M8.288.925a.739.739 0 00-.52.215L1.144 7.735l-.092.11a.738.738 0 00.08.924h0l6.627 6.596a.738.738 0 001.039 0 .731.731 0 000-1.034l-5.364-5.35h11.665a5.558 5.558 0 013.904 1.643 5.505 5.505 0 011.598 3.907v4.913a.736.736 0 001.147.61l.111-.09a.738.738 0 00.217-.52v-4.913a6.972 6.972 0 00-2.03-4.946 7.035 7.035 0 00-4.946-2.072H3.434l5.374-5.34a.732.732 0 000-1.033l-.113-.092a.737.737 0 00-.407-.123z"
                fill={color}
                stroke={color}
                strokeWidth={0.15}
            />
        </Svg>
    );
};

export default ReplySVG;

