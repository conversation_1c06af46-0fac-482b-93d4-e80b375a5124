import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Animated,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Dropdown } from 'react-native-element-dropdown';
import { sample_languages } from './Translation/data';
import { navigationRef } from '../../../../navigation/RootContainer';
import { colors } from '../../../../theme/colors';
import { Ionicons } from '../../../../utils/vectorIcons';
import { zIndex } from '../../../../utils/Filters';
import useAudioRecorder, {
  UseAudioRecorderReturn,
} from '../../../../hooks/common/useAudioRecorder';
import RecordingIndicator from './Translation/RecordingIndicator';
import CloningProgressModal from './Translation/CloningProgressModal';
import {
  embedAudioApi,
  EmbeddingDoc,
  EmbeddingStatus,
  getEmbeddingDocumentApi,
} from '../../../../api/calls/calls.api';

type SelectedLanguage = (typeof sample_languages)[0];

// Header Component

export type RecordingState = 'none' | 'recording' | 'paused' | 'stopped';

// Main Voice Sample Screen Component
const VoiceSampleScreen = () => {
  const safeAreaInsets = useSafeAreaInsets();
  const [languages, setLanguages] = React.useState<SelectedLanguage[]>(sample_languages);
  const [selectedLang, setSelectedLang] = React.useState<SelectedLanguage>(sample_languages[0]);
  const [currState, setCurrState] = useState<RecordingState>('none');
  const min_required_minutes = 3;
  const AudioRecorder = useAudioRecorder();
  const [embeddingState, setEmbeddingState] = useState<
    'loading' | 'idle' | 'success' | 'error' | 'pending'
  >('loading');

  const [embeddingDoc, setEmbeddingDoc] = useState<EmbeddingDoc | null>(null);

  useEffect(() => {
    async function getEmbeddingDocument() {
      try {
        const data = await getEmbeddingDocumentApi();
        // it means user has not uploaded any audio
        if (data.status_code === 404) {
          setEmbeddingState('idle');
          return;
        }
        const embeddingDoc = data.data;
        setEmbeddingDoc(embeddingDoc);
        if (embeddingDoc.status === EmbeddingStatus.COMPLETED) {
          setEmbeddingState('success');
        }
        if (embeddingDoc.status === EmbeddingStatus.PENDING) {
          setEmbeddingState('pending');
        }
        if (embeddingDoc.status === EmbeddingStatus.FAILED) {
          setEmbeddingState('error');
        }
        if (embeddingDoc.status === EmbeddingStatus.IN_PROGRESS) {
          setEmbeddingState('pending');
        }
      } catch (error) {
        console.error(error);
      }
    }
    getEmbeddingDocument();
  }, []);

  const [showCloningProgressModal, setShowCloningProgressModal] = useState(false);

  function retryRecording() {
    setCurrState('none');
    AudioRecorder.refreshRecording();
  }
  function pauseRecording() {
    AudioRecorder.pauseRecording();
    setCurrState('paused');
  }
  function resumeRecording() {
    AudioRecorder.resumeRecording();
    setCurrState('recording');
  }
  async function startRecording() {
    try {
      await AudioRecorder.startRecording();
      setCurrState('recording');
    } catch (error) {
      console.error(error);
    }
  }

  async function handlEmbedAudio(fileInfo: { fileUrl: string; fileName: string }) {
    try {
      const data = await embedAudioApi(fileInfo);
      console.log(data, '?????????????????????????????//');
    } catch (error) {
      console.error(error);
    }
  }

  async function stopRecording() {
    const audioPath = await AudioRecorder.stopRecording();
    setCurrState('stopped');
    return audioPath;
  }
  async function submitRecording() {
    let AudioPath = AudioRecorder.audioPath;
    if (currState !== 'stopped') {
      AudioPath = await stopRecording();
    }
    if (AudioPath && AudioRecorder.recordTime >= min_required_minutes * 60) {
      const apiResponse = await AudioRecorder.uploadAudio(AudioPath);
      handlEmbedAudio({ fileName: apiResponse.fileName, fileUrl: apiResponse.url });
      setShowCloningProgressModal(true);
      console.log('hello');
      setEmbeddingState('pending');
    }
  }

  return (
    <SafeAreaView
      style={{
        ...styles.container,
        paddingTop: safeAreaInsets.top,
        paddingBottom: safeAreaInsets.bottom,
      }}
    >
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <>
        {/* Header */}
        <Header
          title={currState === 'none' ? 'Voice Sample' : 'Read The Story Below'}
          showRefresh={currState !== 'none'}
          onRefresh={retryRecording}
        />

        {/* Content */}
        <View style={styles.content}>
          {/* Language Dropdown */}
          <LanguageDropdown
            languages={languages}
            selectedLang={selectedLang}
            setSelectedLang={setSelectedLang}
          />

          {/* Scrollable Text */}
          <ScrollableText text={selectedLang.text} />

          {/* Recording Button */}
          {embeddingState === 'idle' && (
            <RecordingButton
              state={currState}
              startRecording={startRecording}
              pauseRecording={pauseRecording}
              stopRecording={stopRecording}
              submitRecording={submitRecording}
              duration={AudioRecorder.recordTime}
              AudioRecorder={AudioRecorder}
              resumeRecording={resumeRecording}
              minRequired={min_required_minutes}
              retryRecording={retryRecording}
            />
          )}
          {embeddingState === 'pending' && (
            <View style={styles.pendingContainer}>
              <Text style={styles.pendingText}>Cloning in progress...</Text>
            </View>
          )}
          {embeddingState === 'success' && (
            <View style={styles.pendingContainer}>
              <Text style={styles.pendingText}>Cloning completed successfully</Text>
            </View>
          )}
          {embeddingState === 'error' && (
            <View style={styles.pendingContainer}>
              <Text style={styles.pendingText}>Cloning failed</Text>
              <Text style={styles.errorText}>{embeddingDoc?.error}</Text>
            </View>
          )}
        </View>
      </>
      {showCloningProgressModal && (
        <CloningProgressModal
          showModal={showCloningProgressModal}
          onCloseModal={() => setShowCloningProgressModal(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  pendingContainer: {
    padding: 20,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pendingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.red_ff4444,
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: '#ffffff',
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  statusIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  signalBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 2,
  },
  bar: {
    width: 3,
    backgroundColor: '#1f2937',
    borderRadius: 1,
  },
  bar1: { height: 4 },
  bar2: { height: 6 },
  bar3: { height: 8 },
  bar4: { height: 10 },
  batteryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  battery: {
    width: 20,
    height: 10,
    borderWidth: 1,
    borderColor: '#1f2937',
    borderRadius: 2,
    backgroundColor: '#1f2937',
  },
  batteryTip: {
    width: 2,
    height: 6,
    backgroundColor: '#1f2937',
    borderTopRightRadius: 1,
    borderBottomRightRadius: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',

    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  dropdownContainer: {
    marginTop: 24,
    marginBottom: 24,
  },
  dropdownLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: '#ffffff',
  },
  dropdownText: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
  },
  textContainer: {
    flex: 1,
    marginBottom: 40,
  },
  scrollView: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    backgroundColor: '#fafafa',
  },
  scrollContent: {
    padding: 20,
  },
  textContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
    textAlign: 'left',
  },
  recordingContainer: {
    alignItems: 'center',
    paddingBottom: 40,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.mainPurple,
    alignItems: 'center',
    justifyContent: 'center',

    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 12,
  },
  recordButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.mainPurple,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  ripple: {
    height: 40,
    width: 40,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: 'red',
  },
});

export default VoiceSampleScreen;

type HeaderProps = {
  title: string;
  showRefresh?: boolean;
  onRefresh?: () => void;
};
const Header = ({ title, showRefresh, onRefresh }: HeaderProps) => (
  <View style={styles.header}>
    <View style={{ flexDirection: 'row', gap: 5, alignItems: 'center' }}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => {
          navigationRef.goBack();
        }}
      >
        <Icon name="arrow-back" size={24} color="#1f2937" />
      </TouchableOpacity>
      <Text style={styles.headerTitle}> {title} </Text>
    </View>
    {showRefresh && (
      <TouchableOpacity onPress={onRefresh}>
        <Ionicons name="refresh" size={24} color="#1f2937" />
      </TouchableOpacity>
    )}
  </View>
);

// Language Dropdown Component
type LanguageDropdownProps = {
  languages: typeof sample_languages;
  selectedLang: React.SetStateAction<SelectedLanguage>;
  setSelectedLang: React.Dispatch<React.SetStateAction<SelectedLanguage>>;
};
const LanguageDropdown = ({ languages, selectedLang, setSelectedLang }: LanguageDropdownProps) => (
  <View style={styles.dropdownContainer}>
    <Text style={styles.dropdownLabel}>Select Language</Text>

    <Dropdown
      data={languages}
      placeholderStyle={{
        color: 'black',
      }}
      labelField={'title'}
      onChange={(item: any) => {
        setSelectedLang(item);
      }}
      valueField={'id'}
      value={1}
      selectedTextStyle={{
        color: 'black',
      }}
      style={{
        backgroundColor: 'white',
        borderRadius: 10,
        borderBlockColor: colors.gray_80,
        borderWidth: 1,
        padding: 10,
      }}
      containerStyle={{
        backgroundColor: 'white',
        padding: 5,
      }}
      renderItem={(item) => {
        return (
          <View
            style={{
              backgroundColor: 'white',
            }}
          >
            <Text style={[{ marginVertical: 5, marginHorizontal: 5, padding: 10, color: 'black' }]}>
              {item.title}
            </Text>
          </View>
        );
      }}
    />
  </View>
);

// Scrollable Text Component
const ScrollableText = ({ text }: { text: string }) => {
  const scrollRef = useRef<ScrollView>(null);
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({ x: 0, y: 0, animated: true });
    }
  }, [text]);

  return (
    <View style={styles.textContainer}>
      <ScrollView
        ref={scrollRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={true}
        contentContainerStyle={styles.scrollContent}
      >
        <Text style={styles.textContent}>{text}</Text>
      </ScrollView>
    </View>
  );
};

type RecordingButtonProps = {
  state: RecordingState;
  duration: number;
  startRecording: () => void;
  pauseRecording: () => void;
  submitRecording: () => void;
  AudioRecorder: UseAudioRecorderReturn;
  resumeRecording: () => void;
  stopRecording: () => void;
  minRequired: number;
  retryRecording: () => void;
};
// Recording Button Component
const RecordingButton = ({
  state,
  startRecording,
  pauseRecording,
  submitRecording,
  duration,
  AudioRecorder,
  resumeRecording,
  stopRecording,
  minRequired,
  retryRecording,
}: RecordingButtonProps) => (
  <View style={styles.recordingContainer}>
    {state === 'none' && (
      <TouchableOpacity style={styles.recordButton} onPress={startRecording}>
        <Icon name="mic" size={32} color="#ffffff" />
      </TouchableOpacity>
    )}
    {state === 'none' && <Text style={styles.recordingText}>Tap to start recording</Text>}
    {state !== 'none' && (
      <RecordingIndicator
        state={state}
        duration={duration}
        minRequired={minRequired}
        pauseRecording={pauseRecording}
        resumeRecording={resumeRecording}
        stopRecording={stopRecording}
        AudioRecorder={AudioRecorder}
        retryRecording={retryRecording}
      />
    )}
    {state !== 'none' && (
      <View
        style={{
          width: '100%',
          alignItems: 'center',
          position: 'relative',
          marginTop: 20,
        }}
      >
        <TouchableOpacity
          style={{
            backgroundColor: colors.mainPurple,
            padding: 15,
            borderRadius: 30,
            width: '80%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={() => {
            submitRecording();
          }}
        >
          {AudioRecorder.isFileUploading && <ActivityIndicator size="small" color="white" />}
          {!AudioRecorder.isFileUploading && (
            <Text
              style={{
                color: 'white',
                fontSize: 20,
                fontWeight: '500',
              }}
            >
              Submit
            </Text>
          )}
        </TouchableOpacity>

        {duration < minRequired * 60 && (
          <View
            style={{
              position: 'absolute',
              backgroundColor: 'rgba(255, 255, 255, 0.4)',
              zIndex: zIndex.level_2,
              padding: 15,
              borderRadius: 30,
              width: '80%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text
              style={{
                color: 'transparent',
                fontSize: 20,
                fontWeight: '500',
              }}
            >
              .
            </Text>
          </View>
        )}
      </View>
    )}
  </View>
);
