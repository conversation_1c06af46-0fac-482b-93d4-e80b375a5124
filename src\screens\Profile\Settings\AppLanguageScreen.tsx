import React, { useState } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View, SafeAreaView } from 'react-native';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { colors } from '../../../theme/colors';
import { languages } from '../../../lib/Languages';
import { useNavigation } from '@react-navigation/native';
import { hp } from '../../../theme/fonts';

const AppLanguageScreen = () => {
  const navigation = useNavigation();
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>('English');

  const handleLanguageSelect = (languageId: string) => {
    setSelectedLanguage(languageId);
    console.log('Selected language:', languageId);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBackWithTitle title="App Language" onBack={() => navigation.goBack()} />
      <View style={styles.whiteContainer}>
        <FlatList
          data={languages}
          showsVerticalScrollIndicator={false}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => {
            const isSelected = selectedLanguage === item.id;
            return (
              <TouchableOpacity
                style={styles.languageOptionRow}
                activeOpacity={1}
                onPress={() => handleLanguageSelect(item.id)}
              >
                <View style={styles.languageLeft}>
                  <Text style={styles.symbol}>{item.symbol}</Text>
                  <Text style={[styles.language, isSelected && { color: colors.mainPurple }]}>
                    {item.id}
                  </Text>
                </View>
                {isSelected && (
                  <View style={styles.radioOuter}>
                    <View style={styles.radioInner} />
                  </View>
                )}
              </TouchableOpacity>
            );
          }}
          contentContainerStyle={{ paddingBottom: 100 }}
        />
      </View>
    </SafeAreaView>
  );
};

export default AppLanguageScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.mainPurple,
  },
  whiteContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: hp(2),
    marginTop: 8,
  },
  languageOptionRow: {
    borderWidth: 1,
    borderColor: colors._DADADA_gray,
    borderRadius: 15,
    padding: 10,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  languageLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  symbol: {
    fontSize: 20,
    marginRight: 10,
  },
  language: {
    color: colors.black_23,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 20,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.black_23,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.mainPurple,
  },
});
