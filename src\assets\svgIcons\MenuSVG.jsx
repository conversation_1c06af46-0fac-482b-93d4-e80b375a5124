import * as React from "react";
import Svg, { Path } from "react-native-svg";

function MenuSVG({ width = 17, height = 19, color = "#fff", ...props }) {


    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 17 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M16.292 10.263H.708c-.39 0-.708-.467-.708-1.042S.317 8.18.708 8.18h15.584c.39 0 .708.466.708 1.041s-.317 1.042-.708 1.042zM16.292 2.277H.708C.318 2.277 0 1.81 0 1.235S.317.193.708.193h15.584c.39 0 .708.467.708 1.042s-.317 1.042-.708 1.042zM16.292 18.249H.708c-.39 0-.708-.467-.708-1.042s.317-1.041.708-1.041h15.584c.39 0 .708.466.708 1.041s-.317 1.042-.708 1.042z"
                fill={color}
            />
        </Svg>
    );
};

export default MenuSVG;
