import React, { useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors } from '../../../theme/colors';
import { ChatService } from '../../../service/ChatService';
import { IChatScreenProps } from '../../Home/Chats/ChatSpecificScreen';
import { RouteProp, useRoute } from '@react-navigation/native';
import { getSocket } from '../../../socket-client/socket';
import HeaderBackWithTitle from '../../../component/HeaderBackWithTitle';
import { hp } from '../../../theme/fonts';
import CommonView from '../../../component/CommonView';
import { ConversationInfo } from '../../../device-storage/realm/hooks/useConversationInfo';

type DisappearingMsgParams = {
  DisappearingMsg: {
    conversationInfo: ConversationInfo;
  };
};

const options = [
  { label: '1 min', durationMs: 1 * 60 * 1000 },
  { label: '24 hours', durationMs: 24 * 60 * 60 * 1000 },
  { label: '7 days', durationMs: 7 * 24 * 60 * 60 * 1000 },
  { label: '90 days', durationMs: 90 * 24 * 60 * 60 * 1000 },
  { label: 'Off', durationMs: null },
];

const DisappearingMessagesScreen = () => {
  const { t } = useTranslation();
  const route = useRoute<RouteProp<DisappearingMsgParams, 'DisappearingMsg'>>();
  const { conversationInfo } = route.params;
  const conversationId = conversationInfo?.id;
  const socket = getSocket();

  const [selectedOption, setSelectedOption] = useState<number | null>(
    conversationInfo?.conversationSettings?.disappearDuration ?? null,
  );

  const handleOptionSelect = (durationMs: number | null) => {
    setSelectedOption(durationMs);
    const disappearDuration = durationMs ?? null;

    ChatService.disappearDuration(conversationId, disappearDuration);
    socket.emit('update_disappear_duration', {
      conversationId,
      durationInMs: disappearDuration,
    });
  };

  return (
    <CommonView headerTitle="Disappearing Messages">
      <Text style={styles.descriptionText}>
        This chat history will be cleared automatically after the set period
      </Text>

      {options.map((option) => (
        <TouchableOpacity
          key={option.label}
          style={styles.optionItemRow}
          onPress={() => handleOptionSelect(option.durationMs)}
        >
          <Text style={styles.optionText}>{option.label}</Text>
          <View
            style={[
              styles.optionItemRadioOuter,
              selectedOption === option.durationMs && styles.radioOuterSelected,
            ]}
          >
            {selectedOption === option.durationMs && <View style={styles.radioInner} />}
          </View>
        </TouchableOpacity>
      ))}
    </CommonView>
  );
};

export default DisappearingMessagesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  descriptionText: {
    fontSize: 15,
    fontWeight: '500',
    color: 'rgba(35, 35, 35, 1)',
    marginBottom: 16,
  },
  optionItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.black_23,
  },
  optionItemRadioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: colors.gray_80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuterSelected: {
    borderColor: 'rgba(106, 77, 187, 1)',
    backgroundColor: 'rgba(106, 77, 187, 1)',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 1)',
  },
});
