import { useEffect } from 'react';
import { useRealm } from '../../device-storage/realm/realm';
import useSocket from '../../socket-client/useSocket';
import { getSocket } from '../../socket-client/socket';
import {
  registerChatSocketEvents,
  unregisterChatSocketEvents,
} from '../../socket-client/socketListeners';
import { ChatService } from '../../service/ChatService';

export const useChatsInit = () => {
  const { socket } = useSocket();
  const realm = useRealm();
  useEffect(() => {
    if (!socket) return;
    ChatService.setSocket(socket);
    ChatService.setRealm(realm);
    registerChatSocketEvents(socket);
    return () => unregisterChatSocketEvents(socket);
  }, [socket]);
};
