import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface IconProps extends SvgProps {
    size?: number;
    color?: string;
}

const BackArrowSVG: React.FC<IconProps> = ({
    size = 20,
    color = "#fff",
    ...restProps
}) => {
    return (
        <Svg
            width={size}
            height={(size * 16) / 20} // preserves aspect ratio
            viewBox="0 0 20 16"
            fill="none"
            {...restProps}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.408.925a.812.812 0 010 1.15L3.295 7.188h15.372a.812.812 0 110 1.625H3.295l5.113 5.112a.812.812 0 11-1.149 1.15l-6.5-6.5a.812.812 0 010-1.15l6.5-6.5a.813.813 0 011.149 0z"
                fill={color}
            />
        </Svg>
    );
};

export default BackArrowSVG;

