import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"



const DraftsSVG = ({
    size = 24,
    color = "#232323",
    ...props
}) => {
    return (
        <Svg
            width={size}
            height={size}
            viewBox="0 0 19 19"
            fill="none"
            {...props}
        >
            <Path
                d="M13.33 1.83c1.142-1.244 3.104-1.235 4.438-.004h-.001c.625.523.933 1.353.933 2.174 0 .77-.27 1.634-.816 2.251l-.114.12-9.7 9.6c-.1.1-.227.23-.47.23a.555.555 0 01-.39-.152l-.081-.078-3.5-3.5C3.528 12.37 3.4 12.244 3.4 12c0-.244.128-.37.229-.47l9.7-9.7zm2.215.545c-.477-.024-.944.12-1.27.491l-.005.005L5.14 12l2.46 2.557 9.23-9.228.134-.147A1.85 1.85 0 0017.4 4c0-.469-.185-.839-.56-1.12l-.005-.004a2.21 2.21 0 00-1.29-.501z"
                fill={color}
            />
            <Path
                d="M4.001 11.2c.096 0 .211 0 .314.029.104.03.197.09.261.205l3.495 3.495.086.107c.081.124.144.293.144.464a.502.502 0 01-.155.346.507.507 0 01-.319.15l-5.931 2.472c-.01.082-.04.149-.102.19-.068.045-.152.042-.193.042a.556.556 0 01-.39-.151l-.081-.078a.61.61 0 01-.192-.372 1.023 1.023 0 01.068-.431l.002-.007.093.04-.092-.04 2.5-6 .002-.006.09.045-.09-.045a.979.979 0 01.181-.263.979.979 0 01.264-.182l.022-.01H4zm-1.113 5.512l3.436-1.447-1.989-1.99-1.447 3.437zM8.1 2.3c.182 0 .356.09.483.217.126.127.217.3.217.483 0 .182-.09.356-.217.483A.702.702 0 018.1 3.7H1.6a.701.701 0 01-.483-.217A.702.702 0 01.9 3a.7.7 0 01.217-.483A.7.7 0 011.6 2.3h6.5zM4.1 6.3c.182 0 .356.09.483.217.126.127.217.3.217.483 0 .182-.09.356-.217.483A.702.702 0 014.1 7.7H1.6a.701.701 0 01-.483-.217A.702.702 0 01.9 7a.7.7 0 01.217-.483A.7.7 0 011.6 6.3h2.5z"
                fill={color}
            />
        </Svg>
    )
}

export default DraftsSVG

