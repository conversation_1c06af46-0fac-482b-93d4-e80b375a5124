import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

interface IconProps extends SvgProps {
  size?: number;
  color?: string;
}

const BellSVG: React.FC<IconProps> = ({
  size = 20,
  color = '#000000',
  ...restProps
}) => {
  return (
    <Svg
      width={size}
      height={(size * 20) / 17} // maintains original aspect ratio
      viewBox="0 0 17 20"
      fill="none"
      {...restProps}>
      <Path
        d="M8.5 0.924805C9.36867 0.924805 10.0752 1.63133 10.0752 2.5V3.75977C10.0752 4.00903 9.87332 4.20996 9.625 4.20996C9.37658 4.20996 9.1748 4.00819 9.1748 3.75977V2.5C9.1748 2.12817 8.87183 1.8252 8.5 1.8252C8.12817 1.8252 7.8252 2.12817 7.8252 2.5V3.75977C7.8252 4.00819 7.62342 4.20996 7.375 4.20996C7.12668 4.20996 6.9248 4.00903 6.9248 3.75977V2.5C6.9248 1.63133 7.63133 0.924805 8.5 0.924805Z"
        fill={color}
        stroke={color}
        strokeWidth="0.15"
      />
      <Path
        d="M10.75 15.9248C10.9984 15.9248 11.2002 16.1266 11.2002 16.375C11.2002 17.8639 9.98892 19.0752 8.5 19.0752C7.01108 19.0752 5.7998 17.8639 5.7998 16.375C5.7998 16.1266 6.00158 15.9248 6.25 15.9248C6.49842 15.9248 6.7002 16.1266 6.7002 16.375C6.7002 17.3671 7.50792 18.1748 8.5 18.1748C9.49208 18.1748 10.2998 17.3671 10.2998 16.375C10.2998 16.1266 10.5016 15.9248 10.75 15.9248Z"
        fill={color}
        stroke={color}
        strokeWidth="0.15"
      />
      <Path
        d="M8.5 3.1748C11.4364 3.1748 13.8252 5.56358 13.8252 8.5V10.7783C13.8252 12.2958 14.4895 13.7275 15.6484 14.707L15.6494 14.708C15.9221 14.9418 16.0752 15.2737 16.0752 15.625C16.0752 16.2866 15.5375 16.8252 14.875 16.8252H2.125C1.46333 16.8252 0.924806 16.2867 0.924805 15.625C0.924805 15.2739 1.07765 14.9416 1.34473 14.7129H1.3457C2.5108 13.7281 3.1748 12.2951 3.1748 10.7783V8.5C3.1748 5.56358 5.56358 3.1748 8.5 3.1748ZM8.5 4.0752C6.05967 4.0752 4.0752 6.05967 4.0752 8.5V10.7783C4.0752 12.5614 3.29422 14.2443 1.93262 15.3955L1.93164 15.3945C1.86221 15.454 1.8252 15.5381 1.8252 15.625C1.8252 15.7906 1.95942 15.9248 2.125 15.9248H14.875C15.0406 15.9248 15.1748 15.7906 15.1748 15.625C15.1748 15.5593 15.1532 15.496 15.1143 15.4443L15.0703 15.3975C13.7065 14.244 12.9248 12.5607 12.9248 10.7783V8.5C12.9248 6.05967 10.9403 4.0752 8.5 4.0752Z"
        fill={color}
        stroke={color}
        strokeWidth="0.15"
      />
    </Svg>
  );
};

export default BellSVG; 