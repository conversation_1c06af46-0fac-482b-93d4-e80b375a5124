import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";

interface SvgComponentProps extends SvgProps {
  size?: number;
  color?: string;
}


const SearchSVG: React.FC<SvgComponentProps> = ({
  size = 21,
  color = "#fff",
  ...props
}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 21 21"
      fill="none"
      {...props}
    >
      <Path
        d="M8.454 16.892a8.43 8.43 0 005.18-1.779L19.22 20.7a1.05 1.05 0 001.485-1.485l-5.586-5.586a8.446 8.446 0 10-6.665 3.264zM3.965 3.96a6.347 6.347 0 110 8.977 6.324 6.324 0 010-8.977z"
        fill={color}
      />
    </Svg>
  );
};

export default SearchSVG;


