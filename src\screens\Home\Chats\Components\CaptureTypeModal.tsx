import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import ModalWrapper from '../../../../component/ModalWrapper'
import { colors } from '../../../../theme/colors';
import { hp } from '../../../../theme/fonts';
import Foundation from 'react-native-vector-icons/Foundation';
import Feather from 'react-native-vector-icons/Feather';


interface ICaptureProps {
    imageModal: boolean;
    setImageModal: (value: boolean) => void;
    onCameraPress: (value: 'photo' | 'video' | 'any') => void;
}

const CaptureTypeModal = ({ imageModal, setImageModal, onCameraPress }: ICaptureProps) => {
    return (
        <ModalWrapper isVisible={imageModal} onCloseModal={() => setImageModal(false)}>
            <View
                style={{
                    paddingHorizontal: hp(2),
                    flexDirection: 'row',
                    justifyContent: 'space-evenly',
                    paddingVertical: 15,
                }}
            >
                <TouchableOpacity
                    style={{
                        width: 60,
                        height: 60,
                        borderRadius: 8,
                        justifyContent: 'center',
                        backgroundColor: colors.gray_f3,
                        alignItems: 'center',
                    }}
                    onPress={() => onCameraPress('video')}
                >
                    <Feather name={'video'} size={25} color={colors.gray_80} />
                </TouchableOpacity>
                <TouchableOpacity
                    style={{
                        width: 60,
                        height: 60,
                        borderRadius: 8,
                        justifyContent: 'center',
                        backgroundColor: colors.gray_f3,
                        alignItems: 'center',
                    }}
                    onPress={() => onCameraPress('photo')}
                >
                    <Foundation name={'photo'} size={25} color={colors.gray_80} />
                </TouchableOpacity>
            </View>
        </ModalWrapper>
    )
}

export default CaptureTypeModal

const styles = StyleSheet.create({})