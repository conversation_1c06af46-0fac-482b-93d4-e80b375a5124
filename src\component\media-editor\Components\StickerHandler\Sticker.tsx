import React from 'react';
import { Image, ImageStyle, StyleProp } from 'react-native';

interface StickerProps {
  id: number;
  sticker: { id: number; image: any };
  iconStyle: StyleProp<ImageStyle>;
}

const Sticker: React.FC<StickerProps> = ({ id, sticker, iconStyle }) => {
  return <Image source={sticker.image} style={iconStyle} resizeMode="contain" key={id} />;
};

export default React.memo(Sticker);
