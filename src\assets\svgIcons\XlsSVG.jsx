import * as React from "react";
import Svg, { Path } from "react-native-svg";

function XlsSVG({ width = 22, height = 23, color = "#6A4DBB", ...props }) {
    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 22 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <Path
                d="M19.594 6.816v3.137H2.406V1.574A1.074 1.074 0 013.48.5h9.797v5.457a.86.86 0 00.86.86h5.457zM2.406 20.266h17.188v1.16a1.074 1.074 0 01-1.075 1.074H3.48a1.074 1.074 0 01-1.074-1.074v-1.16z"
                fill={color}
            />
            <Path
                d="M19.594 5.955h-5.455V.5l5.455 5.455zM20.926 10.813H1.074A1.074 1.074 0 000 11.886v6.445a1.074 1.074 0 001.074 1.074h19.852A1.074 1.074 0 0022 18.332v-6.445a1.075 1.075 0 00-1.074-1.075zm-10.894 7.255L9.309 16.3l-.636 1.768h-1.08l1.146-3.185-1.096-2.732h1.235l.65 1.6.527-1.6h1.088l-1.038 3.075 1.153 2.842h-1.226zm4.375 0h-2.664V12.15h1.307v5.033h1.357v.884z"
                fill={color}
            />
        </Svg>
    );
}

export default XlsSVG;

